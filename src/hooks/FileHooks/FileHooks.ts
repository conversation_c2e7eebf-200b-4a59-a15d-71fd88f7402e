import { useMutation, UseMutationResult } from '@tanstack/react-query'
import * as FileService from '@/services/FileService'
import { useCallback } from 'react'
import toast from 'react-hot-toast'

export const useUploadFile = (pathKey: string): UseMutationResult<FileService.UploadFileResponse, Error, File, unknown> => {
  return useMutation({
    mutationFn: (file: File) => FileService.uploadFile(file, pathKey),
    onSuccess: () => {
      toast.success('File uploaded successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message ?? 'An error occurred while uploading the file')
    },
  })
}

export const useDeleteFile = (): UseMutationResult<FileService.DeleteFileResponse, Error, { pathKey: string; fileName: string }, unknown> => {
  return useMutation({
    mutationFn: ({ pathKey, fileName }: { pathKey: string; fileName: string }) => FileService.deleteFile(pathKey, fileName),
    onSuccess: () => {
      toast.success('File deleted successfully')
    },
    onError: (error: Error) => {
      toast.error(error.message ?? 'An error occurred while deleting the file')
    },
  })
}

export const useDownloadFile = () => {
  return useCallback(async (pathKey: string, fileName: string) => {
    try {
      const blob = await FileService.downloadFile(pathKey, fileName)
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
    } catch (_error) {
      toast.error('An error occurred while downloading the file')
    }
  }, [])
}
