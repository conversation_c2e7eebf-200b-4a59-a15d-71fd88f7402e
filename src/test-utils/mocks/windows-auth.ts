// Windows Authentication mock utilities
import { http, HttpResponse } from 'msw'

// NTLM Token structure simulation
export const mockWindowsAuth = {
  // Generate NTLM Type 1 message (negotiate)
  generateNTLMToken: (domain: string, workstation?: string) => {
    const type1 = {
      protocol: 'NTLMSSP',
      type: 1,
      flags: 0x00008201,
      domain: domain.toUpperCase(),
      workstation: workstation ?? 'WORKSTATION',
    }
    return Buffer.from(JSON.stringify(type1)).toString('base64')
  },

  // Generate NTLM Type 2 message (challenge)
  generateChallenge: () => {
    const challenge = {
      protocol: 'NTLMSSP',
      type: 2,
      targetName: 'SERVER',
      flags: 0x00008201,
      challenge: Array.from({ length: 8 }, () => Math.floor(Math.random() * 256)),
      targetInfo: {
        serverName: 'DIGIFLOW-SERVER',
        domainName: 'CORP',
        dnsServerName: 'digiflow.corp.local',
        dnsDomainName: 'corp.local',
      },
    }
    return Buffer.from(JSON.stringify(challenge)).toString('base64')
  },

  // Generate NTLM Type 3 message (authenticate)
  generateAuthResponse: (username: string, domain: string, _challenge: string) => {
    const type3 = {
      protocol: 'NTLMSSP',
      type: 3,
      domain: domain.toUpperCase(),
      username: username.toLowerCase(),
      workstation: 'WORKSTATION',
      sessionKey: Array.from({ length: 16 }, () => Math.floor(Math.random() * 256)),
      flags: 0x00008201,
    }
    return Buffer.from(JSON.stringify(type3)).toString('base64')
  },

  // Generate Kerberos ticket
  generateKerberosTicket: (principal: string, realm: string) => {
    const ticket = {
      version: 5,
      realm: realm.toUpperCase(),
      sname: {
        nameType: 2,
        nameString: ['HTTP', 'digiflow.corp.local'],
      },
      encPart: {
        etype: 18, // AES256-CTS-HMAC-SHA1-96
        kvno: 2,
        cipher: Array.from({ length: 128 }, () => Math.floor(Math.random() * 256)),
      },
      principal,
      authTime: new Date().toISOString(),
      startTime: new Date().toISOString(),
      endTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours
    }
    return `Negotiate ${Buffer.from(JSON.stringify(ticket)).toString('base64')}`
  },

  // Simulate Windows user info from Active Directory
  generateWindowsUser: (username: string, domain: string) => ({
    sAMAccountName: username,
    userPrincipalName: `${username}@${domain}.local`,
    displayName: username
      .split('.')
      .map((n) => n.charAt(0).toUpperCase() + n.slice(1))
      .join(' '),
    givenName: username.split('.')[0],
    sn: username.split('.')[1] ?? 'User',
    mail: `${username}@${domain}.com`,
    domain: domain.toUpperCase(),
    distinguishedName: `CN=${username},OU=Users,DC=${domain},DC=local`,
    memberOf: [`CN=Domain Users,CN=Users,DC=${domain},DC=local`, `CN=DigiFlow Users,OU=Groups,DC=${domain},DC=local`],
    objectSid: `S-1-5-21-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.floor(Math.random() * 9000) + 1000}`,
    lastLogon: new Date().toISOString(),
    accountEnabled: true,
  }),

  // Generate Windows groups/roles
  generateWindowsGroups: (domain: string) => [
    {
      cn: 'DigiFlow Admins',
      distinguishedName: `CN=DigiFlow Admins,OU=Groups,DC=${domain},DC=local`,
      objectSid: `S-1-5-21-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.floor(Math.random() * 9000) + 1000}`,
      members: ['admin.user', 'system.admin'],
    },
    {
      cn: 'DigiFlow Approvers',
      distinguishedName: `CN=DigiFlow Approvers,OU=Groups,DC=${domain},DC=local`,
      objectSid: `S-1-5-21-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.floor(Math.random() * 9000) + 1000}`,
      members: ['manager.one', 'manager.two', 'director.finance'],
    },
    {
      cn: 'DigiFlow Users',
      distinguishedName: `CN=DigiFlow Users,OU=Groups,DC=${domain},DC=local`,
      objectSid: `S-1-5-21-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.random().toString().slice(2, 12)}-${Math.floor(Math.random() * 9000) + 1000}`,
      members: ['*'], // All domain users
    },
  ],

  // Check if browser supports Windows auth
  isBrowserWindowsAuthCapable: () => {
    const ua = window.navigator.userAgent
    return /Edge\/|Trident\/|MSIE/.test(ua) || window.location.hostname === 'localhost' || window.location.hostname.endsWith('.local')
  },

  // Simulate SSO token from browser
  simulateBrowserSSO: (username: string, domain: string) => {
    if (mockWindowsAuth.isBrowserWindowsAuthCapable()) {
      return {
        credentials: 'include',
        headers: {
          Authorization: mockWindowsAuth.generateKerberosTicket(`${username}@${domain.toUpperCase()}.LOCAL`, domain),
        },
      }
    }
    return null
  },
}

// Windows Auth MSW handlers
export const windowsAuthHandlers = [
  // NTLM negotiation endpoint
  http.get('http://localhost:5055/auth/windows/negotiate', ({ request }) => {
    const authHeader = request.headers.get('Authorization')

    if (!authHeader) {
      // Initial challenge
      return new HttpResponse(null, {
        status: 401,
        headers: {
          'WWW-Authenticate': 'NTLM, Negotiate',
        },
      })
    }

    if (authHeader.startsWith('NTLM ')) {
      const token = authHeader.substring(5)
      const decoded = Buffer.from(token, 'base64').toString()

      try {
        const message = JSON.parse(decoded)

        if (message.type === 1) {
          // Type 1: Send challenge
          return new HttpResponse(null, {
            status: 401,
            headers: {
              'WWW-Authenticate': `NTLM ${mockWindowsAuth.generateChallenge()}`,
            },
          })
        } else if (message.type === 3) {
          // Type 3: Authenticate
          const user = mockWindowsAuth.generateWindowsUser(message.username, message.domain)

          return HttpResponse.json({
            authenticated: true,
            authMethod: 'NTLM',
            user,
            token: Buffer.from(
              JSON.stringify({
                user: user.sAMAccountName,
                domain: user.domain,
                sid: user.objectSid,
                exp: Date.now() + 8 * 60 * 60 * 1000,
              }),
            ).toString('base64'),
          })
        }
      } catch {
        // Invalid NTLM token
        return HttpResponse.json({ error: 'Invalid NTLM token' }, { status: 400 })
      }
    }

    if (authHeader.startsWith('Negotiate ')) {
      // Kerberos authentication
      const ticket = authHeader.substring(10)

      try {
        const decoded = Buffer.from(ticket, 'base64').toString()
        const kerberos = JSON.parse(decoded)

        // Check ticket expiry
        if (kerberos.endTime && new Date(kerberos.endTime) < new Date()) {
          return HttpResponse.json({ error: 'Kerberos ticket expired' }, { status: 400 })
        }

        const [username, realm] = kerberos.principal.split('@')
        const domain = realm.split('.')[0]

        const user = mockWindowsAuth.generateWindowsUser(username, domain.toLowerCase())

        return HttpResponse.json({
          authenticated: true,
          authMethod: 'Kerberos',
          user,
          token: Buffer.from(
            JSON.stringify({
              user: user.sAMAccountName,
              domain: user.domain,
              sid: user.objectSid,
              principal: kerberos.principal,
              exp: Date.now() + 8 * 60 * 60 * 1000,
            }),
          ).toString('base64'),
        })
      } catch {
        return HttpResponse.json({ error: 'Invalid Kerberos ticket' }, { status: 400 })
      }
    }

    return HttpResponse.json({ error: 'Unsupported authentication method' }, { status: 401 })
  }),

  // Current Windows user endpoint
  http.get('http://localhost:5055/auth/windows/current', ({ request }) => {
    const authHeader = request.headers.get('Authorization')

    if (!authHeader?.startsWith('Bearer ')) {
      return HttpResponse.json({ error: 'No token provided' }, { status: 401 })
    }

    try {
      const token = authHeader.substring(7)
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString())

      if (decoded.exp < Date.now()) {
        return HttpResponse.json({ error: 'Token expired' }, { status: 401 })
      }

      const user = mockWindowsAuth.generateWindowsUser(decoded.user, decoded.domain.toLowerCase())

      return HttpResponse.json({
        user,
        groups: mockWindowsAuth
          .generateWindowsGroups(decoded.domain.toLowerCase())
          .filter((g) => g.members.includes(decoded.user) || g.members.includes('*'))
          .map((g) => g.cn),
      })
    } catch {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 })
    }
  }),

  // Impersonation endpoint
  http.post('http://localhost:5055/auth/windows/impersonate', async ({ request }) => {
    const body = (await request.json()) as { targetUser: string; reason: string }
    const { targetUser, reason } = body
    const authHeader = request.headers.get('Authorization')

    if (!authHeader?.startsWith('Bearer ')) {
      return HttpResponse.json({ error: 'No token provided' }, { status: 401 })
    }

    try {
      const token = authHeader.substring(7)
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString())

      // Check if user has impersonation rights (admin)
      const userGroups = mockWindowsAuth
        .generateWindowsGroups(decoded.domain.toLowerCase())
        .filter((g) => g.members.includes(decoded.user))
        .map((g) => g.cn)

      if (!userGroups.includes('DigiFlow Admins')) {
        return HttpResponse.json({ error: 'Insufficient privileges for impersonation' }, { status: 403 })
      }

      const [targetUsername, targetDomain] = targetUser.split('@')
      const impersonatedUser = mockWindowsAuth.generateWindowsUser(targetUsername, targetDomain || decoded.domain.toLowerCase())

      return HttpResponse.json({
        originalUser: decoded.user,
        impersonatedUser,
        reason,
        token: Buffer.from(
          JSON.stringify({
            user: impersonatedUser.sAMAccountName,
            domain: impersonatedUser.domain,
            sid: impersonatedUser.objectSid,
            originalUser: decoded.user,
            impersonation: true,
            exp: Date.now() + 4 * 60 * 60 * 1000, // 4 hours for impersonation
          }),
        ).toString('base64'),
      })
    } catch {
      return HttpResponse.json({ error: 'Invalid request' }, { status: 400 })
    }
  }),

  // Token refresh endpoint
  http.post('http://localhost:5055/auth/windows/refresh', ({ request }) => {
    const authHeader = request.headers.get('Authorization')

    if (!authHeader?.startsWith('Bearer ')) {
      return HttpResponse.json({ error: 'No token provided' }, { status: 401 })
    }

    try {
      const token = authHeader.substring(7)
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString())

      // Allow refresh within 1 hour of expiry
      if (decoded.exp < Date.now() - 60 * 60 * 1000) {
        return HttpResponse.json({ error: 'Token expired beyond refresh window' }, { status: 401 })
      }

      const newToken = Buffer.from(
        JSON.stringify({
          ...decoded,
          exp: Date.now() + 8 * 60 * 60 * 1000,
          refreshed: true,
          refreshedAt: new Date().toISOString(),
        }),
      ).toString('base64')

      return HttpResponse.json({
        token: newToken,
        expiresIn: 8 * 60 * 60, // seconds
      })
    } catch {
      return HttpResponse.json({ error: 'Invalid token' }, { status: 401 })
    }
  }),
]
