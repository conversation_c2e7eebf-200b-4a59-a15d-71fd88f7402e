import React, { Profiler, ProfilerOnRenderCallback, useRef, useEffect } from 'react'

/**
 * Performance profiler utilities to identify re-render causes
 */

interface RenderInfo {
  id: string
  phase: 'mount' | 'update'
  actualDuration: number
  baseDuration: number
  startTime: number
  commitTime: number
  interactions: Set<any>
  renderCount: number
}

const renderLogs = new Map<string, RenderInfo[]>()

/**
 * Profiler callback to track component renders
 */
export const onRenderCallback: ProfilerOnRenderCallback = (
  id: string,
  phase: 'mount' | 'update' | 'nested-update',
  actualDuration: number,
  baseDuration: number,
  startTime: number,
  commitTime: number,
) => {
  const info: RenderInfo = {
    id,
    phase: phase === 'nested-update' ? 'update' : phase,
    actualDuration,
    baseDuration,
    startTime,
    commitTime,
    interactions: new Set(),
    renderCount: (renderLogs.get(id)?.length ?? 0) + 1,
  }

  if (!renderLogs.has(id)) {
    renderLogs.set(id, [])
  }
  renderLogs.get(id)!.push(info)

  // Log excessive renders
  if (info.renderCount > 10) {
    console.warn(`⚠️ Component "${id}" rendered ${info.renderCount} times!`, {
      phase,
      duration: `${actualDuration.toFixed(2)}ms`,
      timestamp: new Date(commitTime).toISOString(),
    })
  }
}

/**
 * HOC to wrap components with Profiler
 */
export function withProfiler<P extends object>(Component: React.ComponentType<P>, id: string): React.ComponentType<P> {
  return (props: P) => (
    <Profiler id={id} onRender={onRenderCallback}>
      <Component {...props} />
    </Profiler>
  )
}

/**
 * Hook to track why a component re-rendered
 */
export function useWhyDidYouUpdate(_name: string, props: Record<string, any>) {
  const previousProps = useRef<Record<string, any>>()

  useEffect(() => {
    if (previousProps.current) {
      const allKeys = Object.keys({ ...previousProps.current, ...props })
      const changedProps: Record<string, any> = {}

      allKeys.forEach((key) => {
        if (previousProps.current?.[key] !== props[key]) {
          changedProps[key] = {
            from: previousProps.current![key],
            to: props[key],
          }
        }
      })

      if (Object.keys(changedProps).length > 0) {
        // console.log(`[${name}] Re-rendered due to:`, changedProps)
      }
    }

    previousProps.current = props
  })
}

/**
 * Hook to count renders
 */
export function useRenderCount(_componentName: string) {
  const renderCount = useRef(0)

  useEffect(() => {
    renderCount.current += 1
    // console.log(`[${componentName}] Render count: ${renderCount.current}`)
  })

  return renderCount.current
}

/**
 * Performance report generator
 */
export function generatePerformanceReport() {
  const report: Record<string, any> = {}

  renderLogs.forEach((logs, componentId) => {
    const totalRenders = logs.length
    const avgDuration = logs.reduce((sum, log) => sum + log.actualDuration, 0) / totalRenders
    const updates = logs.filter((log) => log.phase === 'update').length

    report[componentId] = {
      totalRenders,
      updates,
      mounts: totalRenders - updates,
      avgDuration: `${avgDuration.toFixed(2)}ms`,
      totalDuration: `${logs.reduce((sum, log) => sum + log.actualDuration, 0).toFixed(2)}ms`,
    }
  })

  // console.table(report)
  return report
}

/**
 * Clear performance logs
 */
export function clearPerformanceLogs() {
  renderLogs.clear()
  // console.log('Performance logs cleared')
}

/**
 * Component to add temporary performance monitoring
 * Usage: Wrap your app or specific components
 */
export const PerformanceMonitor: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  useEffect(() => {
    // Log report on unmount
    return () => {
      // console.log('=== Performance Report ===')
      generatePerformanceReport()
    }
  }, [])

  // Add keyboard shortcut to generate report
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        generatePerformanceReport()
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [])

  return <>{children}</>
}

/**
 * Debug component that shows render count
 */
export const RenderCounter: React.FC<{ name: string }> = ({ name }) => {
  const count = useRenderCount(name)

  if (process.env.NODE_ENV !== 'development') return null

  return (
    <div
      style={{
        position: 'fixed',
        bottom: 10,
        right: 10,
        background: count > 10 ? '#ff4444' : '#44ff44',
        color: 'white',
        padding: '5px 10px',
        borderRadius: 5,
        fontSize: 12,
        zIndex: 9999,
      }}
    >
      {name}: {count} renders
    </div>
  )
}
