import { Platform } from 'react-native';

/**
 * Allowed origins for WebView navigation
 * These are the only domains that the WebView should be allowed to navigate to
 */
export const ALLOWED_ORIGINS = [
  'https://digiflow.digiturk.com.tr',
  'https://digiflowtest.digiturk.com.tr',
  'http://digiflow.digiturk.com.tr',
  'http://digiflowtest.digiturk.com.tr',
  // Add development URLs only in __DEV__ mode
  ...((__DEV__) ? [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://digiflowtest.digiturk.com.tr',
    'https://digiflowtest.digiturk.com.tr',
    'http://digiflow.digiturk.com.tr',
    'https://digiflow.digiturk.com.tr',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173',
  ] : []),
];

/**
 * Validates if a URL is from an allowed origin
 */
export const isAllowedOrigin = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    const origin = `${urlObj.protocol}//${urlObj.host}`;

    return ALLOWED_ORIGINS.some(allowed => {
      // Check exact match
      if (origin === allowed) {return true;}

      // Check if it's a subdomain of allowed domain
      const allowedHost = new URL(allowed).host;
      return urlObj.host === allowedHost || urlObj.host.endsWith(`.${allowedHost}`);
    });
  } catch (error) {
    console.error('Invalid URL for origin validation:', url);
    return false;
  }
};

/**
 * Validates if a URL is safe to navigate to
 */
export const isSafeUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);

    // Check protocol
    if (!['http:', 'https:'].includes(urlObj.protocol)) {
      console.warn('Blocked navigation to non-HTTP(S) URL:', url);
      return false;
    }

    // Check against allowed origins
    if (!isAllowedOrigin(url)) {
      console.warn('Blocked navigation to unauthorized origin:', url);
      return false;
    }

    // Additional security checks
    // Block URLs with suspicious patterns
    const suspiciousPatterns = [
      'javascript:',
      'data:',
      'vbscript:',
      'file://',
      'about:',
      '../',
      '..\\',
      '%2e%2e',
      '%252e%252e',
    ];

    const lowerUrl = url.toLowerCase();
    for (const pattern of suspiciousPatterns) {
      if (lowerUrl.includes(pattern)) {
        console.warn('Blocked URL with suspicious pattern:', pattern, 'in', url);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Error validating URL safety:', url, error);
    return false;
  }
};

/**
 * Sanitizes a URL by removing potentially dangerous parameters
 */
export const sanitizeUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);

    // Remove potentially dangerous parameters
    const dangerousParams = [
      'redirect',
      'redirect_uri',
      'return_url',
      'next',
      'callback_url',
      'goto',
      'destination',
    ];

    dangerousParams.forEach(param => {
      urlObj.searchParams.delete(param);
    });

    return urlObj.toString();
  } catch (error) {
    console.error('Error sanitizing URL:', url, error);
    return url;
  }
};

/**
 * Security configuration for WebView
 */
export const getWebViewSecurityConfig = () => {
  const config: any = {
    // Basic security settings
    javaScriptEnabled: true,
    domStorageEnabled: true,

    // Origin whitelist - only allow specific origins
    originWhitelist: ALLOWED_ORIGINS,

    // Cookie settings
    sharedCookiesEnabled: true,
    thirdPartyCookiesEnabled: false, // Disable third-party cookies for security

    // Content settings
    mixedContentMode: 'never', // Block mixed content
    allowsInlineMediaPlayback: false,
    mediaPlaybackRequiresUserAction: true,

    // File access settings
    allowFileAccess: false,
    allowFileAccessFromFileURLs: false,
    allowUniversalAccessFromFileURLs: false,

    // Other security settings
    saveFormDataDisabled: true,
    incognito: true, // Use incognito mode to prevent data persistence
  };

  // Platform-specific settings
  if (Platform.OS === 'android') {
    config.allowsProtectedMedia = false;
    config.hardwareAccelerationDisabled = false;
    config.overScrollMode = 'never';
  } else if (Platform.OS === 'ios') {
    config.allowsBackForwardNavigationGestures = true;
    config.allowsLinkPreview = false;
    config.fraudulentWebsiteWarningEnabled = true;
  }

  return config;
};

/**
 * Generates a secure injected JavaScript for WebView with proper JWT injection
 */
export const getSecureInjectedScript = (token: string | null, userId: string | null): string => {
  return `
    (function() {
      'use strict';

      // Freeze important objects to prevent tampering
      Object.freeze(Object.prototype);
      Object.freeze(Array.prototype);
      Object.freeze(Function.prototype);

      // Store JWT token for API authentication
      let jwtToken = '${token || ''}';
      
      // CRITICAL: Store JWT in sessionStorage for React app
      if (jwtToken) {
        sessionStorage.setItem('webview_jwt', jwtToken);
        sessionStorage.setItem('jwt_token', jwtToken);
        console.log('[SecureWebView] JWT token stored in sessionStorage');
      }

      // Store session ID securely
      Object.defineProperty(window, 'SECURE_SESSION_ID', {
        value: '${token ? 'jwt-' + Date.now() : ''}',
        writable: false,
        enumerable: false,
        configurable: false
      });

      console.log('[SecureWebView] Initialized with JWT authentication');

      // Message handler for token updates
      window.addEventListener('message', function(event) {
        try {
          const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
          
          if (data.type === 'JWT_TOKEN' && data.payload && data.payload.token) {
            jwtToken = data.payload.token;
            sessionStorage.setItem('webview_jwt', jwtToken);
            sessionStorage.setItem('jwt_token', jwtToken);
            console.log('[SecureWebView] JWT token updated');
          }
        } catch (e) {
          // Ignore non-JSON messages
        }
      });

      // Secure fetch wrapper with JWT injection
      const originalFetch = window.fetch;
      window.fetch = function(url, options = {}) {
        // Validate URL
        try {
          const urlObj = new URL(url, window.location.origin);
          const allowedHosts = [
            'digiflow.digiturk.com.tr',
            'digiflowtest.digiturk.com.tr'
          ];

          const isAllowed = allowedHosts.some(host =>
            urlObj.hostname === host || urlObj.hostname.endsWith('.' + host)
          );

          if (!isAllowed && !url.startsWith('/')) {
            console.error('Blocked request to unauthorized host:', urlObj.hostname);
            return Promise.reject(new Error('Unauthorized request'));
          }
        } catch (e) {
          console.error('Invalid URL:', url);
          return Promise.reject(new Error('Invalid URL'));
        }

        // Add security headers WITH JWT TOKEN
        const newOptions = { ...options };
        newOptions.headers = newOptions.headers || {};
        newOptions.headers['X-From-Mobile-WebView'] = 'true';
        newOptions.headers['X-Mobile-App'] = 'true';
        newOptions.headers['X-Session-Id'] = window.SECURE_SESSION_ID || '';

        if ('${userId}') {
          newOptions.headers['X-Login-Id'] = '${userId}';
        }

        // Add JWT token for API authentication
        if ((url.includes('/api/') || url.includes('/auth/')) && jwtToken) {
          newOptions.headers['Authorization'] = 'Bearer ' + jwtToken;
        }

        // Ensure credentials are included
        newOptions.credentials = 'include';

        return originalFetch(url, newOptions);
      };

      // XHR interceptor for JWT injection
      const originalOpen = XMLHttpRequest.prototype.open;
      XMLHttpRequest.prototype.open = function() {
        const xhr = this;
        const args = arguments;
        const method = args[0];
        const url = args[1];

        // Override send to add headers
        const originalSend = xhr.send;
        xhr.send = function(body) {
          // Add mobile headers
          xhr.setRequestHeader('X-Mobile-App', 'true');
          xhr.setRequestHeader('X-From-Mobile-WebView', 'true');
          xhr.setRequestHeader('X-Session-Id', window.SECURE_SESSION_ID || '');

          if ('${userId}') {
            xhr.setRequestHeader('X-Login-Id', '${userId}');
          }

          // Add JWT token for API calls
          if ((url.includes('/api/') || url.includes('/auth/')) && jwtToken) {
            xhr.setRequestHeader('Authorization', 'Bearer ' + jwtToken);
          }

          return originalSend.apply(xhr, arguments);
        };

        return originalOpen.apply(xhr, args);
      };

      // Prevent navigation to unauthorized origins
      const checkNavigation = (url) => {
        try {
          const urlObj = new URL(url, window.location.origin);
          const allowedHosts = [
            'digiflow.digiturk.com.tr',
            'digiflowtest.digiturk.com.tr'
          ];

          return allowedHosts.some(host =>
            urlObj.hostname === host || urlObj.hostname.endsWith('.' + host)
          );
        } catch {
          return false;
        }
      };

      // Override window.open
      const originalWindowOpen = window.open;
      window.open = function(url) {
        if (!checkNavigation(url)) {
          console.error('Blocked window.open to unauthorized URL:', url);
          return null;
        }
        return originalWindowOpen.apply(window, arguments);
      };

      // Monitor location changes
      let lastLocation = window.location.href;
      setInterval(() => {
        if (window.location.href !== lastLocation) {
          if (!checkNavigation(window.location.href)) {
            console.error('Unauthorized navigation detected:', window.location.href);
            window.location.href = lastLocation;
          } else {
            lastLocation = window.location.href;
          }
        }
      }, 100);

      // Handle React app authentication requests
      window.addEventListener('message', function(event) {
        try {
          const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
          
          if (data.type === 'REQUEST_SECURE_SESSION' && window.ReactNativeWebView) {
            console.log('[SecureWebView] React app requested secure session');
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'REQUEST_SECURE_SESSION',
              timestamp: Date.now()
            }));
          }
        } catch (e) {
          // Ignore non-JSON messages
        }
      });

      // Request authentication on load
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'AUTH_STATE_REQUEST',
          timestamp: Date.now()
        }));

        // Also request JWT token
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'REQUEST_SECURE_SESSION',
          timestamp: Date.now()
        }));
      }

      // Notify that security script is loaded
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'SECURITY_INITIALIZED',
          timestamp: Date.now()
        }));
      }

      console.log('[SecureWebView] Security and authentication initialized');
    })();
  `;
};

/**
 * Validates postMessage data from WebView
 */
export const isValidMessageData = (data: string): boolean => {
  try {
    // Check if it's a valid JSON
    const parsed = JSON.parse(data);

    // Validate message structure
    if (typeof parsed !== 'object' || !parsed.type) {
      return false;
    }

    // Whitelist allowed message types
    const allowedTypes = [
      'SECURITY_INITIALIZED',
      'CACHE_CLEARED',
      'CACHE_CLEAR_ERROR',
      'PAGE_LOADED',
      'NAVIGATION_STARTED',
      'AUTH_ERROR',
      'ERROR',
      'TOKEN_UPDATED',
    ];

    return allowedTypes.includes(parsed.type);
  } catch {
    // If it's not JSON, check if it's a simple string message
    const allowedStringMessages = [
      'PAGE_LOADED',
      'SCRIPT_LOADED',
      'NAVIGATION_STARTED',
      'TOKEN_UPDATED',
    ];

    return allowedStringMessages.includes(data) ||
           data.startsWith('ERROR:') ||
           data.startsWith('AUTH_ERROR:');
  }
};
