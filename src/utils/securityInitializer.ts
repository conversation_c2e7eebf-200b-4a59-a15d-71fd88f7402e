/**
 * Security Initializer
 *
 * Initializes all security measures for the DigiFlow application
 * Defense in depth approach with comprehensive security hardening
 */

import securityHeadersService from '../services/securityHeadersService'
import securityAuditService from './securityAudit'
import { isInWebView } from './webViewDetection'
import config from '../config'

export interface SecurityInitializationConfig {
  enableCSPViolationReporting: boolean
  enableSecurityAudit: boolean
  enableDevelopmentWarnings: boolean
  enablePerformanceMonitoring: boolean
  auditInterval: number // minutes
}

export class SecurityInitializer {
  private static instance: SecurityInitializer
  private config: SecurityInitializationConfig
  private auditTimer?: NodeJS.Timeout

  private constructor() {
    this.config = this.getDefaultConfig()
  }

  static getInstance(): SecurityInitializer {
    if (!SecurityInitializer.instance) {
      SecurityInitializer.instance = new SecurityInitializer()
    }
    return SecurityInitializer.instance
  }

  /**
   * Get default security configuration
   */
  private getDefaultConfig(): SecurityInitializationConfig {
    return {
      enableCSPViolationReporting: process.env.NODE_ENV === 'production',
      enableSecurityAudit: process.env.NODE_ENV === 'production' && !config.VITE_SECURITY_DISABLE_MONITORING,
      enableDevelopmentWarnings: !config.VITE_SECURITY_DISABLE_WARNINGS,
      enablePerformanceMonitoring: process.env.NODE_ENV === 'production' && !config.VITE_SECURITY_DISABLE_MONITORING,
      auditInterval: process.env.NODE_ENV === 'production' ? 60 : 15, // minutes
    }
  }

  /**
   * Initialize comprehensive security measures
   */
  async initializeSecurity(config?: Partial<SecurityInitializationConfig>): Promise<void> {
    if (config) {
      this.config = { ...this.config, ...config }
    }

    console.info('🛡️ Initializing comprehensive security measures...')

    try {
      // Initialize security headers
      await this.initializeSecurityHeaders()

      // Initialize CSRF protection
      await this.initializeCSRFProtection()

      // Initialize WebView security
      if (isInWebView()) {
        await this.initializeWebViewSecurity()
      }

      // Initialize security monitoring
      await this.initializeSecurityMonitoring()

      // Run initial security audit
      if (this.config.enableSecurityAudit) {
        await this.runInitialSecurityAudit()
      }

      // Start periodic security audits
      this.startPeriodicAudits()

      console.info('✅ Security initialization completed successfully')
    } catch (error) {
      console.error('❌ Security initialization failed:', error)
      throw error
    }
  }

  /**
   * Initialize security headers
   */
  private async initializeSecurityHeaders(): Promise<void> {
    console.info('🔒 Initializing security headers...')

    // Set up CSP violation reporting
    if (this.config.enableCSPViolationReporting) {
      // CSP violation reporting is handled by the security headers service
    }

    // Validate current security headers
    const validation = await securityHeadersService.validateSecurityHeaders()
    if (!validation.compliant) {
      console.warn('⚠️ Security headers validation failed:', validation.violations)

      if (this.config.enableDevelopmentWarnings) {
        validation.recommendations.forEach((rec) => {
          console.warn('💡 Recommendation:', rec)
        })
      }
    }

    console.info('✅ Security headers initialized')
  }

  /**
   * Initialize CSRF protection
   */
  private async initializeCSRFProtection(): Promise<void> {
    console.info('🛡️ Initializing CSRF protection...')

    // For WebView, wait for JWT token before initializing CSRF
    if (isInWebView()) {
      const maxWaitTime = 10000; // 10 seconds
      const startTime = Date.now();
      
      console.info('📱 WebView detected - waiting for JWT token...')
      
      // Wait for JWT token to be available
      while (Date.now() - startTime < maxWaitTime) {
        const jwtToken = sessionStorage.getItem('webview_jwt') || sessionStorage.getItem('jwt_token');
        if (jwtToken) {
          console.info('✅ JWT token found - CSRF protection initialized')
          // Import and initialize CSRF service
          const { default: csrfService } = await import('../services/csrfService');
          await csrfService.initializeAfterAuth();
          return;
        }
        
        // Wait 100ms before checking again
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.warn('⚠️ JWT token not found within timeout - CSRF protection will be limited')
    }

    // For regular web browsers, CSRF token will be fetched after user authentication
    console.info('✅ CSRF protection initialized (token fetch deferred until after authentication)')
  }

  /**
   * Initialize WebView security
   */
  private async initializeWebViewSecurity(): Promise<void> {
    console.info('📱 Initializing WebView security...')

    // Validate WebView security configuration
    const hasSecureSession = !!(window as any).SECURE_SESSION_ID
    const hasJWTToken = !!(window as any).JWT_TOKEN

    if (hasJWTToken) {
      console.error('🚨 CRITICAL: JWT token found in window object - security vulnerability!')
      if (process.env.NODE_ENV === 'production') {
        throw new Error('JWT token exposure detected in production')
      }
    }

    if (!hasSecureSession) {
      console.warn('⚠️ No secure session ID found for WebView')
    }

    // Set up WebView message handlers
    if ((window as any).ReactNativeWebView) {
      console.info('✅ WebView message interface available')
    } else {
      console.warn('⚠️ WebView message interface not available')
    }

    console.info('✅ WebView security initialized')
  }

  /**
   * Initialize security monitoring
   */
  private async initializeSecurityMonitoring(): Promise<void> {
    console.info('📊 Initializing security monitoring...')

    // Set up performance monitoring
    if (this.config.enablePerformanceMonitoring && 'performance' in window) {
      this.setupPerformanceMonitoring()
    }

    // Set up error monitoring
    this.setupErrorMonitoring()

    // Set up network monitoring
    this.setupNetworkMonitoring()

    console.info('✅ Security monitoring initialized')
  }

  /**
   * Set up performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (!('performance' in window)) return

    // Monitor navigation timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navTiming = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navTiming) {
          const loadTime = navTiming.loadEventEnd - navTiming.fetchStart
          console.info(`📈 Page load time: ${loadTime}ms`)

          // Report slow load times
          if (loadTime > 5000) {
            console.warn('⚠️ Slow page load detected:', loadTime)
          }
        }
      }, 0)
    })

    // Monitor resource timing
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.duration > 1000) {
          console.warn('⚠️ Slow resource detected:', entry.name, entry.duration)
        }
      })
    })

    observer.observe({ entryTypes: ['resource'] })
  }

  /**
   * Set up error monitoring
   */
  private setupErrorMonitoring(): void {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('🚨 Global error:', {
        message: event.message,
        source: event.filename,
        line: event.lineno,
        column: event.colno,
        error: event.error,
      })

      // Report security-related errors
      if (event.message.includes('CSP') || event.message.includes('security')) {
        console.error('🔒 Security-related error detected')
      }
    })

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('🚨 Unhandled promise rejection:', event.reason)

      // Report security-related rejections
      if (event.reason?.toString().includes('security')) {
        console.error('🔒 Security-related promise rejection')
      }
    })
  }

  /**
   * Set up network monitoring
   */
  private setupNetworkMonitoring(): void {
    // Monitor fetch requests
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const [resource, config] = args
      const url = typeof resource === 'string' ? resource : resource instanceof URL ? resource.href : resource.url

      // Log non-HTTPS requests
      if (url.startsWith('http://') && !url.includes('localhost')) {
        console.warn('⚠️ Non-HTTPS request detected:', url)
      }

      // Monitor for potential data exfiltration
      if (config?.method && config.method.toUpperCase() !== 'GET') {
        const domain = new URL(url).hostname
        const allowedDomains = ['digiflow.digiturk.com.tr', 'digiflowtest.digiturk.com.tr', 'localhost', '127.0.0.1']

        if (!allowedDomains.some((allowed) => domain.includes(allowed))) {
          console.warn('⚠️ Request to external domain:', domain)
        }
      }

      return originalFetch(...args)
    }
  }

  /**
   * Run initial security audit
   */
  private async runInitialSecurityAudit(): Promise<void> {
    console.info('🔍 Running initial security audit...')

    try {
      const auditResult = await securityAuditService.performSecurityAudit()

      console.info(`📊 Security audit completed: ${auditResult.score}/100 (${auditResult.overall})`)

      // Log critical issues
      if (auditResult.overall === 'CRITICAL') {
        console.error('🚨 CRITICAL security issues found!')
        auditResult.recommendations
          .filter((rec) => rec.priority === 'CRITICAL')
          .forEach((rec) => {
            console.error(`🔴 CRITICAL: ${rec.title} - ${rec.description}`)
          })
      }

      // Log warnings
      if (auditResult.overall === 'WARNING') {
        console.warn('⚠️ Security warnings found')
        auditResult.recommendations
          .filter((rec) => rec.priority === 'HIGH')
          .forEach((rec) => {
            console.warn(`🟡 WARNING: ${rec.title} - ${rec.description}`)
          })
      }

      // Store audit result for later access
      ;(window as any).__SECURITY_AUDIT_RESULT__ = auditResult
    } catch (error) {
      console.error('❌ Security audit failed:', error)
    }
  }

  /**
   * Start periodic security audits
   */
  private startPeriodicAudits(): void {
    if (!this.config.enableSecurityAudit) return

    const intervalMs = this.config.auditInterval * 60 * 1000

    this.auditTimer = setInterval(async () => {
      try {
        const auditResult = await securityAuditService.performSecurityAudit()

        // Only log if status changed or critical issues found
        if (auditResult.overall === 'CRITICAL') {
          console.error('🚨 Periodic audit: CRITICAL issues detected!')
        }

        // Update stored result
        ;(window as any).__SECURITY_AUDIT_RESULT__ = auditResult
      } catch (error) {
        console.error('❌ Periodic security audit failed:', error)
      }
    }, intervalMs)

    console.info(`📅 Periodic security audits scheduled every ${this.config.auditInterval} minutes`)
  }

  /**
   * Stop periodic audits
   */
  stopPeriodicAudits(): void {
    if (this.auditTimer) {
      clearInterval(this.auditTimer)
      this.auditTimer = undefined
      console.info('🛑 Periodic security audits stopped')
    }
  }

  /**
   * Get current security status
   */
  async getCurrentSecurityStatus(): Promise<any> {
    return (window as any).__SECURITY_AUDIT_RESULT__ ?? (await securityAuditService.performSecurityAudit())
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityInitializationConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.info('🔧 Security configuration updated')
  }
}

// Export singleton instance
export default SecurityInitializer.getInstance()
