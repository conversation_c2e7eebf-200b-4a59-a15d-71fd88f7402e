/**
 * Security Audit Utility
 *
 * Comprehensive security assessment tool for DigiFlow application
 * Implements defense in depth validation and threat assessment
 */

import securityHeadersService from '../services/securityHeadersService'
import csrfService from '../services/csrfService'
import { isInWebView, getWebViewSessionId } from './webViewDetection'

export interface SecurityAuditResult {
  overall: 'SECURE' | 'WARNING' | 'CRITICAL'
  score: number // 0-100
  categories: {
    headers: SecurityCategoryResult
    csrf: SecurityCategoryResult
    webview: SecurityCategoryResult
    content: SecurityCategoryResult
    network: SecurityCategoryResult
  }
  recommendations: SecurityRecommendation[]
  threats: ThreatAssessment[]
}

export interface SecurityCategoryResult {
  status: 'SECURE' | 'WARNING' | 'CRITICAL'
  score: number
  checks: SecurityCheck[]
}

export interface SecurityCheck {
  name: string
  status: 'PASS' | 'WARNING' | 'FAIL'
  description: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  evidence?: string
}

export interface SecurityRecommendation {
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  category: string
  title: string
  description: string
  action: string
  impact: string
}

export interface ThreatAssessment {
  threat: string
  likelihood: 'LOW' | 'MEDIUM' | 'HIGH'
  impact: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  mitigations: string[]
  residualRisk: 'LOW' | 'MEDIUM' | 'HIGH'
}

export class SecurityAuditService {
  private static instance: SecurityAuditService

  private constructor() {}

  static getInstance(): SecurityAuditService {
    if (!SecurityAuditService.instance) {
      SecurityAuditService.instance = new SecurityAuditService()
    }
    return SecurityAuditService.instance
  }

  /**
   * Perform comprehensive security audit
   */
  async performSecurityAudit(): Promise<SecurityAuditResult> {
    console.info('🔍 Starting comprehensive security audit...')

    const [headers, csrf, webview, content, network] = await Promise.all([
      this.auditSecurityHeaders(),
      this.auditCSRFProtection(),
      this.auditWebViewSecurity(),
      this.auditContentSecurity(),
      this.auditNetworkSecurity(),
    ])

    const categories = { headers, csrf, webview, content, network }
    const overallScore = this.calculateOverallScore(categories)
    const overallStatus = this.determineOverallStatus(overallScore, categories)

    const recommendations = this.generateRecommendations(categories)
    const threats = this.assessThreats(categories)

    const result: SecurityAuditResult = {
      overall: overallStatus,
      score: overallScore,
      categories,
      recommendations,
      threats,
    }

    console.info(`🛡️ Security audit completed. Overall score: ${overallScore}/100 (${overallStatus})`)
    return result
  }

  /**
   * Audit security headers implementation
   */
  private async auditSecurityHeaders(): Promise<SecurityCategoryResult> {
    const checks: SecurityCheck[] = []

    // Check CSP implementation
    const cspHeader = securityHeadersService.generateCSPHeader()
    checks.push({
      name: 'Content Security Policy',
      status: cspHeader.includes("default-src 'self'") ? 'PASS' : 'FAIL',
      description: 'CSP restricts resource loading to prevent XSS',
      severity: 'HIGH',
      evidence: cspHeader,
    })

    // Check for unsafe CSP directives
    const hasUnsafeInline = cspHeader.includes("'unsafe-inline'")
    const hasUnsafeEval = cspHeader.includes("'unsafe-eval'")
    const isDevelopment = process.env.NODE_ENV === 'development'

    if (hasUnsafeInline || hasUnsafeEval) {
      checks.push({
        name: 'CSP Unsafe Directives',
        status: isDevelopment ? 'WARNING' : 'FAIL',
        description: isDevelopment ? 'CSP contains unsafe directives (expected in development)' : 'CSP contains potentially unsafe directives',
        severity: isDevelopment ? 'LOW' : 'HIGH',
        evidence: `unsafe-inline: ${hasUnsafeInline}, unsafe-eval: ${hasUnsafeEval}`,
      })
    }

    // Check Permissions Policy
    const permissionsHeader = securityHeadersService.generatePermissionsPolicyHeader()
    checks.push({
      name: 'Permissions Policy',
      status: permissionsHeader.includes('geolocation=()') ? 'PASS' : 'FAIL',
      description: 'Permissions Policy restricts dangerous browser APIs',
      severity: 'MEDIUM',
      evidence: permissionsHeader,
    })

    // Check page security validation
    const validation = await securityHeadersService.validateSecurityHeaders()
    checks.push({
      name: 'Page Security Validation',
      status: validation.compliant ? 'PASS' : 'WARNING',
      description: 'Page content complies with security policies',
      severity: 'HIGH',
      evidence: validation.violations.join(', '),
    })

    return this.calculateCategoryResult(checks)
  }

  /**
   * Audit CSRF protection implementation
   */
  private async auditCSRFProtection(): Promise<SecurityCategoryResult> {
    const checks: SecurityCheck[] = []

    // Check CSRF token availability
    const csrfToken = csrfService.getToken()
    checks.push({
      name: 'CSRF Token Availability',
      status: csrfToken ? 'PASS' : 'WARNING',
      description: 'CSRF tokens are available for state-changing requests',
      severity: 'HIGH',
      evidence: csrfToken ? 'Token available' : 'No token found',
    })

    // Check server-side token generation
    try {
      const token = await csrfService.fetchToken()
      checks.push({
        name: 'Server-Side CSRF Generation',
        status: token ? 'PASS' : 'FAIL',
        description: 'CSRF tokens are generated server-side',
        severity: 'CRITICAL',
        evidence: token ? 'Server-generated token received' : 'Token generation failed',
      })
    } catch (error) {
      checks.push({
        name: 'Server-Side CSRF Generation',
        status: 'FAIL',
        description: 'CSRF tokens are generated server-side',
        severity: 'CRITICAL',
        evidence: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      })
    }

    // Check WebView CSRF protection
    if (isInWebView()) {
      const secureSessionId = getWebViewSessionId()
      checks.push({
        name: 'WebView CSRF Protection',
        status: secureSessionId ? 'PASS' : 'WARNING',
        description: 'WebView requests have secure session-based CSRF protection',
        severity: 'HIGH',
        evidence: secureSessionId ? 'Secure session ID available' : 'No secure session ID',
      })
    }

    return this.calculateCategoryResult(checks)
  }

  /**
   * Audit WebView security implementation
   */
  private async auditWebViewSecurity(): Promise<SecurityCategoryResult> {
    const checks: SecurityCheck[] = []

    if (!isInWebView()) {
      checks.push({
        name: 'WebView Context',
        status: 'PASS',
        description: 'Not running in WebView context',
        severity: 'LOW',
        evidence: 'Browser environment',
      })
      return this.calculateCategoryResult(checks)
    }

    // Check secure session implementation
    const secureSessionId = getWebViewSessionId()
    checks.push({
      name: 'Secure Session ID',
      status: secureSessionId ? 'PASS' : 'FAIL',
      description: 'WebView uses secure session IDs instead of JWT tokens',
      severity: 'CRITICAL',
      evidence: secureSessionId ? 'Secure session ID present' : 'No secure session ID',
    })

    // Check for JWT token exposure
    const hasJWTToken = (window as any).JWT_TOKEN
    checks.push({
      name: 'JWT Token Exposure',
      status: hasJWTToken ? 'FAIL' : 'PASS',
      description: 'JWT tokens are not exposed in window object',
      severity: 'CRITICAL',
      evidence: hasJWTToken ? 'JWT token found in window' : 'No JWT token exposure',
    })

    // Check WebView message security
    const hasReactNativeWebView = (window as any).ReactNativeWebView
    checks.push({
      name: 'WebView Message Interface',
      status: hasReactNativeWebView ? 'PASS' : 'WARNING',
      description: 'Secure WebView message interface is available',
      severity: 'MEDIUM',
      evidence: hasReactNativeWebView ? 'ReactNativeWebView interface available' : 'No message interface',
    })

    return this.calculateCategoryResult(checks)
  }

  /**
   * Audit content security implementation
   */
  private async auditContentSecurity(): Promise<SecurityCategoryResult> {
    const checks: SecurityCheck[] = []
    const isDevelopment = process.env.NODE_ENV === 'development'

    // Check for dangerous HTML patterns
    const bodyHTML = document.body.innerHTML
    const hasDangerousHTML = securityHeadersService['isHTMLPotentiallyDangerous'](bodyHTML)

    checks.push({
      name: 'HTML Content Security',
      status: hasDangerousHTML ? 'WARNING' : 'PASS',
      description: 'HTML content does not contain dangerous patterns',
      severity: 'HIGH',
      evidence: hasDangerousHTML ? 'Potentially dangerous HTML found' : 'HTML content secure',
    })

    // Check for inline scripts - Be lenient in development
    const inlineScripts = document.querySelectorAll('script:not([src])')
    const nonReactScripts = Array.from(inlineScripts).filter((script) => {
      const content = script.textContent ?? ''
      const type = (script as HTMLScriptElement).type ?? ''
      return type !== 'module' && content.trim().length > 0 && !content.includes('__vite') && !content.includes('import.meta')
    })

    const scriptCount = isDevelopment ? nonReactScripts.length : inlineScripts.length
    const scriptSeverity = isDevelopment ? 'LOW' : 'MEDIUM'

    checks.push({
      name: 'Inline Script Usage',
      status: scriptCount > 0 ? (isDevelopment ? 'WARNING' : 'WARNING') : 'PASS',
      description: isDevelopment ? 'Development mode - some inline scripts expected' : 'Minimal use of inline scripts',
      severity: scriptSeverity,
      evidence: `${scriptCount} inline scripts found`,
    })

    // Check for inline event handlers
    const inlineHandlers = document.querySelectorAll('[onclick], [onload], [onerror]')
    checks.push({
      name: 'Inline Event Handlers',
      status: inlineHandlers.length > 0 ? 'WARNING' : 'PASS',
      description: 'No inline event handlers used',
      severity: isDevelopment ? 'LOW' : 'MEDIUM',
      evidence: `${inlineHandlers.length} inline event handlers found`,
    })

    // Check for mixed content
    const httpResources = document.querySelectorAll('img[src^="http:"], script[src^="http:"], link[href^="http:"]')
    checks.push({
      name: 'Mixed Content',
      status: httpResources.length > 0 ? 'FAIL' : 'PASS',
      description: 'No mixed HTTP content in HTTPS page',
      severity: 'HIGH',
      evidence: `${httpResources.length} HTTP resources found`,
    })

    return this.calculateCategoryResult(checks)
  }

  /**
   * Audit network security implementation
   */
  private async auditNetworkSecurity(): Promise<SecurityCategoryResult> {
    const checks: SecurityCheck[] = []

    // Check HTTPS usage - Be environment and auth-aware
    const isHTTPS = window.location.protocol === 'https:'
    const isDevelopment = process.env.NODE_ENV === 'development'
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'

    // Import auth provider config
    const authProvider = (window as any)._env_?.VITE_AUTH_PROVIDER ?? (import.meta as any).env?.VITE_AUTH_PROVIDER ?? 'standard'

    // HTTPS requirements based on authentication type and environment
    // Windows authentication can use HTTP for integrated auth to work properly
    // JWT/Standard authentication requires HTTPS for security
    let httpsRequired: boolean
    let httpsDescription: string

    if (authProvider === 'windows') {
      // Windows auth: HTTPS not required (HTTP needed for integrated auth)
      httpsRequired = false
      httpsDescription = 'Windows authentication allows HTTP for integrated auth compatibility'
    } else if (isLocalhost && isDevelopment) {
      // Local development: HTTPS not required
      httpsRequired = false
      httpsDescription = 'HTTPS not required in local development'
    } else {
      // JWT/Standard auth or production: HTTPS required
      httpsRequired = true
      httpsDescription = 'Application is served over HTTPS for secure authentication'
    }

    checks.push({
      name: 'HTTPS Usage',
      status: isHTTPS || !httpsRequired ? 'PASS' : 'FAIL',
      description: httpsDescription,
      severity: httpsRequired ? 'CRITICAL' : 'LOW',
      evidence: `Protocol: ${window.location.protocol}, Auth: ${authProvider}`,
    })

    // Check for secure cookies (if any)
    const cookies = document.cookie
    const hasSecureCookies = cookies.includes('Secure') || cookies.length === 0
    checks.push({
      name: 'Secure Cookies',
      status: hasSecureCookies ? 'PASS' : 'WARNING',
      description: 'Cookies are marked as Secure',
      severity: 'MEDIUM',
      evidence: cookies.length > 0 ? 'Cookies present' : 'No cookies found',
    })

    // Check WebSocket security (if applicable)
    if (typeof WebSocket !== 'undefined') {
      checks.push({
        name: 'WebSocket Security',
        status: 'PASS',
        description: 'WebSocket connections use secure protocols',
        severity: 'MEDIUM',
        evidence: 'WebSocket API available',
      })
    }

    return this.calculateCategoryResult(checks)
  }

  /**
   * Calculate category result from checks
   */
  private calculateCategoryResult(checks: SecurityCheck[]): SecurityCategoryResult {
    const totalChecks = checks.length
    const passedChecks = checks.filter((c) => c.status === 'PASS').length
    const warningChecks = checks.filter((c) => c.status === 'WARNING').length
    const failedChecks = checks.filter((c) => c.status === 'FAIL').length
    const criticalFailures = checks.filter((c) => c.status === 'FAIL' && c.severity === 'CRITICAL').length
    const isDevelopment = process.env.NODE_ENV === 'development'

    // In development, warnings have less impact on score
    const warningWeight = isDevelopment ? 0.8 : 0.5
    const effectiveScore = (passedChecks + warningChecks * warningWeight) / totalChecks

    let score = Math.round(effectiveScore * 100)
    let status: 'SECURE' | 'WARNING' | 'CRITICAL'

    if (criticalFailures > 0) {
      status = 'CRITICAL'
      score = Math.min(score, isDevelopment ? 60 : 40)
    } else if (failedChecks > 0) {
      status = 'WARNING'
      score = Math.min(score, isDevelopment ? 80 : 70)
    } else {
      status = 'SECURE'
    }

    return { status, score, checks }
  }

  /**
   * Calculate overall security score
   */
  private calculateOverallScore(categories: SecurityAuditResult['categories']): number {
    const weights = {
      headers: 0.25,
      csrf: 0.25,
      webview: 0.2,
      content: 0.15,
      network: 0.15,
    }

    return Math.round(
      categories.headers.score * weights.headers +
        categories.csrf.score * weights.csrf +
        categories.webview.score * weights.webview +
        categories.content.score * weights.content +
        categories.network.score * weights.network,
    )
  }

  /**
   * Determine overall security status
   */
  private determineOverallStatus(score: number, categories: SecurityAuditResult['categories']): 'SECURE' | 'WARNING' | 'CRITICAL' {
    const hasCritical = Object.values(categories).some((cat) => cat.status === 'CRITICAL')
    const isDevelopment = process.env.NODE_ENV === 'development'

    // More lenient thresholds in development
    const criticalThreshold = isDevelopment ? 40 : 50
    const warningThreshold = isDevelopment ? 70 : 80

    if (hasCritical || score < criticalThreshold) {
      return 'CRITICAL'
    } else if (score < warningThreshold) {
      return 'WARNING'
    } else {
      return 'SECURE'
    }
  }

  /**
   * Generate security recommendations
   */
  private generateRecommendations(categories: SecurityAuditResult['categories']): SecurityRecommendation[] {
    const recommendations: SecurityRecommendation[] = []

    // Headers recommendations
    if (categories.headers.status !== 'SECURE') {
      recommendations.push({
        priority: 'HIGH',
        category: 'Headers',
        title: 'Strengthen Security Headers',
        description: 'Implement comprehensive security headers to prevent XSS and clickjacking',
        action: 'Review and update CSP directives, remove unsafe-inline and unsafe-eval',
        impact: 'Reduces XSS and injection attack vectors',
      })
    }

    // CSRF recommendations
    if (categories.csrf.status !== 'SECURE') {
      recommendations.push({
        priority: 'CRITICAL',
        category: 'CSRF',
        title: 'Implement CSRF Protection',
        description: 'Ensure all state-changing requests have CSRF protection',
        action: 'Implement server-side CSRF token generation and validation',
        impact: 'Prevents cross-site request forgery attacks',
      })
    }

    // WebView recommendations
    if (categories.webview.status !== 'SECURE') {
      recommendations.push({
        priority: 'CRITICAL',
        category: 'WebView',
        title: 'Secure WebView Implementation',
        description: 'Remove JWT token exposure and implement secure session management',
        action: 'Use secure session IDs instead of JWT tokens in WebView',
        impact: 'Prevents token theft and session hijacking',
      })
    }

    // Content recommendations
    if (categories.content.status !== 'SECURE') {
      recommendations.push({
        priority: 'MEDIUM',
        category: 'Content',
        title: 'Improve Content Security',
        description: 'Reduce inline scripts and event handlers',
        action: 'Move inline scripts to external files, use addEventListener',
        impact: 'Reduces XSS attack surface',
      })
    }

    // Network recommendations - Check auth provider before recommending HTTPS
    if (categories.network.status !== 'SECURE') {
      const authProvider = (window as any)._env_?.VITE_AUTH_PROVIDER ?? (import.meta as any).env?.VITE_AUTH_PROVIDER ?? 'standard'

      // Only recommend HTTPS for non-Windows auth
      if (authProvider !== 'windows') {
        recommendations.push({
          priority: 'CRITICAL',
          category: 'Network',
          title: 'Secure Network Communications',
          description: 'Ensure all communications use HTTPS with secure configurations',
          action: 'Implement HSTS, upgrade all HTTP resources to HTTPS',
          impact: 'Prevents man-in-the-middle attacks',
        })
      }
    }

    return recommendations
  }

  /**
   * Assess security threats
   */
  private assessThreats(categories: SecurityAuditResult['categories']): ThreatAssessment[] {
    const threats: ThreatAssessment[] = []

    // XSS Threats
    threats.push({
      threat: 'Cross-Site Scripting (XSS)',
      likelihood: categories.headers.status === 'SECURE' ? 'LOW' : 'HIGH',
      impact: 'HIGH',
      mitigations: ['Content Security Policy', 'Input Sanitization', 'Output Encoding'],
      residualRisk: categories.headers.status === 'SECURE' ? 'LOW' : 'MEDIUM',
    })

    // CSRF Threats
    threats.push({
      threat: 'Cross-Site Request Forgery (CSRF)',
      likelihood: categories.csrf.status === 'SECURE' ? 'LOW' : 'HIGH',
      impact: 'HIGH',
      mitigations: ['CSRF Tokens', 'SameSite Cookies', 'Referrer Validation'],
      residualRisk: categories.csrf.status === 'SECURE' ? 'LOW' : 'HIGH',
    })

    // Clickjacking Threats
    threats.push({
      threat: 'Clickjacking',
      likelihood: categories.headers.status === 'SECURE' ? 'LOW' : 'MEDIUM',
      impact: 'MEDIUM',
      mitigations: ['X-Frame-Options', 'CSP frame-ancestors', 'Frame-busting JavaScript'],
      residualRisk: categories.headers.status === 'SECURE' ? 'LOW' : 'MEDIUM',
    })

    // Session Hijacking
    threats.push({
      threat: 'Session Hijacking',
      likelihood: categories.webview.status === 'SECURE' ? 'LOW' : 'HIGH',
      impact: 'CRITICAL',
      mitigations: ['Secure Session Management', 'HttpOnly Cookies', 'Session Regeneration'],
      residualRisk: categories.webview.status === 'SECURE' ? 'LOW' : 'HIGH',
    })

    // Man-in-the-Middle
    threats.push({
      threat: 'Man-in-the-Middle (MITM)',
      likelihood: categories.network.status === 'SECURE' ? 'LOW' : 'MEDIUM',
      impact: 'CRITICAL',
      mitigations: ['HTTPS/TLS', 'Certificate Pinning', 'HSTS'],
      residualRisk: categories.network.status === 'SECURE' ? 'LOW' : 'MEDIUM',
    })

    return threats
  }
}

// Export singleton instance
export default SecurityAuditService.getInstance()
