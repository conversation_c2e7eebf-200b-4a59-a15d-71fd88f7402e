import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  TextInput,
  Alert,
  Share,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getToken,
  refreshToken,
  getUserInfo,
  getNumericUserId,
  authenticatedRequest,
} from '../services/api';
import { useAuthStore } from '../store/authStore';
import { generateSecureId, createSignedMessage, validateSignedMessage } from '../utils/secureNonce';

type WebViewTestScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WebViewTest'>;
type WebViewTestScreenRouteProp = RouteProp<RootStackParamList, 'WebViewTest'>;

interface WebViewTestScreenProps {
  navigation: WebViewTestScreenNavigationProp;
  route: WebViewTestScreenRouteProp;
}

// Secure allowed origins - UPDATE THESE TO YOUR ACTUAL DOMAINS
const ALLOWED_ORIGINS = [
  'https://digiflow.digiturk.com.tr',
  'https://digiflowtest.digiturk.com.tr',
];

const WebViewTestScreen: React.FC<WebViewTestScreenProps> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const [numericUserId, setNumericUserId] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);

  // Function to add loginId to URL
  const addLoginIdToUrl = (baseUrl: string, userId?: string) => {
    if (!userId) { return baseUrl; }

    try {
      const url = new URL(baseUrl);
      // Remove any existing loginId parameter first
      url.searchParams.delete('loginId');
      // Then set the new one
      url.searchParams.set('loginId', userId);
      return url.toString();
    } catch (error) {
      // If URL parsing fails, fallback to simple string concatenation
      // Remove any existing loginId parameter
      let cleanUrl = baseUrl.replace(/[?&]loginId=[^&]*/g, '');
      // Fix any double ? or & that might result
      cleanUrl = cleanUrl.replace(/\?&/g, '?').replace(/&&/g, '&').replace(/\?$/, '');

      const separator = cleanUrl.includes('?') ? '&' : '?';
      return `${cleanUrl}${separator}loginId=${userId}`;
    }
  };

  // Default URL for Delegation Cancel screen in DigiflowReact
  const defaultUrl = 'https://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation';
  const initialUrl = route.params?.url || defaultUrl;
  const [url, setUrl] = useState<string>(initialUrl); // Don't add loginId yet, wait for numeric ID
  const [isLoading, setIsLoading] = useState(true);
  // Remove token state - no longer storing JWT client-side
  const [logs, setLogs] = useState<Array<{ message: string; type: string; raw: string }>>([]);
  const [responseText, setResponseText] = useState<string>('');
  const [debugMode, setDebugMode] = useState<boolean>(true);
  const webViewRef = useRef<WebView>(null);

  const addLog = (message: string, type: 'info' | 'error' | 'success' | 'warning' | 'request' | 'response' | 'console' | 'network' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const formattedMessage = `[${timestamp}] [${type.toUpperCase()}] ${message}`;
    setLogs(prevLogs => [{ message: formattedMessage, type, raw: message }, ...prevLogs.slice(0, 49)]);
  };

  // Get JWT token when component mounts
  useEffect(() => {
    const initializeSecureSession = async () => {
      try {
        // Generate secure session ID
        const newSessionId = generateSecureId();
        setSessionId(newSessionId);

        const user = await getUserInfo();
        const numericId = await getNumericUserId();

        setNumericUserId(numericId);

        addLog(`Secure session initialized`, 'success');
        addLog(`User info loaded: ${user ? user.name : 'Not found'}`, user ? 'success' : 'warning');

        // Log user ID from store
        const storeUser = useAuthStore.getState().user;
        if (storeUser) {
          addLog(`User ID from store: ${storeUser.id}`, 'info');
          addLog(`User Name from store: ${storeUser.name}`, 'info');
        }

        // Log numeric user ID
        if (numericId) {
          addLog(`Numeric User ID: ${numericId}`, 'success');
          // Update URL with numeric ID
          setUrl(addLoginIdToUrl(initialUrl, numericId));
        } else {
          addLog('No numeric User ID found', 'warning');
        }

        // Refresh token to ensure it's valid (but don't expose it)
        try {
          await refreshToken();
          addLog('Session refreshed successfully', 'success');
        } catch (refreshError) {
          addLog(`Session refresh failed: ${(refreshError as Error).message}`, 'error');
        }
      } catch (error) {
        addLog(`Error initializing secure session: ${(error as Error).message}`, 'error');
      }
    };

    initializeSecureSession();
  }, []);

  // Function to reload the WebView
  const handleReload = () => {
    if (webViewRef.current) {
      addLog('Reloading WebView...', 'info');
      webViewRef.current.reload();
    }
  };

  // Test direct API call
  const testDirectApiCall = async () => {
    try {
      setIsLoading(true);
      const endpoint = '/users/select-options';
      const method = 'GET';

      addLog('Testing direct API call to DigiflowAPI...', 'request');
      addLog(`Endpoint: ${endpoint}`, 'request');
      addLog(`Method: ${method}`, 'request');
      addLog('Base URL: https://digiflowtest.digiturk.com.tr/mobile', 'request');

      // Log current token state
      const currentToken = await getToken();
      addLog(`Current JWT Token: ${currentToken ? currentToken.substring(0, 20) + '...' : 'Not found'}`, 'info');
      addLog(`X-Login-Id: ${numericUserId || user?.id || 'Not available'}`, 'info');

      // Create a custom fetch to capture more details
      const startTime = Date.now();
      let responseStatus: number | undefined;
      let responseHeaders: any = {};

      try {
        const response = await fetch(`https://digiflowtest.digiturk.com.tr/api${endpoint}`, {
          method,
          headers: {
            'Authorization': `Bearer ${currentToken}`,
            'Content-Type': 'application/json',
            'X-Auth-Scheme': 'Bearer',
            'X-Mobile-App': 'true',
            'X-Is-Mobile': 'true',
            'X-Login-Id': numericUserId || user?.id || '',
          },
        });

        responseStatus = response.status;
        responseHeaders = Object.fromEntries(response.headers.entries());

        const responseTime = Date.now() - startTime;

        addLog(`Response Status: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
        addLog(`Response Time: ${responseTime}ms`, 'info');
        addLog(`Response Headers: ${JSON.stringify(responseHeaders, null, 2)}`, 'response');

        const data = await response.json();

        if (response.ok) {
          setResponseText(JSON.stringify({
            status: response.status,
            statusText: response.statusText,
            headers: responseHeaders,
            data: data,
            responseTime: `${responseTime}ms`,
          }, null, 2));
          addLog('Direct API call successful', 'success');
        } else {
          throw new Error(`API returned ${response.status}: ${JSON.stringify(data)}`);
        }
      } catch (fetchError) {
        const errorDetails = {
          message: (fetchError as Error).message,
          status: responseStatus,
          headers: responseHeaders,
          timestamp: new Date().toISOString(),
        };

        addLog(`API call failed: ${(fetchError as Error).message}`, 'error');
        setResponseText(JSON.stringify(errorDetails, null, 2));
      }
    } catch (error) {
      addLog(`Unexpected error: ${(error as any).message || 'Unknown error'}`, 'error');
      setResponseText(`Error: ${JSON.stringify(error, null, 2)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Test WebView call with custom script
  const testWebViewCall = () => {
    if (webViewRef.current) {
      addLog('Executing test script in WebView...', 'info');

      const testScript = `
        (async function() {
          try {
            // Log current secure session status
            const sessionExists = window.SECURE_SESSION_ID ? true : false;
            console.log('Current Secure Session:', sessionExists ? 'Present' : 'Not set');
            window.ReactNativeWebView.postMessage('LOG: Session status - ' + (sessionExists ? 'Present' : 'Missing'));

            // This will use the secure session authentication we set up in the injected script
            window.ReactNativeWebView.postMessage('LOG: Making test API call to /api/users/select-options');

            const startTime = Date.now();
            const response = await fetch('/api/users/select-options', {
              headers: {
                'X-Auth-Scheme': 'Bearer',
                'X-Mobile-App': 'true',
                'X-Is-Mobile': 'true',
                'X-From-Mobile-WebView': 'true',
                'X-Login-Id': window.LOGIN_ID || ''
              }
            });

            const responseTime = Date.now() - startTime;

            // Get response details
            const responseHeaders = {};
            response.headers.forEach((value, key) => {
              responseHeaders[key] = value;
            });

            if (!response.ok) {
              const errorData = await response.text();
              throw new Error(\`API call failed: \${response.status} \${response.statusText} - \${errorData}\`);
            }

            const data = await response.json();
            const result = {
              status: response.status,
              statusText: response.statusText,
              headers: responseHeaders,
              data: data,
              responseTime: responseTime + 'ms'
            };

            window.ReactNativeWebView.postMessage('API_RESPONSE:' + JSON.stringify(result, null, 2));
            return true;
          } catch (error) {
            console.error('API test error:', error);
            window.ReactNativeWebView.postMessage('API_ERROR: ' + error.message);
            return false;
          }
        })();
      `;

      webViewRef.current.injectJavaScript(testScript);
    }
  };

  // Early injection script for console override and JWT pre-injection
  const injectBeforeScript = `
  (function() {
    // CRITICAL: Pre-inject JWT token before React app loads
    const urlParams = new URLSearchParams(window.location.search);
    const loginId = urlParams.get('loginId');
    
    if (loginId) {
      // Generate a temporary JWT token for testing
      // In production, this should come from the mobile app
      const mockJwt = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.' + btoa(JSON.stringify({
        userId: loginId,
        LoginId: loginId,
        exp: Math.floor(Date.now() / 1000) + 3600
      })) + '.mock-signature';
      
      // Store JWT in sessionStorage BEFORE React app loads
      sessionStorage.setItem('webview_jwt', mockJwt);
      sessionStorage.setItem('jwt_token', mockJwt);
      console.log('[Pre-injection] JWT token stored in sessionStorage for loginId:', loginId);
    }
    
    // Debug: Log that injection is starting
    const originalLog = console.log;
    originalLog('[WebView] Starting early console injection at:', new Date().toISOString());

    // Store original console methods immediately
    window.__originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // Override console methods immediately
    ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
      console[method] = function(...args) {
        // Call original method
        window.__originalConsole[method].apply(console, args);

        // Try to send to React Native if available
        if (window.ReactNativeWebView) {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'CONSOLE_LOG',
              level: method,
              args: args.map(arg => {
                try {
                  if (arg === null) return 'null';
                  if (arg === undefined) return 'undefined';
                  if (typeof arg === 'object') {
                    return JSON.stringify(arg, (key, value) => {
                      if (typeof value === 'function') return '[Function]';
                      if (value instanceof Error) {
                        return {
                          name: value.name,
                          message: value.message,
                          stack: value.stack
                        };
                      }
                      return value;
                    }, 2);
                  }
                  return String(arg);
                } catch (e) {
                  return '[Object with circular reference]';
                }
              }),
              timestamp: new Date().toISOString(),
              url: window.location ? window.location.href : 'unknown'
            }));
          } catch (e) {
            window.__originalConsole.error('Failed to send console log:', e);
          }
        }
      };
    });

    window.__originalConsole.log('[WebView] Console override installed early');
    true;
  })();
  `;

  // Secure JavaScript to inject into WebView with JWT support and network logging
  const injectScript = `
  (function() {
    // Use stored original console or create new one
    const originalConsole = window.__originalConsole || {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // Re-apply console override (in case it was lost)
    ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
      console[method] = function(...args) {
        // Call original method
        originalConsole[method].apply(console, args);
        
        // Send to React Native
        if (window.ReactNativeWebView) {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'CONSOLE_LOG',
              level: method,
              args: args.map(arg => {
                try {
                  if (arg === null) return 'null';
                  if (arg === undefined) return 'undefined';
                  if (typeof arg === 'object') {
                    return JSON.stringify(arg, (key, value) => {
                      if (typeof value === 'function') return '[Function]';
                      if (value instanceof Error) {
                        return {
                          name: value.name,
                          message: value.message,
                          stack: value.stack
                        };
                      }
                      return value;
                    }, 2);
                  }
                  return String(arg);
                } catch (e) {
                  return '[Object with circular reference]';
                }
              }),
              timestamp: new Date().toISOString(),
              url: window.location.href
            }));
          } catch (e) {
            originalConsole.error('Failed to send console log to React Native:', e);
          }
        }
      };
    });

    // Also capture unhandled errors
    window.addEventListener('error', function(event) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'CONSOLE_LOG',
          level: 'error',
          args: ['Uncaught Error: ' + event.message],
          timestamp: new Date().toISOString(),
          url: window.location.href,
          stack: event.error ? event.error.stack : 'No stack trace available'
        }));
      }
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'CONSOLE_LOG',
          level: 'error',
          args: ['Unhandled Promise Rejection: ' + event.reason],
          timestamp: new Date().toISOString(),
          url: window.location.href
        }));
      }
    });

    console.log('[WebView Console] Main console override installed - logs will be captured');

    // Network request tracking
    let requestId = 0;

    // Store JWT token for API requests
    let jwtToken = null;

    // Helper to send network log
    function sendNetworkLog(data) {
      if (window.ReactNativeWebView) {
        try {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'NETWORK_LOG',
            ...data,
            timestamp: new Date().toISOString()
          }));
        } catch (e) {
          originalConsole.error('Failed to send network log:', e);
        }
      }
    }

    // SECURITY: No JWT tokens are exposed to the WebView
    console.log('=== Secure Session Initialization ===');
    console.log('Session ID:', '${sessionId || ''}' ? 'Present' : 'Not provided');

    // Set up secure message listener with origin validation
    window.addEventListener('message', function(event) {
      // Validate message origin - allow empty origin for about:blank
      const allowedOrigins = ${JSON.stringify(ALLOWED_ORIGINS)};
      if (event.origin !== '' && !allowedOrigins.includes(event.origin)) {
        console.warn('[Security] Rejected message from untrusted origin:', event.origin);
        return;
      }

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

        switch(data.type) {
          case 'USER_DATA':
            // Store user data securely (non-sensitive only)
            window.__secureUserData = {
              userId: data.userId,
              username: data.username
              // Only non-sensitive data
            };

            // Store user data for React app compatibility (non-sensitive only)
            if (data.userId) {
              localStorage.setItem('UserId', data.userId);
              console.log('Stored UserId for React app:', data.userId);
            }
            if (data.username) {
              localStorage.setItem('Username', data.username);
              console.log('Stored Username for React app:', data.username);
            }

            // Dispatch event for React app
            window.dispatchEvent(new CustomEvent('userDataReceived', {
              detail: window.__secureUserData
            }));

            window.ReactNativeWebView.postMessage('LOG: User data received securely');
            break;

          case 'JWT_TOKEN':
            // Update the stored JWT token
            if (data && data.payload && data.payload.token) {
              jwtToken = data.payload.token;
              // IMPORTANT: Store in sessionStorage so React app can access it
              sessionStorage.setItem('webview_jwt', jwtToken);
              console.log('[WebView] JWT token received and stored in sessionStorage');
              
              // Notify React app that JWT token is available
              window.dispatchEvent(new CustomEvent('jwtTokenReceived', {
                detail: { token: jwtToken }
              }));
            } else {
              console.error('[WebView] Invalid JWT_TOKEN message format:', data);
            }
            break;

          case 'REQUEST_SECURE_SESSION':
            // React app is requesting secure session
            console.log('[WebView] React app requested secure session - forwarding to mobile');
            // Forward the request to the mobile app
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'REQUEST_SECURE_SESSION',
              timestamp: Date.now()
            }));
            break;
            
          default:
            console.log('[WebView] Unknown message type:', data.type);
        }
      } catch (e) {
        console.error('[WebView] Message parsing error:', e);
      }
    });

    // Request authentication state and JWT token on load
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'AUTH_STATE_REQUEST',
        timestamp: Date.now()
      }));
      
      // Also request JWT token for API authentication
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'REQUEST_SECURE_SESSION',
        timestamp: Date.now()
      }));
    }

    // Enhanced fetch interceptor with network logging
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      const currentRequestId = ++requestId;
      const startTime = Date.now();
      const newOptions = { ...options };
      newOptions.headers = newOptions.headers || {};
      
      // Log specific API calls
      console.log('[Fetch] Request to:', url);
      if (url.includes('/csrf/token')) {
        console.log('[API] CSRF token request detected');
      } else if (url.includes('/users/id') || url.includes('/users/by')) {
        console.log('[API] User info request detected');
      }

      // Add mobile-specific headers for identification ONLY
      newOptions.headers['X-Mobile-App'] = 'true';
      newOptions.headers['X-Is-Mobile'] = 'true';
      newOptions.headers['X-From-Mobile-WebView'] = 'true';
      newOptions.headers['X-Session-Id'] = '${sessionId || ''}';

      // Add login ID header
      const loginId = '${numericUserId || user?.id || ''}';
      if (loginId) {
        newOptions.headers['X-Login-Id'] = loginId;
      }

      // Add JWT token for API calls (including auth endpoints)
      if (url.includes('/api/') || url.includes('/auth/')) {
        if (jwtToken) {
          newOptions.headers['Authorization'] = 'Bearer ' + jwtToken;
          console.log('[API] Adding JWT token to request:', url);
        } else {
          console.error('[API] NO JWT TOKEN AVAILABLE for fetch request:', url);
          console.log('[API] Current jwtToken value:', jwtToken);
          console.log('[API] SessionStorage webview_jwt:', sessionStorage.getItem('webview_jwt'));
          
          // Try to get token from sessionStorage as fallback
          const fallbackToken = sessionStorage.getItem('webview_jwt');
          if (fallbackToken) {
            console.log('[API] Using fallback token from sessionStorage');
            newOptions.headers['Authorization'] = 'Bearer ' + fallbackToken;
            jwtToken = fallbackToken; // Update the variable
          }
        }
      }

      newOptions.credentials = 'include';
      newOptions.mode = 'cors';

      // Capture request details
      const requestData = {
        id: currentRequestId,
        url: url,
        method: newOptions.method || 'GET',
        headers: { ...newOptions.headers },
        body: newOptions.body ? (typeof newOptions.body === 'string' ? newOptions.body.substring(0, 1000) : '[Binary Data]') : null
      };

      // Remove sensitive headers from logging
      if (requestData.headers['Authorization']) {
        requestData.headers['Authorization'] = 'Bearer [HIDDEN]';
      }

      // Send request start log
      sendNetworkLog({
        phase: 'request',
        request: requestData
      });

      // Log API requests for debugging (without sensitive data)
      if (url.includes('/api/')) {
        console.log('[API] Request:', newOptions.method || 'GET', url);
        console.log('[API] Session ID present:', !!newOptions.headers['X-Session-Id']);
        window.ReactNativeWebView.postMessage('LOG: API Request - ' + (newOptions.method || 'GET') + ' ' + url);
      }

      return originalFetch(url, newOptions)
        .then(async response => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // Clone response to read body without consuming it
          const responseClone = response.clone();
          let responseBody = null;
          
          try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              responseBody = await responseClone.json();
            } else if (contentType && contentType.includes('text')) {
              responseBody = await responseClone.text();
            }
          } catch (e) {
            responseBody = '[Unable to parse response body]';
          }

          // Capture response headers
          const responseHeaders = {};
          response.headers.forEach((value, key) => {
            responseHeaders[key] = value;
          });

          // Send response log
          sendNetworkLog({
            phase: 'response',
            request: {
              id: currentRequestId,
              url: url,
              method: newOptions.method || 'GET'
            },
            response: {
              status: response.status,
              statusText: response.statusText,
              headers: responseHeaders,
              body: responseBody ? JSON.stringify(responseBody).substring(0, 1000) : null,
              duration: duration
            }
          });

          if (response.status === 401) {
            console.error('[Auth] Authentication error (401) on request to:', url);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'AUTH_ERROR',
              url: url,
              timestamp: Date.now()
            }));
          }
          return response;
        })
        .catch(error => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // Send error log
          sendNetworkLog({
            phase: 'error',
            request: {
              id: currentRequestId,
              url: url,
              method: newOptions.method || 'GET'
            },
            error: {
              message: error.message,
              stack: error.stack,
              duration: duration
            }
          });

          console.error('[Fetch] Error:', error.message);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'NETWORK_ERROR',
            message: error.message,
            timestamp: Date.now()
          }));
          throw error;
        });
    };

    // Enhanced XHR interceptor with network logging
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
      const xhr = this;
      const args = arguments;
      const method = args[0];
      const url = args[1];
      const currentRequestId = ++requestId;
      let startTime;

      xhr._requestData = {
        id: currentRequestId,
        method: method,
        url: url,
        headers: {}
      };

      // Override setRequestHeader to capture headers
      const originalSetRequestHeader = xhr.setRequestHeader;
      xhr.setRequestHeader = function(header, value) {
        xhr._requestData.headers[header] = header === 'Authorization' ? 'Bearer [HIDDEN]' : value;
        return originalSetRequestHeader.apply(xhr, arguments);
      };

      xhr.addEventListener('loadstart', function() {
        startTime = Date.now();
        sendNetworkLog({
          phase: 'request',
          request: xhr._requestData
        });
      });

      xhr.addEventListener('load', function() {
        const endTime = Date.now();
        const duration = endTime - startTime;

        let responseBody = null;
        try {
          if (xhr.responseType === '' || xhr.responseType === 'text') {
            responseBody = xhr.responseText.substring(0, 1000);
          } else if (xhr.responseType === 'json') {
            responseBody = JSON.stringify(xhr.response).substring(0, 1000);
          }
        } catch (e) {
          responseBody = '[Unable to parse response]';
        }

        // Get response headers
        const responseHeaders = {};
        const headerLines = xhr.getAllResponseHeaders().split('\\n');
        headerLines.forEach(line => {
          const parts = line.split(': ');
          if (parts.length === 2) {
            responseHeaders[parts[0]] = parts[1];
          }
        });

        sendNetworkLog({
          phase: 'response',
          request: {
            id: currentRequestId,
            url: url,
            method: method
          },
          response: {
            status: xhr.status,
            statusText: xhr.statusText,
            headers: responseHeaders,
            body: responseBody,
            duration: duration
          }
        });
      });

      xhr.addEventListener('error', function() {
        const endTime = Date.now();
        const duration = endTime - startTime;

        sendNetworkLog({
          phase: 'error',
          request: {
            id: currentRequestId,
            url: url,
            method: method
          },
          error: {
            message: 'Network request failed',
            duration: duration
          }
        });
      });

      xhr.addEventListener('readystatechange', function() {
        if (xhr.readyState === 4 && xhr.status === 401) {
          console.error('[Auth] XHR Authentication error (401):', url);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'AUTH_ERROR',
            url: url,
            timestamp: Date.now()
          }));
        }
      });

      const originalSend = xhr.send;
      xhr.send = function(body) {
        // Add mobile headers for identification ONLY
        xhr.setRequestHeader('X-Mobile-App', 'true');
        xhr.setRequestHeader('X-Is-Mobile', 'true');
        xhr.setRequestHeader('X-From-Mobile-WebView', 'true');
        xhr.setRequestHeader('X-Session-Id', '${sessionId || ''}');

        // Add X-Login-Id header
        const loginId = '${numericUserId || user?.id || ''}';
        if (loginId) {
          xhr.setRequestHeader('X-Login-Id', loginId);
        }

        // Add JWT token for API calls (including auth endpoints)
        if (url.includes('/api/') || url.includes('/auth/')) {
          if (jwtToken) {
            xhr.setRequestHeader('Authorization', 'Bearer ' + jwtToken);
            console.log('[API] Adding JWT token to XHR request:', url);
          } else {
            console.error('[API] NO JWT TOKEN AVAILABLE for XHR request:', url);
            console.log('[API] Current jwtToken value:', jwtToken);
            console.log('[API] SessionStorage webview_jwt:', sessionStorage.getItem('webview_jwt'));
            
            // Try to get token from sessionStorage as fallback
            const fallbackToken = sessionStorage.getItem('webview_jwt');
            if (fallbackToken) {
              console.log('[API] Using fallback token from sessionStorage for XHR');
              xhr.setRequestHeader('Authorization', 'Bearer ' + fallbackToken);
              jwtToken = fallbackToken; // Update the variable
            }
          }
        }

        // Capture request body
        if (body) {
          xhr._requestData.body = typeof body === 'string' ? body.substring(0, 1000) : '[Binary Data]';
        }

        return originalSend.apply(xhr, arguments);
      };

      return originalOpen.apply(xhr, args);
    };

    // Set up page load handler
    window.addEventListener('load', function() {
      console.log('[WebView] Page loaded with secure session');
      console.log('[WebView Test] Current URL:', window.location.href);
      console.log('[WebView Test] User Agent:', navigator.userAgent);
      console.log('[WebView Test] Console capture is working if you see this!');
      
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'PAGE_LOADED',
        timestamp: Date.now()
      }));
    });

    // Also log immediately to test
    console.log('[WebView] Secure session script loaded - No tokens exposed');
    console.log('[WebView Test] Immediate test - console capture active!');
    
    // IMPORTANT: Check if we already have a JWT token in sessionStorage
    const existingToken = sessionStorage.getItem('webview_jwt');
    if (existingToken) {
      jwtToken = existingToken;
      console.log('[WebView] Found existing JWT token in sessionStorage');
    } else {
      console.log('[WebView] No existing JWT token, requesting from mobile app');
      
      // Immediately request JWT token on script load
      if (window.ReactNativeWebView) {
        console.log('[WebView] Sending early REQUEST_SECURE_SESSION');
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'REQUEST_SECURE_SESSION',
          timestamp: Date.now(),
          source: 'injected_script_init'
        }));
      }
    }
    
    // Test with a timer to see if React app overwrites console
    setTimeout(function() {
      console.log('[WebView Test] 1 second delay - console still working?');
    }, 1000);
    
    setTimeout(function() {
      console.log('[WebView Test] 3 second delay - React app loaded?');
      console.log('[WebView Test] Current JWT token:', jwtToken ? 'Present' : 'Missing');
      console.log('[WebView Test] SessionStorage JWT:', sessionStorage.getItem('webview_jwt') ? 'Present' : 'Missing');
    }, 3000);
    
    // Monitor first API call
    let firstApiCall = false;
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
      const url = args[0]?.toString() || '';
      if (!firstApiCall && (url.includes('/api/') || url.includes('/auth/'))) {
        firstApiCall = true;
        console.log('[WebView Test] FIRST API CALL DETECTED:', url);
        console.log('[WebView Test] JWT available at first API call:', jwtToken ? 'YES' : 'NO');
      }
      return originalFetch.apply(this, args);
    };
    
    // CRITICAL: Override ReactNativeWebView.postMessage to intercept React app messages
    const originalReactNativeWebView = window.ReactNativeWebView;
    if (originalReactNativeWebView && originalReactNativeWebView.postMessage) {
      const originalPostMessage = originalReactNativeWebView.postMessage.bind(originalReactNativeWebView);
      
      window.ReactNativeWebView.postMessage = function(message) {
        try {
          const parsed = JSON.parse(message);
          console.log('[WebView] React app sent message:', parsed.type);
          
          // If React app requests secure session
          if (parsed.type === 'REQUEST_SECURE_SESSION') {
            console.log('[WebView] React app requesting secure session');
            
            // Check sessionStorage first
            const storedToken = sessionStorage.getItem('webview_jwt');
            if (storedToken && !jwtToken) {
              jwtToken = storedToken;
              console.log('[WebView] Restored JWT from sessionStorage for React app');
            }
            
            if (jwtToken) {
              console.log('[WebView] JWT already available, React should find it in sessionStorage');
              // Don't forward since token is already in sessionStorage
              // React app will reload and find it
              return;
            } else {
              console.log('[WebView] No JWT yet, forwarding request to native app');
            }
          }
        } catch (e) {
          // Not JSON, ignore
        }
        
        // Forward all messages to native
        return originalPostMessage(message);
      };
    }
    
    true;
  })();
`;

  const handleMessage = async (event: any) => {
    const { data } = event.nativeEvent;

    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data;

      switch (message.type) {
        case 'PAGE_LOADED':
          setIsLoading(false);
          addLog('Page loaded successfully', 'success');
          
          // IMMEDIATELY send JWT token if available
          const pageLoadToken = await getToken();
          if (pageLoadToken && webViewRef.current) {
            const tokenMessage = await createSignedMessage('JWT_TOKEN', {
              token: pageLoadToken
            });
            webViewRef.current.postMessage(JSON.stringify(tokenMessage));
            addLog('Sent JWT token immediately on page load', 'success');
          }

          // Send user data securely (no tokens)
          if (webViewRef.current && (numericUserId || user?.id)) {
            const signedMessage = await createSignedMessage('USER_DATA', {
              userId: numericUserId || user?.id,
              username: user?.id,
            });

            webViewRef.current.postMessage(JSON.stringify(signedMessage));
            addLog('Sent secure user data to WebView', 'success');
          }
          break;

        case 'AUTH_STATE_REQUEST':
          // Handle auth state request
          addLog('Received auth state request from WebView', 'info');
          const authToken = await getToken();
          const isAuthenticated = !!authToken;
          
          if (webViewRef.current) {
            const signedMessage = await createSignedMessage('AUTH_STATE', {
              authenticated: isAuthenticated,
              sessionId: sessionId
            });
            webViewRef.current.postMessage(JSON.stringify(signedMessage));
            addLog('Sent auth state to WebView', 'success');
            
            // Also send JWT token for webview authentication
            if (authToken) {
              const tokenMessage = await createSignedMessage('JWT_TOKEN', {
                token: authToken
              });
              webViewRef.current.postMessage(JSON.stringify(tokenMessage));
              addLog('Sent JWT token with auth state', 'success');
            }
          }
          break;

        case 'AUTH_ERROR':
          addLog(`Auth Error: ${message.url}`, 'error');

          // Attempt to refresh token in the background
          try {
            await refreshToken();
            addLog('Token refreshed after auth error', 'success');

            // Notify WebView of refreshed auth state
            if (webViewRef.current) {
              const signedMessage = await createSignedMessage('AUTH_REFRESHED');
              webViewRef.current.postMessage(JSON.stringify(signedMessage));
            }
          } catch (err) {
            addLog('Token refresh failed after auth error', 'error');
          }
          break;

        case 'REQUEST_SECURE_SESSION':
          // Handle secure session request from React app
          addLog('Received secure session request from WebView', 'info');
          const currentToken = await getToken();
          if (currentToken && webViewRef.current) {
            const tokenMessage = await createSignedMessage('JWT_TOKEN', {
              token: currentToken
            });
            webViewRef.current.postMessage(JSON.stringify(tokenMessage));
            addLog('Sent JWT token to WebView', 'success');
          } else {
            addLog('No token available for secure session', 'warning');
          }
          break;

        case 'NETWORK_ERROR':
          addLog(`Network Error: ${message.message}`, 'error');
          break;

        case 'CONSOLE_LOG':
          // Handle console logs from WebView
          const logLevel = message.level || 'log';
          const logArgs = message.args || [];
          const logMessage = logArgs.join(' ');
          
          // Map console levels to our log types
          const logTypeMap = {
            'log': 'console',
            'info': 'console',
            'warn': 'warning',
            'error': 'error',
            'debug': 'console'
          };
          
          addLog(`[WebView ${logLevel}] ${logMessage}`, logTypeMap[logLevel] || 'console');
          
          // If there's a stack trace, add it as a separate log
          if (message.stack) {
            addLog(`Stack: ${message.stack}`, 'error');
          }
          break;

        case 'NETWORK_LOG':
          // Handle network logs from WebView
          if (message.phase === 'request') {
            addLog(`${message.request.method} ${message.request.url}`, 'request');
            if (message.request.headers && Object.keys(message.request.headers).length > 0) {
              addLog(`Request Headers: ${JSON.stringify(message.request.headers, null, 2)}`, 'request');
            }
            if (message.request.body) {
              addLog(`Request Body: ${message.request.body}`, 'request');
            }
          } else if (message.phase === 'response') {
            const status = message.response.status;
            const logType = status >= 200 && status < 300 ? 'success' :
                          status >= 400 ? 'error' : 'warning';
            addLog(`${message.request.method} ${message.request.url} - ${status} ${message.response.statusText} (${message.response.duration}ms)`, logType);
            if (message.response.headers && Object.keys(message.response.headers).length > 0) {
              addLog(`Response Headers: ${JSON.stringify(message.response.headers, null, 2)}`, 'response');
            }
            if (message.response.body) {
              addLog(`Response Body: ${message.response.body.substring(0, 500)}...`, 'response');
            }
          } else if (message.phase === 'error') {
            addLog(`${message.request.method} ${message.request.url} - Network Error: ${message.error.message} (${message.error.duration}ms)`, 'error');
            if (message.error.stack) {
              addLog(`Error Stack: ${message.error.stack}`, 'error');
            }
          }
          break;

        default:
          addLog(`Unknown message type: ${message.type}`, 'warning');
      }
    } catch (e) {
      // Handle legacy string messages for backward compatibility
      if (typeof data === 'string') {
        if (data.startsWith('LOG:')) {
          addLog(data.substring(4), 'info');
        } else if (data.startsWith('REQUEST:')) {
          try {
            const requestData = JSON.parse(data.substring(8));
            addLog(`${requestData.method} ${requestData.url}`, 'request');
            addLog(`Headers: ${JSON.stringify(requestData.headers, null, 2)}`, 'request');
            if (requestData.body) {
              addLog(`Body: ${typeof requestData.body === 'string' ? requestData.body : JSON.stringify(requestData.body)}`, 'request');
            }
          } catch (e) {
            addLog(data, 'request');
          }
        } else if (data.startsWith('RESPONSE:')) {
          try {
            const responseData = JSON.parse(data.substring(9));
            const logType = responseData.status >= 200 && responseData.status < 300 ? 'success' :
              responseData.status >= 400 ? 'error' : 'warning';
            addLog(`${responseData.method} ${responseData.url} - ${responseData.status} ${responseData.statusText} (${responseData.responseTime}ms)`, logType);
            addLog(`Response Headers: ${JSON.stringify(responseData.headers, null, 2)}`, 'response');
          } catch (e) {
            addLog(data, 'response');
          }
        } else if (data.startsWith('ERROR:')) {
          const errorMessage = data.substring(6);
          addLog(`Error: ${errorMessage}`, 'error');
        } else if (data.startsWith('FETCH_ERROR:')) {
          try {
            const errorData = JSON.parse(data.substring(12));
            addLog(`Fetch Error: ${errorData.method} ${errorData.url} - ${errorData.error} (${errorData.responseTime}ms)`, 'error');
          } catch (e) {
            addLog(`Network Error: ${data.substring(12)}`, 'error');
          }
        } else if (data.startsWith('API_RESPONSE:')) {
          const responseData = data.substring(13);
          setResponseText(responseData);
          addLog('API test call successful', 'success');
        } else if (data.startsWith('API_ERROR:')) {
          const errorMessage = data.substring(10);
          setResponseText(`Error: ${errorMessage}`);
          addLog(`API test failed: ${errorMessage}`, 'error');
        } else {
          // Log any other messages
          addLog(`WebView message: ${data}`, 'info');
        }
      }
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Authentication Test</Text>
        <TouchableOpacity
          style={styles.reloadButton}
          onPress={handleReload}
        >
          <Text style={styles.reloadButtonText}>↻</Text>
        </TouchableOpacity>
      </View>

      {/* URL Input */}
      <View style={styles.urlInputContainer}>
        <TextInput
          style={styles.urlInput}
          value={url}
          onChangeText={setUrl}
          placeholder="Enter URL to test"
        />
        <TouchableOpacity
          style={styles.goButton}
          onPress={() => {
            setLogs([]);
            setResponseText('');
            if (webViewRef.current) {
              // Parse URL to add loginId if missing
              let finalUrl = url;
              try {
                const urlObj = new URL(url);
                if (!urlObj.searchParams.has('loginId') && (numericUserId || user?.id)) {
                  urlObj.searchParams.set('loginId', numericUserId || user?.id || '');
                  finalUrl = urlObj.toString();
                  setUrl(finalUrl);
                }
              } catch (e) {
                // URL parsing failed, use string method
                if (!url.includes('loginId=')) {
                  finalUrl = addLoginIdToUrl(url, numericUserId || user?.id);
                }
              }
              addLog(`Navigating to: ${finalUrl}`, 'info');
              webViewRef.current.reload();
            }
          }}
        >
          <Text style={styles.goButtonText}>Go</Text>
        </TouchableOpacity>
      </View>

      {/* Test Buttons */}
      <View style={styles.testButtonsContainer}>
        <TouchableOpacity
          style={styles.testButton}
          onPress={testDirectApiCall}
        >
          <Text style={styles.testButtonText}>Test Direct API</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={testWebViewCall}
        >
          <Text style={styles.testButtonText}>Test WebView API</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={() => {
            // New function to test secure session in console
            if (webViewRef.current) {
              webViewRef.current.injectJavaScript(`
                try {
                  console.log('Current page URL:', window.location.href);
                  console.log('Secure Session in window:', window.SECURE_SESSION_ID);
                  console.log('Legacy tokens cleared from localStorage');

                  // Test a fetch call with secure session
                  fetch('https://digiflowtest.digiturk.com.tr/api/users/id')
                  .then(response => {
                    console.log('Auth test response status:', response.status);
                    return response.text();
                  })
                  .then(data => {
                    console.log('Auth test response:', data);
                    window.ReactNativeWebView.postMessage('API_RESPONSE:' + JSON.stringify(data));
                  })
                  .catch(error => {
                    console.log('Auth test error:', error);
                    window.ReactNativeWebView.postMessage('API_ERROR:' + error.message);
                  });
                } catch (e) {
                  console.log('Debug test error:', e);
                }
                true;
              `);
              addLog('Running secure session debug test', 'info');
            }
          }}
        >
          <Text style={styles.testButtonText}>Debug Secure Session</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.testButton}
          onPress={() => {
            setDebugMode(!debugMode);
            addLog(`Debug mode ${!debugMode ? 'enabled' : 'disabled'}`, 'info');
          }}
        >
          <Text style={styles.testButtonText}>
            {debugMode ? 'Hide Debug' : 'Show Debug'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.testButton}
          onPress={async () => {
            // Check JWT status and manually inject if needed
            const currentToken = await getToken();
            if (currentToken && webViewRef.current) {
              webViewRef.current.injectJavaScript(`
                try {
                  const token = '${currentToken}';
                  console.log('[Manual] Injecting JWT token into sessionStorage');
                  sessionStorage.setItem('webview_jwt', token);
                  console.log('[Manual] Token injected, checking React app...');
                  
                  // Check if React app can see it
                  const storedToken = sessionStorage.getItem('webview_jwt');
                  console.log('[Manual] Token in sessionStorage:', storedToken ? 'Present' : 'Missing');
                  
                  // Try to trigger React app reload
                  if (window.location.href.includes('/react/')) {
                    console.log('[Manual] Reloading React app with token...');
                    window.location.reload();
                  }
                } catch (e) {
                  console.error('[Manual] Error injecting token:', e);
                }
                true;
              `);
              addLog('Manually injected JWT token', 'success');
            } else {
              addLog('No JWT token available', 'error');
            }
          }}
        >
          <Text style={styles.testButtonText}>Inject JWT Token</Text>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View style={styles.contentContainer}>
        {/* WebView */}
        <View style={[styles.webViewContainer, debugMode ? { height: '40%' } : { flex: 1 }]}>
          {isLoading && (
            <View style={styles.loadingOverlay}>
              <ActivityIndicator size="large" color="#5E378C" />
            </View>
          )}

          <WebView
            ref={webViewRef}
            source={{ uri: url }}
            style={styles.webView}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            sharedCookiesEnabled={true}
            thirdPartyCookiesEnabled={true}
            injectedJavaScriptBeforeContentLoaded={injectBeforeScript}
            injectedJavaScript={injectScript}
            onLoadStart={() => {
              setIsLoading(true);
              addLog(`Loading URL: ${url}`, 'info');
            }}
            onLoadEnd={() => {
              setIsLoading(false);
              addLog(`Loaded URL: ${url}`, 'success');
            }}
            onMessage={handleMessage}
            onError={(event) => {
              addLog(`WebView error: ${event.nativeEvent.description}`, 'error');
            }}
          />
        </View>

        {/* Debug Console */}
        {debugMode && (
          <View style={styles.debugContainer}>
            <View style={styles.responseContainer}>
              <Text style={styles.debugTitle}>API Response:</Text>
              <ScrollView style={styles.responseScroll}>
                <Text style={styles.responseText}>{responseText}</Text>
              </ScrollView>
            </View>

            <View style={styles.logsContainer}>
              <View style={styles.logHeader}>
                <Text style={styles.debugTitle}>Debug Logs:</Text>
                <View style={styles.logButtons}>
                  <TouchableOpacity
                    style={[styles.clearButton, { marginRight: 8 }]}
                    onPress={async () => {
                      const logText = logs.map(log => log.message).join('\n');
                      try {
                        await Share.share({
                          message: logText,
                          title: 'WebView Debug Logs',
                        });
                        addLog('Logs shared', 'success');
                      } catch (error) {
                        addLog('Failed to share logs', 'error');
                      }
                    }}
                  >
                    <Text style={styles.clearButtonText}>Share</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => {
                      setLogs([]);
                      addLog('Logs cleared', 'info');
                    }}
                  >
                    <Text style={styles.clearButtonText}>Clear</Text>
                  </TouchableOpacity>
                </View>
              </View>
              <ScrollView style={styles.logsScroll}>
                {logs.map((log, index) => (
                  <Text key={index} style={[
                    styles.logText,
                    log.type === 'error' && styles.errorLog,
                    log.type === 'success' && styles.successLog,
                    log.type === 'warning' && styles.warningLog,
                    log.type === 'request' && styles.requestLog,
                    log.type === 'response' && styles.responseLog,
                    log.type === 'console' && styles.consoleLog,
                    log.type === 'network' && styles.networkLog,
                  ]}>{log.message}</Text>
                ))}
              </ScrollView>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: '#5E378C',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    color: 'white',
    fontSize: 20,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  reloadButton: {
    padding: 8,
  },
  reloadButtonText: {
    color: 'white',
    fontSize: 20,
  },
  urlInputContainer: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: '#fff',
    borderBottomColor: '#ddd',
    borderBottomWidth: 1,
  },
  urlInput: {
    flex: 1,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 4,
    padding: 8,
    backgroundColor: '#f9f9f9',
  },
  goButton: {
    marginLeft: 10,
    backgroundColor: '#5E378C',
    padding: 10,
    borderRadius: 4,
    justifyContent: 'center',
  },
  goButtonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  testButtonsContainer: {
    flexDirection: 'row',
    padding: 10,
    backgroundColor: '#fff',
    justifyContent: 'space-between',
    borderBottomColor: '#ddd',
    borderBottomWidth: 1,
  },
  testButton: {
    backgroundColor: '#5E378C',
    padding: 10,
    borderRadius: 4,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  testButtonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 12,
  },
  contentContainer: {
    flex: 1,
  },
  webViewContainer: {
    flex: 1,
    position: 'relative',
  },
  webView: {
    flex: 1,
  },
  loadingOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  debugContainer: {
    flex: 1,
    padding: 10,
    backgroundColor: '#f0f0f0',
  },
  responseContainer: {
    flex: 1,
    marginBottom: 10,
  },
  responseScroll: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 4,
    padding: 8,
  },
  responseText: {
    fontFamily: 'monospace',
    fontSize: 12,
  },
  logsContainer: {
    flex: 1,
  },
  logsScroll: {
    flex: 1,
    backgroundColor: '#222',
    borderRadius: 4,
    padding: 8,
  },
  logText: {
    color: '#00ff00',
    fontFamily: 'monospace',
    fontSize: 11,
    marginBottom: 2,
  },
  debugTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
  },
  errorLog: {
    color: '#ff6b6b',
  },
  successLog: {
    color: '#51cf66',
  },
  warningLog: {
    color: '#ffd43b',
  },
  requestLog: {
    color: '#74c0fc',
  },
  responseLog: {
    color: '#a9e34b',
  },
  consoleLog: {
    color: '#00ff00',
  },
  networkLog: {
    color: '#ff00ff',
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  clearButton: {
    backgroundColor: '#5E378C',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 4,
  },
  clearButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  logButtons: {
    flexDirection: 'row',
  },
});

export default WebViewTestScreen;
