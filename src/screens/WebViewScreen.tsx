import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  SafeAreaView,
  BackHandler,
  ScrollView,
  Modal,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getToken, refreshToken, getNumericUserId } from '../services/api';
import { useAuthStore } from '../store/authStore';
import { generateSecureId, createSignedMessage, validateSignedMessage } from '../utils/secureNonce';

type WebViewScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WebView'>;
type WebViewScreenRouteProp = RouteProp<RootStackParamList, 'WebView'>;

interface WebViewScreenProps {
  navigation: WebViewScreenNavigationProp;
  route: WebViewScreenRouteProp;
}

// Secure allowed origins - UPDATE THESE TO YOUR ACTUAL DOMAINS
const ALLOWED_ORIGINS = [
  'https://digiflow.digiturk.com.tr',
  'https://digiflowtest.digiturk.com.tr',
];

interface ConsoleLog {
  level: 'log' | 'warn' | 'error' | 'info' | 'debug';
  args: string[];
  timestamp: string;
  url?: string;
  stack?: string;
}

interface NetworkLog {
  id?: number;
  phase: 'request' | 'response' | 'error';
  timestamp: string;
  request: {
    id: number;
    url: string;
    method: string;
    headers?: Record<string, string>;
    body?: string;
  };
  response?: {
    status: number;
    statusText: string;
    headers: Record<string, string>;
    body: string;
    duration: number;
  };
  error?: {
    message: string;
    stack?: string;
    duration: number;
  };
}

const WebViewScreen: React.FC<WebViewScreenProps> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const [numericUserId, setNumericUserId] = useState<string | null>(null);
  const { url: initialUrl, title } = route.params;
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [consoleLogs, setConsoleLogs] = useState<ConsoleLog[]>([]);
  const [networkLogs, setNetworkLogs] = useState<NetworkLog[]>([]);
  const [showConsole, setShowConsole] = useState(false);
  const [activeTab, setActiveTab] = useState<'console' | 'network'>('console');

  // Function to add loginId to URL
  const addLoginIdToUrl = (baseUrl: string, userId?: string) => {
    if (!userId) { return baseUrl; }

    try {
      const url = new URL(baseUrl);
      // Remove any existing loginId parameter first
      url.searchParams.delete('loginId');
      // Then set the new one
      url.searchParams.set('loginId', userId);
      return url.toString();
    } catch (error) {
      // If URL parsing fails, fallback to simple string concatenation
      // Remove any existing loginId parameter
      let cleanUrl = baseUrl.replace(/[?&]loginId=[^&]*/g, '');
      // Fix any double ? or & that might result
      cleanUrl = cleanUrl.replace(/\?&/g, '?').replace(/&&/g, '&').replace(/\?$/, '');

      const separator = cleanUrl.includes('?') ? '&' : '?';
      return `${cleanUrl}${separator}loginId=${userId}`;
    }
  };

  const [url, setUrl] = useState<string>(initialUrl); // Don't add loginId yet, wait for numeric ID
  const [isLoading, setIsLoading] = useState(true);
  const [isAsyncStorageReady, setIsAsyncStorageReady] = useState(false);
  const webViewRef = useRef<WebView>(null);
  const [error, setError] = useState<string | null>(null);

  // Get JWT token when component mounts
  useEffect(() => {
    let isMounted = true;

    const initializeSecureSession = async () => {
      try {
        if (!AsyncStorage) {
          console.warn('AsyncStorage is not available');
          setIsAsyncStorageReady(false);
          return;
        }

        // Generate secure session ID
        const newSessionId = generateSecureId();
        setSessionId(newSessionId);

        // Get numeric user ID
        const numericId = await getNumericUserId();

        if (isMounted) {
          setNumericUserId(numericId);
          setIsAsyncStorageReady(true);

          // Update URL with numeric ID
          if (numericId) {
            const urlWithLoginId = addLoginIdToUrl(initialUrl, numericId);
            setUrl(urlWithLoginId);
            console.log('🔗 WebView URL with numeric loginId:', urlWithLoginId);
            console.log('📍 Initial URL was:', initialUrl);
            console.log('👤 Numeric ID:', numericId);
          } else {
            console.log('⚠️ No numeric user ID found, using username');
            const urlWithLoginId = addLoginIdToUrl(initialUrl, user?.id);
            setUrl(urlWithLoginId);
            console.log('🔗 WebView URL with username:', urlWithLoginId);
          }

          // Refresh token to ensure it's valid (but don't expose it)
          try {
            await refreshToken();
          } catch (refreshError) {
            console.warn('Token refresh failed, will continue with existing session');
          }
        }
      } catch (error) {
        console.error('Error initializing secure session:', error);
        if (isMounted) {
          setIsAsyncStorageReady(false);
          setError('Unable to initialize secure session. Some features may be limited.');
        }
      }
    };

    initializeSecureSession();

    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (webViewRef.current) {
        webViewRef.current.goBack();
        return true;
      }
      return false;
    });

    return () => {
      isMounted = false;
      backHandler.remove();
    };
  }, []);

  // Function to reload the WebView
  const handleReload = () => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  // Early injection script for console override
  const injectBeforeScript = `
  (function() {
    // Debug: Log that injection is starting
    const originalLog = console.log;
    originalLog('[WebView] Starting early console injection at:', new Date().toISOString());

    // Store original console methods immediately
    window.__originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // Override console methods immediately
    ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
      console[method] = function(...args) {
        // Call original method
        window.__originalConsole[method].apply(console, args);

        // Try to send to React Native if available
        if (window.ReactNativeWebView) {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'CONSOLE_LOG',
              level: method,
              args: args.map(arg => {
                try {
                  if (arg === null) return 'null';
                  if (arg === undefined) return 'undefined';
                  if (typeof arg === 'object') {
                    return JSON.stringify(arg, (key, value) => {
                      if (typeof value === 'function') return '[Function]';
                      if (value instanceof Error) {
                        return {
                          name: value.name,
                          message: value.message,
                          stack: value.stack
                        };
                      }
                      return value;
                    }, 2);
                  }
                  return String(arg);
                } catch (e) {
                  return '[Object with circular reference]';
                }
              }),
              timestamp: new Date().toISOString(),
              url: window.location ? window.location.href : 'unknown'
            }));
          } catch (e) {
            window.__originalConsole.error('Failed to send console log:', e);
          }
        }
      };
    });

    window.__originalConsole.log('[WebView] Console override installed early');
    true;
  })();
  `;

  // Main injection script with JWT support and network logging
  const injectScript = `
  (function() {
    // Use stored original console or create new one
    const originalConsole = window.__originalConsole || {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug
    };

    // Re-apply console override (in case it was lost)
    ['log', 'warn', 'error', 'info', 'debug'].forEach(method => {
      console[method] = function(...args) {
        // Call original method
        originalConsole[method].apply(console, args);

        // Send to React Native
        if (window.ReactNativeWebView) {
          try {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'CONSOLE_LOG',
              level: method,
              args: args.map(arg => {
                try {
                  if (arg === null) return 'null';
                  if (arg === undefined) return 'undefined';
                  if (typeof arg === 'object') {
                    // Handle circular references and functions
                    return JSON.stringify(arg, (key, value) => {
                      if (typeof value === 'function') return '[Function]';
                      if (value instanceof Error) {
                        return {
                          name: value.name,
                          message: value.message,
                          stack: value.stack
                        };
                      }
                      return value;
                    }, 2);
                  }
                  return String(arg);
                } catch (e) {
                  return '[Object with circular reference]';
                }
              }),
              timestamp: new Date().toISOString(),
              url: window.location.href
            }));
          } catch (e) {
            originalConsole.error('Failed to send console log to React Native:', e);
          }
        }
      };
    });

    // Also capture unhandled errors
    window.addEventListener('error', function(event) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'CONSOLE_LOG',
          level: 'error',
          args: ['Uncaught Error: ' + event.message],
          timestamp: new Date().toISOString(),
          url: window.location.href,
          stack: event.error ? event.error.stack : 'No stack trace available'
        }));
      }
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', function(event) {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'CONSOLE_LOG',
          level: 'error',
          args: ['Unhandled Promise Rejection: ' + event.reason],
          timestamp: new Date().toISOString(),
          url: window.location.href
        }));
      }
    });

    console.log('[WebView Console] Main console override installed - logs will be captured');

    // Network request tracking
    let requestId = 0;
    const networkRequests = new Map();

    // Helper to send network log
    function sendNetworkLog(data) {
      if (window.ReactNativeWebView) {
        try {
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'NETWORK_LOG',
            ...data,
            timestamp: new Date().toISOString()
          }));
        } catch (e) {
          originalConsole.error('Failed to send network log:', e);
        }
      }
    }

    // Store JWT token for API requests (secure implementation)
    let jwtToken = null;

    // Set up secure message listener with origin validation
    window.addEventListener('message', function(event) {
      // Validate message origin
      const allowedOrigins = ${JSON.stringify(ALLOWED_ORIGINS)};
      if (!allowedOrigins.includes(event.origin)) {
        console.warn('[Security] Rejected message from untrusted origin:', event.origin);
        return;
      }

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;

        switch(data.type) {
          case 'USER_DATA':
            // Store user data securely (non-sensitive only)
            window.__secureUserData = {
              userId: data.userId,
              username: data.username
              // Only non-sensitive data
            };

            // Dispatch event for React app
            window.dispatchEvent(new CustomEvent('userDataReceived', {
              detail: window.__secureUserData
            }));
            break;

          case 'JWT_TOKEN':
            // Update the stored JWT token
            jwtToken = data.payload.token;
            console.log('[WebView] JWT token received for API authentication');
            break;

          default:
            console.log('[WebView] Unknown message type:', data.type);
        }
      } catch (e) {
        console.error('[WebView] Message parsing error:', e);
      }
    });

    // Request authentication state and JWT token on load
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'AUTH_STATE_REQUEST',
        timestamp: Date.now()
      }));

      // Also request JWT token for API authentication
      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'REQUEST_SECURE_SESSION',
        timestamp: Date.now()
      }));
    }

    // Enhanced fetch interceptor with JWT token injection and network logging
    const originalFetch = window.fetch;
    window.fetch = function(url, options = {}) {
      const currentRequestId = ++requestId;
      const startTime = Date.now();
      const newOptions = { ...options };
      newOptions.headers = newOptions.headers || {};

      // Add mobile-specific headers for identification
      newOptions.headers['X-Mobile-App'] = 'true';
      newOptions.headers['X-Is-Mobile'] = 'true';
      newOptions.headers['X-From-Mobile-WebView'] = 'true';
      newOptions.headers['X-Session-Id'] = '${sessionId || ''}';

      // Add login ID header
      const loginId = '${numericUserId || user?.id || ''}';
      if (loginId) {
        newOptions.headers['X-Login-Id'] = loginId;
      }

      // Add JWT token for API calls (including auth endpoints)
      if (jwtToken && (url.includes('/api/') || url.includes('/auth/'))) {
        newOptions.headers['Authorization'] = 'Bearer ' + jwtToken;
        console.log('[API] Adding JWT token to request:', url);
      }

      newOptions.credentials = 'include';
      newOptions.mode = 'cors';

      // Log API requests for debugging (without sensitive data)
      if (url.includes('/api/')) {
        console.log('[API] Request:', newOptions.method || 'GET', url);
        console.log('[API] Session ID present:', !!newOptions.headers['X-Session-Id']);
      }

      // Capture request details
      const requestData = {
        id: currentRequestId,
        url: url,
        method: newOptions.method || 'GET',
        headers: { ...newOptions.headers },
        body: newOptions.body ? (typeof newOptions.body === 'string' ? newOptions.body.substring(0, 1000) : '[Binary Data]') : null
      };

      // Remove sensitive headers from logging
      if (requestData.headers['Authorization']) {
        requestData.headers['Authorization'] = 'Bearer [HIDDEN]';
      }

      // Send request start log
      sendNetworkLog({
        phase: 'request',
        request: requestData
      });

      return originalFetch(url, newOptions)
        .then(async response => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // Clone response to read body without consuming it
          const responseClone = response.clone();
          let responseBody = null;

          try {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              responseBody = await responseClone.json();
            } else if (contentType && contentType.includes('text')) {
              responseBody = await responseClone.text();
            }
          } catch (e) {
            responseBody = '[Unable to parse response body]';
          }

          // Capture response headers
          const responseHeaders = {};
          response.headers.forEach((value, key) => {
            responseHeaders[key] = value;
          });

          // Send response log
          sendNetworkLog({
            phase: 'response',
            request: {
              id: currentRequestId,
              url: url,
              method: newOptions.method || 'GET'
            },
            response: {
              status: response.status,
              statusText: response.statusText,
              headers: responseHeaders,
              body: responseBody ? JSON.stringify(responseBody).substring(0, 1000) : null,
              duration: duration
            }
          });

          if (response.status === 401) {
            console.error('[Auth] Authentication error (401) on request to:', url);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'AUTH_ERROR',
              url: url,
              timestamp: Date.now()
            }));
          }
          return response;
        })
        .catch(error => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          // Send error log
          sendNetworkLog({
            phase: 'error',
            request: {
              id: currentRequestId,
              url: url,
              method: newOptions.method || 'GET'
            },
            error: {
              message: error.message,
              stack: error.stack,
              duration: duration
            }
          });

          console.error('[Fetch] Error:', error.message);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'NETWORK_ERROR',
            message: error.message,
            timestamp: Date.now()
          }));
          throw error;
        });
    };

    // Enhanced XHR interceptor with JWT token injection and network logging
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
      const xhr = this;
      const args = arguments;
      const method = args[0];
      const url = args[1];
      const currentRequestId = ++requestId;
      let startTime;

      xhr._requestData = {
        id: currentRequestId,
        method: method,
        url: url,
        headers: {}
      };

      // Override setRequestHeader to capture headers
      const originalSetRequestHeader = xhr.setRequestHeader;
      xhr.setRequestHeader = function(header, value) {
        xhr._requestData.headers[header] = header === 'Authorization' ? 'Bearer [HIDDEN]' : value;
        return originalSetRequestHeader.apply(xhr, arguments);
      };

      xhr.addEventListener('loadstart', function() {
        startTime = Date.now();
        sendNetworkLog({
          phase: 'request',
          request: xhr._requestData
        });
      });

      xhr.addEventListener('load', function() {
        const endTime = Date.now();
        const duration = endTime - startTime;

        let responseBody = null;
        try {
          if (xhr.responseType === '' || xhr.responseType === 'text') {
            responseBody = xhr.responseText.substring(0, 1000);
          } else if (xhr.responseType === 'json') {
            responseBody = JSON.stringify(xhr.response).substring(0, 1000);
          }
        } catch (e) {
          responseBody = '[Unable to parse response]';
        }

        // Get response headers
        const responseHeaders = {};
        const headerLines = xhr.getAllResponseHeaders().split('\n');
        headerLines.forEach(line => {
          const parts = line.split(': ');
          if (parts.length === 2) {
            responseHeaders[parts[0]] = parts[1];
          }
        });

        sendNetworkLog({
          phase: 'response',
          request: {
            id: currentRequestId,
            url: url,
            method: method
          },
          response: {
            status: xhr.status,
            statusText: xhr.statusText,
            headers: responseHeaders,
            body: responseBody,
            duration: duration
          }
        });
      });

      xhr.addEventListener('error', function() {
        const endTime = Date.now();
        const duration = endTime - startTime;

        sendNetworkLog({
          phase: 'error',
          request: {
            id: currentRequestId,
            url: url,
            method: method
          },
          error: {
            message: 'Network request failed',
            duration: duration
          }
        });
      });

      xhr.addEventListener('readystatechange', function() {
        if (xhr.readyState === 4 && xhr.status === 401) {
          console.error('[Auth] XHR Authentication error (401):', url);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'AUTH_ERROR',
            url: url,
            timestamp: Date.now()
          }));
        }
      });

      const originalSend = xhr.send;
      xhr.send = function(body) {
        // Add mobile headers for identification
        xhr.setRequestHeader('X-Mobile-App', 'true');
        xhr.setRequestHeader('X-Is-Mobile', 'true');
        xhr.setRequestHeader('X-From-Mobile-WebView', 'true');
        xhr.setRequestHeader('X-Session-Id', '${sessionId || ''}');

        // Add X-Login-Id header
        const loginId = '${numericUserId || user?.id || ''}';
        if (loginId) {
          xhr.setRequestHeader('X-Login-Id', loginId);
        }

        // Add JWT token for API calls (including auth endpoints)
        if (jwtToken && (url.includes('/api/') || url.includes('/auth/'))) {
          xhr.setRequestHeader('Authorization', 'Bearer ' + jwtToken);
          console.log('[API] Adding JWT token to XHR request:', url);
        }

        // Capture request body
        if (body) {
          xhr._requestData.body = typeof body === 'string' ? body.substring(0, 1000) : '[Binary Data]';
        }

        return originalSend.apply(xhr, arguments);
      };

      return originalOpen.apply(xhr, args);
    };

    // Set up page load handler
    window.addEventListener('load', function() {
      console.log('[WebView] Page loaded with secure session');
      console.log('[WebView Test] Current URL:', window.location.href);
      console.log('[WebView Test] User Agent:', navigator.userAgent);
      console.log('[WebView Test] Console capture is working if you see this!');

      window.ReactNativeWebView.postMessage(JSON.stringify({
        type: 'PAGE_LOADED',
        timestamp: Date.now()
      }));
    });

    // Also log immediately to test
    console.log('[WebView] Secure session script loaded with JWT authentication');
    console.log('[WebView Test] Immediate test - console capture active!');

    // Test with a timer to see if React app overwrites console
    setTimeout(function() {
      console.log('[WebView Test] 1 second delay - console still working?');
    }, 1000);

    setTimeout(function() {
      console.log('[WebView Test] 3 second delay - React app loaded?');
    }, 3000);

    true;
  })();
`;


  const handleMessage = async (event: any) => {
    const { data } = event.nativeEvent;

    // Debug: Log all incoming messages
    console.log('[WebView Message Received]', data);

    try {
      const message = typeof data === 'string' ? JSON.parse(data) : data;

      switch (message.type) {
        case 'PAGE_LOADED':
          setIsLoading(false);

          // Send user data and JWT token
          if (webViewRef.current && (numericUserId || user?.id)) {
            const signedMessage = await createSignedMessage('USER_DATA', {
              userId: numericUserId || user?.id,
              username: user?.id,
            });

            webViewRef.current.postMessage(JSON.stringify(signedMessage));

            // Also send JWT token for initial authentication
            const token = await getToken();
            if (token) {
              const tokenMessage = await createSignedMessage('JWT_TOKEN', {
                token: token
              });
              webViewRef.current.postMessage(JSON.stringify(tokenMessage));
            }
          }
          break;

        case 'AUTH_STATE_REQUEST':
          // Handle auth state request
          const authToken = await getToken();
          const isAuthenticated = !!authToken;

          if (webViewRef.current) {
            const signedMessage = await createSignedMessage('AUTH_STATE', {
              authenticated: isAuthenticated
            });

            webViewRef.current.postMessage(JSON.stringify(signedMessage));

            // Also send JWT token for webview authentication
            if (authToken) {
              const tokenMessage = await createSignedMessage('JWT_TOKEN', {
                token: authToken
              });
              webViewRef.current.postMessage(JSON.stringify(tokenMessage));
            }
          }
          break;

        case 'AUTH_ERROR':
          // Handle authentication errors
          try {
            await refreshToken();
            // Notify WebView of refreshed auth state
            if (webViewRef.current) {
              const signedMessage = await createSignedMessage('AUTH_REFRESHED');
              webViewRef.current.postMessage(JSON.stringify(signedMessage));

              // Send the new JWT token after refresh
              const newToken = await getToken();
              if (newToken) {
                const tokenMessage = await createSignedMessage('JWT_TOKEN', {
                  token: newToken
                });
                webViewRef.current.postMessage(JSON.stringify(tokenMessage));
              }
            }
          } catch (err) {
            console.error('Failed to refresh token after auth error', err);
            // Could navigate back to login here if needed
          }
          break;

        case 'NETWORK_ERROR':
          console.error('Network error from WebView:', message.message);
          break;

        case 'REQUEST_SECURE_SESSION':
          // Handle secure session request from React app
          const currentToken = await getToken();
          if (currentToken && webViewRef.current) {
            const tokenMessage = await createSignedMessage('JWT_TOKEN', {
              token: currentToken
            });
            webViewRef.current.postMessage(JSON.stringify(tokenMessage));
          }
          break;

        case 'CONSOLE_LOG':
          // Handle console logs from WebView
          const consoleLog: ConsoleLog = {
            level: message.level || 'log',
            args: message.args || [],
            timestamp: message.timestamp || new Date().toISOString(),
            url: message.url,
            stack: message.stack
          };

          // Add to console logs (keep last 100)
          setConsoleLogs(prev => [...prev.slice(-99), consoleLog]);

          // Also log to React Native console for debugging
          console.log(`[WebView ${message.level}]`, ...message.args);
          break;

        case 'NETWORK_LOG':
          // Handle network logs from WebView
          const networkLog: NetworkLog = {
            phase: message.phase,
            timestamp: message.timestamp,
            request: message.request,
            response: message.response,
            error: message.error
          };

          if (message.phase === 'request') {
            // Add new request
            setNetworkLogs(prev => [...prev.slice(-99), networkLog]);
          } else {
            // Update existing request with response/error
            setNetworkLogs(prev =>
              prev.map(log =>
                log.request.id === message.request.id
                  ? { ...log, ...networkLog }
                  : log
              )
            );
          }
          break;

        default:
          console.log('Unknown message type from WebView:', message.type);
      }
    } catch (e) {
      // Handle legacy string messages for backward compatibility
      if (typeof data === 'string') {
        if (data === 'NAVIGATION_STARTED') {
          setIsLoading(true);
        } else if (data.startsWith('ERROR:')) {
          const errorMessage = data.substring(6);
          setError(errorMessage);
        }
      }
    }
  };

  // If AsyncStorage is not ready, show a loading state
  if (!isAsyncStorageReady) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
          <View style={styles.reloadButton} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5E378C" />
          <Text style={styles.loadingText}>Initializing storage...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // If there's an error, show error state
  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
          <TouchableOpacity
            style={styles.reloadButton}
            onPress={() => setError(null)}
          >
            <Text style={styles.reloadButtonText}>↻</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity
            style={styles.tryAgainButton}
            onPress={() => {
              setError(null);
              handleReload();
            }}
          >
            <Text style={styles.tryAgainText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // Console modal component
  const ConsoleModal = () => (
    <Modal
      visible={showConsole}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setShowConsole(false)}
    >
      <View style={styles.consoleModalContainer}>
        <View style={styles.consoleModal}>
          <View style={styles.consoleHeader}>
            <Text style={styles.consoleTitle}>Developer Console</Text>
            <TouchableOpacity
              onPress={() => {
                if (activeTab === 'console') {
                  setConsoleLogs([]);
                } else {
                  setNetworkLogs([]);
                }
              }}
              style={styles.clearButton}
            >
              <Text style={styles.clearButtonText}>Clear</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setShowConsole(false)}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>✕</Text>
            </TouchableOpacity>
          </View>

          {/* Tabs */}
          <View style={styles.tabContainer}>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'console' && styles.activeTab]}
              onPress={() => setActiveTab('console')}
            >
              <Text style={[styles.tabText, activeTab === 'console' && styles.activeTabText]}>
                Console ({consoleLogs.length})
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.tab, activeTab === 'network' && styles.activeTab]}
              onPress={() => setActiveTab('network')}
            >
              <Text style={[styles.tabText, activeTab === 'network' && styles.activeTabText]}>
                Network ({networkLogs.filter(log => log.phase === 'request').length})
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          <ScrollView style={styles.consoleContent}>
            {activeTab === 'console' ? (
              // Console logs
              consoleLogs.length === 0 ? (
                <Text style={styles.noLogsText}>No console logs yet</Text>
              ) : (
                consoleLogs.map((log, index) => (
                  <View key={index} style={[styles.logItem, styles[`log${log.level}`]]}>
                    <Text style={styles.logTimestamp}>
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </Text>
                    <Text style={styles.logLevel}>[{log.level.toUpperCase()}]</Text>
                    <Text style={styles.logMessage}>
                      {log.args.join(' ')}
                    </Text>
                    {log.stack && (
                      <Text style={styles.logStack}>{log.stack}</Text>
                    )}
                  </View>
                ))
              )
            ) : (
              // Network logs
              networkLogs.length === 0 ? (
                <Text style={styles.noLogsText}>No network requests yet</Text>
              ) : (
                networkLogs
                  .filter(log => log.phase === 'request')
                  .map((log, index) => {
                    const responseLog = networkLogs.find(
                      l => l.request.id === log.request.id && (l.phase === 'response' || l.phase === 'error')
                    );

                    return (
                      <View key={index} style={styles.networkItem}>
                        <View style={styles.networkHeader}>
                          <Text style={[
                            styles.networkMethod,
                            { color: log.request.method === 'GET' ? '#4CAF50' : '#FF9800' }
                          ]}>
                            {log.request.method}
                          </Text>
                          <Text style={styles.networkUrl} numberOfLines={1}>
                            {log.request.url.replace(/^https?:\/\/[^\/]+/, '')}
                          </Text>
                          {responseLog && (
                            <Text style={[
                              styles.networkStatus,
                              responseLog.response && responseLog.response.status < 400
                                ? styles.statusSuccess
                                : styles.statusError
                            ]}>
                              {responseLog.response?.status || 'Error'}
                            </Text>
                          )}
                          {responseLog?.response?.duration && (
                            <Text style={styles.networkDuration}>
                              {responseLog.response.duration}ms
                            </Text>
                          )}
                        </View>

                        <TouchableOpacity
                          onPress={() => {
                            // You could expand details here
                            console.log('Request details:', log);
                            if (responseLog) {
                              console.log('Response details:', responseLog);
                            }
                          }}
                        >
                          <Text style={styles.networkDetails}>Tap for details</Text>
                        </TouchableOpacity>
                      </View>
                    );
                  })
              )
            )}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title}</Text>
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.consoleButton}
            onPress={() => setShowConsole(true)}
          >
            <Text style={styles.consoleButtonText}>📋</Text>
            {consoleLogs.filter(log => log.level === 'error').length > 0 && (
              <View style={styles.errorBadge}>
                <Text style={styles.errorBadgeText}>
                  {consoleLogs.filter(log => log.level === 'error').length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.reloadButton}
            onPress={handleReload}
          >
            <Text style={styles.reloadButtonText}>↻</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* WebView */}
      <View style={styles.webViewContainer}>
        <WebView
          ref={webViewRef}
          source={{
            uri: url,
            headers: {
              'X-Mobile-App': 'true',
              'X-Is-Mobile': 'true',
              'X-From-Mobile-WebView': 'true',
              'X-Session-Id': sessionId || '',
            },
          }}
          style={styles.webView}
          injectedJavaScriptBeforeContentLoaded={injectBeforeScript}
          injectedJavaScript={injectScript}
          onMessage={handleMessage}
          onLoadStart={() => setIsLoading(true)}
          onLoadEnd={() => setTimeout(() => setIsLoading(false), 500)}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            setError(`Failed to load: ${nativeEvent.description}`);
          }}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          sharedCookiesEnabled={true}
          thirdPartyCookiesEnabled={true}
          userAgent="ReactNative-WebView-DigiHR-App"
          originWhitelist={ALLOWED_ORIGINS}
          mixedContentMode="compatibility"
        />
      </View>

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#5E378C" />
          <Text style={styles.loadingText}>Loading your content...</Text>
        </View>
      )}

      {/* Console Modal */}
      <ConsoleModal />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 24,
    color: '#5E378C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  reloadButton: {
    padding: 8,
  },
  reloadButtonText: {
    fontSize: 24,
    color: '#5E378C',
  },
  webViewContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#5E378C',
    textAlign: 'center',
    marginBottom: 20,
  },
  tryAgainButton: {
    backgroundColor: '#5E378C',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  tryAgainText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#5E378C',
  },
  headerButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  consoleButton: {
    padding: 8,
    marginRight: 8,
    position: 'relative',
  },
  consoleButtonText: {
    fontSize: 20,
  },
  errorBadge: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: 'red',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  errorBadgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
  consoleModalContainer: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  consoleModal: {
    backgroundColor: 'white',
    height: '60%',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 10,
  },
  consoleHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  consoleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    flex: 1,
  },
  clearButton: {
    padding: 8,
    marginRight: 8,
  },
  clearButtonText: {
    color: '#5E378C',
    fontSize: 14,
    fontWeight: '600',
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 20,
    color: '#666',
  },
  consoleContent: {
    flex: 1,
    padding: 16,
  },
  noLogsText: {
    textAlign: 'center',
    color: '#999',
    marginTop: 20,
  },
  logItem: {
    marginBottom: 12,
    padding: 8,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderLeftWidth: 3,
  },
  loglog: {
    borderLeftColor: '#666',
  },
  logwarn: {
    borderLeftColor: '#ff9800',
    backgroundColor: '#fff3e0',
  },
  logerror: {
    borderLeftColor: '#f44336',
    backgroundColor: '#ffebee',
  },
  loginfo: {
    borderLeftColor: '#2196f3',
    backgroundColor: '#e3f2fd',
  },
  logdebug: {
    borderLeftColor: '#9c27b0',
    backgroundColor: '#f3e5f5',
  },
  logTimestamp: {
    fontSize: 10,
    color: '#666',
    marginBottom: 2,
  },
  logLevel: {
    fontSize: 11,
    fontWeight: 'bold',
    marginBottom: 2,
  },
  logMessage: {
    fontSize: 13,
    color: '#333',
    fontFamily: 'monospace',
  },
  logStack: {
    fontSize: 11,
    color: '#666',
    marginTop: 4,
    fontFamily: 'monospace',
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  activeTab: {
    borderBottomColor: '#5E378C',
  },
  tabText: {
    fontSize: 14,
    color: '#666',
  },
  activeTabText: {
    color: '#5E378C',
    fontWeight: 'bold',
  },
  networkItem: {
    marginBottom: 8,
    padding: 12,
    backgroundColor: '#f8f8f8',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#2196f3',
  },
  networkHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  networkMethod: {
    fontSize: 12,
    fontWeight: 'bold',
    marginRight: 8,
    fontFamily: 'monospace',
  },
  networkUrl: {
    flex: 1,
    fontSize: 12,
    color: '#333',
    fontFamily: 'monospace',
  },
  networkStatus: {
    fontSize: 12,
    fontWeight: 'bold',
    marginLeft: 8,
    fontFamily: 'monospace',
  },
  statusSuccess: {
    color: '#4CAF50',
  },
  statusError: {
    color: '#f44336',
  },
  networkDuration: {
    fontSize: 11,
    color: '#666',
    marginLeft: 8,
    fontFamily: 'monospace',
  },
  networkDetails: {
    fontSize: 11,
    color: '#5E378C',
    marginTop: 4,
  },
});

export default WebViewScreen;
