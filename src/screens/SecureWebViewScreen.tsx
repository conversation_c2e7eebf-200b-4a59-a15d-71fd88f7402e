import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  SafeAreaView,
  BackHandler,
  Alert,
} from 'react-native';
import { WebView, WebViewNavigation } from 'react-native-webview';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { RootStackParamList } from '../navigation/types';
import { getToken, refreshToken, getNumericUserId } from '../services/api';
import { useAuthStore } from '../store/authStore';
import {
  getWebViewSecurityConfig,
  getSecureInjectedScript,
  isSafeUrl,
  isValidMessageData,
  sanitizeUrl,
} from '../utils/webViewSecurity';

type SecureWebViewScreenNavigationProp = StackNavigationProp<RootStackParamList, 'WebView'>;
type SecureWebViewScreenRouteProp = RouteProp<RootStackParamList, 'WebView'>;

interface SecureWebViewScreenProps {
  navigation: SecureWebViewScreenNavigationProp;
  route: SecureWebViewScreenRouteProp;
}

const SecureWebViewScreen: React.FC<SecureWebViewScreenProps> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const [numericUserId, setNumericUserId] = useState<string | null>(null);
  const { url: initialUrl, title } = route.params;

  // Validate and sanitize initial URL
  const [url, setUrl] = useState<string>(() => {
    if (!isSafeUrl(initialUrl)) {
      Alert.alert(
        'Security Warning',
        'The requested URL is not allowed for security reasons.',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
      return '';
    }
    return sanitizeUrl(initialUrl);
  });

  const [isLoading, setIsLoading] = useState(true);
  const [token, setToken] = useState<string | null>(null);
  const [isReady, setIsReady] = useState(false);
  const webViewRef = useRef<WebView>(null);
  const [error, setError] = useState<string | null>(null);
  const [navigationAttempts, setNavigationAttempts] = useState(0);

  // Function to add loginId to URL securely
  const addLoginIdToUrl = (baseUrl: string, userId?: string) => {
    if (!userId || !baseUrl) {return baseUrl;}

    try {
      const url = new URL(baseUrl);
      url.searchParams.set('loginId', userId);
      return url.toString();
    } catch (error) {
      console.error('Error adding loginId to URL:', error);
      return baseUrl;
    }
  };

  // Get JWT token when component mounts
  useEffect(() => {
    let isMounted = true;

    const initialize = async () => {
      try {
        const [authToken, numericId] = await Promise.all([
          getToken(),
          getNumericUserId(),
        ]);

        if (!isMounted) {return;}

        setToken(authToken);
        setNumericUserId(numericId);

        // Update URL with numeric ID
        if (numericId && isSafeUrl(initialUrl)) {
          const urlWithLoginId = addLoginIdToUrl(initialUrl, numericId);
          setUrl(sanitizeUrl(urlWithLoginId));
        }

        // Refresh token if needed
        if (authToken) {
          try {
            await refreshToken();
            const refreshedToken = await getToken();
            if (refreshedToken && refreshedToken !== authToken) {
              setToken(refreshedToken);
            }
          } catch (refreshError) {
            console.warn('Token refresh failed:', refreshError);
          }
        }

        setIsReady(true);
      } catch (error) {
        console.error('Error initializing WebView:', error);
        if (isMounted) {
          setError('Failed to initialize secure connection');
          setIsReady(true);
        }
      }
    };

    initialize();

    // Handle back button
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (webViewRef.current) {
        webViewRef.current.goBack();
        return true;
      }
      return false;
    });

    return () => {
      isMounted = false;
      backHandler.remove();
    };
  }, [initialUrl]);

  // Function to reload the WebView
  const handleReload = () => {
    setError(null);
    setNavigationAttempts(0);
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  // Handle navigation requests
  const handleNavigationStateChange = (navState: WebViewNavigation) => {
    if (!navState.url) {return;}

    // Check if navigation is to an allowed origin
    if (!isSafeUrl(navState.url)) {
      // Prevent navigation
      if (webViewRef.current) {
        webViewRef.current.stopLoading();
      }

      // Track repeated attempts
      setNavigationAttempts(prev => prev + 1);

      if (navigationAttempts >= 3) {
        Alert.alert(
          'Security Alert',
          'Multiple attempts to navigate to unauthorized URLs detected. For your security, this session will be terminated.',
          [{ text: 'OK', onPress: () => navigation.goBack() }]
        );
      } else {
        Alert.alert(
          'Navigation Blocked',
          `Navigation to ${navState.url} was blocked for security reasons.`,
          [{ text: 'OK' }]
        );
      }

      return false;
    }

    return true;
  };

  // Handle messages from WebView
  const handleMessage = (event: any) => {
    const { data } = event.nativeEvent;

    // Validate message data
    if (!isValidMessageData(data)) {
      console.warn('Invalid message data received from WebView:', data);
      return;
    }

    // Handle different message types
    if (data === 'PAGE_LOADED') {
      setIsLoading(false);
    } else if (data === 'NAVIGATION_STARTED') {
      setIsLoading(true);
    } else if (data && data.startsWith('ERROR:')) {
      const errorMessage = data.substring(6);
      setError(errorMessage);
    } else if (data && data.startsWith('AUTH_ERROR:')) {
      // Handle authentication errors
      refreshToken().catch((err: Error) => {
        console.error('Failed to refresh token after auth error', err);
        Alert.alert(
          'Authentication Error',
          'Your session has expired. Please log in again.',
          [{ text: 'OK', onPress: () => navigation.navigate('Login') }]
        );
      });
    }

    // Handle JSON messages
    try {
      const parsed = JSON.parse(data);
      if (parsed.type === 'SECURITY_INITIALIZED') {
        console.log('WebView security initialized successfully');
      }
    } catch {
      // Not JSON, already handled above
    }
  };

  // Get secure WebView configuration
  const webViewConfig = getWebViewSecurityConfig();

  // Get secure injected script
  const injectedScript = getSecureInjectedScript(token, numericUserId || user?.id || null);

  if (!isReady) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
          <View style={styles.reloadButton} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#5E378C" />
          <Text style={styles.loadingText}>Establishing secure connection...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !url) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>{title}</Text>
          <TouchableOpacity
            style={styles.reloadButton}
            onPress={handleReload}
          >
            <Text style={styles.reloadButtonText}>↻</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.errorContainer}>
          <Text style={styles.errorIcon}>⚠️</Text>
          <Text style={styles.errorText}>{error || 'Invalid URL'}</Text>
          <TouchableOpacity
            style={styles.tryAgainButton}
            onPress={handleReload}
          >
            <Text style={styles.tryAgainText}>Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{title}</Text>
        <TouchableOpacity
          style={styles.reloadButton}
          onPress={handleReload}
        >
          <Text style={styles.reloadButtonText}>↻</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.webViewContainer}>
        <WebView
          ref={webViewRef}
          source={{
            uri: url,
            headers: {
              'X-Mobile-App': 'true',
              'X-From-Mobile-WebView': 'true',
              'X-Request-Source': 'DigiHRApp',
            },
          }}
          style={styles.webView}
          {...webViewConfig}
          injectedJavaScript={injectedScript}
          onMessage={handleMessage}
          onNavigationStateChange={handleNavigationStateChange}
          onShouldStartLoadWithRequest={(request) => {
            // Additional check for navigation requests
            return isSafeUrl(request.url);
          }}
          onLoadStart={() => setIsLoading(true)}
          onLoadEnd={() => setTimeout(() => setIsLoading(false), 500)}
          onError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            setError(`Failed to load: ${nativeEvent.description}`);
          }}
          onHttpError={(syntheticEvent) => {
            const { nativeEvent } = syntheticEvent;
            if (nativeEvent.statusCode === 401) {
              // Handle 401 Unauthorized
              refreshToken().catch(() => {
                navigation.navigate('Login');
              });
            } else {
              setError(`HTTP Error ${nativeEvent.statusCode}: ${nativeEvent.description}`);
            }
          }}
          renderError={(errorDomain, errorCode, errorDesc) => (
            <View style={styles.errorContainer}>
              <Text style={styles.errorIcon}>⚠️</Text>
              <Text style={styles.errorText}>
                {errorDesc || 'An error occurred while loading the page'}
              </Text>
              <TouchableOpacity style={styles.tryAgainButton} onPress={handleReload}>
                <Text style={styles.tryAgainText}>Try Again</Text>
              </TouchableOpacity>
            </View>
          )}
        />
      </View>

      {isLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#5E378C" />
          <Text style={styles.loadingText}>Loading securely...</Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    height: 56,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 8,
  },
  backButtonText: {
    fontSize: 24,
    color: '#5E378C',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    flex: 1,
    textAlign: 'center',
  },
  reloadButton: {
    padding: 8,
  },
  reloadButtonText: {
    fontSize: 24,
    color: '#5E378C',
  },
  webViewContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  tryAgainButton: {
    backgroundColor: '#5E378C',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  tryAgainText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#5E378C',
  },
});

export default SecureWebViewScreen;
