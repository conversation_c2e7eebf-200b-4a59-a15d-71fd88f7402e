import React, { useEffect, useState, use<PERSON><PERSON>back, JSX } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  RefreshControl,
  Image,
  StyleSheet,
  StatusBar,
  ActivityIndicator,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';
import { useAuthStore } from '../store/authStore';
import WorkflowSummarySheet from '../components/WorkflowSummarySheet';
import AnnouncementCarousel, { Announcement } from '../components/AnnouncementCarousel';
import { CarIcon, ToolsIcon, CalendarIcon, MoreIcon } from '../components/WorkflowIcons';
import TaskList from '../components/TaskList';
import { TaskCardProps } from '../components/TaskCard';
import FavoriteWorkflowSelectionSheet, { Workflow } from '../components/FavoriteWorkflowSelectionSheet';
import Images from '../assets/images';
import { colors } from '../styles/theme';
import Icon from '../components/Icon';
import Avatar from '../components/Avatar';
import { DashboardResponse, InboxItem, HistoryItem } from '../types/dashboard';
import { authenticatedRequest } from '../services/api';
import CampaignSlider from '../components/CampaignSlider';
import { Campaign, exampleCampaigns } from '../types/campaign';
import FoodMenu from '../components/FoodMenu';
import FavoriteWorkflows from '../components/FavoriteWorkflows';

import {
  responsiveWidth,
  responsiveHeight,
  responsiveFontSize,
  moderateScale,
  responsiveIconSize,
  moderateVerticalScale,
  widthPercentage,
  heightPercentage,
  getTabBarBottomPadding,
} from '../utils/responsive';

// Navigation prop type
type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MainTabs'>;

interface HomeScreenProps {
  navigation: HomeScreenNavigationProp;
}

// Quick action items - matching the image exactly
const quickActionItems = [
  { id: '1', title: 'İzin Talebi', icon: '🏖️' },
  { id: '2', title: 'Bakımlar', icon: '🔧' },
  { id: '3', title: 'Bordro', icon: '📋' },
  { id: '4', title: 'Sipariş Servisi', icon: '🛒' },
  { id: '5', title: 'Avans', icon: '💰' },
  { id: '6', title: 'Diğer', icon: '➕' },
  { id: '7', title: 'Test Secure', icon: '🔒', action: 'testSecureWebView' },
];

// Info cards
const infoCards = [
  {
    id: '1',
    title: 'İzin Bilgisi',
    value: '15',
    icon: '→',
  },
  {
    id: '2',
    title: 'Zimmet Yönetimi',
    value: '',
    icon: '→',
  },
  {
    id: '3',
    title: 'İş Akışları Özeti',
    value: '',
    icon: '→',
    onPress: 'toggleWorkflowSheet',
  },
];

// Carousel announcements
const carouselAnnouncements: Announcement[] = [
  {
    id: '1',
    department: 'İç Hizmetler',
    image: Images.hero,
    text: '25. yıl kutlamalarımızda uzun yıllar bizimle olan çalışanlarımız plaketlerini aldı.',
  },
  {
    id: '2',
    department: 'İnsan Kaynakları',
    image: Images.hero,
    text: 'Yıllık şirket toplantımız başarıyla tamamlandı.',
  },
  {
    id: '3',
    department: 'Yönetim',
    image: Images.hero,
    text: 'İstanbul ofisimiz hizmet vermeye başladı.',
  },
];

// Team members - birthday section
const teamMembers = [
  { id: '1', name: 'Ayşe', position: 'İnsan', avatar: 'https://i.pravatar.cc/100?u=1' },
  { id: '2', name: 'Hamza', position: 'Finans', avatar: 'https://i.pravatar.cc/100?u=2' },
  { id: '3', name: 'Büşra', position: 'Pazarlama', avatar: 'https://i.pravatar.cc/100?u=3' },
  { id: '4', name: 'Ahmet', position: 'Yazılım', avatar: 'https://i.pravatar.cc/100?u=4' },
];


// Favorite workflow items with component references
const favoriteWorkflows = [
  { id: '1', title: 'Araç Talep', iconComponent: CarIcon },
  { id: '2', title: 'Ekipman Talep', iconComponent: ToolsIcon },
  { id: '3', title: 'Yıllık İzin Talep', iconComponent: CalendarIcon },
  { id: '4', title: 'Daha Fazla', iconComponent: MoreIcon },
];

// Weekly birthdays data
const birthdaysThisWeek = [
  { id: '1', name: 'Nazlı Günaydın', date: '15.01.2025', avatar: 'https://i.pravatar.cc/100?u=c' },
  { id: '2', name: 'Merve Macun', date: '17.01.2025', avatar: 'https://i.pravatar.cc/100?u=mary' },
  { id: '3', name: 'Kerem Bayraktar', date: '20.01.2025', avatar: 'https://i.pravatar.cc/100?u=ş' },
  { id: '4', name: 'Yasemin Elmas', date: '22.01.2025', avatar: 'https://i.pravatar.cc/100?u=f' },
];

// Campaigns data
const campaignSlides = [
  { id: '1', title: 'İstanbul Çalıştayı', date: '15-16-17-18 Aralık', image: Images.hero },
  { id: '2', title: 'Yılbaşı Kutlaması', date: '31 Aralık 2025', image: Images.hero },
];


// Service information
const serviceInfoSlides = [
  {
    id: '1',
    title: 'Digiturk\'te Sağlık ve Şirket Doktorumuz',
    text: 'Günlük çalışma saatlerimiz içinde meydana gelen rahatsızlıklar için, şirket doktoruna başvurabilirsiniz. Şirket doktorumuz Hikmet Mirzaoğlu 11:00 - 13:00 saatleri arasında Beşiktaş lokasyonunda, 13:30 - 14:30 saatleri arasında...',
    icon: 'i',
  },
  {
    id: '2',
    title: 'Servis Saatleri',
    text: 'Servis saatleri ve güzergahları hakkında bilgi',
    icon: 'i',
  },
];

// Employees on leave
const employeesOnLeave = [
  { id: '1', name: 'Ali Yılmaz', department: 'IT', dates: '15-22 Ocak' },
  { id: '2', name: 'Ayşe Kaya', department: 'İK', dates: '18-25 Ocak' },
  { id: '3', name: 'Mehmet Öz', department: 'Finans', dates: '20-27 Ocak' },
];

// New employees
const newEmployees = [
  { id: '1', name: 'Abdullah Musab Koyuncu', department: 'beIN Sports', avatar: 'https://i.pravatar.cc/100?u=abdullah' },
  { id: '2', name: 'Başak Delikan', department: 'beIN Sports', avatar: 'https://i.pravatar.cc/100?u=c' },
  { id: '3', name: 'Bengisu Özkeş', department: 'Eğlence İçeriği', avatar: 'https://i.pravatar.cc/100?u=l' },
];

// Convert inbox items to task card format
const convertInboxToTasks = (inboxItems: InboxItem[]): TaskCardProps[] => {
  return inboxItems.map(item => {
    const date = new Date(item.createdDate);
    const formattedDate = date.toLocaleDateString('tr-TR');
    const formattedTime = date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });

    return {
      id: item.id.toString(),
      date: formattedDate,
      time: formattedTime,
      title: item.title,
      status: {
        label: item.status,
        type: 'progress', // Tasks in inbox are always in progress
      },
      description: item.description,
      creator: {
        name: item.lastLoginNameSurname || item.senderName,
        // User image endpoints not available in backend, using placeholder
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(item.senderName)}&background=5E378C&color=fff&size=100`,
        placeholderName: item.lastLoginNameSurname || item.senderName,
      },
    };
  });
};

// Convert history items to task card format
const convertHistoryToTasks = (historyItems: HistoryItem[]): TaskCardProps[] => {
  return historyItems.slice(0, 5).map(item => {
    const date = new Date(item.timestamp);
    const formattedDate = date.toLocaleDateString('tr-TR');
    const formattedTime = date.toLocaleTimeString('tr-TR', { hour: '2-digit', minute: '2-digit' });

    return {
      id: item.id.toString(),
      date: formattedDate,
      time: formattedTime,
      title: item.title,
      status: {
        label: item.action,
        type: 'approval', // History items are completed/approved
      },
      description: item.description,
      creator: {
        name: item.lastLoginNameSurname || item.relatedTo,
        // User image endpoints not available in backend, using placeholder
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(item.relatedTo)}&background=5E378C&color=fff&size=100`,
        placeholderName: item.lastLoginNameSurname || item.relatedTo,
      },
    };
  });
};

// Modify favoriteWorkflows to match our Workflow interface
interface WorkflowIconProps {
  width: number;
  height: number;
  color: string;
}

const initialFavoriteWorkflows: Workflow[] = [
  { id: '11', title: 'Araç Talep', icon: 'car', category: 'INSAN KAYNAKLARI' },
  { id: '13', title: 'Eğitim Talep', icon: 'calendar', category: 'INSAN KAYNAKLARI' },
];

const HomeScreen: React.FC<HomeScreenProps> = ({ navigation }) => {
  const { user, token, logout } = useAuthStore();
  const [refreshing, setRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<string>('connected');
  const [isWorkflowSheetVisible, setIsWorkflowSheetVisible] = useState(false);
  const [isWorkflowSelectionVisible, setIsWorkflowSelectionVisible] = useState(false);
  const [favoriteWorkflows, setFavoriteWorkflows] = useState<Workflow[]>(initialFavoriteWorkflows);
  const [dashboardData, setDashboardData] = useState<DashboardResponse | null>(null);


  const fetchDashboard = useCallback(async () => {
    try {
      const response = await authenticatedRequest('/mobile/dashboard', 'GET');
      setDashboardData(response);
    } catch (error) {
      console.error('Failed to fetch dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  }, [token]);

  useEffect(() => {
    fetchDashboard();
  }, [fetchDashboard]);

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await fetchDashboard();
    } finally {
      setRefreshing(false);
    }
  }, [fetchDashboard]);

  const handleLogout = () => {
    logout();
  };

  const toggleWorkflowSheet = () => {
    setIsWorkflowSheetVisible(!isWorkflowSheetVisible);
  };

  const toggleWorkflowSelectionSheet = () => {
    setIsWorkflowSelectionVisible(!isWorkflowSelectionVisible);
  };

  const handleAddWorkflow = (workflow: Workflow) => {
    // Only allow up to 3 favorite workflows
    if (favoriteWorkflows.length < 3) {
      setFavoriteWorkflows(prev => [...prev, workflow]);
    }
  };

  const handleRemoveWorkflow = (workflowId: string) => {
    const workflow = favoriteWorkflows.find(wf => wf.id === workflowId);
    if (workflow) {
      setFavoriteWorkflows(prev => prev.filter(wf => wf.id !== workflowId));
    }
  };

  // Add function to navigate to WebViewTest
  const navigateToWebViewTest = () => {
    navigation.navigate('WebViewTest', {
      url: 'https://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation',
    });
  };

  // Handle announcement press
  const handleAnnouncementPress = (announcement: Announcement) => {
    // In a real app, you would navigate to the announcement details screen
    console.log('Announcement pressed');
  };

  // Handle info card press
  const handleInfoCardPress = (cardId: string) => {
    const card = infoCards.find(c => c.id === cardId);
    if (card && card.onPress === 'toggleWorkflowSheet') {
      toggleWorkflowSheet();
    }
  };

  // Helper function to get clean user name (filter out error messages)
  const getCleanUserName = (): string => {
    const userName = dashboardData?.userInfo?.name;
    if (!userName || userName.toLowerCase().includes('error') || userName.toLowerCase().includes('loading')) {
      return '';
    }
    return userName;
  };

  // Helper function to get display user name (no fallback)
  const getDisplayUserName = (): string => {
    return getCleanUserName();
  };

  // Helper function to get work anniversary message
  const getWorkAnniversaryMessage = (): string => {
    const userInfo = dashboardData?.userInfo;

    // Debug logging for development
    if (__DEV__ && userInfo) {
      console.log('Work Anniversary Debug:', {
        hasWorkAnniversary: userInfo.hasWorkAnniversary,
        daysWorking: userInfo.daysWorking,
        startDate: userInfo.startDate,
        workAnniversaryMessage: userInfo.workAnniversaryMessage,
      });
    }

    // Use dynamic message from API if available
    if (userInfo?.hasWorkAnniversary && userInfo?.workAnniversaryMessage) {
      return userInfo.workAnniversaryMessage;
    }

    // Fallback to generic message if work anniversary data not available
    if (__DEV__) {
      console.log('Using fallback work anniversary message - API data not available');
    }
    return 'Digitürk ailesindeyiz!';
  };

  // Development helper - show work anniversary details on long press
  const handleWorkAnniversaryLongPress = () => {
    if (__DEV__ && dashboardData?.userInfo) {
      const userInfo = dashboardData.userInfo;
      alert(`Work Anniversary Debug:

Start Date: ${userInfo.startDate || 'Not available'}
Days Working: ${userInfo.daysWorking || 'Not calculated'}
Has Anniversary: ${userInfo.hasWorkAnniversary ? 'Yes' : 'No'}
Message: ${userInfo.workAnniversaryMessage || 'Not available'}`);
    }
  };


  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent={true} />

      {/* Main ScrollView for entire screen */}
      <ScrollView
        style={styles.mainScrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Carousel Section */}

        <View style={styles.carouselSection}>
          {/* User Profile Header */}
          <View style={styles.profileHeader}>
            <View style={styles.profileInfo}>
              <Avatar
                size="md"
                variant="rounded"
                imageUrl="" // User image endpoint not available in backend
                name={getCleanUserName()}
                loading={isLoading}
                style={styles.profileImage}
              />
              <View style={styles.profileText}>
                <Text style={styles.profileName}>{getDisplayUserName()}</Text>
                <Text
                  style={styles.profileDays}
                  onLongPress={handleWorkAnniversaryLongPress}
                >
                  {getWorkAnniversaryMessage()}
                </Text>
              </View>
            </View>

            <View style={styles.headerIcons}>
              {/* Add test button in dev mode */}
              {__DEV__ && (
                <TouchableOpacity
                  style={styles.iconButton}
                  onPress={navigateToWebViewTest}
                >
                  <Text style={styles.iconText}>🔬</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity style={styles.iconButton}>
                <Icon name="essentials/Search Dark" size={responsiveIconSize(24)} color="white" />
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconButton}>
                <View>
                  <Icon name="essentials/Bell Dark" size={responsiveIconSize(24)} color="white" />
                  {/* Notification indicator - show when there are notifications */}
                  {dashboardData && dashboardData.inboxItems && dashboardData.inboxItems.length > 0 && (
                    <View style={styles.notificationBadge}>
                      <View style={styles.notificationDot} />
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Announcement Carousel */}
          <AnnouncementCarousel
            announcements={dashboardData?.slides?.length ?
              dashboardData.slides.map(slide => ({
                id: slide.id,
                department: 'Duyuru',
                image: slide.imageUrl || Images.hero,
                text: slide.description || slide.title,
              })) : carouselAnnouncements
            }
            onAnnouncementPress={handleAnnouncementPress}
          />
        </View>

        {/* Content Container with Rounded Corners */}
        <View style={styles.contentContainer}>
          {/* Info Cards Row */}
          <View style={styles.infoCardsContainer}>
            {infoCards.map(card => (
              <TouchableOpacity
                key={card.id}
                style={styles.infoCard}
                onPress={() => handleInfoCardPress(card.id)}
              >
                <View style={styles.infoCardContent}>
                  <Text style={styles.infoCardTitle}>{card.title}</Text>

                  <View style={styles.infoCardBottom}>
                    {card.value && (
                      <Text style={styles.infoCardValue}>{card.value}</Text>
                    )}
                    <Icon name="chevrons/Right" size={responsiveIconSize(20)} color="#8A94A6" />
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>

          {/* Test Buttons for WebView Screens */}
          <View style={{ flexDirection: 'row', marginTop: 10, marginHorizontal: 20, gap: 10 }}>
            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: '#5E378C',
                padding: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}
              onPress={() => {
                navigation.navigate('WebView', {
                  url: 'https://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation',
                  title: 'Test Secure WebView',
                });
              }}
            >
              <Text style={{ color: 'white', fontWeight: 'bold' }}>🔒 Test Secure WebView</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={{
                flex: 1,
                backgroundColor: '#28a745',
                padding: 12,
                borderRadius: 8,
                alignItems: 'center',
              }}
              onPress={() => {
                navigation.navigate('WebViewTest', {
                  url: 'https://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation',
                });
              }}
            >
              <Text style={{ color: 'white', fontWeight: 'bold' }}>🧪 Test WebView Debug</Text>
            </TouchableOpacity>
          </View>

          {/* Favorite Workflows Section */}
          <FavoriteWorkflows
            favoriteWorkflows={favoriteWorkflows}
            onAddWorkflow={toggleWorkflowSelectionSheet}
            onMoreWorkflows={toggleWorkflowSelectionSheet}
          />

          {/* Task List Component - REPLACING THE PROCESS TABS */}
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
            </View>
          ) : (
            <TaskList
              activeTasks={dashboardData ? convertInboxToTasks(dashboardData.inboxItems) : []}
              historyTasks={dashboardData ? convertHistoryToTasks(dashboardData.historyItems) : []}
            />
          )}

          {/* Bu Hafta Doğanlar Section */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Bu Hafta Doğanlar</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScrollView}>
              {birthdaysThisWeek.map((person) => (
                <View key={person.id} style={styles.birthdayCard}>
                  <Image source={{ uri: person.avatar }} style={styles.birthdayAvatar} />
                  {person.id === '2' && (
                    <View style={styles.birthdayEmoji}>
                      <Text style={styles.birthdayEmojiText}>😘</Text>
                    </View>
                  )}
                  <Text style={styles.birthdayName}>{person.name.split(' ')[0]}</Text>
                  <Text style={styles.birthdayLastName}>{person.name.split(' ')[1]}</Text>
                  <Text style={styles.birthdayDate}>{person.date}</Text>
                </View>
              ))}
            </ScrollView>
          </View>
          {/* Campaigns Section */}
          <View style={styles.campaignsSection}>
            <View style={styles.campaignHeader}>
              <Text style={styles.campaignTitle}>Kampanyalar</Text>
              <TouchableOpacity>
                <Text style={styles.campaignAction}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <CampaignSlider
              campaigns={exampleCampaigns}
              onCampaignPress={(campaign) => {
                console.log('Campaign pressed:', campaign.title);
                // Navigate to campaign details or handle campaign press
              }}
            />
          </View>


          {/* Food Menu Section */}
          <View style={styles.foodMenuSection}>
            <View style={styles.foodMenuHeader}>
              <Text style={styles.foodMenuTitle}>Yemek Menüsü</Text>
              <TouchableOpacity style={styles.foodMenuActionButton}>
                <Text style={styles.foodMenuActionText}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <FoodMenu />
          </View>

          {/* Service Information */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Servis Bilgileri</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.serviceInfoCard}>
              <View style={styles.serviceInfoHeader}>
                <View style={styles.serviceInfoIconContainer}>
                  <Icon name="essentials/Info Light" size={responsiveIconSize(20)} color="#5C2D91" />
                </View>
                <Text style={styles.serviceInfoTitle}>{serviceInfoSlides[0].title}</Text>
              </View>
              <Text style={styles.serviceInfoText} numberOfLines={3}>{serviceInfoSlides[0].text}</Text>
              <TouchableOpacity style={styles.serviceInfoButton}>
                <Text style={styles.serviceInfoButtonText}>Devamını oku</Text>
                <Icon name="chevrons/Right" size={responsiveIconSize(16)} color="#5C2D91" />
              </TouchableOpacity>
            </View>
            <View style={styles.serviceInfoIndicators}>
              {serviceInfoSlides.map((_, index) => (
                <View
                  key={index}
                  style={[
                    styles.serviceInfoIndicator,
                    index === 0 && styles.activeServiceInfoIndicator,
                  ]}
                />
              ))}
            </View>
          </View>

          {/* Employees on Leave */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>İzindeki Çalışanlar</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.employeeLeaveContainer}>
              {employeesOnLeave.map((employee) => (
                <View key={employee.id} style={styles.employeeLeaveItem}>
                  <View style={styles.employeeLeaveInfo}>
                    <Text style={styles.employeeLeaveName}>{employee.name}</Text>
                    <Text style={styles.employeeLeaveDepartment}>{employee.department}</Text>
                  </View>
                  <Text style={styles.employeeLeaveDates}>{employee.dates}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* New Employees */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Digitürk'e Yeni Katılanlar</Text>
              <TouchableOpacity>
                <Text style={styles.sectionAction}>Tümü</Text>
              </TouchableOpacity>
            </View>

            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.horizontalScrollView}>
              {newEmployees.map((employee) => (
                <View key={employee.id} style={styles.newEmployeeCard}>
                  <Image source={{ uri: employee.avatar }} style={styles.newEmployeeAvatar} />
                  <Text style={styles.newEmployeeName}>{employee.name.split(' ')[0]}</Text>
                  <Text style={styles.newEmployeeLastName}>{employee.name.split(' ')[1]}</Text>
                  <Text style={styles.newEmployeeDepartment}>{employee.department}</Text>
                </View>
              ))}
            </ScrollView>
          </View>
        </View>
      </ScrollView>

      {/* Workflow Summary Sheet */}
      <WorkflowSummarySheet
        isVisible={isWorkflowSheetVisible}
        onClose={() => setIsWorkflowSheetVisible(false)}
      />

      {/* Workflow Selection Sheet */}
      <FavoriteWorkflowSelectionSheet
        isVisible={isWorkflowSelectionVisible}
        onClose={toggleWorkflowSelectionSheet}
        onAddWorkflow={handleAddWorkflow}
        favoriteWorkflows={favoriteWorkflows}
        onRemoveWorkflow={handleRemoveWorkflow}
      />


    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black', // Changed from colors.primary to black
  },
  mainScrollView: {
    flex: 1,
  },
  carouselSection: {
    position: 'relative',
    zIndex: 1,
  },
  // Profile Header Styles
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: responsiveWidth(16),
    paddingTop: moderateVerticalScale(50),
    paddingBottom: moderateVerticalScale(16),
    backgroundColor: 'transparent',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: moderateScale(48),
    height: moderateScale(48),
    borderRadius: moderateScale(6),
  },
  profileText: {
    marginLeft: responsiveWidth(12),
  },
  profileName: {
    color: 'white',
    fontSize: responsiveFontSize(20),
    fontWeight: '600',
  },
  profileDays: {
    color: '#FFFFFF',
    fontSize: responsiveFontSize(14),
    opacity: 0.8,
  },
  headerIcons: {
    flexDirection: 'row',
  },
  iconButton: {
    marginLeft: responsiveWidth(20),
  },
  iconText: {
    color: 'white',
    fontSize: responsiveFontSize(20),
  },
  notificationBadge: {
    position: 'absolute',
    top: -4,
    right: -4,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#DC2626',
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: 'white',
  },
  contentContainer: {
    backgroundColor: '#f8f9fa',
    borderTopLeftRadius: moderateScale(24),
    borderTopRightRadius: moderateScale(24),
    marginTop: moderateVerticalScale(16),
    paddingTop: moderateVerticalScale(16),
    paddingBottom: getTabBarBottomPadding(),
    zIndex: 2,
  },
  // Info Cards
  infoCardsContainer: {
    flexDirection: 'row',
    marginHorizontal: responsiveWidth(12),
    marginBottom: moderateVerticalScale(20),
    paddingTop: moderateVerticalScale(12),
  },
  infoCard: {
    flex: 1,
    height: responsiveHeight(120),
    backgroundColor: 'white',
    margin: moderateScale(6),
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
    shadowColor: '#000',
    shadowOffset: { width: 0, height: moderateScale(2) },
    shadowOpacity: 0.05,
    shadowRadius: moderateScale(5),
    elevation: 2,
  },
  infoCardContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  infoCardTitle: {
    color: '#8A94A6',
    fontSize: responsiveFontSize(16),
  },
  infoCardBottom: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  infoCardValue: {
    color: '#333',
    fontSize: responsiveFontSize(36),
    fontWeight: '500',
  },
  infoCardIcon: {
    color: '#8A94A6',
    fontSize: responsiveFontSize(20),
  },
  // Section Styles
  section: {
    backgroundColor: 'white',
    marginHorizontal: responsiveWidth(16),
    marginBottom: moderateVerticalScale(20),
    borderRadius: moderateScale(16),
    padding: moderateScale(16),
  },
  sectionTitle: {
    fontSize: responsiveFontSize(18),
    fontWeight: '600',
    color: '#333',
    marginBottom: moderateVerticalScale(20),
  },
  // Section header with title and "Tümü" action
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateVerticalScale(16),
  },
  sectionAction: {
    color: colors.primary,
    fontSize: responsiveFontSize(14),
    fontWeight: '500',
  },
  // Horizontal ScrollView
  horizontalScrollView: {
    flexDirection: 'row',
    marginHorizontal: responsiveWidth(-8),
  },
  // Birthday cards
  birthdayCard: {
    alignItems: 'center',
    marginHorizontal: responsiveWidth(8),
    width: responsiveWidth(90),
  },
  birthdayAvatar: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    marginBottom: moderateVerticalScale(8),
  },
  birthdayEmoji: {
    position: 'absolute',
    bottom: moderateVerticalScale(60),
    right: 0,
    backgroundColor: 'white',
    borderRadius: moderateScale(12),
    width: moderateScale(24),
    height: moderateScale(24),
    justifyContent: 'center',
    alignItems: 'center',
  },
  birthdayEmojiText: {
    fontSize: responsiveFontSize(16),
  },
  birthdayName: {
    fontSize: responsiveFontSize(16),
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  birthdayLastName: {
    fontSize: responsiveFontSize(16),
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginBottom: moderateVerticalScale(4),
  },
  birthdayDate: {
    fontSize: responsiveFontSize(14),
    color: '#8A94A6',
    textAlign: 'center',
  },
  // Campaigns Section
  campaignsSection: {
    marginBottom: moderateVerticalScale(20),
  },
  campaignHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: moderateVerticalScale(16),
    paddingHorizontal: moderateScale(20),
  },
  campaignTitle: {
    fontSize: responsiveFontSize(22),
    fontWeight: '700',
    color: '#1a1a1a',
  },
  campaignAction: {
    fontSize: responsiveFontSize(14),
    fontWeight: '400',
    color: '#8A94A6',
  },
  // Food Menu Section
  foodMenuSection: {
    backgroundColor: 'white',
    marginHorizontal: responsiveWidth(16),
    marginBottom: moderateVerticalScale(20),
    borderRadius: moderateScale(16),
    overflow: 'hidden',
  },
  foodMenuHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    paddingHorizontal: moderateScale(20),
    paddingVertical: moderateVerticalScale(16),
  },
  foodMenuTitle: {
    fontSize: responsiveFontSize(20),
    fontWeight: '700',
    color: '#111827',
  },
  foodMenuActionButton: {
    paddingVertical: moderateVerticalScale(6),
    paddingHorizontal: moderateScale(12),
  },
  foodMenuActionText: {
    fontSize: responsiveFontSize(14),
    fontWeight: '500',
    color: '#6B7280',
  },
  // Service info
  serviceInfoContainer: {
    flexDirection: 'row',
    padding: moderateScale(16),
    backgroundColor: '#f8f9fa',
    borderRadius: moderateScale(12),
  },
  serviceInfoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: moderateScale(12),
    padding: moderateScale(16),
  },
  serviceInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: moderateVerticalScale(8),
  },
  serviceInfoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: moderateVerticalScale(8),
  },
  serviceInfoButtonText: {
    fontSize: responsiveFontSize(14),
    color: '#5C2D91',
    fontWeight: '500',
    marginRight: responsiveWidth(4),
  },
  serviceInfoIconContainer: {
    marginRight: responsiveWidth(12),
  },
  serviceInfoIcon: {
    fontSize: responsiveFontSize(24),
  },
  serviceInfoContent: {
    flex: 1,
  },
  serviceInfoTitle: {
    fontSize: responsiveFontSize(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: moderateVerticalScale(8),
  },
  serviceInfoText: {
    fontSize: responsiveFontSize(14),
    color: '#8A94A6',
    lineHeight: responsiveFontSize(20),
    marginBottom: moderateVerticalScale(8),
  },
  serviceInfoLink: {
    fontSize: responsiveFontSize(14),
    color: colors.primary,
    fontWeight: '500',
  },
  serviceInfoIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingTop: moderateVerticalScale(16),
  },
  serviceInfoIndicator: {
    width: moderateScale(8),
    height: moderateScale(8),
    borderRadius: moderateScale(4),
    backgroundColor: '#E0E0E0',
    marginHorizontal: responsiveWidth(4),
  },
  activeServiceInfoIndicator: {
    backgroundColor: colors.primary,
  },
  // Employees on leave
  employeeLeaveContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: moderateScale(12),
    overflow: 'hidden',
  },
  employeeLeaveItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: moderateScale(12),
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  employeeLeaveInfo: {
    flex: 1,
  },
  employeeLeaveName: {
    fontSize: responsiveFontSize(16),
    fontWeight: '500',
    color: '#333',
    marginBottom: moderateVerticalScale(4),
  },
  employeeLeaveDepartment: {
    fontSize: responsiveFontSize(14),
    color: '#8A94A6',
  },
  employeeLeaveDates: {
    fontSize: responsiveFontSize(14),
    color: '#333',
    fontWeight: '500',
  },
  // New employees
  newEmployeeCard: {
    alignItems: 'center',
    marginHorizontal: responsiveWidth(8),
    width: responsiveWidth(100),
  },
  newEmployeeAvatar: {
    width: moderateScale(80),
    height: moderateScale(80),
    borderRadius: moderateScale(40),
    marginBottom: moderateVerticalScale(8),
  },
  newEmployeeName: {
    fontSize: responsiveFontSize(16),
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  newEmployeeLastName: {
    fontSize: responsiveFontSize(16),
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
    marginBottom: moderateVerticalScale(4),
  },
  newEmployeeDepartment: {
    fontSize: responsiveFontSize(14),
    color: '#8A94A6',
    textAlign: 'center',
  },
  loadingContainer: {
    backgroundColor: 'white',
    marginHorizontal: responsiveWidth(16),
    marginBottom: moderateVerticalScale(20),
    borderRadius: moderateScale(16),
    padding: moderateScale(40),
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default HomeScreen;
