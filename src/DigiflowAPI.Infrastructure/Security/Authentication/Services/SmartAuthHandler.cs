﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Linq;
using System.Security.Claims;
using System.Security.Principal;
using System.Text.Encodings.Web;

namespace DigiflowAPI.Infrastructure.Security.Authentication
{
    /// <summary>
    /// SmartAuthHandler that coordinates between JWT and Windows authentication
    /// </summary>
    public class SmartAuthHandler : AuthenticationHandler<AuthenticationSchemeOptions>
    {
        private readonly ILogger<SmartAuthHandler> _logger;

        public SmartAuthHandler(
            IOptionsMonitor<AuthenticationSchemeOptions> options,
            ILoggerFactory logger,
            UrlEncoder encoder) : base(options, logger, encoder)
        {
            _logger = logger.CreateLogger<SmartAuthHandler>();
        }

        protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
        {
            var requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            _logger.LogInformation("[{RequestId}] SmartAuth: Starting authentication flow", requestId);

            // Get the authentication service
            var authService = Context.RequestServices.GetRequiredService<IAuthenticationService>();

            // Log request details for debugging
            var path = Context.Request.Path.Value?.ToLowerInvariant() ?? "";
            var hasAuthHeader = Context.Request.Headers.ContainsKey("Authorization");
            var authHeader = hasAuthHeader ? Context.Request.Headers["Authorization"].ToString() : "None";
            var isMobileApp = Context.Request.Headers.ContainsKey("X-Mobile-App");
            var isFromMobileWebView = Context.Request.Headers.ContainsKey("X-From-Mobile-WebView");
            
            // Enhanced logging for mobile requests
            if (isMobileApp || isFromMobileWebView)
            {
                _logger.LogInformation("[{RequestId}] MOBILE AUTH: Path={Path}, HasAuth={HasAuth}, IsMobileApp={IsMobileApp}, IsWebView={IsWebView}, Headers: X-Mobile-App={MobileApp}, X-Is-Mobile={IsMobile}, X-From-Mobile-WebView={WebView}",
                    requestId, path, hasAuthHeader, isMobileApp, isFromMobileWebView,
                    Context.Request.Headers["X-Mobile-App"].ToString(),
                    Context.Request.Headers["X-Is-Mobile"].ToString(),
                    Context.Request.Headers["X-From-Mobile-WebView"].ToString());
                
                if (hasAuthHeader)
                {
                    // Log token info without exposing the full token
                    var tokenPrefix = authHeader.Length > 20 ? authHeader.Substring(0, 20) + "..." : authHeader;
                    _logger.LogInformation("[{RequestId}] MOBILE AUTH: Token present, prefix={TokenPrefix}", requestId, tokenPrefix);
                }
            }
            else
            {
                _logger.LogDebug("[{RequestId}] Request: Path={Path}, HasAuth={HasAuth}",
                    requestId, path, hasAuthHeader);
            }

            // Check if this is a React request early
            var isReactRequest = Context.Request.Headers.ContainsKey("X-DigiflowReact");

            // 0. First check if user is already authenticated (e.g., by Windows auth middleware)
            if (Context.User?.Identity?.IsAuthenticated == true && 
                !string.IsNullOrEmpty(Context.User.Identity.Name))
            {
                var authType = Context.User.Identity.AuthenticationType;
                _logger.LogInformation("[{RequestId}] User already authenticated: {User}, AuthType: {AuthType}, IsReact: {IsReact}",
                    requestId, Context.User.Identity.Name, authType, isReactRequest);
                
                // Determine if this is Windows authentication
                bool isWindowsAuth = Context.User.Identity is WindowsIdentity ||
                                   authType == "Negotiate" ||
                                   authType == "NTLM" ||
                                   authType == "Kerberos" ||
                                   authType == "Windows";
                
                if (isWindowsAuth)
                {
                    Context.Items["AuthType"] = "Windows";
                    if (isReactRequest)
                    {
                        Context.Items["IsReactRequest"] = true;
                    }
                    _logger.LogInformation("[{RequestId}] Using existing Windows authentication", requestId);
                    return AuthenticateResult.Success(new AuthenticationTicket(Context.User, "Smart"));
                }
            }

            // 1. First check if JWT token is present and valid (like GlobalHelper checks JwtUsername)
            if (hasAuthHeader && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogInformation("[{RequestId}] JWT Bearer token detected, delegating to JWT handler", requestId);
                try
                {
                    var jwtResult = await authService.AuthenticateAsync(Context, JwtBearerDefaults.AuthenticationScheme);
                    if (jwtResult.Succeeded)
                    {
                        _logger.LogInformation("[{RequestId}] JWT authentication succeeded for user: {User}",
                            requestId, jwtResult.Principal?.Identity?.Name);
                        
                        // Enhanced logging for mobile JWT success
                        if (isMobileApp || isFromMobileWebView)
                        {
                            var claims = jwtResult.Principal?.Claims?.Select(c => $"{c.Type}={c.Value}") ?? new List<string>();
                            _logger.LogInformation("[{RequestId}] MOBILE JWT SUCCESS: User={User}, UserId={UserId}, Claims={Claims}",
                                requestId, 
                                jwtResult.Principal?.Identity?.Name,
                                jwtResult.Principal?.FindFirst("userId")?.Value ?? jwtResult.Principal?.FindFirst("sub")?.Value ?? "N/A",
                                string.Join(", ", claims.Take(5))); // Log first 5 claims only
                        }

                        // Store JWT username in HttpContext.Items for GlobalHelper compatibility
                        Context.Items["JwtUsername"] = jwtResult.Principal?.Identity?.Name;
                        Context.Items["AuthType"] = "JWT";

                        return jwtResult;
                    }
                    else
                    {
                        // Enhanced logging for mobile JWT failure
                        if (isMobileApp || isFromMobileWebView)
                        {
                            _logger.LogWarning("[{RequestId}] MOBILE JWT FAILED: {FailureType} - {Failure}, Token prefix: {TokenPrefix}",
                                requestId, 
                                jwtResult.Failure?.GetType().Name ?? "Unknown",
                                jwtResult.Failure?.Message,
                                authHeader.Length > 20 ? authHeader.Substring(0, 20) + "..." : authHeader);
                        }
                        else
                        {
                            _logger.LogWarning("[{RequestId}] JWT authentication failed: {Failure}",
                                requestId, jwtResult.Failure?.Message);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[{RequestId}] Error during JWT authentication", requestId);
                }
            }

            // 2. Check if JWT auth was already processed by middleware
            if (Context.Items.TryGetValue("JwtUsername", out var jwtUsername) && jwtUsername != null)
            {
                _logger.LogInformation("[{RequestId}] Using pre-authenticated JWT user: {User}",
                    requestId, jwtUsername);
                if (Context.User?.Identity?.IsAuthenticated == true)
                {
                    return AuthenticateResult.Success(new AuthenticationTicket(Context.User, "Smart"));
                }
            }

            // 3. Check if this is a Windows auth endpoint that requires explicit Windows authentication
            // This is needed for endpoints with [Authorize(AuthenticationSchemes = NegotiateDefaults.AuthenticationScheme)]
            var endpoint = Context.GetEndpoint();
            var authorizeData = endpoint?.Metadata?.GetOrderedMetadata<Microsoft.AspNetCore.Authorization.IAuthorizeData>();
            var requiresWindowsAuth = authorizeData?.Any(a =>
                a.AuthenticationSchemes?.Contains(NegotiateDefaults.AuthenticationScheme) == true) ?? false;

            if (requiresWindowsAuth)
            {
                _logger.LogInformation("[{RequestId}] Endpoint requires Windows authentication, skipping Smart auth", requestId);
                // Don't handle authentication here - let the framework handle it with the specified scheme
                return AuthenticateResult.NoResult();
            }

            // 4. Try Windows authentication for all requests (following GlobalHelper pattern)
            // This is especially important for React requests with X-DigiflowReact header
            _logger.LogInformation("[{RequestId}] Trying Windows authentication (React request: {IsReact})", requestId, isReactRequest);
            
            // Check if we're running in an environment that supports Windows authentication
            var isWindowsAuthAvailable = Context.Features.Get<Microsoft.AspNetCore.Http.Features.IHttpConnectionFeature>() != null;
            
            if (isWindowsAuthAvailable)
            {
                try
                {
                    var windowsResult = await authService.AuthenticateAsync(Context, NegotiateDefaults.AuthenticationScheme);
                    if (windowsResult.Succeeded && windowsResult.Principal?.Identity?.IsAuthenticated == true)
                    {
                        _logger.LogInformation("[{RequestId}] Windows authentication succeeded for user: {User} (React: {IsReact})",
                            requestId, windowsResult.Principal?.Identity?.Name, isReactRequest);

                        // Store Windows auth info for compatibility
                        Context.Items["AuthType"] = "Windows";

                        // Mark as React request for AuthenticationMiddleware JWT token generation
                        if (isReactRequest)
                        {
                            Context.Items["IsReactRequest"] = true;
                            _logger.LogInformation("[{RequestId}] Marked as React request for JWT token generation", requestId);
                        }

                        return windowsResult;
                    }
                    else if (windowsResult.Failure != null)
                    {
                        _logger.LogWarning("[{RequestId}] Windows authentication failed: {Failure} (React: {IsReact})",
                            requestId, windowsResult.Failure?.Message, isReactRequest);
                    }
                    else
                    {
                        _logger.LogWarning("[{RequestId}] Windows authentication returned no result or unauthenticated principal", requestId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "[{RequestId}] Windows authentication error (React: {IsReact})", requestId, isReactRequest);
                }
            }
            else
            {
                _logger.LogWarning("[{RequestId}] Windows authentication not available in current hosting environment", requestId);
            }

            // 5. Check if Windows identity is already in the context (from IIS or Kestrel)
            // This could be either WindowsIdentity or ClaimsIdentity with Windows/Negotiate authentication
            if (Context.User?.Identity?.IsAuthenticated == true && 
                !string.IsNullOrEmpty(Context.User.Identity.Name))
            {
                var authType = Context.User.Identity.AuthenticationType;
                
                // Check if this is Windows authentication (various possible auth types)
                bool isWindowsAuth = Context.User.Identity is WindowsIdentity ||
                                   authType == "Negotiate" ||
                                   authType == "NTLM" ||
                                   authType == "Kerberos" ||
                                   authType == "Windows";
                
                if (isWindowsAuth)
                {
                    _logger.LogInformation("[{RequestId}] Found Windows identity in context: {User}, AuthType: {AuthType} (React: {IsReact})",
                        requestId, Context.User.Identity.Name, authType, isReactRequest);

                    // Store Windows auth info for compatibility
                    Context.Items["AuthType"] = "Windows";

                    // Mark as React request for AuthenticationMiddleware JWT token generation
                    if (isReactRequest)
                    {
                        Context.Items["IsReactRequest"] = true;
                        _logger.LogInformation("[{RequestId}] Marked existing Windows identity as React request for JWT token generation", requestId);
                    }

                    return AuthenticateResult.Success(new AuthenticationTicket(Context.User, "Smart"));
                }
                else
                {
                    _logger.LogDebug("[{RequestId}] Found non-Windows authenticated user: {User}, AuthType: {AuthType}",
                        requestId, Context.User.Identity.Name, authType);
                }
            }

            // 6. No authentication succeeded - authentication required
            if (isMobileApp || isFromMobileWebView)
            {
                _logger.LogWarning("[{RequestId}] MOBILE AUTH FAILED: No authentication method succeeded. Headers: X-Mobile-App={MobileApp}, X-Is-Mobile={IsMobile}, X-From-Mobile-WebView={WebView}, HasAuth={HasAuth}, Path={Path}",
                    requestId,
                    Context.Request.Headers["X-Mobile-App"].ToString(),
                    Context.Request.Headers["X-Is-Mobile"].ToString(), 
                    Context.Request.Headers["X-From-Mobile-WebView"].ToString(),
                    hasAuthHeader,
                    path);
            }
            else
            {
                _logger.LogInformation("[{RequestId}] No authentication method succeeded - authentication required", requestId);
            }
            
            // Return NoResult to let the framework handle the challenge
            return AuthenticateResult.NoResult();
        }

        protected override async Task HandleChallengeAsync(AuthenticationProperties properties)
        {
            var requestId = Guid.NewGuid().ToString("N").Substring(0, 8);
            _logger.LogInformation("[{RequestId}] SmartAuth: Handling authentication challenge", requestId);

            var hasBearer = Context.Request.Headers["Authorization"].ToString()
                .StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase);

            _logger.LogDebug("[{RequestId}] Challenge: HasBearer={HasBearer}",
                requestId, hasBearer);

            var authService = Context.RequestServices.GetRequiredService<IAuthenticationService>();

            // If JWT was attempted, challenge with Bearer
            if (hasBearer)
            {
                _logger.LogInformation("[{RequestId}] Challenging with JWT Bearer", requestId);
                await authService.ChallengeAsync(Context, JwtBearerDefaults.AuthenticationScheme, properties);
                return;
            }

            // Default to Windows authentication challenge
            _logger.LogInformation("[{RequestId}] Challenging with Windows authentication", requestId);
            await authService.ChallengeAsync(Context, NegotiateDefaults.AuthenticationScheme, properties);
        }

    }
}