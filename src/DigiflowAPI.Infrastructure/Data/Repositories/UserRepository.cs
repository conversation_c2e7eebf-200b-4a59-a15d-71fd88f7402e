﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Dapper;
using DigiflowAPI.Application.Interfaces.DataAccess;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Domain.Entities;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Infrastructure.Repositories.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace DigiflowAPI.Infrastructure.Data.Repositories
{
    /// <summary>
    /// High-performance Dapper implementation of IUserRepository for authentication and user queries
    /// </summary>
    public partial class UserRepository : DapperRepositoryBase, IUserRepository
    {
        private readonly IGlobalHelpers _globalHelpers;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IOracleConnectionManager _frameworkConnectionManager;
        private readonly IOracleConnectionManager _workflowConnectionManager;

        public UserRepository(
            IOracleConnectionManager connectionManager,
            IGlobalHelpers globalHelpers,
            IHttpContextAccessor httpContextAccessor,
            IOrganizationRepository organizationRepository,
            ILogger<UserRepository> logger)
            : base(connectionManager, logger, "DT_WORKFLOW")
        {
            _globalHelpers = globalHelpers;
            _httpContextAccessor = httpContextAccessor;
            _organizationRepository = organizationRepository;

            // Create separate connection managers for different databases
            _frameworkConnectionManager = connectionManager;
            _workflowConnectionManager = connectionManager;
        }

        // High-performance authentication query
        public async Task<long> GetUserInfo(Dictionary<string, object> parameters)
        {
            const string sql = @"
                SELECT LOGIN_ID 
                FROM FRAMEWORK.F_LOGIN 
                WHERE DOMAIN_USER_NAME = :DomainUserName";

            return await _frameworkConnectionManager.UseConnectionAsync(async connection =>
            {
                var result = await connection.ExecuteScalarAsync<long?>(sql, parameters);
                return result ?? 0;
            }, "FrameworkConnection");
        }

        // High-performance overload for current user
        public async Task<long> GetUserInfo()
        {
            var username = _globalHelpers.GetUserName();
            var parts = username.Split('\\');
            var name = parts.Length > 1 ? parts[1] : username;
            var domainUserName = name.ToUpper().Replace("İ", "I").Replace("Ö", "O").Replace("Ü", "U");

            const string sql = @"
                SELECT LOGIN_ID 
                FROM FRAMEWORK.F_LOGIN 
                WHERE DOMAIN_USER_NAME = :DomainUserName";

            return await _frameworkConnectionManager.UseConnectionAsync(async connection =>
            {
                var result = await connection.ExecuteScalarAsync<long?>(sql, new { DomainUserName = domainUserName });
                return result ?? 0;
            }, "FrameworkConnection");
        }

        // High-performance scalar query for department
        public async Task<long> GetUserDepartmentId(long userId)
        {
            const string sql = @"
                SELECT DEPT_ID 
                FROM DT_WORKFLOW.VW_USER_INFORMATION 
                WHERE LOGIN_ID = :UserId 
                  AND IS_DELETED = 'N' 
                ORDER BY NAME_SURNAME";

            var result = await ExecuteScalarAsync<long?>(sql, new { UserId = userId });
            return result ?? 0;
        }

        // Get user by username - optimized for authentication with fallback for missing DP_HR_USERS table
        public async Task<VwUserInformation?> GetByUsernameAsync(string username)
        {
            // Try query with StartDate first
            const string sqlWithStartDate = @"
                SELECT u.LOGIN_ID as LoginId,
                       u.NAME_SURNAME as NameSurname,
                       u.DEPT_ID as DeptId,
                       u.DEPT_NAME as DeptName,
                       u.IS_DELETED as IsDeleted,
                       u.E_MAIL as EMail,
                       u.USERNAME as Username,
                       u.SICIL as Sicil,
                       u.BUTCE_MANAGER as ButceManager,
                       u.CARI_KOD as CariKod,
                       u.PERSONEL_TIPI as PersonelTipi,
                       h.GIRIS_TARIHI as StartDate
                FROM DT_WORKFLOW.VW_USER_INFORMATION u
                LEFT JOIN FRAMEWORK.DP_HR_USERS h ON u.SICIL = h.SICIL
                WHERE UPPER(u.USERNAME) = UPPER(:Username)
                  AND u.IS_DELETED = 'N'";

            try
            {
                return await QuerySingleOrDefaultAsync<VwUserInformation>(sqlWithStartDate, new { Username = username });
            }
            catch (Oracle.ManagedDataAccess.Client.OracleException ex) when (ex.Number == 942)
            {
                // ORA-00942: table or view does not exist - fallback to query without StartDate
                _logger.LogWarning("FRAMEWORK.DP_HR_USERS table not available, using fallback query without StartDate for username {Username}", username);
                
                const string sqlFallback = @"
                    SELECT LOGIN_ID as LoginId,
                           NAME_SURNAME as NameSurname,
                           DEPT_ID as DeptId,
                           DEPT_NAME as DeptName,
                           IS_DELETED as IsDeleted,
                           E_MAIL as EMail,
                           USERNAME as Username,
                           SICIL as Sicil,
                           BUTCE_MANAGER as ButceManager,
                           CARI_KOD as CariKod,
                           PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE UPPER(USERNAME) = UPPER(:Username)
                      AND IS_DELETED = 'N'";

                return await QuerySingleOrDefaultAsync<VwUserInformation>(sqlFallback, new { Username = username });
            }
        }

        // Get user by ID - optimized query with fallback for missing DP_HR_USERS table
        public async Task<VwUserInformation?> GetByIdAsync(long? id)
        {
            long userId = id != null ? id.Value : await _globalHelpers.GetUserId();

            // Try query with StartDate first
            const string sqlWithStartDate = @"
                SELECT u.LOGIN_ID as LoginId,
                       u.NAME_SURNAME as NameSurname,
                       u.DEPT_ID as DeptId,
                       u.DEPT_NAME as DeptName,
                       u.IS_DELETED as IsDeleted,
                       u.E_MAIL as EMail,
                       u.USERNAME as Username,
                       u.SICIL as Sicil,
                       u.BUTCE_MANAGER as ButceManager,
                       u.CARI_KOD as CariKod,
                       u.PERSONEL_TIPI as PersonelTipi,
                       h.GIRIS_TARIHI as StartDate
                FROM DT_WORKFLOW.VW_USER_INFORMATION u
                LEFT JOIN FRAMEWORK.DP_HR_USERS h ON u.SICIL = h.SICIL
                WHERE u.LOGIN_ID = :Id
                  AND u.IS_DELETED = 'N'";

            try
            {
                return await QuerySingleOrDefaultAsync<VwUserInformation>(sqlWithStartDate, new { Id = userId });
            }
            catch (Oracle.ManagedDataAccess.Client.OracleException ex) when (ex.Number == 942)
            {
                // ORA-00942: table or view does not exist - fallback to query without StartDate
                _logger.LogWarning("FRAMEWORK.DP_HR_USERS table not available, using fallback query without StartDate for user {UserId}", userId);
                
                const string sqlFallback = @"
                    SELECT LOGIN_ID as LoginId,
                           NAME_SURNAME as NameSurname,
                           DEPT_ID as DeptId,
                           DEPT_NAME as DeptName,
                           IS_DELETED as IsDeleted,
                           E_MAIL as EMail,
                           USERNAME as Username,
                           SICIL as Sicil,
                           BUTCE_MANAGER as ButceManager,
                           CARI_KOD as CariKod,
                           PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE LOGIN_ID = :Id
                      AND IS_DELETED = 'N'";

                return await QuerySingleOrDefaultAsync<VwUserInformation>(sqlFallback, new { Id = userId });
            }
        }

        // Get all active users - consider pagination for large datasets
        public async Task<IEnumerable<VwUserInformation?>> GetAllAsync()
        {
            // Try query with StartDate first
            const string sqlWithStartDate = @"
                SELECT u.LOGIN_ID as LoginId,
                       u.NAME_SURNAME as NameSurname,
                       u.DEPT_ID as DeptId,
                       u.DEPT_NAME as DeptName,
                       u.IS_DELETED as IsDeleted,
                       u.E_MAIL as EMail,
                       u.USERNAME as Username,
                       u.SICIL as Sicil,
                       u.BUTCE_MANAGER as ButceManager,
                       u.CARI_KOD as CariKod,
                       u.PERSONEL_TIPI as PersonelTipi,
                       h.GIRIS_TARIHI as StartDate
                FROM DT_WORKFLOW.VW_USER_INFORMATION u
                LEFT JOIN FRAMEWORK.DP_HR_USERS h ON u.SICIL = h.SICIL
                WHERE u.IS_DELETED = 'N'
                ORDER BY u.NAME_SURNAME";

            try
            {
                var results = await QueryAsync<VwUserInformation>(sqlWithStartDate);
                return results.Cast<VwUserInformation?>();
            }
            catch (Oracle.ManagedDataAccess.Client.OracleException ex) when (ex.Number == 942)
            {
                // ORA-00942: table or view does not exist - fallback to query without StartDate
                _logger.LogWarning("FRAMEWORK.DP_HR_USERS table not available, using fallback query without StartDate for GetAllAsync");
                
                const string sqlFallback = @"
                    SELECT LOGIN_ID as LoginId,
                           NAME_SURNAME as NameSurname,
                           DEPT_ID as DeptId,
                           DEPT_NAME as DeptName,
                           IS_DELETED as IsDeleted,
                           E_MAIL as EMail,
                           USERNAME as Username,
                           SICIL as Sicil,
                           BUTCE_MANAGER as ButceManager,
                           CARI_KOD as CariKod,
                           PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE IS_DELETED = 'N'
                    ORDER BY NAME_SURNAME";

                var results = await QueryAsync<VwUserInformation>(sqlFallback);
                return results.Cast<VwUserInformation?>();
            }
        }

        // Helper method to check if user is manager
        private async Task<bool> IsManagerAsync(string username)
        {
            const string sql = @"
                SELECT IS_MANAGER 
                FROM DT_WORKFLOW.DP_HR_USERS 
                WHERE USERNAME = :Username";

            var result = await ExecuteScalarAsync<string>(sql, new { Username = username });
            return result == "Y";
        }

        // Get active user ID with admin bypass logic
        public async Task<long> GetActiveUserIdAsync()
        {
            var loginIdHeader = _httpContextAccessor.HttpContext?.Request.Headers["X-Login-Id"].ToString();
            if (loginIdHeader == "0")
            {
                loginIdHeader = (await _globalHelpers.GetUserId()).ToString();
            }

            var windowsUserId = await _globalHelpers.GetUserId();
            var isAdmin = _globalHelpers.IsSystemAdmin();

            if (isAdmin && !string.IsNullOrEmpty(loginIdHeader) && loginIdHeader != windowsUserId.ToString())
            {
                return Convert.ToInt64(loginIdHeader);
            }

            return windowsUserId;
        }

        public string GetActiveUserName()
        {
            return _globalHelpers.GetUserName();
        }

        // These methods still use the original repository pattern due to complexity
        public async Task<DpHrDeps?> GetDepartmentById(decimal departmentId)
        {
            const string sql = @"
                SELECT ID as Id,
                       BOLUM as Bolum,
                       MANAGER as Manager,
                       UST_BOLUM_ID as UstBolumId,
                       TITLE as Title,
                       SHORT_NAME as ShortName,
                       DEPS_EN as DepsEn,
                       MOSSID as Mossid
                FROM DT_WORKFLOW.DP_HR_DEPS 
                WHERE ID = :DepartmentId";

            return await QuerySingleOrDefaultAsync<DpHrDeps>(sql, new { DepartmentId = departmentId });
        }

        public async Task<IEnumerable<DpHrDeps>> GetDepartmentsAtLevel(long ustBolumId)
        {
            const string sql = @"
                SELECT ID as Id,
                       BOLUM as Bolum,
                       MANAGER as Manager,
                       UST_BOLUM_ID as UstBolumId,
                       TITLE as Title,
                       SHORT_NAME as ShortName,
                       DEPS_EN as DepsEn,
                       MOSSID as Mossid
                FROM DT_WORKFLOW.DP_HR_DEPS 
                WHERE UST_BOLUM_ID = :UstBolumId";

            return await QueryAsync<DpHrDeps>(sql, new { UstBolumId = ustBolumId });
        }

        public async Task<IEnumerable<VwUserInformation>> GetUsersByDepartmentId(long departmentId, bool excludeActiveUser)
        {
            var excludeUserId = 0L;
            if (excludeActiveUser)
            {
                excludeUserId = await GetActiveUserIdAsync();
            }

            // Try query with StartDate first
            const string sqlWithStartDate = @"
                SELECT u.LOGIN_ID as LoginId,
                       u.NAME_SURNAME as NameSurname,
                       u.DEPT_ID as DeptId,
                       u.DEPT_NAME as DeptName,
                       u.IS_DELETED as IsDeleted,
                       u.E_MAIL as EMail,
                       u.USERNAME as Username,
                       u.SICIL as Sicil,
                       u.BUTCE_MANAGER as ButceManager,
                       u.CARI_KOD as CariKod,
                       u.PERSONEL_TIPI as PersonelTipi,
                       h.GIRIS_TARIHI as StartDate
                FROM DT_WORKFLOW.VW_USER_INFORMATION u
                LEFT JOIN FRAMEWORK.DP_HR_USERS h ON u.SICIL = h.SICIL
                WHERE u.DEPT_ID = :DepartmentId 
                  AND u.LOGIN_ID != :ExcludeUser 
                  AND u.IS_DELETED = 'N' 
                ORDER BY u.NAME_SURNAME";

            var parameters = new
            {
                DepartmentId = departmentId,
                ExcludeUser = excludeUserId
            };

            try
            {
                return await QueryAsync<VwUserInformation>(sqlWithStartDate, parameters);
            }
            catch (Oracle.ManagedDataAccess.Client.OracleException ex) when (ex.Number == 942)
            {
                // ORA-00942: table or view does not exist - fallback to query without StartDate
                _logger.LogWarning("FRAMEWORK.DP_HR_USERS table not available, using fallback query without StartDate for GetUsersByDepartmentId {DepartmentId}", departmentId);
                
                const string sqlFallback = @"
                    SELECT LOGIN_ID as LoginId,
                           NAME_SURNAME as NameSurname,
                           DEPT_ID as DeptId,
                           DEPT_NAME as DeptName,
                           IS_DELETED as IsDeleted,
                           E_MAIL as EMail,
                           USERNAME as Username,
                           SICIL as Sicil,
                           BUTCE_MANAGER as ButceManager,
                           CARI_KOD as CariKod,
                           PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE DEPT_ID = :DepartmentId 
                      AND LOGIN_ID != :ExcludeUser 
                      AND IS_DELETED = 'N' 
                    ORDER BY NAME_SURNAME";

                return await QueryAsync<VwUserInformation>(sqlFallback, parameters);
            }
        }

        public async Task<IEnumerable<VwUserInformation>> GetForwardPersonel(string username)
        {
            var isManager = await IsManagerAsync(username);

            string sqlWithStartDate;
            string sqlFallback;
            object parameters;

            if (isManager)
            {
                sqlWithStartDate = @"
                    SELECT DISTINCT 
                           vi.LOGIN_ID as LoginId,
                           vi.NAME_SURNAME as NameSurname,
                           vi.DEPT_ID as DeptId,
                           vi.DEPT_NAME as DeptName,
                           vi.IS_DELETED as IsDeleted,
                           vi.E_MAIL as EMail,
                           vi.USERNAME as Username,
                           vi.SICIL as Sicil,
                           vi.BUTCE_MANAGER as ButceManager,
                           vi.CARI_KOD as CariKod,
                           vi.PERSONEL_TIPI as PersonelTipi,
                           h.GIRIS_TARIHI as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION vi
                    INNER JOIN DT_WORKFLOW.DP_HR_USERS u ON vi.LOGIN_ID = u.F_LOGIN_ID
                    LEFT JOIN FRAMEWORK.DP_HR_USERS h ON vi.SICIL = h.SICIL
                    WHERE u.IS_MANAGER = 'Y' 
                      AND vi.IS_DELETED = 'N'
                    ORDER BY vi.NAME_SURNAME";
                    
                sqlFallback = @"
                    SELECT DISTINCT 
                           vi.LOGIN_ID as LoginId,
                           vi.NAME_SURNAME as NameSurname,
                           vi.DEPT_ID as DeptId,
                           vi.DEPT_NAME as DeptName,
                           vi.IS_DELETED as IsDeleted,
                           vi.E_MAIL as EMail,
                           vi.USERNAME as Username,
                           vi.SICIL as Sicil,
                           vi.BUTCE_MANAGER as ButceManager,
                           vi.CARI_KOD as CariKod,
                           vi.PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION vi
                    INNER JOIN DT_WORKFLOW.DP_HR_USERS u ON vi.LOGIN_ID = u.F_LOGIN_ID
                    WHERE u.IS_MANAGER = 'Y' 
                      AND vi.IS_DELETED = 'N'
                    ORDER BY vi.NAME_SURNAME";
                parameters = null;
            }
            else
            {
                sqlWithStartDate = @"
                    SELECT vi.LOGIN_ID as LoginId,
                           vi.NAME_SURNAME as NameSurname,
                           vi.DEPT_ID as DeptId,
                           vi.DEPT_NAME as DeptName,
                           vi.IS_DELETED as IsDeleted,
                           vi.E_MAIL as EMail,
                           vi.USERNAME as Username,
                           vi.SICIL as Sicil,
                           vi.BUTCE_MANAGER as ButceManager,
                           vi.CARI_KOD as CariKod,
                           vi.PERSONEL_TIPI as PersonelTipi,
                           h.GIRIS_TARIHI as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION vi
                    LEFT JOIN FRAMEWORK.DP_HR_USERS h ON vi.SICIL = h.SICIL
                    WHERE vi.DEPT_ID = (
                        SELECT DEPT_ID 
                        FROM DT_WORKFLOW.VW_USER_INFORMATION 
                        WHERE UPPER(USERNAME) = UPPER(:Username)
                    )
                    AND vi.IS_DELETED = 'N'
                    ORDER BY vi.NAME_SURNAME";
                    
                sqlFallback = @"
                    SELECT LOGIN_ID as LoginId,
                           NAME_SURNAME as NameSurname,
                           DEPT_ID as DeptId,
                           DEPT_NAME as DeptName,
                           IS_DELETED as IsDeleted,
                           E_MAIL as EMail,
                           USERNAME as Username,
                           SICIL as Sicil,
                           BUTCE_MANAGER as ButceManager,
                           CARI_KOD as CariKod,
                           PERSONEL_TIPI as PersonelTipi,
                           NULL as StartDate
                    FROM DT_WORKFLOW.VW_USER_INFORMATION
                    WHERE DEPT_ID = (
                        SELECT DEPT_ID 
                        FROM DT_WORKFLOW.VW_USER_INFORMATION 
                        WHERE UPPER(USERNAME) = UPPER(:Username)
                    )
                    AND IS_DELETED = 'N'
                    ORDER BY NAME_SURNAME";
                parameters = new { Username = username };
            }

            try
            {
                return await QueryAsync<VwUserInformation>(sqlWithStartDate, parameters);
            }
            catch (Oracle.ManagedDataAccess.Client.OracleException ex) when (ex.Number == 942)
            {
                // ORA-00942: table or view does not exist - fallback to query without StartDate
                _logger.LogWarning("FRAMEWORK.DP_HR_USERS table not available, using fallback query without StartDate for GetForwardPersonel {Username}", username);
                
                return await QueryAsync<VwUserInformation>(sqlFallback, parameters);
            }
        }

        // Delegate to organization repository
        public async Task<Domain.Entities.Organization.OrganizationHierarchy> GetOrganizationHierarchy(long? wfInstanceId = null)
        {
            return await _organizationRepository.GetOrganizationHierarchy(wfInstanceId);
        }
    }
}