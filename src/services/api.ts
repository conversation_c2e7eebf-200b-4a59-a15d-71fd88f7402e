import AsyncStorage from '@react-native-async-storage/async-storage';
import DeviceInfo from 'react-native-device-info';
import { getMobileApiUrl, getDigiflowApiUrl } from './configService';
import { secureStorage } from './simpleSecureStorage';
import { secureFetch } from './apiInterceptor';

// API endpoints
const ENDPOINTS = {
  LOGIN: '/auth/login',
  REFRESH: '/auth/refresh',
  PROFILE: '/auth/profile',
};

// User Information
export interface UserInfo {
  id: string;
  name: string;
  email: string;
  roles?: string[];
}

// Response types
export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
  userInfo?: {
    id: string;
    name: string;
    email: string;
    roles?: string[];
  };
}

export interface ApiError {
  message: string;
  statusCode?: number;
}

/**
 * Unified token storage keys
 */
const TOKEN_KEYS = {
  ACCESS_TOKEN: 'unified_auth_token',
  REFRESH_TOKEN: 'unified_refresh_token',
  USER_INFO: 'unified_user_info',
  TOKEN_EXPIRY: 'unified_token_expiry',
};

/**
 * Store the JWT token with unified storage approach
 */
export const storeToken = async (token: string, expiresIn?: number): Promise<void> => {
  try {
    // Store in AsyncStorage as primary storage
    await AsyncStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, token);

    // Store expiry time if provided
    if (expiresIn) {
      const expiryTime = Date.now() + (expiresIn * 1000);
      await AsyncStorage.setItem(TOKEN_KEYS.TOKEN_EXPIRY, expiryTime.toString());
    }

    // Clean up old storage keys to prevent confusion
    await cleanupLegacyTokens();

    console.log('Token stored successfully with unified storage');
  } catch (error) {
    console.error('Error storing token:', error);
    throw new Error('Failed to store authentication token');
  }
};

/**
 * Get the JWT token from unified storage with expiry check
 */
export const getToken = async (): Promise<string | null> => {
  try {
    // Check token expiry first
    const expiryStr = await AsyncStorage.getItem(TOKEN_KEYS.TOKEN_EXPIRY);
    if (expiryStr) {
      const expiryTime = parseInt(expiryStr, 10);
      if (Date.now() >= expiryTime) {
        console.log('Token has expired, clearing...');
        await removeToken();
        return null;
      }
    }

    // Get token from unified storage
    const token = await AsyncStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);

    // If no token in unified storage, try legacy locations
    if (!token) {
      const legacyToken = await getLegacyToken();
      if (legacyToken) {
        console.log('Found legacy token, migrating to unified storage...');
        await storeToken(legacyToken);
        return legacyToken;
      }
    }

    return token;
  } catch (error) {
    console.error('Error getting token:', error);
    return null;
  }
};

/**
 * Get token from legacy storage locations
 */
const getLegacyToken = async (): Promise<string | null> => {
  try {
    // Try old AsyncStorage key
    let token = await AsyncStorage.getItem('auth_token');
    if (token) return token;

    // Try old secure storage key
    try {
      token = await secureStorage.getItem('auth_token_secure');
      if (token) return token;
    } catch {
      // Secure storage might not be available
    }

    // Try JWT token key
    token = await AsyncStorage.getItem('jwt_token');
    if (token) return token;

    return null;
  } catch {
    return null;
  }
};

/**
 * Clean up legacy token storage
 */
const cleanupLegacyTokens = async (): Promise<void> => {
  const legacyKeys = [
    'auth_token',
    'auth_token_secure',
    'jwt_token',
    'access_token',
    'id_token',
  ];

  for (const key of legacyKeys) {
    try {
      await AsyncStorage.removeItem(key);
    } catch {
      // Ignore errors
    }

    try {
      await secureStorage.removeItem(key);
    } catch {
      // Ignore errors
    }
  }
};

/**
 * Remove the JWT token from unified storage
 */
export const removeToken = async (): Promise<void> => {
  try {
    // Remove from unified storage
    await AsyncStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    await AsyncStorage.removeItem(TOKEN_KEYS.TOKEN_EXPIRY);

    // Also clean up any legacy tokens
    await cleanupLegacyTokens();

    console.log('Token removed from unified storage');
  } catch (error) {
    console.error('Error removing token:', error);
  }
};

/**
 * Store user info using unified storage
 */
export const storeUserInfo = async (userInfo: UserInfo): Promise<void> => {
  try {
    // Use unified storage
    await AsyncStorage.setItem(TOKEN_KEYS.USER_INFO, JSON.stringify(userInfo));
    console.log('User info stored in unified storage');
  } catch (error) {
    console.error('Error storing user info:', error);
    throw new Error('Failed to store user information');
  }
};

/**
 * Get user info from unified storage
 */
export const getUserInfo = async (): Promise<UserInfo | null> => {
  try {
    // Get from unified storage
    let userInfoStr = await AsyncStorage.getItem(TOKEN_KEYS.USER_INFO);

    // If not found, try legacy locations
    if (!userInfoStr) {
      // Try legacy secure storage
      try {
        userInfoStr = await secureStorage.getItem('user_info');
      } catch {
        // Ignore errors
      }

      // Try legacy AsyncStorage
      if (!userInfoStr) {
        userInfoStr = await AsyncStorage.getItem('user_info');
      }

      // If found in legacy location, migrate to unified storage
      if (userInfoStr) {
        console.log('Migrating user info to unified storage...');
        const userInfo = JSON.parse(userInfoStr) as UserInfo;
        await storeUserInfo(userInfo);
      }
    }

    if (!userInfoStr) return null;
    return JSON.parse(userInfoStr) as UserInfo;
  } catch (error) {
    console.error('Error getting user info:', error);
    return null;
  }
};

/**
 * Get numeric user ID from stored JWT token
 */
export const getNumericUserId = async (): Promise<string | null> => {
  try {
    const token = await getToken();
    if (!token) {return null;}

    // Parse JWT token to get numeric userId
    const tokenParts = token.split('.');
    if (tokenParts.length === 3) {
      const payload = JSON.parse(atob(tokenParts[1]));

      // Return userId claim if available
      if (payload.userId) {
        return payload.userId;
      }

      // Fallback to sub claim if it's numeric
      if (payload.sub && payload.sub !== payload.username) {
        return payload.sub;
      }
    }

    // If no numeric ID in token, try to get from stored user info
    const userInfo = await getUserInfo();
    return userInfo?.id || null;
  } catch (error) {
    console.error('Error getting numeric user ID:', error);
    return null;
  }
};

// For backward compatibility with code that expects digest auth
export const storeDigestCredentials = async (_credentials: any): Promise<void> => {
  console.warn('storeDigestCredentials is deprecated, using JWT authentication instead');
  return;
};

export const getDigestCredentials = async (): Promise<any | null> => {
  console.warn('getDigestCredentials is deprecated, using JWT authentication instead');
  return null;
};

export const removeDigestCredentials = async (): Promise<void> => {
  console.warn('removeDigestCredentials is deprecated, using JWT authentication instead');
  return;
};

/**
 * Login API call to mobile endpoint
 */
export const login = async (username: string, password: string, domain?: string): Promise<LoginResponse> => {
  // Get mobile API URL
  const mobileApiUrl = getMobileApiUrl();
  const loginUrl = `${mobileApiUrl}${ENDPOINTS.LOGIN}`;

  try {
    console.log(`Attempting login to ${loginUrl}`);

    // Get device information for security tracking
    const deviceId = await DeviceInfo.getUniqueId();
    const deviceName = await DeviceInfo.getDeviceName();

    // Prepare request body with domain and device information
    const requestBody = {
      Username: username,
      Password: password,
      Domain: domain || 'DIGITURK', // Default to DIGITURK if not specified
      DeviceId: deviceId,
      DeviceName: deviceName,
    };

    console.log(`Request body:`, JSON.stringify(requestBody));

    const response = await secureFetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Mobile-App': 'true',
        'X-Is-Mobile': 'true',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    // Log response details for debugging
    console.log('Login response status:', response.status);
    console.log('Login response headers:', JSON.stringify(Object.fromEntries([...response.headers.entries()])));

    // Get the raw text first to check if it's valid JSON
    const responseText = await response.text();
    console.log('Login response text preview:', responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''));

    // Check if the response is HTML instead of JSON
    if (responseText.trim().startsWith('<')) {
      console.error(`Received HTML from ${loginUrl}`);
      throw {
        message: 'Received HTML instead of JSON. The server might be returning an error page or redirect.',
        statusCode: response.status,
        htmlResponse: true,
      };
    }

    // Parse the JSON manually
    let data;
    try {
      data = JSON.parse(responseText);
    } catch (parseError) {
      console.error(`Failed to parse JSON from ${loginUrl}`);
      throw {
        message: `Failed to parse response as JSON: ${(parseError as Error).message}`,
        statusCode: response.status,
        rawResponse: responseText.substring(0, 500),
      };
    }

    if (!response.ok) {
      console.error(`Received error status ${response.status} from ${loginUrl}`);

      // Handle specific error messages from the server
      let errorMessage = 'Login failed';
      if (data.error) {
        errorMessage = data.error;
      } else if (data.message) {
        errorMessage = data.message;
      }

      // Get retry-after header for rate limiting
      const retryAfter = response.headers.get('retry-after');
      const rateLimitReset = response.headers.get('x-ratelimit-reset');

      throw {
        message: errorMessage,
        statusCode: response.status,
        retryAfter: retryAfter ? parseInt(retryAfter) : null,
        rateLimitReset: rateLimitReset ? parseInt(rateLimitReset) : null,
      };
    }

    console.log('Login successful, received data structure:', Object.keys(data));

    // Check if we received a JWT token response
    if (data.accessToken && data.refreshToken) {
      console.log('Received JWT token response');

      // Store the JWT token with expiry
      await storeToken(data.accessToken, data.expiresIn);

      // Store refresh token if available using unified storage
      if (data.refreshToken) {
        await AsyncStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, data.refreshToken);
      }

      // Store user info if available
      if (data.userInfo) {
        const userInfo: UserInfo = {
          id: data.userInfo.id || 'unknown',
          name: data.userInfo.name || 'Unknown User',
          email: data.userInfo.email || username,
          roles: data.userInfo.roles || [],
        };
        await storeUserInfo(userInfo);
      }

      return data as LoginResponse;
    }

    // If we got a response but it doesn't have the expected JWT structure
    console.error('Response missing JWT tokens:', data);
    throw {
      message: 'Server response missing JWT tokens',
      statusCode: 200,
      data,
    };

  } catch (error) {
    console.error(`Error with ${loginUrl}:`, error);
    throw error;
  }
};

/**
 * Logout - clear ALL authentication data
 */
export const logout = async (): Promise<void> => {
  try {
    console.log('Starting comprehensive logout...');

    // Get token for the request
    const token = await getToken();
    const refreshToken = await AsyncStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);

    if (token) {
      // Call logout endpoint to invalidate tokens on server
      await fetch(`${getMobileApiUrl()}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
          'X-Mobile-App': 'true',
          'X-Is-Mobile': 'true',
        },
        body: JSON.stringify({ refreshToken }),
      }).catch(error => {
        // Just log errors but continue with logout
        console.warn('Logout API error:', error);
      });
    }

    // Clear ALL authentication-related data
    const authKeys = [
      // Unified storage keys
      TOKEN_KEYS.ACCESS_TOKEN,
      TOKEN_KEYS.REFRESH_TOKEN,
      TOKEN_KEYS.USER_INFO,
      TOKEN_KEYS.TOKEN_EXPIRY,
      // Legacy keys
      'auth_token',
      'jwt_token',
      'refresh_token',
      'user_info',
      'digest_credentials',
      'login_credentials',
      'access_token',
      'id_token',
      'profile_data',
      'user_data',
      'session_data',
    ];

    console.log('Clearing authentication keys:', authKeys);

    // Remove all auth-related keys
    await Promise.all(authKeys.map(key => AsyncStorage.removeItem(key)));

    // Also try to get all keys and remove any that contain auth-related terms
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const authRelatedKeys = allKeys.filter(key =>
        key.toLowerCase().includes('auth') ||
        key.toLowerCase().includes('token') ||
        key.toLowerCase().includes('jwt') ||
        key.toLowerCase().includes('user') ||
        key.toLowerCase().includes('login') ||
        key.toLowerCase().includes('session')
      );

      if (authRelatedKeys.length > 0) {
        console.log('Found additional auth-related keys to clear:', authRelatedKeys);
        await Promise.all(authRelatedKeys.map(key => AsyncStorage.removeItem(key)));
      }
    } catch (error) {
      console.warn('Error clearing additional auth keys:', error);
    }

    console.log('Logout completed - all authentication data cleared');
  } catch (error) {
    console.error('Logout error:', error);
    // Still try to remove core tokens
    await removeToken();
    await AsyncStorage.removeItem('refresh_token');
    await AsyncStorage.removeItem('user_info');
  }
};

/**
 * Determine which API endpoint to use based on the request endpoint
 */
const getApiBaseUrl = (endpoint: string): string => {
  // Define endpoints that should use MobileApi (/mobile)
  const mobileApiEndpoints = [
    '/auth/login',
    '/auth/refresh',
    '/auth/logout',
    '/auth/profile'
    // Note: user-image endpoints removed as they don't exist in backend
  ];

  // Check if endpoint or any path in endpoint matches MobileApi patterns
  const shouldUseMobileApi = mobileApiEndpoints.some(mobileEndpoint =>
    endpoint.startsWith(mobileEndpoint) || endpoint.includes(mobileEndpoint)
  );

  if (shouldUseMobileApi) {
    console.log(`Using MobileApi for endpoint: ${endpoint}`);
    return getMobileApiUrl();
  } else {
    console.log(`Using WebApi for endpoint: ${endpoint}`);
    return getDigiflowApiUrl();
  }
};

/**
 * Make an authenticated API request with automatic token refresh and intelligent routing
 */
export const authenticatedRequest = async (
  endpoint: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  body?: any
): Promise<any> => {
  const makeRequest = async (token: string, retryCount: number = 0): Promise<any> => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'X-Mobile-App': 'true',
      'X-Is-Mobile': 'true',
      'X-From-Mobile-WebView': 'true',
    };

    const options: RequestInit = {
      method,
      headers,
      // REMOVED: credentials: 'include' - Don't mix JWT with Windows Auth
      // Mobile apps should use JWT exclusively
    };

    if (body && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(body);
    }

    // Use intelligent API URL routing
    const baseUrl = getApiBaseUrl(endpoint);
    const fullUrl = `${baseUrl}${endpoint}`;

    // Detailed request logging
    console.log('===== API REQUEST DETAILS =====');
    console.log('Timestamp:', new Date().toISOString());
    console.log('Method:', method);
    console.log('Endpoint:', endpoint);
    console.log('Base URL:', baseUrl);
    console.log('Full URL:', fullUrl);
    console.log('Headers:', JSON.stringify(headers, null, 2));
    if (body) {
      console.log('Body:', JSON.stringify(body, null, 2));
    }
    console.log('==============================');

    const startTime = Date.now();

    try {
      const response = await fetch(fullUrl, options);
      const elapsedTime = Date.now() - startTime;

      console.log('===== API RESPONSE DETAILS =====');
      console.log('Timestamp:', new Date().toISOString());
      console.log('Elapsed Time:', elapsedTime, 'ms');
      console.log('Status:', response.status);
      console.log('Status Text:', response.statusText);
      console.log('Response Headers:', JSON.stringify(Object.fromEntries([...response.headers.entries()]), null, 2));
      console.log('================================');

      // Handle 401 Unauthorized - Token might be expired
      if (response.status === 401) {
        // Only retry once to prevent infinite recursion
        if (retryCount < 1) {
          console.log(`Received 401, attempting token refresh (retry ${retryCount + 1})...`);

          // Try to refresh the token
          const refreshSuccess = await refreshToken();
          if (refreshSuccess) {
            // Retry the request with the new token
            const newToken = await getToken();
            if (newToken) {
              console.log('Retrying request with new token...');
              return makeRequest(newToken, retryCount + 1); // Retry with incremented count
            } else {
              console.error('Failed to get new token after refresh');
            }
          } else {
            console.error('Token refresh failed');
          }
        } else {
          console.error('Already retried once, not retrying again');
        }

        throw {
          message: 'Authentication failed. Please log in again.',
          statusCode: 401,
          retryAttempted: retryCount > 0,
        };
      }

      // For successful responses or other error types
      const contentType = response.headers.get('content-type');
      const data = contentType && contentType.includes('application/json')
        ? await response.json()
        : await response.text();

      if (!response.ok) {
        throw {
          message: typeof data === 'object' && data.message
            ? data.message
            : `Request to ${endpoint} failed`,
          statusCode: response.status,
          data,
        };
      }

      return data;
    } catch (error) {
      const elapsedTime = Date.now() - startTime;
      console.error('===== API REQUEST ERROR =====');
      console.error('Timestamp:', new Date().toISOString());
      console.error('Elapsed Time:', elapsedTime, 'ms');
      console.error('URL:', fullUrl);
      console.error('Error Type:', error.constructor.name);
      console.error('Error Message:', error.message);
      console.error('Error Stack:', error.stack);
      console.error('=============================');
      throw error;
    }
  };

  try {
    const token = await getToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    return await makeRequest(token);
  } catch (error) {
    console.error('API request error:', error);
    throw error;
  }
};

/**
 * Token refresh mutex to prevent concurrent refresh attempts
 */
let refreshMutex: Promise<boolean> | null = null;

/**
 * Refresh the authentication token with mutex protection
 */
export const refreshToken = async (): Promise<boolean> => {
  // If a refresh is already in progress, wait for it
  if (refreshMutex) {
    console.log('Token refresh already in progress, waiting...');
    try {
      return await refreshMutex;
    } catch {
      // If the existing refresh failed, we'll try again
      refreshMutex = null;
    }
  }

  // Create new refresh promise
  refreshMutex = performTokenRefresh();

  try {
    const result = await refreshMutex;
    return result;
  } finally {
    // Clear mutex after completion
    refreshMutex = null;
  }
};

/**
 * Internal token refresh implementation
 */
const performTokenRefresh = async (): Promise<boolean> => {
  try {
    // Get both tokens - access token is required even if expired
    const currentAccessToken = await AsyncStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
    const currentRefreshToken = await AsyncStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
    
    if (!currentRefreshToken || !currentAccessToken) {
      console.log('Missing tokens for refresh - access token:', !!currentAccessToken, 'refresh token:', !!currentRefreshToken);
      return false;
    }

    console.log('Performing token refresh...');
    const response = await fetch(`${getMobileApiUrl()}${ENDPOINTS.REFRESH}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Mobile-App': 'true',
        'X-Is-Mobile': 'true',
      },
      body: JSON.stringify({ 
        accessToken: currentAccessToken,    // API requires both tokens
        refreshToken: currentRefreshToken 
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token refresh failed with status:', response.status, 'Error:', errorText);
      console.error('Token refresh failed with status:', response.status);
      return false;
    }

    const data = await response.json();
    console.log('Token refresh successful');

    // Store new tokens using unified storage
    await storeToken(data.accessToken, data.expiresIn);
    if (data.refreshToken) {
      await AsyncStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, data.refreshToken);
    }

    return true;
  } catch (error) {
    console.error('Token refresh error:', error);
    return false;
  }
};

/**
 * For backward compatibility with code that expects digest auth
 */
export const refreshDigestCredentials = refreshToken;

/**
 * Get user profile (authenticated)
 */
export const getUserProfile = async (): Promise<any> => {
  return authenticatedRequest(ENDPOINTS.PROFILE, 'GET');
};
