/**
 * Security Headers Service
 *
 * Comprehensive security headers management and validation service
 * Defense in depth approach with multiple layers of protection
 */

type CSPViolation = {
  directive: string
  blockedUri: string
  violatedDirective: string
  timestamp: Date
  sourceFile?: string
  lineNumber?: number
}

export interface SecurityHeadersConfig {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: string[]
      scriptSrc: string[]
      styleSrc: string[]
      fontSrc: string[]
      imgSrc: string[]
      connectSrc: string[]
      frameSrc: string[]
      frameAncestors: string[]
      objectSrc: string[]
      mediaSrc: string[]
      workerSrc: string[]
      childSrc: string[]
      manifestSrc: string[]
      baseUri: string[]
      formAction: string[]
      upgradeInsecureRequests: boolean
      blockAllMixedContent: boolean
      requireSriFor?: string[]
      sandbox?: string[]
    }
    reportUri?: string
    reportTo?: string
    enforceMode: boolean
  }
  frameOptions: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM'
  contentTypeOptions: boolean
  xssProtection: {
    enabled: boolean
    mode: 'block' | 'report'
    reportUri?: string
  }
  referrerPolicy:
    | 'no-referrer'
    | 'no-referrer-when-downgrade'
    | 'origin'
    | 'origin-when-cross-origin'
    | 'same-origin'
    | 'strict-origin'
    | 'strict-origin-when-cross-origin'
    | 'unsafe-url'
  permissionsPolicy: {
    geolocation: string[]
    microphone: string[]
    camera: string[]
    payment: string[]
    usb: string[]
    magnetometer: string[]
    gyroscope: string[]
    accelerometer: string[]
    ambientLightSensor: string[]
    autoplay: string[]
    encryptedMedia: string[]
    fullscreen: string[]
    pictureInPicture: string[]
    syncXhr: string[]
    webShare: string[]
    bluetooth: string[]
    midi: string[]
    notifications: string[]
    pushNotifications: string[]
  }
  strictTransportSecurity: {
    enabled: boolean
    maxAge: number
    includeSubDomains: boolean
    preload: boolean
  }
  cors: {
    allowedOrigins: string[]
    allowedMethods: string[]
    allowedHeaders: string[]
    exposedHeaders: string[]
    maxAge: number
    credentials: boolean
  }
  cacheControl: {
    noCache: boolean
    noStore: boolean
    mustRevalidate: boolean
    private: boolean
    maxAge?: number
  }
  crossDomainPolicy: 'none' | 'master-only' | 'by-content-type' | 'by-ftp-filename' | 'all'
  expectCT: {
    enabled: boolean
    maxAge: number
    enforce: boolean
    reportUri?: string
  }
  clearSiteData: {
    enabled: boolean
    types: ('cache' | 'cookies' | 'storage' | 'executionContexts')[]
  }
}

export interface SecurityMetrics {
  totalViolations: number
  violationsByType: Record<string, number>
  lastViolationTime?: Date
  securityScore: number
  recommendations: string[]
}

export class SecurityHeadersService {
  private static instance: SecurityHeadersService
  private config: SecurityHeadersConfig
  private violations: CSPViolation[] = []
  private metrics: SecurityMetrics = {
    totalViolations: 0,
    violationsByType: {},
    securityScore: 100,
    recommendations: [],
  }

  private constructor() {
    this.config = this.getDefaultConfig()
    this.initializeSecurityHeaders()
  }

  static getInstance(): SecurityHeadersService {
    if (!SecurityHeadersService.instance) {
      SecurityHeadersService.instance = new SecurityHeadersService()
    }
    return SecurityHeadersService.instance
  }

  /**
   * Get default security headers configuration
   * Defense in depth with strict default policies
   */
  private getDefaultConfig(): SecurityHeadersConfig {
    const isDevelopment = process.env.NODE_ENV === 'development'
    return {
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", ...(isDevelopment ? ["'unsafe-inline'", "'unsafe-eval'"] : []), 'https://use.fontawesome.com'],
          styleSrc: [
            "'self'",
            "'unsafe-inline'", // Required for CSS-in-JS until we implement nonce
            'https://use.fontawesome.com',
            'https://fonts.googleapis.com',
          ],
          fontSrc: ["'self'", 'https://use.fontawesome.com', 'https://fonts.gstatic.com'],
          imgSrc: ["'self'", 'data:', 'https:', 'blob:'],
          connectSrc: [
            "'self'",
            'https://digiflow.digiturk.com.tr',
            'https://digiflowtest.digiturk.com.tr',
            'http://digiflowtest.digiturk.com.tr',
            'wss://digiflow.digiturk.com.tr',
            'wss://digiflowtest.digiturk.com.tr',
            ...(isDevelopment ? ['http://localhost:*', 'ws://localhost:*'] : []),
          ],
          frameSrc: ["'none'"],
          frameAncestors: ["'none'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          workerSrc: ["'self'", 'blob:'],
          childSrc: ["'self'"],
          manifestSrc: ["'self'"],
          baseUri: ["'self'"],
          formAction: ["'self'"],
          upgradeInsecureRequests: !isDevelopment,
          blockAllMixedContent: !isDevelopment,
          requireSriFor: ['script', 'style'],
        },
        reportUri: isDevelopment ? undefined : '/api/security/csp-report',
        reportTo: 'default',
        enforceMode: true,
      },
      frameOptions: 'DENY',
      contentTypeOptions: true,
      xssProtection: {
        enabled: true,
        mode: 'block',
        reportUri: '/api/security/xss-report',
      },
      referrerPolicy: 'strict-origin-when-cross-origin',
      permissionsPolicy: {
        geolocation: [],
        microphone: [],
        camera: [],
        payment: [],
        usb: [],
        magnetometer: [],
        gyroscope: [],
        accelerometer: [],
        ambientLightSensor: [],
        autoplay: [],
        encryptedMedia: [],
        fullscreen: ["'self'"],
        pictureInPicture: [],
        syncXhr: [],
        webShare: [],
        bluetooth: [],
        midi: [],
        notifications: [],
        pushNotifications: [],
      },
      strictTransportSecurity: {
        enabled: true,
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true,
      },
      cors: {
        allowedOrigins: [
          'https://digiflow.digiturk.com.tr',
          'https://digiflowtest.digiturk.com.tr',
          ...(isDevelopment ? ['http://localhost:3000', 'http://localhost:5173'] : []),
        ],
        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-CSRF-Token', 'X-Requested-With'],
        exposedHeaders: ['X-Request-Id', 'X-Response-Time'],
        maxAge: 86400, // 24 hours
        credentials: true,
      },
      cacheControl: {
        noCache: true,
        noStore: true,
        mustRevalidate: true,
        private: true,
      },
      crossDomainPolicy: 'none',
      expectCT: {
        enabled: !isDevelopment,
        maxAge: 86400,
        enforce: false,
        reportUri: '/api/security/ct-report',
      },
      clearSiteData: {
        enabled: true,
        types: ['cache', 'cookies', 'storage', 'executionContexts'],
      },
    }
  }

  /**
   * Initialize security headers monitoring and violation reporting
   */
  private initializeSecurityHeaders(): void {
    // CSP Violation Reporting
    if (typeof document !== 'undefined') {
      document.addEventListener('securitypolicyviolation', (e) => {
        this.handleCSPViolation(e)
      })
    }

    // Monitor for unsafe practices
    this.monitorForUnsafePractices()

    console.info('🛡️ Security Headers Service initialized with defense in depth')
  }

  /**
   * Handle Content Security Policy violations
   */
  private handleCSPViolation(event: SecurityPolicyViolationEvent): void {
    const violation = {
      directive: event.effectiveDirective,
      blockedUri: event.blockedURI,
      violatedDirective: event.violatedDirective,
      timestamp: new Date(),
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber,
    }

    this.violations.push(violation)
    this.updateMetrics(violation)

    // Log violation with security context
    console.warn('🚨 CSP Violation Detected')
    console.error('Directive:', violation.directive)
    console.error('Blocked URI:', violation.blockedUri)
    console.error('Source:', violation.sourceFile)
    console.error('Line:', violation.lineNumber)
    console.error('Timestamp:', violation.timestamp.toISOString())
    // End of CSP violation details

    // Report to security endpoint
    void this.reportViolation(violation)

    // Assess threat level
    const threatLevel = this.assessThreatLevel(violation)
    if (threatLevel === 'HIGH') {
      console.error('🚨 HIGH THREAT CSP VIOLATION - Potential XSS attack detected')
      this.metrics.securityScore = Math.max(0, this.metrics.securityScore - 10)
    } else if (threatLevel === 'MEDIUM') {
      this.metrics.securityScore = Math.max(0, this.metrics.securityScore - 5)
    } else {
      this.metrics.securityScore = Math.max(0, this.metrics.securityScore - 1)
    }
  }

  /**
   * Assess threat level of CSP violation
   */
  private assessThreatLevel(violation: any): 'LOW' | 'MEDIUM' | 'HIGH' {
    const { directive, blockedUri, violatedDirective } = violation

    // High threat indicators
    if (directive === 'script-src' && blockedUri.includes('javascript:')) {
      return 'HIGH'
    }
    if (directive === 'script-src' && blockedUri.includes('data:')) {
      return 'HIGH'
    }
    if (violatedDirective.includes('unsafe-eval') && !this.isKnownSafeEval(blockedUri)) {
      return 'HIGH'
    }
    if (blockedUri.includes('eval') || blockedUri.includes('Function')) {
      return 'HIGH'
    }

    // Medium threat indicators
    if (directive === 'style-src' && blockedUri.includes('data:')) {
      return 'MEDIUM'
    }
    if (directive === 'img-src' && blockedUri.startsWith('http://')) {
      return 'MEDIUM'
    }

    return 'LOW'
  }

  /**
   * Check if eval usage is from known safe sources
   */
  private isKnownSafeEval(blockedUri: string): boolean {
    const safeEvalSources = ['react-devtools', 'webpack', 'vite', 'hot-reload']
    return safeEvalSources.some((source) => blockedUri.includes(source))
  }

  /**
   * Monitor for unsafe practices in the application
   */
  private monitorForUnsafePractices(): void {
    if (typeof window === 'undefined') return

    // Monitor for dangerous globals (disabled in development to reduce noise)
    if (process.env.NODE_ENV === 'production') {
      const dangerousGlobals = ['eval', 'Function']
      dangerousGlobals.forEach((globalName) => {
        if ((window as any)[globalName]) {
          const original = (window as any)[globalName]
          ;(window as any)[globalName] = (...args: any[]) => {
            console.warn(`⚠️ Potentially unsafe ${globalName} usage detected:`, args)
            return original.apply(window, args)
          }
        }
      })
    }

    // Monitor for innerHTML usage (only in production)
    if (process.env.NODE_ENV === 'production') {
      this.monitorInnerHTML()
    }
  }

  /**
   * Monitor for dangerous innerHTML usage
   */
  private monitorInnerHTML(): void {
    if (typeof Element === 'undefined') return

    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML')
    if (!originalInnerHTML) return

    const service = this

    Object.defineProperty(Element.prototype, 'innerHTML', {
      set(value: string) {
        if (service.isHTMLPotentiallyDangerous(value)) {
          console.warn('⚠️ Potentially dangerous innerHTML detected:', value)
        }
        if (originalInnerHTML.set) {
          originalInnerHTML.set.call(this, value)
        }
      },
      get() {
        return originalInnerHTML.get ? originalInnerHTML.get.call(this) : ''
      },
    })
  }

  /**
   * Check if HTML content is potentially dangerous
   */
  private isHTMLPotentiallyDangerous(html: string): boolean {
    const dangerousPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe[^>]*>/gi,
      /<object[^>]*>/gi,
      /<embed[^>]*>/gi,
      /<form[^>]*>/gi,
    ]

    return dangerousPatterns.some((pattern) => pattern.test(html))
  }

  /**
   * Report security violation to backend
   */
  private async reportViolation(violation: any): Promise<void> {
    // Skip reporting in development to avoid unnecessary errors
    if (process.env.NODE_ENV === 'development') {
      return
    }

    try {
      const reportUrl = this.config.contentSecurityPolicy.reportUri
      if (!reportUrl) {
        return
      }

      // Construct full URL properly
      const fullUrl = new URL(reportUrl, window.location.origin)

      await fetch(fullUrl.toString(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'csp-violation',
          violation,
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          url: window.location.href,
        }),
      })
    } catch (error) {
      console.error('Failed to report CSP violation:', error)
    }
  }

  /**
   * Generate Content Security Policy header string
   */
  generateCSPHeader(): string {
    const { directives } = this.config.contentSecurityPolicy
    const { upgradeInsecureRequests } = directives

    const policies = [
      `default-src ${directives.defaultSrc.join(' ')}`,
      `script-src ${directives.scriptSrc.join(' ')}`,
      `style-src ${directives.styleSrc.join(' ')}`,
      `font-src ${directives.fontSrc.join(' ')}`,
      `img-src ${directives.imgSrc.join(' ')}`,
      `connect-src ${directives.connectSrc.join(' ')}`,
      `frame-src ${directives.frameSrc.join(' ')}`,
      `object-src ${directives.objectSrc.join(' ')}`,
      `base-uri ${directives.baseUri.join(' ')}`,
      `form-action ${directives.formAction.join(' ')}`,
    ]

    if (upgradeInsecureRequests) {
      policies.push('upgrade-insecure-requests')
    }

    if (this.config.contentSecurityPolicy.reportUri) {
      policies.push(`report-uri ${this.config.contentSecurityPolicy.reportUri}`)
    }

    return policies.join('; ')
  }

  /**
   * Generate Permissions Policy header string
   */
  generatePermissionsPolicyHeader(): string {
    const { permissionsPolicy } = this.config

    const policies = Object.entries(permissionsPolicy).map(([permission, allowlist]) => {
      const formattedPermission = permission.replace(/([A-Z])/g, '-$1').toLowerCase()
      const formattedAllowlist = allowlist.length > 0 ? allowlist.join(' ') : '()'
      return `${formattedPermission}=${formattedAllowlist}`
    })

    return policies.join(', ')
  }

  /**
   * Validate current page against security headers
   */
  async validateSecurityHeaders(): Promise<{
    compliant: boolean
    violations: string[]
    recommendations: string[]
  }> {
    const violations: string[] = []
    const recommendations: string[] = []

    // In development, be more lenient with React-injected scripts and styles
    const isDevelopment = process.env.NODE_ENV === 'development'

    // Check for inline scripts
    const inlineScripts = document.querySelectorAll('script:not([src])')

    // In development, log what we found for debugging
    if (isDevelopment && inlineScripts.length > 0) {
      console.info(
        'Found inline scripts:',
        Array.from(inlineScripts).map((s) => {
          const script = s as HTMLScriptElement
          return {
            content: `${script.textContent?.substring(0, 100)}...`,
            id: script.id,
            type: script.type,
          }
        }),
      )
    }

    const nonReactScripts = Array.from(inlineScripts).filter((element) => {
      const script = element as HTMLScriptElement
      // Filter out React/Vite development scripts and empty scripts
      const content = script.textContent ?? ''
      const type = script.type ?? ''

      // Skip module scripts as they're likely from Vite
      if (type === 'module') return false

      return (
        content.trim().length > 0 &&
        !content.includes('__vite') &&
        !content.includes('import.meta.hot') &&
        !content.includes('window.__REACT') &&
        !content.includes('import ') && // ES modules
        !content.includes('import.meta')
      )
    })

    if (nonReactScripts.length > 0) {
      violations.push(`Found ${nonReactScripts.length} inline scripts`)
      recommendations.push('Move inline scripts to external files')
    } else if (isDevelopment && inlineScripts.length > 0) {
      // In development, just log info instead of violation
      console.info(`ℹ️ Development mode: ${inlineScripts.length} inline scripts detected (likely from React/Vite)`)
    }

    // Check for inline styles
    const inlineStyles = document.querySelectorAll('style')

    // In development, log what we found for debugging
    if (isDevelopment && inlineStyles.length > 0) {
      console.info(
        'Found inline styles:',
        Array.from(inlineStyles).map((s) => ({
          content: `${s.textContent?.substring(0, 100)}...`,
          id: s.id ?? 'no-id',
          parent: s.parentElement?.tagName,
          dataAttr: s.getAttribute('data-vite-dev-id') ?? s.getAttribute('data-emotion') ?? 'none',
        })),
      )
    }

    const nonReactStyles = Array.from(inlineStyles).filter((style) => {
      // Filter out styles injected by React libraries and development tools
      const content = style.textContent ?? ''
      const id = style.getAttribute('id') ?? ''
      const parent = style.parentElement

      // Check for various indicators of framework/library styles
      const hasDataAttr =
        style.hasAttribute('data-vite-dev-id') ||
        style.hasAttribute('data-emotion') ||
        style.hasAttribute('data-styled') ||
        style.hasAttribute('data-css') ||
        style.hasAttribute('data-s')

      // Check if it's in the head and is for noscript styles
      const isNoscriptStyle = parent?.tagName === 'HEAD' && content.includes('noscript')

      // Check for common CSS-in-JS patterns
      const isCssInJs =
        content.includes('css-') ||
        content.includes('emotion-') ||
        content.includes('styled-') ||
        content.includes('sc-') || // styled-components
        content.includes('mui-') || // Material-UI
        content.includes('ant-') // Ant Design

      // Filter out known safe/development styles
      return (
        !hasDataAttr &&
        !id.includes('vite') &&
        !id.includes('react') &&
        !id.includes('_next') &&
        !content.includes('__vite') &&
        !content.includes('_app') &&
        !isNoscriptStyle &&
        !isCssInJs &&
        content.trim().length > 0
      )
    })

    if (nonReactStyles.length > 0) {
      // Log what wasn't filtered out for debugging
      if (isDevelopment) {
        console.info(
          'Non-React styles found:',
          nonReactStyles.map((s) => ({
            content: `${s.textContent?.substring(0, 100)}...`,
            id: s.id ?? 'no-id',
            parent: s.parentElement?.tagName,
          })),
        )
      }
      violations.push(`Found ${nonReactStyles.length} inline styles`)
      recommendations.push('Use external CSS files or CSS-in-JS with nonce')
    } else if (isDevelopment && inlineStyles.length > 0) {
      // In development, just log info instead of violation
      console.info(`ℹ️ Development mode: ${inlineStyles.length} inline styles detected (likely from React libraries)`)
    }

    // Check for mixed content - but be lenient in development
    const isHTTPS = window.location.protocol === 'https:'
    const httpResources = Array.from(document.querySelectorAll('img[src], script[src], link[href]')).filter((el) => {
      const url = el.getAttribute('src') ?? el.getAttribute('href')
      return url?.startsWith('http://') && !url.includes('localhost')
    })

    // Only check for mixed content if we're on HTTPS (not relevant for local dev)
    if (isHTTPS && httpResources.length > 0) {
      violations.push(`Found ${httpResources.length} HTTP resources in HTTPS page`)
      recommendations.push('Upgrade all resources to HTTPS')
    } else if (!isHTTPS && isDevelopment && httpResources.length > 0) {
      // In development on HTTP, this is expected
      console.info(`ℹ️ Development mode: ${httpResources.length} HTTP resources detected (expected in local dev)`)
    }

    // Check for potentially dangerous attributes
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover']
    const elementsWithDangerousAttrs = dangerousAttrs.flatMap((attr) => Array.from(document.querySelectorAll(`[${attr}]`)))

    if (elementsWithDangerousAttrs.length > 0) {
      violations.push(`Found ${elementsWithDangerousAttrs.length} elements with inline event handlers`)
      recommendations.push('Use addEventListener instead of inline event handlers')
    }

    return {
      compliant: violations.length === 0,
      violations,
      recommendations,
    }
  }

  /**
   * Get security headers report
   */
  getSecurityReport(): {
    config: SecurityHeadersConfig
    violations: CSPViolation[]
    recommendations: string[]
  } {
    const recommendations: string[] = []

    // Analyze violations for patterns
    const scriptViolations = this.violations.filter((v) => v.directive === 'script-src')
    const styleViolations = this.violations.filter((v) => v.directive === 'style-src')

    if (scriptViolations.length > 0) {
      recommendations.push('Consider removing unsafe-inline from script-src')
    }

    if (styleViolations.length > 0) {
      recommendations.push('Consider using nonce-based CSP for styles')
    }

    // Check for development vs production
    if (process.env.NODE_ENV === 'production') {
      if (this.config.contentSecurityPolicy.directives.scriptSrc.includes("'unsafe-eval'")) {
        recommendations.push('Remove unsafe-eval from script-src in production')
      }
    }

    return {
      config: this.config,
      violations: this.violations,
      recommendations,
    }
  }

  /**
   * Update security headers configuration
   */
  updateConfig(newConfig: Partial<SecurityHeadersConfig>): void {
    this.config = { ...this.config, ...newConfig }
    console.info('🛡️ Security headers configuration updated')
  }

  /**
   * Clear violation history
   */
  clearViolations(): void {
    this.violations = []
    console.info('🛡️ Security violation history cleared')
  }

  /**
   * Update security metrics based on violation
   */
  private updateMetrics(violation: CSPViolation): void {
    this.metrics.totalViolations++
    this.metrics.violationsByType[violation.directive] = (this.metrics.violationsByType[violation.directive] || 0) + 1
    this.metrics.lastViolationTime = violation.timestamp
  }

  /**
   * Get current security metrics
   */
  getSecurityMetrics(): SecurityMetrics {
    return { ...this.metrics }
  }

  /**
   * Generate all security headers for HTTP responses
   */
  getAllSecurityHeaders(): Record<string, string> {
    const headers: Record<string, string> = {}

    // CSP
    headers['Content-Security-Policy'] = this.generateCSPHeader()

    // Frame Options
    headers['X-Frame-Options'] = this.config.frameOptions

    // Content Type Options
    if (this.config.contentTypeOptions) {
      headers['X-Content-Type-Options'] = 'nosniff'
    }

    // XSS Protection
    if (this.config.xssProtection.enabled) {
      headers['X-XSS-Protection'] = `1; mode=${this.config.xssProtection.mode}`
    }

    // Referrer Policy
    headers['Referrer-Policy'] = this.config.referrerPolicy

    // Permissions Policy
    headers['Permissions-Policy'] = this.generatePermissionsPolicyHeader()

    // HSTS
    if (this.config.strictTransportSecurity.enabled) {
      const { maxAge, includeSubDomains, preload } = this.config.strictTransportSecurity
      let hstsValue = `max-age=${maxAge}`
      if (includeSubDomains) hstsValue += '; includeSubDomains'
      if (preload) hstsValue += '; preload'
      headers['Strict-Transport-Security'] = hstsValue
    }

    // Cache Control
    const cacheDirectives: string[] = []
    if (this.config.cacheControl.noCache) cacheDirectives.push('no-cache')
    if (this.config.cacheControl.noStore) cacheDirectives.push('no-store')
    if (this.config.cacheControl.mustRevalidate) cacheDirectives.push('must-revalidate')
    if (this.config.cacheControl.private) cacheDirectives.push('private')
    if (this.config.cacheControl.maxAge !== undefined) {
      cacheDirectives.push(`max-age=${this.config.cacheControl.maxAge}`)
    }
    headers['Cache-Control'] = cacheDirectives.join(', ')

    // Cross Domain Policy
    headers['X-Permitted-Cross-Domain-Policies'] = this.config.crossDomainPolicy

    // Expect-CT
    if (this.config.expectCT.enabled) {
      let expectCT = `max-age=${this.config.expectCT.maxAge}`
      if (this.config.expectCT.enforce) expectCT += ', enforce'
      if (this.config.expectCT.reportUri) {
        expectCT += `, report-uri="${this.config.expectCT.reportUri}"`
      }
      headers['Expect-CT'] = expectCT
    }

    return headers
  }

  /**
   * Get CORS headers based on origin
   */
  getCORSHeaders(origin: string): Record<string, string> {
    const headers: Record<string, string> = {}

    if (this.config.cors.allowedOrigins.includes(origin) || this.config.cors.allowedOrigins.includes('*')) {
      headers['Access-Control-Allow-Origin'] = origin
      headers['Access-Control-Allow-Methods'] = this.config.cors.allowedMethods.join(', ')
      headers['Access-Control-Allow-Headers'] = this.config.cors.allowedHeaders.join(', ')
      headers['Access-Control-Expose-Headers'] = this.config.cors.exposedHeaders.join(', ')
      headers['Access-Control-Max-Age'] = this.config.cors.maxAge.toString()

      if (this.config.cors.credentials) {
        headers['Access-Control-Allow-Credentials'] = 'true'
      }
    }

    return headers
  }

  /**
   * Get Clear-Site-Data header for logout
   */
  getClearSiteDataHeader(): string | null {
    if (!this.config.clearSiteData.enabled) return null

    const types = this.config.clearSiteData.types.map((t) => `"${t}"`).join(', ')
    return types
  }

  /**
   * Calculate Subresource Integrity (SRI) hash
   */
  async calculateSRI(content: string, algorithm: 'sha256' | 'sha384' | 'sha512' = 'sha384'): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(content)
    const hashBuffer = await crypto.subtle.digest(algorithm.toUpperCase().replace('SHA', 'SHA-'), data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const hashBase64 = btoa(String.fromCharCode(...hashArray))
    return `${algorithm}-${hashBase64}`
  }

  /**
   * Verify SRI hash
   */
  async verifySRI(content: string, integrity: string): Promise<boolean> {
    const [algorithm, _expectedHash] = integrity.split('-') as ['sha256' | 'sha384' | 'sha512', string]
    const actualHash = await this.calculateSRI(content, algorithm)
    return actualHash === integrity
  }

  /**
   * Apply security headers to fetch requests
   */
  applySecurityHeadersToRequest(init: RequestInit = {}): RequestInit {
    const headers = new Headers(init.headers ?? {})

    // Add security headers
    headers.set('X-Requested-With', 'XMLHttpRequest')
    headers.set('X-Content-Type-Options', 'nosniff')

    return {
      ...init,
      headers,
      credentials: 'same-origin',
      mode: 'cors',
    }
  }

  /**
   * Get content security policy for inline styles with nonce
   */
  generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return btoa(String.fromCharCode(...array)).replace(/[+/=]/g, '')
  }

  /**
   * Update CSP with nonce for current page load
   */
  updateCSPWithNonce(nonce: string): void {
    const { scriptSrc } = this.config.contentSecurityPolicy.directives
    const { styleSrc } = this.config.contentSecurityPolicy.directives

    // Add nonce to script-src and style-src
    if (!scriptSrc.includes(`'nonce-${nonce}'`)) {
      scriptSrc.push(`'nonce-${nonce}'`)
    }
    if (!styleSrc.includes(`'nonce-${nonce}'`)) {
      styleSrc.push(`'nonce-${nonce}'`)
    }
  }
}

// Export singleton instance
export const securityHeadersService = SecurityHeadersService.getInstance()
export default securityHeadersService

// Export helper functions
export const getContentSecurityPolicy = () => securityHeadersService.generateCSPHeader()
export const getSecurityHeaders = () => securityHeadersService.getAllSecurityHeaders()
export const validateSecurity = () => securityHeadersService.validateSecurityHeaders()
export const getSecurityMetrics = () => securityHeadersService.getSecurityMetrics()
