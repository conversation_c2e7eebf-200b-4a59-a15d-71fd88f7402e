import 'reflect-metadata'
// Fix routing for IIS without URL Rewrite
import './utils/routingFix'
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './assets/css/index.css'
import './styles/design-system.css'

// SECURITY: Import comprehensive security initialization
import securityInitializer from './utils/securityInitializer'

// Enhanced error handling for mobile WebView with security context
const handleGlobalError = (message: string | Event, source?: string, lineno?: number, colno?: number, error?: Error) => {
  const errorDetails = {
    message: typeof message === 'string' ? message : message.toString(),
    source,
    lineno,
    colno,
    error: error?.stack ?? null,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    isSecurityRelated: isSecurityRelatedError(message, error),
  }

  // Security-related errors require immediate attention
  if (errorDetails.isSecurityRelated) {
    console.error('🚨 SECURITY ERROR DETECTED:', errorDetails)
  }

  // Send to mobile WebView handler if available
  if ((window as any).nativeMessageHandler) {
    try {
      ;(window as any).nativeMessageHandler.postMessage(JSON.stringify(errorDetails))
    } catch (e) {
      console.error('Failed to send error to native handler:', e)
    }
  } else {
    // Fallback for debugging
    ;(window as any).errorMessage = JSON.stringify(errorDetails)
  }

  // Also log to console for development
  console.error('Global error captured:', errorDetails)
}

// Detect security-related errors
const isSecurityRelatedError = (message: string | Event, error?: Error): boolean => {
  const messageStr = typeof message === 'string' ? message : message.toString()
  const errorStr = error?.message ?? ''

  const securityKeywords = [
    'csp',
    'content security policy',
    'security policy violation',
    'csrf',
    'cross-site',
    'xss',
    'injection',
    'certificate',
    'ssl',
    'tls',
    'https',
    'unauthorized',
    'forbidden',
    'authentication',
    'token',
    'session',
    'cookie',
  ]

  return securityKeywords.some((keyword) => messageStr.toLowerCase().includes(keyword) || errorStr.toLowerCase().includes(keyword))
}

// Set up global error handlers
window.onerror = handleGlobalError
window.addEventListener('unhandledrejection', (event) => {
  handleGlobalError(
    `Unhandled Promise Rejection: ${event.reason}`,
    'promise-rejection',
    0,
    0,
    event.reason instanceof Error ? event.reason : new Error(String(event.reason)),
  )
})

// Check if we're in a WebView
const isInWebView = () => {
  return (
    typeof window !== 'undefined' &&
    (typeof (window as any).ReactNativeWebView !== 'undefined' ||
      navigator.userAgent.toLowerCase().includes('digiflowmobile') ||
      navigator.userAgent.toLowerCase().includes('wv') ||
      navigator.userAgent.toLowerCase().includes('reactnative'))
  )
}

// SECURITY: Initialize comprehensive security measures
const initializeApplicationSecurity = async () => {
  try {
    // For WebView, check if we need to wait for authentication
    if (isInWebView()) {
      console.info('📱 WebView detected - checking authentication state...')
      
      // Check if JWT token is already available
      const existingToken = sessionStorage.getItem('webview_jwt') || sessionStorage.getItem('jwt_token');
      
      if (!existingToken) {
        console.info('📱 No JWT token found - requesting from mobile app...')
        // Request authentication from mobile app
        if ((window as any).ReactNativeWebView) {
          (window as any).ReactNativeWebView.postMessage(
            JSON.stringify({
              type: 'REQUEST_SECURE_SESSION',
            }),
          )
        }
        
        // Wait a short time for token to arrive
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    await securityInitializer.initializeSecurity({
      enableCSPViolationReporting: process.env.NODE_ENV === 'production',
      enableSecurityAudit: true,
      enableDevelopmentWarnings: process.env.NODE_ENV === 'development',
      enablePerformanceMonitoring: true,
      auditInterval: process.env.NODE_ENV === 'production' ? 60 : 15,
    })

    console.info('✅ Application security initialized successfully')
  } catch (error) {
    console.error('❌ Security initialization failed:', error)

    // In production, this is a critical failure
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Critical security initialization failure')
    }
  }
}

// Initialize security before rendering the app
initializeApplicationSecurity()
  .then(() => {
    console.info('🚀 Starting DigiFlow application with security measures active')
  })
  .catch((error) => {
    console.error('💥 Failed to initialize security:', error)
  })

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  (
    <React.StrictMode>
      <App />
    </React.StrictMode>
  ) as any,
)
