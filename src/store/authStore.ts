import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { storeToken, removeToken, logout as apiLogout, getToken, storeUserInfo, getUserInfo } from '../services/api';
import { secureStorage } from '../services/simpleSecureStorage';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface AuthState {
  token: string | null;
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isSecurityRestricted: boolean;
  login: (user: User, token: string) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
  clearAllData: () => Promise<void>;
  setSecurityRestricted: (restricted: boolean) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  token: null,
  user: null,
  isLoading: false,
  error: null,
  isSecurityRestricted: false,

  login: async (user, token) => {
    set({ isLoading: true, error: null });
    try {
      // Store token using unified storage (this handles expiry too)
      await storeToken(token);

      // Store user info using unified storage
      await storeUserInfo({
        id: user.id,
        name: user.name,
        email: user.email,
      });

      // Update state
      set({
        token: token,
        user: user,
        isLoading: false,
      });
    } catch (error) {
      set({
        error: 'Login failed',
        isLoading: false,
      });
      throw error;
    }
  },

  logout: async () => {
    try {
      console.log('Starting logout process...');

      // Call API logout first to invalidate tokens on server
      try {
        await apiLogout();
        console.log('API logout successful');
      } catch (apiError) {
        console.warn('API logout failed, continuing with local cleanup:', apiError);
      }

      // Clear all authentication data
      await get().clearAllData();

      console.log('Logout completed successfully');
    } catch (error) {
      console.error('Logout error:', error);
      // Even if there's an error, try to clear local data
      try {
        await get().clearAllData();
      } catch (clearError) {
        console.error('Failed to clear local data:', clearError);
      }
    }
  },

  clearAllData: async () => {
    try {
      console.log('Clearing all authentication data...');

      // Remove all authentication tokens using unified storage
      await removeToken();
      
      // Remove refresh token
      await AsyncStorage.removeItem('unified_refresh_token');
      
      // Remove user info
      await AsyncStorage.removeItem('unified_user_info');
      
      // Clear any legacy keys that might exist
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('jwt_token');
      await AsyncStorage.removeItem('refresh_token');
      await AsyncStorage.removeItem('user_info');
      await AsyncStorage.removeItem('webview_cache');
      await AsyncStorage.removeItem('digest_credentials');
      await AsyncStorage.removeItem('login_credentials');

      // Also clear from secure storage
      await secureStorage.removeItem('refresh_token');
      await secureStorage.removeItem('user_info');

      // Update state to logged out
      set({
        token: null,
        user: null,
        error: null,
        isSecurityRestricted: false,
      });

      console.log('All authentication data cleared');
    } catch (error) {
      console.error('Error clearing authentication data:', error);
      throw error;
    }
  },

  setSecurityRestricted: (restricted: boolean) => {
    set({ isSecurityRestricted: restricted });
  },

  checkAuth: async () => {
    try {
      console.log('Checking authentication status...');
      
      // Check if token exists using unified storage
      const token = await getToken();
      
      // Get user info using unified storage
      const userInfo = await getUserInfo();

      if (token && userInfo) {
        console.log('Found valid authentication');
        const user: User = {
          id: userInfo.id,
          name: userInfo.name,
          email: userInfo.email,
          avatar: undefined,
        };
        
        set({
          token,
          user,
        });
        return true;
      }
      
      console.log('No valid authentication found');
      return false;
    } catch (error) {
      console.error('Auth check error:', error);
      return false;
    }
  },
}));
