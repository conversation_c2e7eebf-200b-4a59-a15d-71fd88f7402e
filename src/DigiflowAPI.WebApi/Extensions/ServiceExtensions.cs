﻿using DigiflowAPI.Application;
using DigiflowAPI.Infrastructure;
using MediatR;
using System.Reflection;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Globalization;
using Microsoft.AspNetCore.Localization;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Infrastructure.Services;
using DigiflowAPI.Infrastructure.Helpers;
using DigiflowAPI.Domain.Interfaces.Repositories;
using DigiflowAPI.Application.Models;
using DigiflowAPI.Application.Commands.Workflow;
using DigiflowAPI.Application.Queries.Organizations;
using DigiflowAPI.Application.Handlers.Users;
using DigiflowAPI.Application.Validators.User;
using DigiflowAPI.Application.Validators.Organization;
using DigiflowAPI.Application.Commands.Organization;
using DigiflowAPI.Api.Extensions;
using Microsoft.AspNetCore.Mvc;
using DigiflowAPI.Application.Interfaces.Services.Workflow;
using DigiflowAPI.Application.Handlers.Workflow;
using FluentValidation;
using DigiflowAPI.Application.Queries.Users;
using DigiflowAPI.WebApi.Middlewares;

namespace DigiflowAPI.WebApi.Extensions
{
    public static class ServiceExtensions
    {
        public static IServiceCollection AddApiServices(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment environment)
        {
            // Add Application layer services
            services.AddApplication();

            // Add Infrastructure layer services (repositories, services, etc.)
            services.AddInfrastructure();

            services.AddMemoryCache();

            // Add session support for CSRF tokens and other stateful operations
            services.AddDistributedMemoryCache();
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
                options.Cookie.SameSite = SameSiteMode.Strict;
            });

            // Add Rate Limiting services
            services.AddRateLimiting(configuration);

            // Configure mobile-specific options
            services.Configure<MobileSecurityOptions>(configuration.GetSection("Mobile:Security"));
            // Rate limiting is now consolidated in SecurityMiddleware

            //HTTP Service
            services.AddHttpClient<IHttpService, HttpService>()
                .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
                {
                    UseDefaultCredentials = true
                });

            // Register WorkflowTypeHelper and related services (not in Infrastructure DI)
            services.AddScoped<WorkflowTypeHelper>();
            services.AddHostedService<WorkflowTypeInitializer>();

            // Use Func<T> for services with circular dependencies
            services.AddScoped<Func<IWorkflowHelperService>>(sp => () => sp.GetRequiredService<IWorkflowHelperService>());
            services.AddScoped<Func<IWorkflowRepository>>(sp => () => sp.GetRequiredService<IWorkflowRepository>());
            services.AddScoped<Func<IGenericMailService>>(sp => () => sp.GetRequiredService<IGenericMailService>());
            services.AddScoped<Func<WorkflowTypeHelper>>(sp => () => sp.GetRequiredService<WorkflowTypeHelper>());

            // Register localization service
            services.AddScoped<DigiflowAPI.Application.Services.Interfaces.IResourceLocalizationService, DigiflowAPI.Application.Services.ResourceLocalizationService>();

            services.AddLogging();
            services.AddTransient<IRequestHandler<UpdateEntityCommand, bool>, UpdateEntityCommandHandler>();

            services.Configure<PermissionProcessSettings>(configuration.GetSection("ServiceSettings"));

            //Helper Services (already registered in Infrastructure)
            // services.AddTransient<IGlobalHelpers, GlobalHelpers>(); // Removed - already in Infrastructure

            //Session, HTTPContext and Cache Services
            services.AddHttpContextAccessor();
            services.AddDistributedMemoryCache();
            services.AddMemoryCache(); // For CSRF token storage
            services.AddSession(options =>
            {
                options.IdleTimeout = TimeSpan.FromMinutes(30);
                options.Cookie.HttpOnly = true;
                options.Cookie.IsEssential = true;
                options.Cookie.SameSite = SameSiteMode.Strict; // CSRF protection
            });

            // CSRF Protection
            // ICSRFService is now registered in Infrastructure layer's DependencyInjection.cs
            services.AddScoped<IAdvancedCSRFService, CSRFService>();

            //Authentication Service
            // DELETE ↓ (Windows only – now registered globally in Program.cs)
            // services.AddAuthentication(IISDefaults.AuthenticationScheme); // ← remove, policy scheme now handles both

            // Register MediatR and scan for handlers
            services.AddMediatR(cfg =>
            {
                cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly());
                cfg.RegisterServicesFromAssembly(typeof(Program).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(ApplicationEntryPoint).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetAllWorkflowHandler).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetAllAdminWorkflowHandler).Assembly);
                cfg.RegisterServicesFromAssembly(typeof(GetWorkflowQueryHandler<>).Assembly); // Register open generic handlers
            });            // Register open generic handlers
            services.AddOpenGenericHandlerType(typeof(Program).Assembly);            // AutoMapper is configured in AddApplication() through centralized configuration

            // Oracle services (must be registered before workflow services)
            // Note: These are also registered in Infrastructure, but keeping here for explicit control
            // services.AddSingleton<IOracleConnectionManager, OracleConnectionManager>(); // Already in Infrastructure
            // services.AddSingleton<IOracleDataAccessRepositoryFactory, OracleDataAccessRepositoryFactory>(); // Already in Infrastructure


            // Fluent Validation Services
            services.AddTransient<IValidator<GetDepartmentsQuery>, GetDepartmentQueryValidator>();
            services.AddTransient<IValidator<GetSchemaCommand>, GetSchemaQueryValidator>();
            services.AddTransient<IValidator<GetUsersByDepartmentIdQuery>, GetUsersByDepartmentIdQueryValidator>();

            //Custom Authorization Services (already registered in Infrastructure)
            // services.AddScoped<IWorkflowAuthorizationService, WorkflowAuthorizationService>(); // Already in Infrastructure
            // services.AddSingleton<WorkflowAuthorizationServiceFactory>(); // Already in Infrastructure

            //Cors Service
            services.AddCors(options =>
            {
                options.AddPolicy("AllowSpecificOrigin", policy =>
                {
                    // Get React app origins from configuration to match appsettings.json
                    var reactOrigins = configuration.GetSection("ReactAppOrigins").Get<string[]>() ?? [];
                    
                    // Get Mobile app origins from configuration
                    var mobileOrigins = configuration.GetSection("MobileAppOrigins").Get<string[]>() ?? [];
                    
                    // Combine all allowed origins
                    var allOrigins = reactOrigins.Concat(mobileOrigins).Distinct().ToArray();

                    // Only allow specifically configured origins
                    policy.WithOrigins(allOrigins)
                        .WithMethods("GET", "POST", "PUT", "DELETE", "OPTIONS") // Specific methods instead of Any
                        .WithHeaders("Content-Type", "Authorization", "X-Requested-With", "X-CSRF-Token", "X-Login-Id",
                            "X-Auth-Scheme", "X-Auth-Domain", "X-Auth-Provider", "X-DigiflowReact", "X-React-App", "X-From-Mobile-WebView", "X-Mobile-App",
                            "X-Is-Mobile", "X-Workflow-Definition-Id", "X-Workflow-Instance-Id") // All custom headers
                        .AllowCredentials()
                        // Add these specifically for SignalR
                        .WithExposedHeaders("X-Login-Id", "X-Workflow-Instance-Id", "X-Workflow-Definition-Id",
                            "X-Is-SysAdmin", "x-is-sysadmin", "X-Workflow-Copy-Instance-Id", "X-Workflow-Authorization");
                });
            });


            //Localization Service
            var supportedCultures = new[] { "tr-TR", "en-US" };
            services.Configure<RequestLocalizationOptions>(options =>
            {
                options.DefaultRequestCulture = new RequestCulture("en-US");
                options.SupportedCultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
                options.SupportedUICultures = supportedCultures.Select(c => new CultureInfo(c)).ToList();
            });

            //Swagger Service
            services.AddEndpointsApiExplorer();
            // SECURITY: Only add Swagger in development environment
            if (environment.IsDevelopment())
            {
                services.AddSwaggerGen(options =>
                {
                    options.SwaggerDoc("v1", new OpenApiInfo
                    {
                        Title = "Digiflow API",
                        Version = "v1",
                        Description = "⚠️ DEVELOPMENT ENVIRONMENT ONLY - Requires Windows Authentication"
                    });

                    options.CustomOperationIds(apiDesc =>
                    {
                        return apiDesc.TryGetMethodInfo(out MethodInfo methodInfo) ? methodInfo.Name : null;
                    });

                    // SECURITY: Windows Authentication scheme only (more secure than exposing multiple schemes)
                    options.AddSecurityDefinition("Windows", new OpenApiSecurityScheme
                    {
                        Type = SecuritySchemeType.Http,
                        Scheme = "negotiate",
                        Description = "Windows Domain Authentication Required"
                    });

                    // SECURITY: Global security requirement - all endpoints require authentication
                    options.AddSecurityRequirement(new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                {
                                    Type = ReferenceType.SecurityScheme,
                                    Id = "Windows"
                                }
                            },
                            Array.Empty<string>()
                        }
                    });

                    // Add explicit type mappings for file uploads to prevent parameter generation errors
                    options.MapType<IFormFile>(() => new OpenApiSchema
                    {
                        Type = "string",
                        Format = "binary"
                    });
                    options.MapType<IFormFileCollection>(() => new OpenApiSchema
                    {
                        Type = "array",
                        Items = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary"
                        }
                    });

                    options.OperationFilter<AddRequiredHeaderParameter>();
                    options.OperationFilter<FileUploadOperationFilter>();
                    options.OperationFilter<RemoveDuplicateApiVersionParameter>();
                });
            }


            // Add clean workflow services (replaces massive repetitive registrations)
            services.AddCleanWorkflowServices();

            return services;
        }

        public static void AddOpenGenericHandlerType(this IServiceCollection services, Assembly assembly)
        {
            var types = assembly.GetTypes();
            foreach (var type in types)
            {
                var interfaces = type.GetInterfaces()
                    .Where(i => i.IsGenericType && i.GetGenericTypeDefinition() == typeof(IRequestHandler<,>));

                foreach (var handlerInterface in interfaces)
                {
                    services.AddTransient(handlerInterface, type);
                }
            }
        }

        public class AddRequiredHeaderParameter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                operation.Parameters ??= [];

                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = "X-Login-ID",
                    In = ParameterLocation.Header,
                    Required = false,
                    Schema = new OpenApiSchema
                    {
                        Type = "integer"
                    }
                });
            }
        }

        public class FileUploadOperationFilter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                // Check if this is a file upload operation by looking at the action method
                var methodInfo = context.MethodInfo;
                var hasFormFile = methodInfo.GetParameters()
                    .Any(p => p.ParameterType == typeof(IFormFile) || p.ParameterType == typeof(IFormFileCollection));

                if (!hasFormFile)
                    return;

                // Clear any existing parameters that might cause issues
                operation.Parameters?.Clear();

                // Manually create the multipart/form-data request body
                operation.RequestBody = new OpenApiRequestBody
                {
                    Required = true,
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["multipart/form-data"] = new OpenApiMediaType
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = new Dictionary<string, OpenApiSchema>()
                            }
                        }
                    }
                };

                var schema = operation.RequestBody.Content["multipart/form-data"].Schema;

                // Add properties for each parameter in the method
                foreach (var parameter in methodInfo.GetParameters())
                {
                    if (parameter.ParameterType == typeof(IFormFile))
                    {
                        schema.Properties[parameter.Name ?? "file"] = new OpenApiSchema
                        {
                            Type = "string",
                            Format = "binary",
                            Description = $"Upload file for {parameter.Name}"
                        };
                    }
                    else if (parameter.ParameterType == typeof(IFormFileCollection))
                    {
                        schema.Properties[parameter.Name ?? "files"] = new OpenApiSchema
                        {
                            Type = "array",
                            Items = new OpenApiSchema
                            {
                                Type = "string",
                                Format = "binary"
                            },
                            Description = $"Upload multiple files for {parameter.Name}"
                        };
                    }
                    else if (parameter.GetCustomAttributes(typeof(FromFormAttribute), false).Any())
                    {
                        // Handle other [FromForm] parameters
                        schema.Properties[parameter.Name ?? "value"] = new OpenApiSchema
                        {
                            Type = "string",
                            Description = $"Form field for {parameter.Name}"
                        };
                    }
                }
            }
        }

        public class RemoveDuplicateApiVersionParameter : IOperationFilter
        {
            public void Apply(OpenApiOperation operation, OperationFilterContext context)
            {
                if (operation.Parameters == null) return;

                // Remove duplicate api-version parameters
                // Keep only the query string version if both exist
                var apiVersionParams = operation.Parameters
                    .Where(p => p.Name.Equals("api-version", StringComparison.OrdinalIgnoreCase))
                    .ToList();

                if (apiVersionParams.Count > 1)
                {
                    // Remove all api-version parameters first
                    foreach (var param in apiVersionParams)
                    {
                        operation.Parameters.Remove(param);
                    }

                    // Add back only the query string version
                    operation.Parameters.Add(new OpenApiParameter
                    {
                        Name = "api-version",
                        In = ParameterLocation.Query,
                        Required = false,
                        Schema = new OpenApiSchema
                        {
                            Type = "string",
                            Default = new Microsoft.OpenApi.Any.OpenApiString("1.0")
                        },
                        Description = "API version"
                    });
                }
            }
        }

    }

}
