﻿{
  "ConnectionStrings": {
    "Comment": "Connection strings are built dynamically from environment variables for security",
    "DefaultConnection": "",
    "DT_WORKFLOW": "",
    "FrameworkConnection": ""
  },
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft": "Information",
      "Microsoft.AspNetCore": "Information",
      "DigiflowAPI": "Debug"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Debug",
      "Override": {
        "Microsoft": "Information",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.AspNetCore.Authentication": "Debug",
        "Microsoft.AspNetCore.Authorization": "Debug",
        "Microsoft.EntityFrameworkCore": "Information",
        "System": "Information",
        "DigiflowAPI": "Debug",
        "DigiflowAPI.Application.Services.HighPerformanceMemoryCache": "Debug",
        "DigiflowAPI.Application.Services.DistributedRateLimitService": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-webapi-test-.txt",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Debug",
          "retainedFileCountLimit": 7,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-webapi-test-errors-.txt",
          "restrictedToMinimumLevel": "Error",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 14,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}{NewLine}---{NewLine}"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithEnvironmentName"
    ]
  },
  "Security": {
    "EnableWindowsAuthCsrfFallback": true,
    "EnableDoubleSubmitCookie": true,
    "SkipCsrfForJwtApi": false
  },
  "ReactAppOrigins": [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://digiflowtest.digiturk.com.tr",
    "https://digiflowtest.digiturk.com.tr",
    "http://digiflowtest",
    "https://digiflowtest",
    "http://dtl1iis5",
    "https://dtl1iis5"
  ],
  "MobileAppOrigins": [
    "http://localhost:8081",
    "http://localhost:19000",
    "http://localhost:19001",
    "http://localhost:19002",
    "exp://localhost:19000",
    "capacitor://localhost",
    "ionic://localhost",
    "http://********:8081",
    "http://***********/24"
  ],
  "AllowedHosts": "*"
}