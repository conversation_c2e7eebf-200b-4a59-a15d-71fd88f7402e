﻿using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Services;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
namespace DigiflowAPI.WebApi.Middlewares
{
    public class AuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<AuthenticationMiddleware> _logger;

        public AuthenticationMiddleware(RequestDelegate next, ILogger<AuthenticationMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IUserService userService, IGlobalHelpers globalHelpers, IConfiguration configuration, IJwtConfigurationService? jwtService = null, ISecurityMonitoringService? securityMonitoring = null)
        {
            // Skip authentication middleware for auth endpoints and diagnostics
            var path = context.Request.Path.Value?.ToLowerInvariant();
            if (path?.Contains("/auth/", StringComparison.OrdinalIgnoreCase) == true ||
                path?.Contains("/csrf/", StringComparison.OrdinalIgnoreCase) == true ||
                path?.Contains("/diagnostics/", StringComparison.OrdinalIgnoreCase) == true)
            {
                await _next(context);
                return;
            }

            Console.WriteLine("configuration" + configuration["ASPNETCORE_ENVIRONMENT"]);
            var loginIdHeader = context.Request.Headers["X-Login-Id"].ToString();
            var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";

            // Check if this is a mobile request authenticated with JWT
            bool isJwtAuth = context.Items["AuthType"]?.ToString() == "JWT";
            bool isMobile = context.Items["IsMobile"] is true;
            bool isMobileApp = context.Request.Headers.ContainsKey("X-Mobile-App");
            bool isFromMobileWebView = context.Request.Headers.ContainsKey("X-From-Mobile-WebView");

            // Also check if user is authenticated via JWT Bearer scheme
            if (!isJwtAuth && context.User?.Identity?.IsAuthenticated == true &&
                context.User.Identity.AuthenticationType == "Bearer")
            {
                isJwtAuth = true;
                context.Items["AuthType"] = "JWT";
                _logger.LogInformation("Detected JWT authentication from Bearer scheme");
            }

            // Enhanced logging for mobile requests
            if (isMobileApp || isFromMobileWebView)
            {
                _logger.LogInformation("MOBILE AUTH MIDDLEWARE: Path={Path}, IsJwtAuth={IsJwtAuth}, IsMobile={IsMobile}, IsMobileApp={IsMobileApp}, IsWebView={IsWebView}, Authenticated={IsAuthenticated}, AuthType={AuthType}, User={User}, LoginIdHeader={LoginId}",
                    path,
                    isJwtAuth,
                    isMobile,
                    isMobileApp,
                    isFromMobileWebView,
                    context.User?.Identity?.IsAuthenticated,
                    context.User?.Identity?.AuthenticationType,
                    context.User?.Identity?.Name,
                    loginIdHeader);
            }
            else
            {
                _logger.LogInformation("Authentication Middleware - Is JWT Auth: {IsJwtAuth}, Is Mobile: {IsMobile}, User authenticated: {IsAuthenticated}, Auth type: {AuthType}",
                    isJwtAuth,
                    isMobile,
                    context.User?.Identity?.IsAuthenticated,
                    context.User?.Identity?.AuthenticationType);
            }

            long windowsUserId;
            bool isAdmin;

            // Handle JWT authenticated users
            if (isJwtAuth && context.User?.Identity?.IsAuthenticated == true)
            {
                // For JWT Auth requests, we already have the authenticated username
                _logger.LogInformation("Using JWT authentication for user: {Username}", context.User.Identity.Name);

                // For JWT auth, get user info from claims
                var jwtUsername = context.User.Identity.Name;

                // Store JWT username in context for GlobalHelper
                if (!string.IsNullOrEmpty(jwtUsername))
                {
                    context.Items["JwtUsername"] = jwtUsername;
                }

                // Extract numeric user ID from JWT claims
                var userIdClaim = context.User.FindFirst("userId") ??
                                context.User.FindFirst("sub") ??
                                context.User.FindFirst("nameid");

                string numericUserId = null;
                if (userIdClaim != null && !string.IsNullOrEmpty(userIdClaim.Value))
                {
                    numericUserId = userIdClaim.Value;
                    _logger.LogInformation($"Found numeric user ID in JWT claims: {numericUserId}");
                }
                else
                {
                    _logger.LogWarning($"No numeric user ID found in JWT claims for user {jwtUsername}");
                }

                // If using X-Login-Id is 0, replace with numeric user ID (not username)
                if (loginIdHeader == "0" && !string.IsNullOrEmpty(numericUserId))
                {
                    loginIdHeader = numericUserId;
                }

                // For JWT users, use the numeric ID from claims
                windowsUserId = !string.IsNullOrEmpty(numericUserId) && long.TryParse(numericUserId, out var id) ? id : 0;

                // Check if JWT user has admin role
                isAdmin = context.User.IsInRole("Admin") || context.User.IsInRole("SysAdmin");

                _logger.LogInformation($"Processing JWT request. JWT User: {jwtUsername}, Numeric ID: {numericUserId}, X-Login-Id: {loginIdHeader}, IsAdmin: {isAdmin}");
            }
            else
            {
                // Standard Windows Auth processing
                // Check if this is a request that should use Windows auth
                bool shouldUseWindowsAuth = !isJwtAuth &&
                                          !context.Request.Headers.ContainsKey("X-From-Mobile-WebView") &&
                                          !context.Request.Headers.ContainsKey("X-Mobile-App");

                if (shouldUseWindowsAuth)
                    {
                        // Check if Windows authentication has completed
                        if (context.User?.Identity?.IsAuthenticated == true)
                        {
                            try
                            {
                                if (loginIdHeader == "0")
                                {
                                    loginIdHeader = (await globalHelpers.GetUserId()).ToString();
                                }
                                windowsUserId = await userService.GetUserInfo();

                                // Check if user was found
                                if (windowsUserId == 0)
                                {
                                    _logger.LogWarning($"User not found for authenticated user: {context.User.Identity.Name}, X-Login-Id: {loginIdHeader}");

                                    // Return 401 Unauthorized without exposing internal details
                                    context.Response.StatusCode = 401;
                                    context.Response.ContentType = "application/json";
                                    await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new
                                    {
                                        Type = "Unauthorized",
                                        Message = "Authentication failed",
                                        ErrorId = Guid.NewGuid().ToString(),
                                        Timestamp = DateTime.UtcNow,
                                        Status = 401,
                                        TraceId = context.TraceIdentifier
                                    }));
                                    return;
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error retrieving user information for authenticated user: {User}", context.User.Identity.Name);

                                // Return 401 Unauthorized without exposing internal details
                                context.Response.StatusCode = 401;
                                context.Response.ContentType = "application/json";
                                await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(new
                                {
                                    Type = "Unauthorized",
                                    Message = "Authentication failed",
                                    ErrorId = Guid.NewGuid().ToString(),
                                    Timestamp = DateTime.UtcNow,
                                    Status = 401,
                                    TraceId = context.TraceIdentifier
                                }));
                                return;
                            }

                            isAdmin = globalHelpers.IsSystemAdmin();

                            _logger.LogInformation($"Processing Windows auth request. X-Login-Id: {loginIdHeader}, WindowsUserId: {windowsUserId}, IsAdmin: {isAdmin}");

                            // React clients now use transparent authentication without JWT token generation
                            _logger.LogInformation($"Windows authenticated user: {context.User.Identity.Name}, UserId: {windowsUserId}");
                        }
                        else
                        {
                            // Windows auth might not have completed yet, continue to next middleware
                            _logger.LogInformation($"Windows auth not yet completed for {context.Request.Path}, continuing...");
                            await _next(context);
                            return;
                        }
                    }
                    else
                    {
                        // This is a mobile/WebView request without JWT token
                        // Allow it to proceed - SmartAuthHandler will handle authentication challenge
                        _logger.LogWarning($"MOBILE AUTH MIDDLEWARE: Mobile request without JWT to {context.Request.Path}. Headers: X-Mobile-App={context.Request.Headers["X-Mobile-App"]}, X-From-Mobile-WebView={context.Request.Headers["X-From-Mobile-WebView"]}, Authorization={context.Request.Headers.ContainsKey("Authorization")}");
                        
                        // Set default values for mobile requests
                        windowsUserId = 0;
                        isAdmin = false;
                        
                        // Don't return 401 here - let the authentication framework handle it
                        // This allows proper authentication challenges and error responses
                        _logger.LogInformation($"MOBILE AUTH MIDDLEWARE: Proceeding to authentication handlers for proper challenge handling");
                    }
            }

            if (!string.IsNullOrWhiteSpace(loginIdHeader))
            {
                if (isAdmin)
                {
                    // Admin can impersonate any user
                    _logger.LogInformation($"Admin user {windowsUserId} is impersonating user {loginIdHeader}");
                    // The X-Login-Id header is already set, so we don't need to do anything
                }
                else
                {
                    // Non-admin user, force use of their real ID
                    // For JWT Auth, we use the numeric user ID from claims
                    if (isJwtAuth)
                    {
                        var userIdClaim = context.User.FindFirst("userId") ??
                                        context.User.FindFirst("sub") ??
                                        context.User.FindFirst("nameid");
                        var numericUserId = userIdClaim?.Value;

                        if (!string.IsNullOrEmpty(numericUserId) && loginIdHeader != numericUserId)
                        {
                            _logger.LogWarning($"Non-admin JWT user {context.User?.Identity?.Name} attempted to use X-Login-Id: {loginIdHeader}. Overriding with authenticated numeric ID: {numericUserId}.");
                            context.Request.Headers["X-Login-Id"] = numericUserId;
                        }
                    }
                    else
                    {
                        // For Windows Auth
                        _logger.LogWarning($"Non-admin user {windowsUserId} attempted to use X-Login-Id: {loginIdHeader}. Overriding with real ID.");
                        context.Request.Headers["X-Login-Id"] = windowsUserId.ToString();
                    }
                }
            }
            else
            {
                // No X-Login-Id provided
                if (isJwtAuth)
                {
                    // Use the numeric user ID from JWT claims
                    var userIdClaim = context.User.FindFirst("userId") ??
                                    context.User.FindFirst("sub") ??
                                    context.User.FindFirst("nameid");
                    var numericUserId = userIdClaim?.Value;

                    if (!string.IsNullOrEmpty(numericUserId))
                    {
                        _logger.LogInformation($"No X-Login-Id provided. Using JWT authenticated numeric ID: {numericUserId} for user: {context.User?.Identity?.Name}");
                        context.Request.Headers["X-Login-Id"] = numericUserId;
                    }
                    else
                    {
                        _logger.LogError($"No X-Login-Id provided and no numeric user ID found in JWT claims for user: {context.User?.Identity?.Name}");
                    }
                }
                else if (windowsUserId > 0)
                {
                    // Use Windows authenticated user ID (only if valid)
                    _logger.LogInformation($"No X-Login-Id provided. Using Windows authenticated user ID: {windowsUserId}");
                    context.Request.Headers["X-Login-Id"] = windowsUserId.ToString();
                }
                else
                {
                    // No valid user ID available - this will be handled by authorization
                    _logger.LogWarning($"No X-Login-Id provided and no valid user ID available");
                }
            }

            // Add the final used login ID to the HttpContext.Items for use in controllers if needed
            var finalLoginId = context.Request.Headers["X-Login-Id"].ToString();
            if (!string.IsNullOrEmpty(finalLoginId) && finalLoginId != "0")
            {
                context.Items["UserLoginId"] = finalLoginId;
            }

            await _next(context);
        }

    }

    public static class AuthenticationMiddlewareExtensions
    {
        public static IApplicationBuilder UseAuthenticationMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<AuthenticationMiddleware>();
        }
    }
}