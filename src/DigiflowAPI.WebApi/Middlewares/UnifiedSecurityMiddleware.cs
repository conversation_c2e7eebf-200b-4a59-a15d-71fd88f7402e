﻿using System.Text;
using System.Security.Cryptography;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Services;
using DigiflowAPI.Infrastructure.Security;
using Microsoft.Extensions.Options;
using SecurityConfig = DigiflowAPI.Infrastructure.Configuration.SecurityConfiguration;
using Microsoft.Extensions.Primitives;
using DigiflowAPI.Infrastructure.Services;
using Microsoft.Extensions.DependencyInjection;

namespace DigiflowAPI.WebApi.Middlewares
{
    /// <summary>
    /// Unified security middleware that combines request validation, anti-tampering, and security headers
    /// for improved performance and consolidated security checks
    /// </summary>
    public class UnifiedSecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<UnifiedSecurityMiddleware> _logger;
        private readonly ISecurityEventLoggingService _securityEventLogger;
        private readonly IInputSanitizationService _sanitizationService;
        private readonly SecurityConfig _securityConfig;
        private readonly IOptionsMonitor<RequestSizeLimitOptions> _sizeLimitOptions;
        private readonly HmacSettings _hmacSettings;
        private readonly IServiceProvider _serviceProvider;
        private readonly bool _rateLimitingEnabled;

        // Security header constants
        private const string ContentSecurityPolicy = "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; " +
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " +
            "font-src 'self' https://fonts.gstatic.com data:; " +
            "img-src 'self' data: https: blob:; " +
            "connect-src 'self' ws: wss: https:; " +
            "frame-ancestors 'none'; " +
            "base-uri 'self'; " +
            "form-action 'self'";

        // WebView-friendly CSP that allows embedding in mobile apps
        private const string WebViewContentSecurityPolicy = "default-src 'self'; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; " +
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " +
            "font-src 'self' https://fonts.gstatic.com data:; " +
            "img-src 'self' data: https: blob:; " +
            "connect-src 'self' ws: wss: https:; " +
            "frame-ancestors 'self' capacitor://localhost ionic://localhost; " +
            "base-uri 'self'; " +
            "form-action 'self'";

        // Request validation patterns
        private static readonly string[] SuspiciousPatterns = new[]
        {
            "../", "..\\", "%2e%2e", "%252e%252e", // Path traversal
            "<script", "javascript:", "vbscript:", "onload=", "onerror=", // XSS
            "union select", "or 1=1", "'; drop table", "waitfor delay", // SQL injection
            "cmd.exe", "/bin/bash", "powershell", // Command injection
        };

        // WebView detection patterns
        private static readonly string[] WebViewUserAgents = new[]
        {
            "ReactNative-WebView",
            "DigiHR-App",
            "DigiflowMobile",
            "wv",
            "reactnative"
        };

        private static readonly string[] WebViewHeaders = new[]
        {
            "X-Mobile-App",
            "X-From-Mobile-WebView",
            "X-Is-Mobile"
        };

        public UnifiedSecurityMiddleware(
            RequestDelegate next,
            ILogger<UnifiedSecurityMiddleware> logger,
            ISecurityEventLoggingService securityEventLogger,
            IInputSanitizationService sanitizationService,
            IOptions<SecurityConfig> securityConfig,
            IOptionsMonitor<RequestSizeLimitOptions> sizeLimitOptions,
            IOptions<HmacSettings> hmacSettings,
            IServiceProvider serviceProvider)
        {
            _next = next;
            _logger = logger;
            _securityEventLogger = securityEventLogger;
            _sanitizationService = sanitizationService;
            _securityConfig = securityConfig.Value;
            _sizeLimitOptions = sizeLimitOptions;
            _hmacSettings = hmacSettings.Value;
            _serviceProvider = serviceProvider;
            _rateLimitingEnabled = bool.TryParse(Environment.GetEnvironmentVariable("DIGIFLOW_API_ENABLE_RATE_LIMITING"), out var enabled) && enabled;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var clientIp = GetClientIpAddress(context);
            var requestPath = context.Request.Path.Value;

            try
            {
                // 1. Apply security headers first (always applied to responses)
                ApplySecurityHeaders(context);

                // 2. Skip security checks for allowed paths
                if (IsAllowedPath(requestPath))
                {
                    await _next(context);
                    return;
                }

                // 3. Apply rate limiting if enabled
                if (_rateLimitingEnabled && !await CheckRateLimit(context, clientIp))
                {
                    return;
                }

                // 4. Validate request size
                if (!await ValidateRequestSize(context, clientIp))
                {
                    return;
                }

                // 5. Validate request for security threats
                if (!await ValidateRequest(context, clientIp))
                {
                    return;
                }

                // 6. Verify request integrity (anti-tampering)
                if (_hmacSettings.Enabled && !await VerifyRequestIntegrity(context, clientIp))
                {
                    return;
                }

                // 6. Enhanced security checks for authenticated requests
                if (context.User?.Identity?.IsAuthenticated == true)
                {
                    await PerformAuthenticatedSecurityChecks(context);
                }

                // Process the request
                await _next(context);

                // 7. Sign response if anti-tampering is enabled
                if (_hmacSettings.Enabled)
                {
                    await SignResponse(context);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Security middleware error for path: {Path}", requestPath);
                _securityEventLogger.LogSecurityException(ex, "Security middleware error", new { ClientIp = clientIp, Path = requestPath });
                throw; // Let exception handling middleware deal with it
            }
        }

        private void ApplySecurityHeaders(HttpContext context)
        {
            var headers = context.Response.Headers;
            var path = context.Request.Path.Value;

            // Core security headers
            headers["X-Content-Type-Options"] = "nosniff";

            // X-Frame-Options: Allow webview embedding for mobile apps
            if (IsWebViewRequest(context))
            {
                headers["X-Frame-Options"] = "SAMEORIGIN";
            }
            else
            {
                headers["X-Frame-Options"] = "DENY";
            }

            headers["X-XSS-Protection"] = "1; mode=block";
            headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
            headers["Permissions-Policy"] = "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()";

            // Content Security Policy with nonce for inline scripts
            // Exception: Swagger UI requires 'unsafe-inline' and cannot work with nonces
            if (path?.Contains("/swagger", StringComparison.OrdinalIgnoreCase) == true)
            {
                // Use relaxed CSP for Swagger UI
                headers["Content-Security-Policy"] = ContentSecurityPolicy;
            }
            else if (IsWebViewRequest(context))
            {
                // Use WebView-friendly CSP that allows embedding
                var nonce = GenerateNonce();
                context.Items["CSP-Nonce"] = nonce;
                headers["Content-Security-Policy"] = WebViewContentSecurityPolicy.Replace("'unsafe-inline'", $"'nonce-{nonce}'");
            }
            else
            {
                var nonce = GenerateNonce();
                context.Items["CSP-Nonce"] = nonce;
                headers["Content-Security-Policy"] = ContentSecurityPolicy.Replace("'unsafe-inline'", $"'nonce-{nonce}'");
            }

            // HSTS for HTTPS connections
            if (context.Request.IsHttps)
            {
                headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload";
            }

            // Remove unnecessary headers
            headers.Remove("Server");
            headers.Remove("X-Powered-By");
            headers.Remove("X-AspNet-Version");
        }

        private async Task<bool> ValidateRequestSize(HttpContext context, string clientIp)
        {
            var maxSize = _sizeLimitOptions.CurrentValue?.MaxRequestBodySize ?? 30_000_000; // 30MB default

            if (context.Request.ContentLength > maxSize)
            {
                _logger.LogWarning("Request size limit exceeded from {IP}: {Size} bytes", clientIp, context.Request.ContentLength);
                _securityEventLogger.LogSuspiciousActivity(clientIp, "Request size limit exceeded", $"Size: {context.Request.ContentLength}");

                context.Response.StatusCode = 413; // Payload Too Large
                await context.Response.WriteAsJsonAsync(new { error = "Request size exceeds maximum allowed size" });
                return false;
            }

            return true;
        }

        private async Task<bool> ValidateRequest(HttpContext context, string clientIp)
        {
            var request = context.Request;

            // Validate request path
            if (!IsValidPath(request.Path.Value))
            {
                _logger.LogWarning("Invalid path detected from {IP}: {Path}", clientIp, request.Path);
                _securityEventLogger.LogPathTraversalAttempt(clientIp, request.Path);

                context.Response.StatusCode = 400;
                await context.Response.WriteAsJsonAsync(new { error = "Invalid request path" });
                return false;
            }

            // SECURITY FIX: Check if this is a Windows authentication negotiation request
            // These requests contain legitimate patterns that could trigger false positives
            var authHeader = request.Headers["Authorization"].FirstOrDefault();
            if (authHeader != null && (authHeader.StartsWith("Negotiate", StringComparison.OrdinalIgnoreCase) ||
                                      authHeader.StartsWith("NTLM", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogDebug("Windows authentication negotiation detected, applying relaxed validation");
                // Still check the path but skip deep content validation
                return true;
            }

            // Check for suspicious patterns in various parts of the request
            var suspiciousContent = new List<string>();

            // Check URL
            if (ContainsSuspiciousPattern(request.Path.Value + request.QueryString.Value, out var urlPattern, context))
            {
                suspiciousContent.Add($"URL: {urlPattern}");
            }

            // Check headers - but skip authentication-related and system headers
            var safeHeaders = new[] {
                "Authorization", "WWW-Authenticate", "Negotiate", "NTLM",
                "User-Agent", "Accept", "Accept-Encoding", "Accept-Language",
                "Host", "Connection", "Cache-Control", "Upgrade-Insecure-Requests",
                "Sec-", "X-Forwarded-", "X-Real-IP", "X-Original-",
                "Content-Length", "Content-Type", "Origin", "Referer"
            };

            foreach (var header in request.Headers)
            {
                // Skip safe headers which may contain legitimate patterns
                if (safeHeaders.Any(sh => header.Key.StartsWith(sh, StringComparison.OrdinalIgnoreCase)))
                {
                    continue;
                }

                if (ContainsSuspiciousPattern(header.Value.ToString(), out var headerPattern, context))
                {
                    suspiciousContent.Add($"Header {header.Key}: {headerPattern}");
                }
            }

            // Check body for POST/PUT requests
            if (request.Method == "POST" || request.Method == "PUT" || request.Method == "PATCH")
            {
                if (request.ContentType?.Contains("application/json") == true ||
                    request.ContentType?.Contains("application/xml") == true ||
                    request.ContentType?.Contains("text/") == true)
                {
                    request.EnableBuffering();
                    var body = await ReadRequestBody(request);

                    if (!string.IsNullOrEmpty(body) && ContainsSuspiciousPattern(body, out var bodyPattern, context))
                    {
                        suspiciousContent.Add($"Body: {bodyPattern}");
                    }
                }
            }

            if (suspiciousContent.Any())
            {
                // SECURITY FIX: Add more context to help debug false positives
                _logger.LogWarning("Suspicious patterns detected from {IP}: {Patterns}, Path: {Path}, Method: {Method}, User: {User}",
                    clientIp, string.Join(", ", suspiciousContent), request.Path, request.Method, context.User?.Identity?.Name ?? "Anonymous");

                // Log all headers for debugging (in development only)
                var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                if (env == "Development")
                {
                    _logger.LogDebug("Request headers: {Headers}",
                        string.Join(", ", request.Headers.Select(h => $"{h.Key}={h.Value}")));
                }

                _securityEventLogger.LogSuspiciousActivity(clientIp, "Malicious patterns detected", string.Join("; ", suspiciousContent));

                context.Response.StatusCode = 400;
                await context.Response.WriteAsJsonAsync(new { error = "Request contains invalid content" });
                return false;
            }

            // Mobile request validation
            var hasMobileApp = request.Headers.ContainsKey("X-Mobile-App");
            var hasIsMobile = request.Headers.ContainsKey("X-Is-Mobile");
            var hasFromWebView = request.Headers.ContainsKey("X-From-Mobile-WebView");

            // Log mobile header details for debugging
            if (hasMobileApp || hasIsMobile || hasFromWebView)
            {
                _logger.LogInformation("Mobile headers detected from {IP}: X-Mobile-App={MobileApp} (value={MobileAppValue}), X-Is-Mobile={IsMobile} (value={IsMobileValue}), X-From-Mobile-WebView={WebView} (value={WebViewValue}), Path={Path}",
                    clientIp,
                    hasMobileApp,
                    hasMobileApp ? request.Headers["X-Mobile-App"].ToString() : "N/A",
                    hasIsMobile,
                    hasIsMobile ? request.Headers["X-Is-Mobile"].ToString() : "N/A",
                    hasFromWebView,
                    hasFromWebView ? request.Headers["X-From-Mobile-WebView"].ToString() : "N/A",
                    request.Path);
            }

            // Mobile validation: If any mobile header is present, we consider it a valid mobile request
            // This is more flexible than requiring all three headers
            var isMobileRequest = hasMobileApp || hasIsMobile || hasFromWebView;

            // Only validate if this is explicitly a mobile request
            if (isMobileRequest)
            {
                // Check for common mobile header combinations
                var hasValidMobileHeaders =
                    (hasMobileApp && hasIsMobile) || // Standard mobile app
                    (hasFromWebView) || // WebView requests
                    (hasMobileApp); // Legacy mobile app support

                if (!hasValidMobileHeaders)
                {
                    _logger.LogWarning("Invalid mobile header combination from {IP}: X-Mobile-App={MobileApp}, X-Is-Mobile={IsMobile}, X-From-Mobile-WebView={WebView}",
                        clientIp, hasMobileApp, hasIsMobile, hasFromWebView);
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsJsonAsync(new { error = "Invalid mobile request headers" });
                    return false;
                }

                // Additional validation: check if headers have valid values (not empty)
                if ((hasMobileApp && string.IsNullOrWhiteSpace(request.Headers["X-Mobile-App"])) ||
                    (hasIsMobile && string.IsNullOrWhiteSpace(request.Headers["X-Is-Mobile"])) ||
                    (hasFromWebView && string.IsNullOrWhiteSpace(request.Headers["X-From-Mobile-WebView"])))
                {
                    _logger.LogWarning("Mobile headers with empty values from {IP}", clientIp);
                    context.Response.StatusCode = 400;
                    await context.Response.WriteAsJsonAsync(new { error = "Mobile headers cannot be empty" });
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> VerifyRequestIntegrity(HttpContext context, string clientIp)
        {
            var signature = context.Request.Headers["X-Request-Signature"].FirstOrDefault();
            if (string.IsNullOrEmpty(signature))
            {
                // Allow requests without signature in development
                if (_hmacSettings.RequireSignature)
                {
                    _logger.LogWarning("Missing request signature from {IP}", clientIp);
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsJsonAsync(new { error = "Request signature required" });
                    return false;
                }
                return true;
            }

            // Verify signature
            var timestamp = context.Request.Headers["X-Request-Timestamp"].FirstOrDefault();
            if (string.IsNullOrEmpty(timestamp))
            {
                _logger.LogWarning("Missing request timestamp from {IP}", clientIp);
                context.Response.StatusCode = 400;
                await context.Response.WriteAsJsonAsync(new { error = "Request timestamp required" });
                return false;
            }

            // Check timestamp to prevent replay attacks
            if (DateTimeOffset.TryParse(timestamp, out var requestTime))
            {
                var timeDiff = DateTimeOffset.UtcNow - requestTime;
                if (Math.Abs(timeDiff.TotalMinutes) > 5) // 5 minute window
                {
                    _logger.LogWarning("Request timestamp outside acceptable window from {IP}", clientIp);
                    _securityEventLogger.LogTamperingAttempt(clientIp, context.Request.Path, "Timestamp outside window");
                    context.Response.StatusCode = 401;
                    await context.Response.WriteAsJsonAsync(new { error = "Request timestamp invalid" });
                    return false;
                }
            }

            // Calculate expected signature
            var requestData = $"{context.Request.Method}:{context.Request.Path}{context.Request.QueryString}:{timestamp}";
            if (context.Request.ContentLength > 0)
            {
                context.Request.EnableBuffering();
                var body = await ReadRequestBody(context.Request);
                requestData += $":{body}";
            }

            var expectedSignature = CalculateHmac(requestData);
            if (signature != expectedSignature)
            {
                _logger.LogWarning("Invalid request signature from {IP}", clientIp);
                _securityEventLogger.LogTamperingAttempt(clientIp, context.Request.Path, "Invalid signature");
                context.Response.StatusCode = 401;
                await context.Response.WriteAsJsonAsync(new { error = "Invalid request signature" });
                return false;
            }

            return true;
        }

        private async Task SignResponse(HttpContext context)
        {
            if (context.Response.StatusCode >= 200 && context.Response.StatusCode < 300)
            {
                var timestamp = DateTimeOffset.UtcNow.ToString("O");
                context.Response.Headers["X-Response-Timestamp"] = timestamp;

                // Note: Signing response body requires buffering which can impact performance
                // For now, we'll sign just the status and timestamp
                var responseData = $"{context.Response.StatusCode}:{timestamp}";
                context.Response.Headers["X-Response-Signature"] = CalculateHmac(responseData);
            }
        }

        private async Task PerformAuthenticatedSecurityChecks(HttpContext context)
        {
            var userId = context.User.FindFirst("UserId")?.Value ?? context.User.Identity?.Name;
            var userRoles = context.User.Claims.Where(c => c.Type == "role").Select(c => c.Value).ToList();
            var requestPath = context.Request.Path.Value?.ToLower() ?? "";

            // SECURITY FIX: Comprehensive privilege escalation detection
            var privilegedPaths = new Dictionary<string, string[]>
            {
                // Admin paths requiring admin role
                { "/admin", new[] { "Admin", "SystemAdmin" } },
                { "/management", new[] { "Admin", "SystemAdmin", "Manager" } },
                { "/config", new[] { "Admin", "SystemAdmin" } },
                { "/system", new[] { "Admin", "SystemAdmin" } },

                // User management paths
                { "/api/users/create", new[] { "Admin", "UserManager" } },
                { "/api/users/delete", new[] { "Admin", "UserManager" } },
                { "/api/users/update", new[] { "Admin", "UserManager" } },
                { "/api/users/roles", new[] { "Admin", "UserManager" } },
                { "/api/users/permissions", new[] { "Admin", "UserManager" } },

                // Organization management paths
                { "/api/organizations/create", new[] { "Admin", "OrgManager" } },
                { "/api/organizations/delete", new[] { "Admin", "OrgManager" } },
                { "/api/organizations/update", new[] { "Admin", "OrgManager" } },
                { "/api/organizations/schema", new[] { "Admin", "OrgManager", "SchemaManager" } },

                // Workflow management paths - more permissive for business users
                { "/api/workflows/create", new[] { "Admin", "WorkflowManager", "User" } },
                { "/api/workflows/delete", new[] { "Admin", "WorkflowManager" } },
                { "/api/workflows/approve", new[] { "Admin", "WorkflowManager", "Approver", "User" } },
                { "/api/workflows/reject", new[] { "Admin", "WorkflowManager", "Approver", "User" } },
                { "/api/workflows/forward", new[] { "Admin", "WorkflowManager", "User" } },

                // Security-sensitive paths
                { "/api/auth/config", new[] { "Admin", "SecurityManager" } },
                { "/api/security", new[] { "Admin", "SecurityManager" } },
                { "/api/logs", new[] { "Admin", "SecurityManager", "AuditManager" } },
                { "/api/audit", new[] { "Admin", "SecurityManager", "AuditManager" } },

                // File management paths
                { "/api/file/upload", new[] { "Admin", "FileManager", "User" } },
                { "/api/file/delete", new[] { "Admin", "FileManager" } },
                { "/api/file/download", new[] { "Admin", "FileManager", "User" } },

                // Report paths
                { "/api/reports/sensitive", new[] { "Admin", "ReportManager" } },
                { "/api/reports/financial", new[] { "Admin", "ReportManager", "FinanceManager" } }
            };

            // Check for privilege escalation attempts
            foreach (var privilegedPath in privilegedPaths)
            {
                if (requestPath.Contains(privilegedPath.Key, StringComparison.OrdinalIgnoreCase))
                {
                    var hasRequiredRole = privilegedPath.Value.Any(role => userRoles.Contains(role, StringComparer.OrdinalIgnoreCase));
                    if (!hasRequiredRole)
                    {
                        _logger.LogWarning("Privilege escalation attempt by user {User} with roles {Roles} to path {Path}",
                            userId, string.Join(",", userRoles), context.Request.Path);
                        _securityEventLogger.LogUnauthorizedAccess(userId, context.Request.Path,
                            $"Privilege escalation attempt - Required roles: {string.Join(",", privilegedPath.Value)}");

                        // SECURITY FIX: Return 403 Forbidden for privilege escalation attempts
                        context.Response.StatusCode = 403;
                        context.Response.ContentType = "application/json";
                        await context.Response.WriteAsync("{\"error\":\"Insufficient privileges\",\"code\":\"PRIVILEGE_ESCALATION\"}");
                        return;
                    }
                }
            }

            // SECURITY FIX: Check for suspicious parameter manipulation
            CheckParameterManipulation(context, userId);

            // SECURITY FIX: Check for role injection attempts
            CheckRoleInjection(context, userId);

            // SECURITY FIX: Check for user impersonation attempts
            CheckUserImpersonation(context, userId);

            // Add user tracking headers for audit
            context.Response.Headers["X-User-Id"] = userId;
            context.Response.Headers["X-Request-Id"] = context.TraceIdentifier;
        }

        private void CheckParameterManipulation(HttpContext context, string userId)
        {
            // Check for privilege escalation via parameter manipulation
            var suspiciousParams = new[] { "role", "admin", "privilege", "permission", "access_level", "user_id", "is_admin" };

            foreach (var param in context.Request.Query)
            {
                if (suspiciousParams.Any(sp => param.Key.Contains(sp, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("Suspicious parameter manipulation attempt by user {User}: {Parameter}={Value}",
                        userId, param.Key, param.Value);
                    _securityEventLogger.LogSuspiciousActivity(userId,
                        "Parameter manipulation attempt",
                        $"{param.Key}={param.Value}");
                }
            }
        }

        private void CheckRoleInjection(HttpContext context, string userId)
        {
            // Check headers for role injection attempts
            var suspiciousHeaders = new[] { "X-User-Role", "X-Admin", "X-Privilege", "X-Permission", "X-Access-Level" };

            foreach (var header in context.Request.Headers)
            {
                if (suspiciousHeaders.Any(sh => header.Key.Contains(sh, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("Role injection attempt via header by user {User}: {Header}={Value}",
                        userId, header.Key, header.Value);
                    _securityEventLogger.LogSuspiciousActivity(userId,
                        "Role injection attempt via header",
                        $"{header.Key}={header.Value}");
                }
            }
        }

        private void CheckUserImpersonation(HttpContext context, string userId)
        {
            // Check for user impersonation attempts
            var impersonationHeaders = new[] { "X-Original-User", "X-Impersonate", "X-Act-As", "X-Sudo" };

            foreach (var header in context.Request.Headers)
            {
                if (impersonationHeaders.Any(ih => header.Key.Contains(ih, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("User impersonation attempt by user {User}: {Header}={Value}",
                        userId, header.Key, header.Value);
                    _securityEventLogger.LogSuspiciousActivity(userId,
                        "User impersonation attempt",
                        $"{header.Key}={header.Value}");
                }
            }
        }

        private bool IsAllowedPath(string path)
        {
            var allowedPaths = new[] {
                "/health",
                "/swagger",
                "/api/auth/login",
                "/api/auth/windows",
                "/api/csrf/token",
                "/csrf/token",
                "/api/diagnostics"  // Allow diagnostics endpoints
            };
            return allowedPaths.Any(p => path?.StartsWith(p, StringComparison.OrdinalIgnoreCase) == true);
        }

        private bool IsHighRiskEndpoint(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLower() ?? "";

            // Endpoints that are more likely to interact with database and need SQL injection protection
            var highRiskPaths = new[]
            {
                "/api/search",
                "/api/query",
                "/api/report",
                "/api/data",
                "/api/workflows/search",
                "/api/users/search",
                "/api/organizations/search",
                "/api/histories/search",
                "/api/inbox/search"
            };

            // Also check if the request contains certain query parameters that indicate database operations
            var hasRiskyQueryParams = context.Request.Query.Keys.Any(k =>
                k.Contains("filter", StringComparison.OrdinalIgnoreCase) ||
                k.Contains("where", StringComparison.OrdinalIgnoreCase) ||
                k.Contains("query", StringComparison.OrdinalIgnoreCase) ||
                k.Contains("search", StringComparison.OrdinalIgnoreCase));

            return highRiskPaths.Any(p => path.StartsWith(p, StringComparison.OrdinalIgnoreCase)) || hasRiskyQueryParams;
        }

        private bool IsValidPath(string path)
        {
            if (string.IsNullOrWhiteSpace(path))
                return false;

            // Check for path traversal
            if (path.Contains("..") || path.Contains("//") || path.Contains("\\"))
                return false;

            // Check for suspicious file extensions
            var suspiciousExtensions = new[] { ".exe", ".dll", ".bat", ".cmd", ".ps1", ".sh" };
            if (suspiciousExtensions.Any(ext => path.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
                return false;

            return true;
        }

        private bool ContainsSuspiciousPattern(string input, out string detectedPattern, HttpContext context = null)
        {
            detectedPattern = null;
            if (string.IsNullOrEmpty(input))
                return false;

            var lowerInput = input.ToLowerInvariant();
            foreach (var pattern in SuspiciousPatterns)
            {
                if (lowerInput.Contains(pattern))
                {
                    detectedPattern = pattern;
                    return true;
                }
            }

            // SECURITY FIX: Context-aware SQL injection detection for high-risk endpoints
            // Only check SQL patterns on endpoints that are more likely to interact with databases
            if (context != null && IsHighRiskEndpoint(context) && SqlInjectionProtection.ContainsSqlInjectionPattern(input))
            {
                detectedPattern = "SQL injection pattern";
                return true;
            }

            return false;
        }

        private string GenerateNonce()
        {
            var bytes = new byte[16];
            using (var rng = RandomNumberGenerator.Create())
            {
                rng.GetBytes(bytes);
            }
            return Convert.ToBase64String(bytes);
        }

        private string CalculateHmac(string data)
        {
            using (var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(_hmacSettings.SecretKey)))
            {
                var hash = hmac.ComputeHash(Encoding.UTF8.GetBytes(data));
                return Convert.ToBase64String(hash);
            }
        }

        private async Task<string> ReadRequestBody(HttpRequest request)
        {
            request.Body.Position = 0;
            using (var reader = new StreamReader(request.Body, Encoding.UTF8, leaveOpen: true))
            {
                var body = await reader.ReadToEndAsync();
                request.Body.Position = 0;
                return body;
            }
        }

        private async Task<bool> CheckRateLimit(HttpContext context, string clientIp)
        {
            using (var scope = _serviceProvider.CreateScope())
            {
                var rateLimitService = scope.ServiceProvider.GetService<IDistributedRateLimitService>();
                if (rateLimitService == null)
                {
                    _logger.LogWarning("Rate limiting is enabled but IDistributedRateLimitService is not registered");
                    return true; // Allow request if service is not available
                }

                var endpoint = context.Request.Path.Value?.ToLower() ?? "/";
                var method = context.Request.Method;
                var key = $"{clientIp}:{endpoint}:{method}";

                // Determine rate limit based on endpoint
                var limit = GetRateLimitForEndpoint(endpoint);
                var window = int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_WINDOW") ?? "60");

                var result = await rateLimitService.CheckRateLimitAsync(clientIp, endpoint, limit, window);

                // Check if client is blocked first
                if (await rateLimitService.IsBlockedAsync(clientIp))
                {
                    _logger.LogWarning("Blocked client {IP} attempted to access {Endpoint}", clientIp, endpoint);
                    _securityEventLogger.LogSuspiciousActivity(clientIp, "Blocked client access attempt", $"Endpoint: {endpoint}");

                    context.Response.StatusCode = 429;
                    context.Response.Headers["Retry-After"] = "3600"; // 1 hour
                    await context.Response.WriteAsJsonAsync(new
                    {
                        error = "Access blocked due to repeated violations"
                    });
                    return false;
                }

                if (!result.IsAllowed)
                {
                    _logger.LogWarning("Rate limit exceeded for {IP} on endpoint {Endpoint}", clientIp, endpoint);
                    _securityEventLogger.LogRateLimitExceeded(clientIp, endpoint);

                    // Track violations for auto-blocking
                    await TrackRateLimitViolation(clientIp, rateLimitService);

                    context.Response.StatusCode = 429; // Too Many Requests
                    context.Response.Headers["Retry-After"] = window.ToString();
                    await context.Response.WriteAsJsonAsync(new
                    {
                        error = "Rate limit exceeded",
                        retryAfter = window
                    });
                    return false;
                }

                return true;
            }
        }

        private int GetRateLimitForEndpoint(string endpoint)
        {
            // Check for specific endpoint limits
            if (endpoint.Contains("/auth/login", StringComparison.OrdinalIgnoreCase))
            {
                return int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_LOGIN") ?? "5");
            }
            if (endpoint.Contains("/auth/refresh", StringComparison.OrdinalIgnoreCase))
            {
                return int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_REFRESH") ?? "10");
            }
            if (endpoint.Contains("/upload", StringComparison.OrdinalIgnoreCase) ||
                endpoint.Contains("/file", StringComparison.OrdinalIgnoreCase))
            {
                return int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_UPLOAD") ?? "10");
            }

            // Default rate limit
            return int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_DEFAULT") ?? "100");
        }

        private async Task TrackRateLimitViolation(string clientIp, IDistributedRateLimitService rateLimitService)
        {
            var violationKey = $"violations:{clientIp}";
            var maxViolations = int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_MAX_VIOLATIONS") ?? "10");
            var blockDuration = int.Parse(Environment.GetEnvironmentVariable("DIGIFLOW_API_RATE_LIMIT_BLOCK_DURATION") ?? "30");

            // This is a simplified implementation - in production, you'd track violations in the distributed cache
            // For now, we'll check if we should block based on recent activity
            var shouldBlock = false; // This would be determined by tracking violations

            if (shouldBlock)
            {
                await rateLimitService.BlockClientAsync(clientIp, blockDuration);
                _logger.LogWarning("Client {IP} has been blocked for {Duration} minutes due to repeated violations", clientIp, blockDuration);
                _securityEventLogger.LogSuspiciousActivity(clientIp, "Client blocked", $"Blocked for {blockDuration} minutes after {maxViolations} violations");
            }
        }

        private string GetClientIpAddress(HttpContext context)
        {
            // Check for proxy headers
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',').First().Trim();
            }

            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }

        /// <summary>
        /// Detects if the request is coming from a mobile app WebView
        /// </summary>
        private bool IsWebViewRequest(HttpContext context)
        {
            var userAgent = context.Request.Headers["User-Agent"].ToString().ToLowerInvariant();
            var headers = context.Request.Headers;

            // Check for WebView-specific user agent patterns
            if (WebViewUserAgents.Any(pattern => userAgent.Contains(pattern.ToLowerInvariant())))
            {
                return true;
            }

            // Check for mobile app headers
            if (WebViewHeaders.Any(header => headers.ContainsKey(header)))
            {
                return true;
            }

            // Check for specific mobile app identification headers
            if (headers.ContainsKey("X-Request-Source") &&
                headers["X-Request-Source"].ToString().Contains("DigiHRApp"))
            {
                return true;
            }

            return false;
        }
    }

    public class RequestSizeLimitOptions
    {
        public long? MaxRequestBodySize { get; set; }
    }

    public class HmacSettings
    {
        public bool Enabled { get; set; }
        public bool RequireSignature { get; set; }
        public string SecretKey { get; set; }
    }
}