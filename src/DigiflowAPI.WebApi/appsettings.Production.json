﻿{
  "ConnectionStrings": {
    "Comment": "Connection strings are built dynamically from environment variables by ConnectionStringBuilder.ConfigureConnectionStrings()",
    "DefaultConnection": "",
    "FrameworkConnection": "",
    "DT_WORKFLOW": "",
    "ReportConnection": "",
    "DBSConnection": ""
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft": "Warning",
      "Microsoft.AspNetCore": "Warning",
      "DigiflowAPI": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.AspNetCore.Authentication": "Warning",
        "Microsoft.AspNetCore.Authorization": "Warning",
        "Microsoft.EntityFrameworkCore": "Error",
        "System": "Warning",
        "DigiflowAPI": "Information",
        "DigiflowAPI.Application.Services.HighPerformanceMemoryCache": "Information",
        "DigiflowAPI.Application.Services.DistributedRateLimitService": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "restrictedToMinimumLevel": "Warning",
          "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-webapi-prod-.txt",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 90,
          "fileSizeLimitBytes": 104857600,
          "rollOnFileSizeLimit": true,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{MachineName}] [{ThreadId}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-webapi-prod-errors-.txt",
          "restrictedToMinimumLevel": "Error",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 180,
          "fileSizeLimitBytes": 104857600,
          "rollOnFileSizeLimit": true,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{MachineName}] [{ThreadId}] {Message:lj}{NewLine}{Exception}{NewLine}---{NewLine}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-webapi-audit-.txt",
          "restrictedToMinimumLevel": "Information",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 365,
          "fileSizeLimitBytes": 104857600,
          "rollOnFileSizeLimit": true,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] [{MachineName}] [{ThreadId}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithEnvironmentName"
    ]
  },
  "Security": {
    "EnableWindowsAuthCsrfFallback": true,
    "EnableDoubleSubmitCookie": true,
    "SkipCsrfForJwtApi": false
  },
  "ReactAppOrigins": [
    "https://digiflow.digiturk.com.tr",
    "http://digiflow.digiturk.com.tr",
    "https://digiflow",
    "http://digiflow"
  ],
  "MobileAppOrigins": [
    "http://localhost:8081",
    "http://localhost:19000",
    "http://localhost:19001",
    "http://localhost:19002",
    "exp://localhost:19000",
    "capacitor://localhost",
    "ionic://localhost",
    "http://********:8081",
    "http://***********/24",
    "digihrapp://",
    "file://",
    "about:blank"
  ],
  "AllowedHosts": "digiflow.digiturk.com.tr;digiflowtest.digiturk.com.tr;localhost"
}