﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "DigiflowAPI": "Information"
    },
    "EventLog": {
      "LogLevel": {
        "Default": "Warning",
        "DigiflowAPI.Application.Services.WindowsSecurityMonitoringService": "Information"
      },
      "SourceName": "DigiflowAPI"
    }
  },
  "AllowedHosts": "*",
  "DatabaseProvider": "Oracle",
  "ConnectionStrings": {
    "Comment": "Connection strings are built dynamically from environment variables by ConnectionStringBuilder.ConfigureConnectionStrings()",
    "DefaultConnection": "",
    "FrameworkConnection": "",
    "DT_WORKFLOW": "",
    "ReportConnection": "",
    "DBSConnection": ""
  },
  "Oracle": {
    "TNSAdmin": "/opt/oracle/network/admin",
    "ConnectionPooling": {
      "MinPoolSize": 0,
      "MaxPoolSize": 100,
      "ConnectionLifetime": 0,
      "ConnectionTimeout": 15,
      "IncrPoolSize": 5,
      "DecrPoolSize": 2
    },
    "Performance": {
      "StatementCacheSize": 50,
      "FetchSize": 131072,
      "SelfTuning": true
    }
  },
  "Authentication": {
    "Windows": {
      "Enabled": true,
      "Domain": "DIGITURK",
      "MixedMode": true,
      "MapToOracleUsers": true,
      "AuthenticationTypes": [
        "Negotiate",
        "NTLM"
      ],
      "RequireSSL": true
    },
    "Kerberos": {
      "Enabled": false,
      "Realm": "DIGITURK.LOCAL",
      "KDC": "dc.digiturk.local:88",
      "ServicePrincipalName": "HTTP/digiflowapi.digiturk.local",
      "KeytabPath": "/etc/krb5.keytab"
    }
  },
  "ReactAppOrigins": [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://digiflowtest.digiturk.com.tr",
    "https://digiflowtest.digiturk.com.tr",
    "http://digiflowtest",
    "https://digiflowtest",
    "https://digiflow.digiturk.com.tr",
    "http://digiflow.digiturk.com.tr",
    "https://digiflow",
    "http://digiflow"
  ],
  "MobileAppOrigins": [
    "http://localhost:8081",
    "http://localhost:19000",
    "http://localhost:19001",
    "http://localhost:19002",
    "exp://localhost:19000",
    "capacitor://localhost",
    "ionic://localhost",
    "http://********:8081",
    "http://***********/24"
  ],
  "DOTNET_SYSTEM_GLOBALIZATION_INVARIANT": "0",
  "AppSettings": {
    "ApplicationName": "",
    "aspnet:MaxHttpCollectionKeys": "100000",
    "AssemblyLookupFolders": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\bin",
    "AuthenticateUserOnAnyServiceCall": "true",
    "DefinitionConfigration": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\Definiton.xml",
    "DynamicHistoryWorker": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\History",
    "DynamicInboxWorker": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\Inbox",
    "LogicalGroupDefinition": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\LogicalGroups.xml",
    "StateDefinition": "C:\\TFS\\DigiFlowPM\\Digiturk.Workflow.DigiFlow_v3\\WFPages\\StateDefinition.xml",
    "PageTitle": "Digiturk Is Akislari",
    "ReadConfigFilesFromXML": "false",
    "ServiceName": "ApplicationServer1",
    "SingleInstance": "true",
    "statement_cache.size": "10"
  },
  "ServiceSettings": {
    "IsProxyUsing": true,
    "IsCredentialUsing": true,
    "UserName": "",
    "Password": "",
    "Domain": "DIGITURK",
    "ProxyServicesIp": "http://************:8080",
    "Sharepoint": {
      "BaseUrl": "http://dtl1sp1:20000",
      "DocumentLibrary": "/Digiturk_Dokuman/",
      "ActionPanelUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ActionPanelDocs/",
      "SMSTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SMSTalepDocs/",
      "3555TalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/3555TalepDocs/",
      "BayiTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiTalepDocs/Test/",
      "RaporTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/RaporTalepDocs/",
      "ProjeTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ProjeTalepDocs/",
      "KurumsalTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/KurumsalTalepDocs/",
      "MakroTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/MakroTalepDocs/",
      "OdemeTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OdemeTalepDocs/",
      "OdemeSozlesmeUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OdemeTalepSozlesmeDocs/",
      "SharePointList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/Szleme/",
      "SharePointFinalList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/imzaliSozlesme/",
      "SharePointFlowList": "http://digiflowdocs.digiturk.com.tr/sites/Workflows/FlowDocs/",
      "ReturnRequestUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ReturnRequest/",
      "KurumsalIletisimTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/KurumsalIletisimTalepDocs/",
      "TransmisyonList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/TransmisyonAricaDocs",
      "IseGirisFormList": "http://digiflowdocs.digiturk.com.tr/sites/sozlesme/IseGirisFormDocs",
      "JobEntranceFormList": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/IseGirisFormuDocs",
      "CampaignReqTeklifGorseli": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/CampaignRequestDocs/TeklifGorseli/",
      "CampaignReqLastAnalysis": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/CampaignRequestDocs/SonAnalizDocs/",
      "OperationalWorkRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/OperationalWorkRequestDocs/",
      "ConsultantJobEntranceDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ConsultantJobEntranceDocs/",
      "ProsedurUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ProsedurTalepDocs/",
      "BriefRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BriefRequestDocs/",
      "ActivityRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/ActivityRequestDocs/",
      "FaturaDuzenlemeDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/FaturaDuzenlemeDocs/",
      "AdHocTalepUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/AdHocTalepDocs/",
      "BayiCezaRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiCezaRequestDocs/",
      "BayiKesinHesapDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiKesinHesapDocs/",
      "RotaRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/RotaRequestDocs/",
      "MacroRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/MacroRequestDocs/",
      "EkipmanRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/EkipmanRequestDocs/",
      "PvrLnbRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/PvrLnbRequestDocs/",
      "HavaMuhalefetUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/HavaMuhalefetDocs/",
      "TicariUyeFiyatIstisnaFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/TicariUyeFiyatIstisnaDocs/Test/",
      "SatisKurulumDegerlendirmeFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SatisKurulumDegerlendirmeDocs/",
      "TsMalzemeBirimFiyat": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/TsMalzemeBirimFiyatDocs/Test/",
      "HurdaIhaleDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/HurdaIhaleDocs/Test/",
      "PersonelUploadFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/PersonelTalepDocs/test/",
      "SatisCiroHesaplamaFolder": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SatisCiroHesaplamaDocs/test/",
      "BiFikrimVarDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BiFikrimVarDocs/",
      "ProsedurlerFolder": "http://digiflowdocs.digiturk.com.tr/DIGITURK Formlar/Prosedürler/",
      "InstallationDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SharePointInstallationDocs/",
      "IdeaEvaluationDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/IdeaEvaluationDocs/",
      "EmployeeAppreciationDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/EmployeeAppreciationDocs/",
      "BayiAvansOdemeDoc": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/BayiAvansOdemeDoc/",
      "SatinAlmaSurecleriOnayRequestDocs": "http://digiflowdocs.digiturk.com.tr/DigiFlowDocs/SatinAlmaSurecleriOnayRequestDocs/Test/"
    }
  },
  "Session": {
    "MaxConcurrentSessions": 5,
    "InactivityTimeoutMinutes": 30,
    "AbsoluteTimeoutHours": 8,
    "IdleTimeoutMinutes": 30
  },
  "RequestSizeLimits": {
    "Enabled": true,
    "Default": 5242880,
    "FileUpload": 31457280,
    "FileUploadMultiple": 104857600,
    "Workflow": 1048576,
    "Auth": 102400,
    "Reports": 10485760
  },
  "Kestrel": {
    "Limits": {
      "MaxRequestBodySize": 104857600,
      "MaxRequestHeadersTotalSize": 32768,
      "MaxRequestLineSize": 8192,
      "RequestHeadersTimeoutSeconds": 30,
      "KeepAliveTimeoutMinutes": 2
    }
  },
  "Security": {
    "EnableAntiTampering": false,
    "AntiTampering": {
      "Enabled": true,
      "ValidateRequests": true,
      "SignResponses": true,
      "SecretKey": "",
      "TimestampToleranceMinutes": 5
    },
    "Password": {
      "MinLength": 14,
      "MaxLength": 128,
      "RequireUppercase": true,
      "RequireLowercase": true,
      "RequireDigit": true,
      "RequireSpecialChar": true,
      "MinUniqueChars": 8,
      "PreventCommonPasswords": true,
      "PreventUserInfo": true,
      "HistoryCount": 24,
      "MaxConsecutiveChars": 3,
      "PreventKeyboardPatterns": true,
      "RequirePasswordChangeOnFirstLogin": true,
      "PasswordExpiryDays": 90,
      "PreventPasswordReuseForDays": 365,
      "AccountLockoutThreshold": 5,
      "AccountLockoutDurationMinutes": 30
    },
    "CsrfTokenExpirationHours": 24,
    "MaxCsrfTokensPerUser": 10,
    "EnableCsrfFingerprinting": false,
    "SkipCsrfForJwtApi": false,
    "EnableDoubleSubmitCookie": true,
    "EnableWindowsAuthCsrfFallback": true,
    "AllowedOrigins": [
      "http://localhost:3000",
      "http://localhost:5173",
      "http://localhost:5055",
      "http://digiflowtest.digiturk.com.tr",
      "https://digiflowtest.digiturk.com.tr",
      "http://digiflowtest",
      "https://digiflowtest",
      "https://digiflow.digiturk.com.tr",
      "http://digiflow.digiturk.com.tr",
      "https://digiflow",
      "http://digiflow"
    ],
    "Hmac": {
      "Enabled": false,
      "RequireSignature": false,
      "SecretKey": "${HMAC_SECRET_KEY}",
      "WindowMinutes": 5
    },
    "ContentSecurityPolicy": {
      "Enabled": true,
      "ReportOnly": false,
      "ReportUri": "/reports/csp"
    },
    "Headers": {
      "RemoveServerHeader": true,
      "EnableHSTS": true,
      "HSTSMaxAge": 31536000,
      "HSTSIncludeSubDomains": true,
      "HSTSPreload": true
    }
  },
  "RequestSizeLimit": {
    "MaxRequestBodySize": 31457280,
    "MaxRequestLineSize": 8192,
    "MaxRequestHeadersTotalSize": 1048576
  },
  "CacheProvider": "HighPerformance",
  "HighPerformanceCache": {
    "CleanupIntervalMinutes": 1
  },
  "MemoryCache": {
    "SizeLimit": 1024,
    "CompactionPercentage": 0.25,
    "ExpirationScanFrequency": "00:05:00"
  },
  "RateLimiting": {
    "Enabled": true,
    "DefaultLimit": 100,
    "DefaultWindowSeconds": 60,
    "BlockAfterExceed": true,
    "BlockDurationMinutes": 15,
    "Endpoints": {
      "/api/auth/login": {
        "Limit": 5,
        "WindowSeconds": 300,
        "BlockAfterExceed": true,
        "BlockDurationMinutes": 30
      },
      "/auth/login": {
        "Limit": 5,
        "WindowSeconds": 900,
        "BlockAfterExceed": true,
        "BlockDurationMinutes": 15
      },
      "/mobile/auth/login": {
        "Limit": 5,
        "WindowSeconds": 900,
        "BlockAfterExceed": true,
        "BlockDurationMinutes": 15
      },
      "/api/SmartAuth/login": {
        "Limit": 5,
        "WindowSeconds": 900,
        "BlockAfterExceed": true,
        "BlockDurationMinutes": 15
      },
      "/api/auth/refresh": {
        "Limit": 10,
        "WindowSeconds": 60
      },
      "/auth/refresh": {
        "Limit": 10,
        "WindowSeconds": 60
      },
      "/mobile/auth/refresh": {
        "Limit": 10,
        "WindowSeconds": 60
      },
      "/api/SmartAuth/refresh": {
        "Limit": 10,
        "WindowSeconds": 60
      },
      "/api/Csrf/token": {
        "Limit": 20,
        "WindowSeconds": 60,
        "BlockAfterExceed": true,
        "BlockDurationMinutes": 5
      },
      "/api/workflow/submit": {
        "Limit": 50,
        "WindowSeconds": 60
      },
      "/api/workflow/approve": {
        "Limit": 100,
        "WindowSeconds": 60
      },
      "/api/file/upload": {
        "Limit": 10,
        "WindowSeconds": 300
      },
      "/api/report/generate": {
        "Limit": 5,
        "WindowSeconds": 300
      },
      "/proxy/*": {
        "Limit": 200,
        "WindowSeconds": 60
      }
    }
  },
  "ApiVersioning": {
    "DeprecatedVersions": [
      "0.9"
    ],
    "SunsetDates": {
      "0.9": "2024-06-30T00:00:00Z"
    },
    "DocumentationUrl": "https://api.digiflow.com/docs"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.Hosting.Lifetime": "Information",
        "Microsoft.AspNetCore.Authentication": "Debug",
        "Microsoft.AspNetCore.Authorization": "Debug",
        "DigiflowAPI.WebApi.Middlewares": "Debug",
        "Microsoft.EntityFrameworkCore": "Warning",
        "System": "Warning",
        "DigiflowAPI": "Debug",
        "DigiflowAPI.Application.Services.HighPerformanceMemoryCache": "Debug",
        "DigiflowAPI.Application.Services.DistributedRateLimitService": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-detailed-.txt",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Debug",
          "retainedFileCountLimit": 30,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs/digiflow-mobile-.txt",
          "rollingInterval": "Day",
          "restrictedToMinimumLevel": "Debug",
          "retainedFileCountLimit": 7,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz}] [{Level:u3}] Mobile: {Message:lj}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": [
      "FromLogContext",
      "WithMachineName",
      "WithThreadId",
      "WithEnvironmentName"
    ]
  }
}