using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Negotiate;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using DigiflowAPI.Application.DTOs.Auth;
using Asp.Versioning;
using Microsoft.Extensions.DependencyInjection;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Services;
using System.Security.Cryptography;

namespace DigiflowAPI.WebApi.Controllers
{
    [ApiController]
    [ApiVersion("1.0")]
    [Route("auth")]
    [Route("v{version:apiVersion}/auth")]
    [AllowAnonymous] // Auth endpoints need to be accessible without authentication
    public class AuthController : SecureApiController
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;
        private readonly IGlobalHelpers _globalHelpers;

        public AuthController(IConfiguration configuration, ILogger<AuthController> logger, IGlobalHelpers globalHelpers)
        {
            _configuration = configuration;
            _logger = logger;
            _globalHelpers = globalHelpers;
        }


        /// <summary>
        /// Validate existing JWT token
        /// DISABLED: Authentication validation is now handled transparently by AuthenticationMiddleware
        /// </summary>
        /*
        [HttpGet("validate")]
        [Authorize(AuthenticationSchemes = "Bearer")]
        public IActionResult ValidateToken()
        {
            try
            {
                if (User.Identity?.IsAuthenticated == true)
                {
                    return Ok(new
                    {
                        valid = true,
                        username = User.Identity.Name,
                        authenticationType = User.Identity.AuthenticationType
                    });
                }

                return Unauthorized(new { valid = false, message = "Token is invalid" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating token");
                return Unauthorized(new { valid = false, message = "Token validation failed" });
            }
        }
        */



        /// <summary>
        /// Get current user info
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public IActionResult GetCurrentUser()
        {
            try
            {
                if (User.Identity?.IsAuthenticated == true)
                {
                    return Ok(new
                    {
                        username = User.Identity.Name,
                        authenticationType = User.Identity.AuthenticationType,
                        claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList()
                    });
                }

                return Unauthorized(new { message = "User not authenticated" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user info");
                return StatusCode(500, new { message = "Error retrieving user info" });
            }
        }

        /// <summary>
        /// Verify current authentication session for React webview pages
        /// </summary>
        [HttpGet("verify-session")]
        [Authorize]
        public IActionResult VerifySession()
        {
            try
            {
                if (User.Identity?.IsAuthenticated == true)
                {
                    _logger.LogDebug("Session verification successful for user: {Username}, AuthType: {AuthType}", 
                        User.Identity.Name, User.Identity.AuthenticationType);

                    return Ok(new
                    {
                        authenticated = true,
                        username = User.Identity.Name,
                        authenticationType = User.Identity.AuthenticationType,
                        sessionValid = true
                    });
                }

                _logger.LogWarning("Session verification failed - user not authenticated");
                return Unauthorized(new { 
                    authenticated = false, 
                    sessionValid = false, 
                    message = "Session not valid" 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error verifying session");
                return StatusCode(500, new { 
                    authenticated = false, 
                    sessionValid = false, 
                    message = "Session verification failed" 
                });
            }
        }

        /// <summary>
        /// Get session configuration
        /// </summary>
        [HttpGet("session-config")]
        [Authorize]
        public IActionResult GetSessionConfig()
        {
            var config = new SessionConfigDto
            {
                SessionTimeoutMinutes = _configuration.GetValue<int>("Session:TimeoutMinutes", 30),
                SessionWarningMinutes = _configuration.GetValue<int>("Session:WarningMinutes", 5),
                MaxSessionsPerUser = _configuration.GetValue<int>("Session:MaxPerUser", 5),
                AllowMultipleSessions = _configuration.GetValue<bool>("Session:AllowMultiple", true)
            };

            return Ok(config);
        }

        /// <summary>
        /// Windows authentication endpoint for mobile WebView that generates JWT tokens
        /// </summary>
        [HttpPost("windows")]
        [Authorize(AuthenticationSchemes = NegotiateDefaults.AuthenticationScheme)]
        public async Task<IActionResult> WindowsAuthForMobile()
        {
            try
            {
                // Check if this is a mobile WebView request
                var isMobileWebView = Request.Headers.ContainsKey("X-From-Mobile-WebView") ||
                                     Request.Headers.ContainsKey("X-Mobile-App") ||
                                     Request.Headers.ContainsKey("X-Is-Mobile");
                
                if (!isMobileWebView)
                {
                    _logger.LogWarning("Windows auth endpoint called without mobile headers");
                    return BadRequest(new { error = "This endpoint is for mobile WebView authentication only" });
                }

                // Verify Windows authentication succeeded
                if (!User.Identity?.IsAuthenticated == true || string.IsNullOrEmpty(User.Identity.Name))
                {
                    _logger.LogWarning("Windows authentication failed or user not authenticated");
                    return Unauthorized(new { error = "Windows authentication required" });
                }

                var windowsUsername = User.Identity.Name;
                _logger.LogInformation("Windows auth successful for mobile user: {Username}", windowsUsername);

                // Get JWT configuration service
                var jwtService = HttpContext.RequestServices.GetService<IJwtConfigurationService>();
                if (jwtService == null)
                {
                    _logger.LogError("JWT configuration service not available");
                    return StatusCode(500, new { error = "Authentication service unavailable" });
                }

                // Get user ID from database
                var userService = HttpContext.RequestServices.GetRequiredService<IUserService>();
                var userId = await userService.GetUserInfo();
                
                if (userId == 0)
                {
                    _logger.LogWarning("User not found in database: {Username}", windowsUsername);
                    return Unauthorized(new { error = "User not found" });
                }

                // Generate JWT token
                var tokenResponse = GenerateJwtTokenForMobile(userId.ToString(), windowsUsername, jwtService);
                
                _logger.LogInformation("JWT token generated for mobile user: {Username}, UserId: {UserId}", 
                    windowsUsername, userId);

                return Ok(tokenResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in Windows auth for mobile");
                return StatusCode(500, new { error = "Authentication failed" });
            }
        }

        /// <summary>
        /// Refresh JWT token for mobile
        /// </summary>
        [HttpPost("refresh")]
        [AllowAnonymous]
        public IActionResult RefreshToken([FromBody] RefreshRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request?.RefreshToken))
                {
                    return BadRequest(new { error = "Refresh token required" });
                }

                // Get JWT configuration service
                var jwtService = HttpContext.RequestServices.GetService<IJwtConfigurationService>();
                if (jwtService == null)
                {
                    _logger.LogError("JWT configuration service not available");
                    return StatusCode(500, new { error = "Authentication service unavailable" });
                }

                // TODO: Validate refresh token from storage
                // For now, we'll parse the access token to get user info
                var handler = new JwtSecurityTokenHandler();
                JwtSecurityToken? jsonToken = null;
                
                try
                {
                    // Try to read the token even if expired
                    handler.ValidateToken(request.AccessToken, new TokenValidationParameters
                    {
                        ValidateIssuerSigningKey = true,
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtService.GetSecret())),
                        ValidateIssuer = true,
                        ValidIssuer = jwtService.GetIssuer(),
                        ValidateAudience = true,
                        ValidAudience = jwtService.GetAudience(),
                        ValidateLifetime = false, // Don't validate lifetime for refresh
                        ClockSkew = TimeSpan.Zero
                    }, out var validatedToken);
                    
                    jsonToken = validatedToken as JwtSecurityToken;
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error validating access token for refresh");
                    return Unauthorized(new { error = "Invalid token" });
                }

                if (jsonToken == null)
                {
                    return Unauthorized(new { error = "Invalid token format" });
                }

                var userId = jsonToken.Claims.FirstOrDefault(x => x.Type == "userId")?.Value ?? 
                            jsonToken.Claims.FirstOrDefault(x => x.Type == "sub")?.Value;
                var username = jsonToken.Claims.FirstOrDefault(x => x.Type == "unique_name")?.Value;

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(username))
                {
                    return Unauthorized(new { error = "Invalid token claims" });
                }

                // Generate new tokens
                var tokenResponse = GenerateJwtTokenForMobile(userId, username, jwtService);
                
                _logger.LogInformation("Token refreshed for user: {Username}, UserId: {UserId}", 
                    username, userId);

                return Ok(tokenResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return StatusCode(500, new { error = "Token refresh failed" });
            }
        }

        private object GenerateJwtTokenForMobile(string userId, string username, IJwtConfigurationService jwtService)
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtService.GetSecret()));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            
            var accessTokenMinutes = jwtService.GetAccessTokenMinutes();
            var refreshTokenDays = jwtService.GetRefreshTokenDays();

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, userId),
                new Claim("userId", userId),
                new Claim(JwtRegisteredClaimNames.UniqueName, username),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            };

            var token = new JwtSecurityToken(
                issuer: jwtService.GetIssuer(),
                audience: jwtService.GetAudience(),
                claims: claims,
                notBefore: DateTime.UtcNow,
                expires: DateTime.UtcNow.AddMinutes(accessTokenMinutes),
                signingCredentials: creds
            );

            var accessToken = new JwtSecurityTokenHandler().WriteToken(token);
            var refreshToken = GenerateRefreshToken();

            return new
            {
                accessToken,
                refreshToken,
                tokenType = "Bearer",
                expiresIn = (int)TimeSpan.FromMinutes(accessTokenMinutes).TotalSeconds,
                userId,
                username,
                issuedAt = DateTime.UtcNow,
                // Include token lifetimes for client-side management
                accessTokenMinutes,
                refreshTokenDays,
                // Include auto-refresh settings if enabled
                autoRefreshEnabled = jwtService.IsAutoRefreshOnResumeEnabled(),
                tokenRefreshBufferMinutes = jwtService.GetTokenRefreshBufferMinutes()
            };
        }

        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }

    }
}
