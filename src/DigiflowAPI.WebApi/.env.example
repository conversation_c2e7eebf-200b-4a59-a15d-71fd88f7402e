# DigiflowAPI WebApi Environment Variables Template
# Copy this file to .env.development, .env.test, or .env.production and fill in the values

# Common Digiflow Settings (shared between Web and Mobile APIs)
# Database Configuration
DIGIFLOW_DB_HOST=your_db_host
DIGIFLOW_DB_PORT=1521
DIGIFLOW_DB_SERVICE_NAME=your_service_name

# Framework Database
DIGIFLOW_FRAMEWORK_USER=your_framework_user
DIGIFLOW_FRAMEWORK_PASSWORD=your_framework_password

# Workflow Database
DIGIFLOW_WORKFLOW_USER=your_workflow_user
DIGIFLOW_WORKFLOW_PASSWORD=your_workflow_password

# Application Database
DIGIFLOW_APP_USER=your_app_user
DIGIFLOW_APP_PASSWORD=your_app_password

# Inquiry Database
DIGIFLOW_INQUIRY_USER=your_inquiry_user
DIGIFLOW_INQUIRY_PASSWORD=your_inquiry_password

# Additional Database Users (for legacy compatibility)
DIGIFLOW_DB_ESSB_PASSWORD=your_essb_password
DIGIFLOW_DB_DT_APPLICATION_PASSWORD=your_dt_application_password
DIGIFLOW_DB_DT_WORKFLOW_PASSWORD=your_dt_workflow_password
DIGIFLOW_DB_FRAMEWORK_PASSWORD=your_framework_password
DIGIFLOW_DB_NETSIS_PASSWORD=your_netsis_password
DIGIFLOW_DB_INQUIRY_PASSWORD=your_inquiry_password
DIGIFLOW_DB_SAPERION_PASSWORD=your_saperion_password

# Legacy Database Compatibility (for app.config)
DB_USER=your_framework_user
DB_PASSWORD=your_framework_password
DB_HOST_LIVE=your_live_db_host
DB_PORT_LIVE=1522
DB_SERVICE_LIVE=your_live_service
INQUIRY_HOST=your_inquiry_host
INQUIRY_PORT=1521
INQUIRY_SERVICE=your_inquiry_service
PRODUCTION_CONNECTION_STRING=your_production_connection_string

# JWT Configuration (minimum 32 characters)
DIGIFLOW_JWT_SECRET=your-secret-key-minimum-32-characters
JWT_SECRET=your-secret-key-minimum-32-characters
DIGIFLOW_JWT_KEY=your-secret-key-minimum-32-characters
DIGIFLOW_JWT_ISSUER=DigiflowAPI
DIGIFLOW_JWT_AUDIENCE=DigiflowAPI

# JWT Token Lifetimes
DIGIFLOW_JWT_ACCESS_TOKEN_MINUTES=30
DIGIFLOW_JWT_REFRESH_TOKEN_DAYS=90
DIGIFLOW_JWT_WEBSOCKET_TOKEN_MINUTES=240
DIGIFLOW_MAX_REFRESH_TOKEN_DAYS=180
DIGIFLOW_MAX_REFRESH_TOKENS_PER_USER=5

# Smart Auth Configuration
DIGIFLOW_MOBILE_LONG_TERM_AUTH=true
DIGIFLOW_PASSWORD_CHECK_INTERVAL_HOURS=24
DIGIFLOW_SMART_JWT_ENABLED=true
DIGIFLOW_SILENT_AUTH_ENABLED=true
DIGIFLOW_AUTO_REFRESH_ON_RESUME=true
DIGIFLOW_TOKEN_REFRESH_BUFFER_MINUTES=5

# WebAPI specific JWT settings
DIGIFLOW_API_JWT_ACCESS_TOKEN_MINUTES=30
DIGIFLOW_API_JWT_REFRESH_TOKEN_MINUTES=129600
DIGIFLOW_API_JWT_EXPIRY_MINUTES=43200
DIGIFLOW_API_JWT_REFRESH_EXPIRY_DAYS=14
DIGIFLOW_API_JWT_REFRESH_TOKEN_ROTATION=true
DIGIFLOW_API_JWT_MAX_REFRESH_TOKENS_PER_USER=5

# Environment (Development, Test, Production)
ASPNETCORE_ENVIRONMENT=Development
DIGIFLOW_ENVIRONMENT=Development
ENVIRONMENT=Development

# CORS Configuration (comma-separated)
DIGIFLOW_API_CORS_ALLOWED_ORIGINS=http://localhost:3000
CORS_ALLOWED_ORIGINS=http://localhost:3000
ADDITIONAL_CORS_ORIGINS=

# Mobile API CORS
DIGIFLOW_MOBILE_API_CORS_ALLOWED_ORIGINS=http://localhost:3000

# File Upload Configuration
DIGIFLOW_API_MAX_FILE_SIZE_MB=10
DIGIFLOW_API_ALLOWED_FILE_EXTENSIONS=.pdf,.doc,.docx,.xls,.xlsx,.png,.jpg,.jpeg
DIGIFLOW_API_ENABLE_VIRUS_SCAN=true
DIGIFLOW_API_TEMP_UPLOAD_PATH=/var/digiflow/temp/uploads
DIGIFLOW_API_SECURE_STORAGE_PATH=/var/digiflow/secure/files
DIGIFLOW_VIRUS_SCANNER_PATH=/usr/bin/clamscan

# Workflow Configuration
DIGIFLOW_API_WORKFLOW_STATE_CACHE_MINUTES=60
DIGIFLOW_API_WORKFLOW_DEFAULT_TIMEOUT_SECONDS=300

# Logging
DIGIFLOW_API_LOG_LEVEL=Information
DIGIFLOW_API_LOG_FILE_PATH=./Logs/digiflow-webapi-.txt

# LDAP Configuration
DIGIFLOW_LDAP_PATH=LDAP://your_ldap_server
LDAP_PATH=LDAP://your_ldap_server
DIGIFLOW_LDAP_DOMAIN=your_domain
LDAP_DOMAIN=your_domain
LDAP_USERNAME=your_ldap_username
LDAP_PASSWORD=your_ldap_password

# Email Configuration
DIGIFLOW_API_MAIL_SERVER=your_mail_server
DIGIFLOW_API_MAIL_FROM_ADDRESS=<EMAIL>
DIGIFLOW_API_MAIL_FROM_ADDRESS_USERNAME=your_mail_username
DIGIFLOW_API_MAIL_FROM_ADDRESS_PASSWORD=your_mail_password
DIGIFLOW_API_MAIL_DEBUG_MODE=true
DIGIFLOW_API_MAIL_DEBUG_ADDRESS=<EMAIL>
DIGIFLOW_API_MAIL_LINK_DOMAIN=https://yourapp.com
DIGIFLOW_API_MAIL_EMBEDDED_IMAGES_PATH=/var/yourapp/MailImages

# SharePoint Configuration
DIGIFLOW_API_SHAREPOINT_BASE_URL=http://your_sharepoint:20000
DIGIFLOW_API_SHAREPOINT_DOCUMENT_LIBRARY=/YourCompany_Documents/

# Web Service Configuration
DIGIFLOW_API_WEB_SERVICES_DOMAIN=your_domain
DIGIFLOW_API_WEB_SERVICES_USERNAME=your_web_service_user
DIGIFLOW_API_WEB_SERVICES_PASSWORD=your_web_service_password
DIGIFLOW_API_WEB_SERVICES_USE_PROXY=false
DIGIFLOW_API_WEB_SERVICES_PROXY_URL=http://your_proxy:8080

# Service Settings (for WebServicesConfiguration)
DIGIFLOW_API_SERVICE_USE_PROXY=false
DIGIFLOW_API_SERVICE_USE_CREDENTIAL=true
DIGIFLOW_API_SERVICE_USERNAME=your_service_username
DIGIFLOW_API_SERVICE_PASSWORD=your_service_password
DIGIFLOW_API_SERVICE_DOMAIN=your_domain
DIGIFLOW_API_SERVICE_PROXY_URL=http://your_proxy:8080

# Application Settings
DIGIFLOW_API_APP_NAME=DigiflowAPI
DIGIFLOW_API_APP_DOMAIN=https://yourapp.com/
DIGIFLOW_API_DEBUG_MODE=true
DIGIFLOW_API_COMPATIBILITY_LAYER_URL=http://localhost:56681
DIGIFLOW_API_BASE_URL=https://yourapi.com/api
DIGIFLOW_API_BASE_URL_TEST=https://test.yourapi.com/api
DIGIFLOW_API_BYPASS_SSL=false
DIGIFLOW_API_TIMEOUT=30

# Security Settings
DIGIFLOW_API_SYSTEM_ADMIN_USERS=admin1,admin2
SECURITY_ADMIN_USERS=admin1,admin2
DIGIFLOW_API_IS_SID_CONTROLS=false

# DBS Configuration
DIGIFLOW_API_DBS_INQUIRY_USER=your_dbs_user
DIGIFLOW_API_DBS_INQUIRY_APPSTR=YourApp
DIGIFLOW_API_DBS_INQUIRY_UNIQUE=your_dbs_unique_key
DIGIFLOW_API_DBS_RAPOR_UNIQUE=your_rapor_unique_key
DIGIFLOW_API_DBS_REQUEST_ROW_SIZE=100
# ESSB Password Box usage (E=Enabled, H=Disabled)
DIGIFLOW_API_ESSBDURUM=E

# PasswordBox Application Credentials
DIGIFLOW_API_DBSLIVE_INQUIRY_APPSTR=Digiflow
DIGIFLOW_API_DBSLIVE_INQUIRY_UNIQUE=CHANGE_ME_UNIQUE_KEY
DIGIFLOW_API_DBSTEST_INQUIRY_APPSTR=Digiflow
DIGIFLOW_API_DBSTEST_INQUIRY_UNIQUE=CHANGE_ME_UNIQUE_KEY
DIGIFLOW_API_ITTPTEST_INQUIRY_APPSTR=Digiflow
DIGIFLOW_API_ITTPTEST_INQUIRY_UNIQUE=CHANGE_ME_UNIQUE_KEY
DIGIFLOW_API_SUBSET15_INQUIRY_APPSTR=Digiflow
DIGIFLOW_API_SUBSET15_INQUIRY_UNIQUE=CHANGE_ME_UNIQUE_KEY
DIGIFLOW_API_SUBSET15_RAPOR_APPSTR=Rota
DIGIFLOW_API_SUBSET15_RAPOR_UNIQUE=CHANGE_ME_RAPOR_KEY
DIGIFLOW_API_DBSLIVE_RAPOR_APPSTR=Rota
DIGIFLOW_API_DBSLIVE_RAPOR_UNIQUE=CHANGE_ME_RAPOR_KEY

# Performance Settings
DIGIFLOW_API_HIBERNATE_USE_REFLECTION_OPTIMIZER=false
DIGIFLOW_API_STATEMENT_CACHE_SIZE=10

# External Service URLs
DIGIFLOW_API_AVANS_ENDPOINT=http://your_server:20000/Service.asmx
DIGIFLOW_API_HR_SERVICES_ENDPOINT=http://your_hr_server:3331/PersonelBilgisi.asmx
DIGIFLOW_API_HR_SERVICES_ENDPOINT_MEDIA=http://your_hr_server:3335/PersonelBilgisi.asmx

# Workflow Settings
DIGIFLOW_API_WORKFLOW_ADMIN_GROUPS=1,2,3
DIGIFLOW_API_WORKFLOW_IMAGE_FOLDER=http://your_server:889/users/
DIGIFLOW_API_WORKFLOW_IMAGE_UPLOAD_PATH=/var/yourapp/users/
DIGIFLOW_API_WORKFLOW_MAIL_PARAMS=your_workflow_mail_params

# Feature Flags
DIGIFLOW_API_IS_YYS_ACTIVE=false
DIGIFLOW_API_IS_AVANS_KONTROL_CALISSIN=H
DIGIFLOW_API_UZAKTAN_CALISMA_KONTROL=H

# Education Web Service Credentials
DIGIFLOW_API_EDUCATION_WS_USERNAME=your_education_username
DIGIFLOW_API_EDUCATION_WS_PASSWORD=your_education_password

# Organisational Sale Record Service Credentials
DIGIFLOW_API_ORG_SALE_USERNAME=your_org_sale_username
DIGIFLOW_API_ORG_SALE_PASSWORD=your_org_sale_password
DIGIFLOW_API_ORG_SALE_URL=http://your_org_sale_server/service.svc
DIGIFLOW_API_ORG_SALE_APPLICATION=YourApp
DIGIFLOW_API_ORG_SALE_CHANNEL=DEFAULT
DIGIFLOW_API_ORG_SALE_COMPANY=YourCompany

# Rate Limiting Configuration
DIGIFLOW_API_ENABLE_RATE_LIMITING=true
DIGIFLOW_API_RATE_LIMIT_DEFAULT=100          # Default: 100 requests per minute
DIGIFLOW_API_RATE_LIMIT_LOGIN=5              # Login: 5 attempts per minute
DIGIFLOW_API_RATE_LIMIT_REFRESH=10           # Token refresh: 10 per minute
DIGIFLOW_API_RATE_LIMIT_UPLOAD=10            # File upload: 10 per minute
DIGIFLOW_API_RATE_LIMIT_WINDOW=60            # Window size in seconds (1 minute)
DIGIFLOW_API_RATE_LIMIT_MAX_VIOLATIONS=10    # Block after 10 violations
DIGIFLOW_API_RATE_LIMIT_BLOCK_DURATION=15    # Block duration in minutes

# Redis Configuration (optional - for distributed rate limiting)
DIGIFLOW_REDIS_CONNECTION=localhost:6379,password=yourpassword

# Connection String environment variables (for legacy compatibility)
CONNECTIONSTRING_FRAMEWORKCONNECTION=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_db_host)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=your_service)));User Id=FRAMEWORK;Password=your_framework_password;Pooling=true;Self Tuning=false
CONNECTIONSTRING_WORKFLOWCONNECTION=
CONNECTIONSTRING_DEFAULTCONNECTION=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_db_host)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=your_service)));User Id=DT_APPLICATION_USR;Password=your_app_password;Pooling=true;Self Tuning=false
CONNECTIONSTRING_DT_WORKFLOW=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_db_host)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=your_service)));User Id=DT_WORKFLOW;Password=your_workflow_password;Pooling=true
CONNECTIONSTRING_REPORTCONNECTION=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_db_host)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=your_service)));User Id=DT_WORKFLOW;Password=your_workflow_password;Pooling=true;Self Tuning=false
CONNECTIONSTRING_DBSCONNECTION=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_dbs_host)(PORT=1521))(CONNECT_DATA=(SID=your_dbs_sid)));User Id=INQUIRY;Password=your_inquiry_password;Self Tuning=false
# ESSB Connection Strings
CONNECTIONSTRING_CONNSTRESSB=User Id={0};Password={1};Data Source=(DESCRIPTION = (ADDRESS = (PROTOCOL = TCP)(HOST = your_essb_host)(PORT = 1521)) (CONNECT_DATA = (SERVER = DEDICATED) (SERVICE_NAME = your_essb_service)))
CONNECTIONSTRING_CONNSTRESSB_TEST=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_test_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_test_service)));User Id=ESSB_USR;Password=your_essb_password
CONNECTIONSTRING_CONNSTRESSB_TEST_M8=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_test_m8_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_test_m8_service)));User Id=ESSB_USR;Password=your_essb_password
CONNECTIONSTRING_CONNSTRESSB_LIVE=Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = your_prod_host)(PORT = 1522))(CONNECT_DATA = (SID = your_prod_sid)));User Id=ESSB_USR;Password=your_essb_password
# Database Template Connection Strings
CONNECTIONSTRING_SUBSET15=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_subset_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_subset_service)));User Id={0};Password={1}
CONNECTIONSTRING_DBSLIVE=Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = your_dbslive_host)(PORT = 1522))(CONNECT_DATA = (SID = your_dbslive_sid)));User Id={0};Password={1}
CONNECTIONSTRING_ITTPTEST=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_ittp_host)(PORT=1521))(CONNECT_DATA=(SID=your_ittp_sid)));User Id={0};Password={1}
CONNECTIONSTRING_DBSTEST=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_dbstest_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_dbstest_service)));User Id={0};Password={1}
CONNECTIONSTRING_RAPOR=Data Source=(DESCRIPTION =(ADDRESS = (PROTOCOL = TCP)(HOST = your_rapor_host)(PORT = 1522))(CONNECT_DATA = (SID = your_rapor_sid)));User Id={0};Password={1}
CONNECTIONSTRING_NETSISCONNECTION=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_netsis_host)(PORT=1527))(CONNECT_DATA=(SID=your_netsis_sid)));User Id=NETSISORTAK;Password=your_netsis_password;Pooling=true;Statement Cache Size=10;Self Tuning=false
CONNECTIONSTRING_DBSCONNECTIONMT=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_mt_host)(PORT=1521))(CONNECT_DATA=(SID=your_mt_sid)));User Id=INQUIRY;Password=your_inquiry_password;Self Tuning=false
CONNECTIONSTRING_DBSCONNECTION_M8=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_m8_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_m8_service)));User Id=INQUIRY;Password=your_inquiry_password;Self Tuning=false
CONNECTIONSTRING_SUBSET15_M8=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_m8_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_m8_service)));User Id={0};Password={1}
CONNECTIONSTRING_DBSLIVE_M8=Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=your_dbslive_scan_host)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=your_dbslive_service)));User Id={0};Password={1}
CONNECTIONSTRING_SAPERION=Data Source=your_saperion_host;Initial Catalog=SAPERION;Persist Security Info=True;User ID=saperion;Password=your_saperion_password;Pooling=False;

# Mobile API Database Configuration (for completeness)
DIGIFLOW_MOBILE_DB_CONNECTION=your_mobile_db_connection
DIGIFLOW_MOBILE_DB_DT_WORKFLOW=your_mobile_workflow_connection
DIGIFLOW_MOBILE_DB_FRAMEWORK=your_mobile_framework_connection
DIGIFLOW_MOBILE_DB_CONNECTION_TEST=your_mobile_test_connection
DIGIFLOW_MOBILE_DB_DT_WORKFLOW_TEST=your_mobile_test_workflow_connection
DIGIFLOW_MOBILE_DB_FRAMEWORK_TEST=your_mobile_test_framework_connection

# ESSB Database
DIGIFLOW_ESSB_USER=your_essb_user
DIGIFLOW_ESSB_PASSWORD=your_essb_password

# Netsis Database
DIGIFLOW_NETSIS_USER=your_netsis_user
DIGIFLOW_NETSIS_PASSWORD=your_netsis_password

# Saperion Database
DIGIFLOW_SAPERION_USER=your_saperion_user
DIGIFLOW_SAPERION_PASSWORD=your_saperion_password

# SharePoint Configuration
SHAREPOINT_ACTION_PANEL_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/ActionPanelDocs/
SHAREPOINT_SMS_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/SMSTalepDocs/
SHAREPOINT_3555_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/3555TalepDocs/
SHAREPOINT_BAYI_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/BayiTalepDocs/
SHAREPOINT_RAPOR_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/RaporTalepDocs/
SHAREPOINT_PROJE_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/ProjeTalepDocs/
SHAREPOINT_KURUMSAL_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/KurumsalTalepDocs/
SHAREPOINT_MAKRO_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/MakroTalepDocs/
SHAREPOINT_ODEME_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/OdemeTalepDocs/
SHAREPOINT_ODEME_SOZLESME_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/OdemeTalepSozlesmeDocs/
SHAREPOINT_LIST=http://your_sharepoint_docs/sites/sozlesme/Szleme/
SHAREPOINT_FINAL_LIST=http://your_sharepoint_docs/sites/sozlesme/imzaliSozlesme/
SHAREPOINT_FLOW_LIST=http://your_sharepoint_docs/sites/Workflows/FlowDocs/

# ===== SECURITY CONFIGURATION =====
# Anti-Tampering Security (HMAC)
# SECURITY WARNING: Generate a secure secret minimum 32 characters
# Generate with: openssl rand -base64 32
HMAC_SECRET_KEY=your_anti_tampering_secret_minimum_32_chars
ANTI_TAMPERING_SECRET=your_anti_tampering_secret_minimum_32_chars

# Content Security Policy
DIGIFLOW_API_CSP_ENABLED=true
DIGIFLOW_API_CSP_REPORT_ONLY=false
DIGIFLOW_API_CSP_REPORT_URI=/reports/csp

# Security Headers
DIGIFLOW_API_HSTS_ENABLED=true
DIGIFLOW_API_HSTS_MAX_AGE=31536000
DIGIFLOW_API_HSTS_INCLUDE_SUBDOMAINS=true
DIGIFLOW_API_HSTS_PRELOAD=true

# Request Size Limits
DIGIFLOW_API_MAX_REQUEST_BODY_SIZE=31457280        # 30MB
DIGIFLOW_API_MAX_REQUEST_LINE_SIZE=8192            # 8KB
DIGIFLOW_API_MAX_REQUEST_HEADERS_SIZE=1048576      # 1MB

# Enhanced Logging Options
DIGIFLOW_API_LOG_DETAILED=true
DIGIFLOW_API_LOG_REQUEST_BODY=true
DIGIFLOW_API_LOG_RESPONSE_BODY=false
DIGIFLOW_API_LOG_MAX_BODY_SIZE=4096
DIGIFLOW_API_LOG_SLOW_REQUEST_MS=1000

# Security Event Logging
DIGIFLOW_API_SECURITY_EVENT_LOG_PATH=./Logs/security-events-.txt
DIGIFLOW_API_SECURITY_EVENT_LOG_LEVEL=Information

# CSRF Protection
DIGIFLOW_API_CSRF_TOKEN_NAME=X-CSRF-Token
DIGIFLOW_API_CSRF_COOKIE_NAME=.AspNetCore.Antiforgery
DIGIFLOW_API_CSRF_HEADER_NAME=X-CSRF-Token

# Advanced Security Features
DIGIFLOW_API_ENABLE_REQUEST_SIGNING=false
DIGIFLOW_API_REQUIRE_REQUEST_SIGNATURE=false
DIGIFLOW_API_REQUEST_SIGNATURE_WINDOW_MINUTES=5

# Security Monitoring
DIGIFLOW_API_MONITOR_SUSPICIOUS_ACTIVITY=true
DIGIFLOW_API_BLOCK_AFTER_VIOLATIONS=10
DIGIFLOW_API_BLOCK_DURATION_HOURS=24

# Path Traversal Protection
DIGIFLOW_API_BLOCK_PATH_TRAVERSAL=true
DIGIFLOW_API_ALLOWED_FILE_PATHS=/uploads,/downloads,/temp

# SQL Injection Protection
DIGIFLOW_API_SQL_INJECTION_PROTECTION=true
DIGIFLOW_API_VALIDATE_ALL_INPUTS=true

# XSS Protection
DIGIFLOW_API_XSS_PROTECTION_MODE=block
DIGIFLOW_API_SANITIZE_HTML_INPUTS=true

# Clickjacking Protection
# Note: Changed from DENY to SAMEORIGIN to allow webview embedding for mobile apps
DIGIFLOW_API_X_FRAME_OPTIONS=SAMEORIGIN
DIGIFLOW_API_FRAME_ANCESTORS=self

# SMTP Configuration (alternative names)
DIGIFLOW_SMTP_SERVER=your_smtp_server
DIGIFLOW_SMTP_USERNAME=your_smtp_username
DIGIFLOW_SMTP_PASSWORD=your_smtp_password

# Mobile API Specific JWT Configuration
DIGIFLOW_MOBILE_API_JWT_EXPIRY_MINUTES=30
DIGIFLOW_MOBILE_API_JWT_REFRESH_EXPIRY_DAYS=90
DIGIFLOW_MOBILE_API_JWT_REFRESH_TOKEN_ROTATION=true
DIGIFLOW_MOBILE_API_JWT_MAX_REFRESH_TOKENS_PER_USER=5
DIGIFLOW_MOBILE_API_SMART_AUTH_ENABLED=true
DIGIFLOW_MOBILE_API_MOBILE_LONG_TERM_AUTH=true
DIGIFLOW_MOBILE_API_PASSWORD_CHECK_INTERVAL_HOURS=24
DIGIFLOW_MOBILE_API_SILENT_AUTH_ENABLED=true
DIGIFLOW_MOBILE_API_REQUIRE_HTTPS=false
DIGIFLOW_MOBILE_API_COOKIE_SECURE_POLICY=SameAsRequest

# System Admin Users
SYSTEM_ADMIN_USERS=admin1,admin2,admin3

# Tester Users (semicolon-separated)
TESTER_USERS=DTBSAYGINOZ;DTIAYDIN;DTICETIN;DTNOZKAN;DTTCILINGIR

# Manager Users (semicolon-separated)
MANAGER_USERS=DTAHEPCILINGIRLER;DTCDOGAN;DTGOZTURK;DTITESAYGIVAR;DTTDENIZLERKURDU;ITHMEMIGUVEN

# Additional SharePoint Folders
SHAREPOINT_RETURN_REQUEST_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/ReturnRequest/
SHAREPOINT_KURUMSAL_ILETISIM_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/KurumsalIletisimTalepDocs/
SHAREPOINT_TRANSMISYON_LIST=http://your_sharepoint_docs/sites/sozlesme/TransmisyonAricaDocs
SHAREPOINT_ISE_GIRIS_FORM_LIST=http://your_sharepoint_docs/sites/sozlesme/IseGirisFormDocs
SHAREPOINT_JOB_ENTRANCE_FORM_LIST=http://your_sharepoint_docs/DigiFlowDocs/IseGirisFormuDocs
SHAREPOINT_CAMPAIGN_REQ_TEKLIF_GORSELI=http://your_sharepoint_docs/DigiFlowDocs/CampaignRequestDocs/TeklifGorseli/
SHAREPOINT_CAMPAIGN_REQ_LAST_ANALYSIS=http://your_sharepoint_docs/DigiFlowDocs/CampaignRequestDocs/SonAnalizDocs/
SHAREPOINT_OPERATIONAL_WORK_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/OperationalWorkRequestDocs/
SHAREPOINT_CONSULTANT_JOB_ENTRANCE_DOCS=http://your_sharepoint_docs/DigiFlowDocs/ConsultantJobEntranceDocs/
SHAREPOINT_PROSEDUR_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/ProsedurTalepDocs/
SHAREPOINT_BRIEF_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/BriefRequestDocs/
SHAREPOINT_ACTIVITY_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/ActivityRequestDocs/
SHAREPOINT_FATURA_DUZENLEME_DOCS=http://your_sharepoint_docs/DigiFlowDocs/FaturaDuzenlemeDocs/
SHAREPOINT_ADHOC_TALEP_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/AdHocTalepDocs/
SHAREPOINT_BAYI_CEZA_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/BayiCezaRequestDocs/
SHAREPOINT_BAYI_KESIN_HESAP_DOCS=http://your_sharepoint_docs/DigiFlowDocs/BayiKesinHesapDocs/
SHAREPOINT_ROTA_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/RotaRequestDocs/
SHAREPOINT_MACRO_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/MacroRequestDocs/
SHAREPOINT_EKIPMAN_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/EkipmanRequestDocs/
SHAREPOINT_PVR_LNB_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/PvrLnbRequestDocs/
SHAREPOINT_HAVA_MUHALEFET_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/HavaMuhalefetDocs/
SHAREPOINT_TICARI_FIYAT_ISTISNA_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/TicariUyeFiyatIstisnaDocs/
SHAREPOINT_SATIS_KURULUM_DEGERLENDIRME_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/SatisKurulumDegerlendirmeDocs/
SHAREPOINT_TS_MALZEME_BIRIM_FIYAT=http://your_sharepoint_docs/DigiFlowDocs/TsMalzemeBirimFiyatDocs/
SHAREPOINT_HURDA_IHALE_DOCS=http://your_sharepoint_docs/DigiFlowDocs/HurdaIhaleDocs/
SHAREPOINT_PERSONEL_UPLOAD_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/PersonelTalepDocs/
SHAREPOINT_SATIS_CIRO_HESAPLAMA_FOLDER=http://your_sharepoint_docs/DigiFlowDocs/SatisCiroHesaplamaDocs/
SHAREPOINT_BI_FIKRIM_VAR_DOCS=http://your_sharepoint_docs/DigiFlowDocs/BiFikrimVarDocs/
SHAREPOINT_PROSEDURLER=http://your_sharepoint_docs/YourCompany Formlar/Prosedurler/
SHAREPOINT_INSTALLATION_DOCS=http://your_sharepoint_docs/DigiFlowDocs/SharePointInstallationDocs/
SHAREPOINT_IDEA_EVALUATION_DOCS=http://your_sharepoint_docs/DigiFlowDocs/IdeaEvaluationDocs/
SHAREPOINT_EMPLOYEE_APPRECIATION_DOCS=http://your_sharepoint_docs/DigiFlowDocs/EmployeeAppreciationDocs/
SHAREPOINT_BAYI_AVANS_ODEME_DOC=http://your_sharepoint_docs/DigiFlowDocs/BayiAvansOdemeDoc/
SATIN_ALMA_SURECLERI_ONAY_REQUEST_DOCS=http://your_sharepoint_docs/DigiFlowDocs/SatinAlmaSurecleriOnayRequestDocs/

# Environment-specific configuration
DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false