import axios, { AxiosError, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { toast } from 'react-hot-toast'
import i18n from './i18n'
import config from './config'
import csrfService from './services/csrfService'
import { isInWebView as isInWebViewNew, getWebViewSessionId, notifyWebViewAuthError } from './utils/webViewDetection'
import { apiCacheService, clearApiCacheOnLogout } from './services/apiCacheService'
import { rateLimitService } from './services/rateLimitService'

// Add window type extension for secure WebView integration
declare global {
  interface Window {
    // For React Native WebView integration
    ReactNativeWebView?: {
      postMessage: (msg: string) => void
    }
    // Secure session ID for WebView communication
    SECURE_SESSION_ID?: string
    // Debug functions for WebView
    debugAuthenticationState?: () => Promise<unknown>
    debugStorageState?: () => void
  }
}

let apiUrl = config.VITE_API_URL

const { protocol } = window.location // http: or https:
if (apiUrl.split('://')[0] != protocol) {
  apiUrl = `${protocol}//${apiUrl.split('://')[1]}`
}

// For test/production servers, ensure /api is in the path
if (window.location.hostname == 'digiflowtest' || window.location.hostname == 'digiflow') {
  // If the URL doesn't already end with /api, add it
  if (!apiUrl.endsWith('/api')) {
    apiUrl = `${apiUrl.replace(/\/$/, '')}/api`
  }
}

const api = axios.create({
  baseURL: apiUrl,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
    'X-DigiflowReact': 'true', // Identify as React client
    'X-Auth-Provider': config.VITE_AUTH_PROVIDER ?? 'standard', // Windows auth indicator
    'X-Auth-Domain': config.VITE_AUTH_DOMAIN ?? '', // Domain for Windows auth
  },
})

const getWfInstanceIdFromUrl = () => new URLSearchParams(window.location.search).get('wfInstanceId')

const showErrorToast = (message: string) => {
  toast.dismiss()
  toast.error(message, {
    duration: 3000,
    style: { border: '1px solid #ff4d4f', padding: '16px', color: '#ff4d4f' },
    iconTheme: { primary: '#ff4d4f', secondary: '#FFFAEE' },
  })
}

// Use the secure WebView detection from our utility
const isInWebView = isInWebViewNew

// SECURITY: JWT tokens are no longer stored client-side
// Authentication is handled via httpOnly cookies set by the server
// For WebView, secure session IDs are used instead of JWT tokens

// Use the secure session ID retrieval from our utility
const getSecureSessionId = getWebViewSessionId

// SECURITY FIX: Use secure session ID for WebView communication
if (isInWebView()) {
  // Request secure session establishment from mobile app
  if (window.ReactNativeWebView) {
    window.ReactNativeWebView.postMessage(
      JSON.stringify({
        type: 'REQUEST_SECURE_SESSION',
      }),
    )
  }
}

// Clear any legacy tokens that might exist in storage
const clearLegacyTokens = () => {
  try {
    // Clear from storage only - no window token references
    // Note: Don't clear sessionStorage jwt_token as we use it for valid JWTs
    // sessionStorage.removeItem('jwt_token') // Commented out - we need this for JWT auth
    localStorage.removeItem('jwt_token') // Clear localStorage version as we use sessionStorage
    localStorage.removeItem('auth_token')

    if (process.env.NODE_ENV === 'development') {
      console.info('Legacy tokens cleared for security')
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Error clearing legacy tokens:', error)
    }
  }
}

// Clear legacy tokens on load
clearLegacyTokens()

api.interceptors.request.use(async (config: InternalAxiosRequestConfig): Promise<InternalAxiosRequestConfig> => {
  // Get userId from secure session or user store instead of localStorage
  const userStore = await import('@/stores/userStore').then((m) => m.useUserStore.getState())
  const userId = userStore.selectedUser?.value
  const language = localStorage.getItem('language') ?? 'en'

  // Detect if we're in a WebView
  const isWebView = isInWebView()

  // Get secure session ID for WebView authentication
  const secureSessionId = getSecureSessionId()

  // Development-only logging (no sensitive data)
  if (process.env.NODE_ENV === 'development') {
    console.info('API Request:', {
      url: config.url,
      method: config.method?.toUpperCase(),
      hasSecureSession: isWebView && !!secureSessionId,
      isWebView,
    })
  }

  // Initialize headers if not present, but don't overwrite existing ones
  config.headers = config.headers ?? {}

  // CRITICAL: Clean up any existing duplicate headers that might have been set by WebView
  // Check for and clean duplicate values in common headers
  const cleanHeader = (headerName: string) => {
    const value = config.headers[headerName]
    if (value && typeof value === 'string' && value.includes(',')) {
      // If header contains comma, it might be duplicated - take first value
      const firstValue = value.split(',')[0].trim()
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Detected duplicate header ${headerName}: "${value}" - using first value: "${firstValue}"`)
      }
      config.headers[headerName] = firstValue
    }
  }

  // Clean critical headers that are known to be duplicated
  cleanHeader('Authorization')
  cleanHeader('X-Auth-Scheme')
  cleanHeader('X-Login-Id')
  cleanHeader('X-Debug-Log')
  cleanHeader('X-Secure-Session')

  // CRITICAL: Prevent duplicate headers by checking if they already exist
  // Only set headers if they haven't been set already
  config.headers['Accept-Language'] ??= language

  if (isWebView) {
    if (process.env.NODE_ENV === 'development') {
      console.info('Request from WebView detected')
    }

    // Add mobile/WebView headers - only if not already present
    config.headers['X-Mobile-App'] ??= 'true'
    config.headers['X-Is-Mobile'] ??= 'true'
    config.headers['X-From-Mobile-WebView'] ??= 'true'
    config.headers['X-Auth-Scheme'] ??= 'SecureSession' // Use secure session authentication
    config.headers['X-Debug-Log'] ??= 'true' // Enable detailed server-side logging

    // If we have a JWT token from a previous auth, include it
    const storedJwt = sessionStorage.getItem('webview_jwt')
    if (storedJwt && !config.headers['Authorization']) {
      config.headers['Authorization'] = `Bearer ${storedJwt}`
    }

    // Add React app identifier - only if not already present
    config.headers['X-DigiflowReact'] ??= 'true'

    // CRITICAL: Add X-Login-Id header for WebView requests - only if not already present
    // CRITICAL: Add X-Login-Id header for WebView requests - only if not already present
    if (userId && userId !== 'undefined' && !config.headers['X-Login-Id']) {
      config.headers['X-Login-Id'] = userId // Use consistent lowercase 'd'
      if (process.env.NODE_ENV === 'development') {
        console.info('Added X-Login-Id header for WebView:', userId)
      }
    } else if (!userId || userId === 'undefined') {
      if (process.env.NODE_ENV === 'development') {
        console.warn('No UserId available for WebView request')
      }
    }

    // Add secure session ID for WebView authentication
    if (secureSessionId && !config.headers['X-Secure-Session']) {
      config.headers['X-Secure-Session'] = secureSessionId
      if (process.env.NODE_ENV === 'development') {
        console.info('Added X-Secure-Session header for WebView')
      }
    }

    // WebView needs credentials for cookies
    // This allows CSRF cookies to work properly
    config.withCredentials = true
  } else {
    // Regular web request - use JWT token if available, otherwise Windows auth
    config.headers['X-DigiflowReact'] ??= 'true' // Always identify as React client

    // Add user ID for web requests (consistent header naming) - only if not already present
    if (userId && userId !== 'undefined' && !config.headers['X-Login-Id']) {
      config.headers['X-Login-Id'] = userId // Use consistent lowercase 'd'
    }

    // Check if we have a JWT token from previous authentication
    const storedJwt = sessionStorage.getItem('jwt_token')
    if (storedJwt && !config.headers['Authorization']) {
      // Validate that the JWT has the required userId claim
      try {
        const base64Url = storedJwt.split('.')[1]
        const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
        const jsonPayload = decodeURIComponent(
          atob(base64)
            .split('')
            .map((c) => {
              return `%${`00${c.charCodeAt(0).toString(16)}`.slice(-2)}`
            })
            .join(''),
        )
        const claims = JSON.parse(jsonPayload)

        if (!claims.userId && !claims.LoginId) {
          // Token doesn't have required claims, clear it and force re-auth
          if (process.env.NODE_ENV === 'development') {
            console.warn('JWT token missing userId/LoginId claims, clearing for re-authentication')
            console.warn('JWT claims found:', Object.keys(claims))
            console.warn('userId claim:', claims.userId)
            console.warn('LoginId claim:', claims.LoginId)
          }
          sessionStorage.removeItem('jwt_token')
          sessionStorage.removeItem('auth_username')
          delete api.defaults.headers.common['Authorization']

          // Trigger re-authentication
          if (window.location.pathname !== '/login') {
            window.location.reload()
          }
          return config
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.info('JWT token validation passed - has userId:', !!claims.userId, 'has LoginId:', !!claims.LoginId)
          }
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Failed to decode JWT token:', error)
        }
      }

      config.headers['Authorization'] = `Bearer ${storedJwt}`

      if (process.env.NODE_ENV === 'development') {
        console.info('Using stored JWT token for authentication')
      }
    }

    // Ensure credentials are included for httpOnly cookies
    config.withCredentials = true
  }

  void i18n.changeLanguage(language)

  if (config.url?.startsWith('workflows') || config.url?.startsWith('/workflows')) {
    const wfInstanceId = getWfInstanceIdFromUrl()
    if (wfInstanceId && !config.headers['X-Workflow-Instance-Id']) {
      config.headers['X-Workflow-Instance-Id'] = wfInstanceId
    }
  }

  if (process.env.NODE_ENV === 'development') {
    console.info('Final request headers:', {
      method: config.method,
      url: config.url,
      hasAuth: !!config.headers?.['Authorization'],
      authScheme: config.headers?.['X-Auth-Scheme'],
      isWebView,
    })
  }

  // Add CSRF token for state-changing requests
  const updatedConfig = (await csrfService.addTokenToRequest(config)) as InternalAxiosRequestConfig

  return updatedConfig
})

api.interceptors.response.use(
  (response: AxiosResponse) => {
    // No longer expect JWT tokens in response headers
    // Server handles authentication transparently

    return response
  },
  async (error: AxiosError) => {
    let errorMessage = i18n.t('errors:unexpectedError')

    // Check if this is a WebView request
    const isWebView = isInWebView()

    // Enhanced error analysis for better categorization
    const isNetworkError =
      !error.response &&
      (error.code === 'ERR_NETWORK' ||
        error.code === 'ECONNABORTED' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ECONNREFUSED' ||
        !navigator.onLine)

    const isServiceUnavailable = error.response?.status && [502, 503, 504].includes(error.response.status)
    const isTimeout = error.code === 'ECONNABORTED'

    // Distinguish between real auth errors and service unavailable
    const isRealAuthError =
      error.response?.status === 401 &&
      error.response.headers &&
      Object.keys(error.response.headers).length > 0 && // We got a response from server
      !isServiceUnavailable &&
      !isNetworkError

    // If we get a 401 but with network-like symptoms, treat as service unavailable
    const isServiceUnavailableWith401 =
      error.response?.status === 401 &&
      (!error.response.headers ||
        Object.keys(error.response.headers).length === 0 || // No proper response headers
        isNetworkError ||
        isServiceUnavailable)

    if (process.env.NODE_ENV === 'development') {
      console.info('Error analysis:', {
        isNetworkError,
        isServiceUnavailable,
        isTimeout,
        isRealAuthError,
        isServiceUnavailableWith401,
        hasResponse: !!error.response,
        status: error.response?.status,
        code: error.code,
      })
    }

    // Handle authentication errors (only real 401s from working backend)
    if (isRealAuthError) {
      if (isWebView) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Authentication error in WebView:', error.response?.status)
        }

        // Notify WebView about authentication error using secure communication
        notifyWebViewAuthError(error.config?.url ?? '')

        // Clear the current token from storage as it's invalid
        if (process.env.NODE_ENV === 'development') {
          console.info('Clearing invalid JWT token')
        }
        sessionStorage.removeItem('webview_jwt')
        delete api.defaults.headers.common['Authorization']
        // Removed localStorage.removeItem('jwt_token')
        // Removed localStorage.removeItem('jwtToken')

        // Don't show toast for auth errors in WebView
        return Promise.reject(
          Object.assign(error, {
            isAuthenticationError: true,
          }),
        )
      } else {
        // For web requests, server handles authentication transparently
        // Don't clear tokens or treat 401 specially
        if (process.env.NODE_ENV === 'development') {
          console.info('401 error - server will handle authentication transparently')
        }

        // Just pass the error through
        return Promise.reject(error)
      }
    }

    // Handle network/service unavailable errors (including 401s that are really service issues)
    if (isNetworkError || isServiceUnavailable || isTimeout || isServiceUnavailableWith401) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('Service unavailable:', {
          isNetworkError,
          isServiceUnavailable,
          isTimeout,
          status: error.response?.status,
          code: error.code,
        })
      }

      // Skip toast notifications for WebView requests
      if (isWebView) {
        if (window.ReactNativeWebView) {
          window.ReactNativeWebView.postMessage(
            JSON.stringify({
              type: 'SERVICE_ERROR',
              status: error.response?.status ?? 0,
              code: error.code,
              message: 'Service unavailable',
              isNetworkError,
              isTimeout,
            }),
          )
        }
        return Promise.reject(
          Object.assign(error, {
            isServiceUnavailable: true,
            errorType: !navigator.onLine ? 'offline' : isTimeout ? 'timeout' : 'network',
          }),
        )
      }

      // For web requests, mark the error appropriately but don't show toast
      const errorType = !navigator.onLine ? 'offline' : isTimeout ? 'timeout' : isServiceUnavailable ? 'server' : 'network'

      return Promise.reject(
        Object.assign(error, {
          isServiceUnavailable: true,
          errorType,
        }),
      )
    }

    // Skip toast notifications for WebView requests
    if (isWebView) {
      return Promise.reject(error)
    }

    // Handle other errors for regular web requests (still show toast for these)
    if (error.response) {
      const { status, data } = error.response as any
      if (status === 400 && data.errors) {
        errorMessage = Object.values(data.errors).flat().join('\n')
      } else {
        errorMessage = i18n.t('errors:apiError', {
          status,
          message: data.message ?? error.response.statusText,
        })
      }
    } else if (error.request) {
      errorMessage = i18n.t('errors:noResponse')
    }

    showErrorToast(errorMessage)
    return Promise.reject(error)
  },
)

// Helper function to add workflow definition ID to config
const addDefinitionId = (config: AxiosRequestConfig = {}, definitionId?: string): AxiosRequestConfig => {
  if (definitionId) {
    config.headers = {
      ...config.headers,
      'X-Workflow-Definition-Id': definitionId,
    }
  }
  return config
}

// Type definitions for our enhanced methods

type EnhancedGet = <T = unknown>(url: string, config?: AxiosRequestConfig, definitionId?: string) => Promise<AxiosResponse<T>>

type EnhancedPost = <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig, definitionId?: string) => Promise<AxiosResponse<T>>

type EnhancedPut = <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig, definitionId?: string) => Promise<AxiosResponse<T>>

type EnhancedDelete = <T = unknown>(url: string, config?: AxiosRequestConfig, definitionId?: string) => Promise<AxiosResponse<T>>

// Enhanced API with caching and rate limiting support
const enhancedApi = {
  get: ((url: string, configOrDefinitionId?: AxiosRequestConfig | string, definitionId?: string) => {
    const finalConfig =
      typeof configOrDefinitionId === 'string' ? addDefinitionId({}, configOrDefinitionId) : addDefinitionId(configOrDefinitionId, definitionId)

    // Use cache service for GET requests with rate limiting
    return rateLimitService.executeWithRateLimit({ ...finalConfig, url, method: 'GET' }, () =>
      apiCacheService.getCachedOrDeduplicated({ ...finalConfig, url, method: 'GET' }, () => api.get(url, finalConfig)),
    )
  }) as EnhancedGet,

  post: ((url: string, data?: unknown, configOrDefinitionId?: AxiosRequestConfig | string, definitionId?: string) => {
    const finalConfig =
      typeof configOrDefinitionId === 'string' ? addDefinitionId({}, configOrDefinitionId) : addDefinitionId(configOrDefinitionId, definitionId)

    // Apply rate limiting to POST requests
    return rateLimitService.executeWithRateLimit({ ...finalConfig, url, method: 'POST', data }, () => api.post(url, data, finalConfig))
  }) as EnhancedPost,

  put: ((url: string, data?: unknown, configOrDefinitionId?: AxiosRequestConfig | string, definitionId?: string) => {
    const finalConfig =
      typeof configOrDefinitionId === 'string' ? addDefinitionId({}, configOrDefinitionId) : addDefinitionId(configOrDefinitionId, definitionId)

    // Apply rate limiting to PUT requests
    return rateLimitService.executeWithRateLimit({ ...finalConfig, url, method: 'PUT', data }, () => api.put(url, data, finalConfig))
  }) as EnhancedPut,

  delete: ((url: string, configOrDefinitionId?: AxiosRequestConfig | string, definitionId?: string) => {
    const finalConfig =
      typeof configOrDefinitionId === 'string' ? addDefinitionId({}, configOrDefinitionId) : addDefinitionId(configOrDefinitionId, definitionId)

    // Apply rate limiting to DELETE requests
    return rateLimitService.executeWithRateLimit({ ...finalConfig, url, method: 'DELETE' }, () => api.delete(url, finalConfig))
  }) as EnhancedDelete,
}

// Proxy to allow both original and enhanced usage
const apiProxy = new Proxy(api, {
  get(target, prop) {
    if (prop in enhancedApi) {
      return (enhancedApi as any)[prop]
    }
    return target[prop as keyof typeof target]
  },
}) as typeof api & typeof enhancedApi

// Function to update JWT token for WebView
// Removed updateJwtToken function as client-side token handling is removed

// Initialize JWT token on load
if (typeof window !== 'undefined') {
  try {
    // Removed getJwtToken()
    // Removed isInWebView() check
    // Removed window.JWT_TOKEN = token
    // Removed api.defaults.headers.common['Authorization'] = `Bearer ${token}`
    // SECURITY: Tokens should be in httpOnly cookies set by the server
    // Do not store JWT in localStorage to prevent XSS attacks
  } catch {
    // Removed console.error for security
  }
}

// Function to clear all authentication data
const clearAuthData = () => {
  if (typeof window !== 'undefined') {
    if (process.env.NODE_ENV === 'development') {
      // Development logging removed for security
    }

    // Clear window JWT token
    // Removed delete window.JWT_TOKEN

    // Clear any localStorage tokens (for cleanup)
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
    localStorage.removeItem('UserId')
    // Removed localStorage.removeItem('jwt_token')

    // Clear axios default headers
    if (api.defaults.headers && api.defaults.headers.common) {
      delete api.defaults.headers.common['Authorization']
    }

    // Clear API cache on logout
    clearApiCacheOnLogout()

    // Reset rate limits on logout
    rateLimitService.reset()

    // Notify WebView if available
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(
        JSON.stringify({
          type: 'CACHE_CLEARED',
          success: true,
        }),
      )
    }

    // Removed console.log for security
    return true
  }
  return false
}

// Function to debug storage state (for WebView debugging)
const debugStorageState = async () => {
  if (typeof window !== 'undefined') {
    const userStore = await import('@/stores/userStore').then((m) => m.useUserStore.getState())
    const storageData = {
      userId: userStore.selectedUser?.value ?? 'No user selected',
      // Removed jwtToken: localStorage.getItem('jwt_token')
      userStorage: 'Using secure session state',
      // Removed windowJwtToken: window.JWT_TOKEN
      isWebView: isInWebView(),
      userAgent: navigator.userAgent,
    }

    if (process.env.NODE_ENV === 'development') {
      console.info('Storage Debug:', storageData)
    }

    // Notify WebView if available
    if (window.ReactNativeWebView) {
      window.ReactNativeWebView.postMessage(
        JSON.stringify({
          type: 'STORAGE_DEBUG',
          data: storageData,
        }),
      )
    }

    return storageData
  }
  return null
}

// Debug function to test authentication state and API connectivity
const debugAuthenticationState = async () => {
  if (process.env.NODE_ENV === 'development') {
    console.info('=== Authentication State Debug ===')
  }

  const isWebView = isInWebView()
  // Get userId from secure user store
  const userStore = await import('@/stores/userStore').then((m) => m.useUserStore.getState())
  const userId = userStore.selectedUser?.value

  // Removed console.log for security - was logging environment details

  // Removed console.log for security - was logging localStorage contents

  // Removed console.log for security - was logging window.JWT_TOKEN

  // Removed token validity check

  return {
    isWebView,
    hasJwtToken: false, // No client-side token
    userId,
    jwtValid: false, // No client-side token
  }
}

// Expose functions to window for WebView access
if (typeof window !== 'undefined') {
  // Removed ;(window as any).updateJwtToken = updateJwtToken
  ;(window as any).isInWebView = isInWebView
  // Removed ;(window as any).getJwtToken = getJwtToken
  ;(window as any).clearAuthData = clearAuthData
  ;(window as any).debugStorageState = debugStorageState
  window.debugAuthenticationState = debugAuthenticationState
}

// Export the isInWebView function for use in components
export { isInWebView }

// Export cache utilities for use in components
export { clearApiCacheOnLogout, clearResourceCache } from './services/apiCacheService'

// Export rate limit service for monitoring
export { rateLimitService } from './services/rateLimitService'

export default apiProxy
