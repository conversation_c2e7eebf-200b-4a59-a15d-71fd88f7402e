﻿using System.Text.Json;
using DigiflowAPI.Application.Interfaces.Services;
using DigiflowAPI.Application.Services;
using DigiflowAPI.Infrastructure.Security;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using InfraSecurityConfig = DigiflowAPI.Infrastructure.Configuration.SecurityConfiguration;

namespace DigiflowAPI.MobileApi.Middlewares
{
    /// <summary>
    /// Unified security middleware for DigiflowMobileAPI that combines:
    /// - Security headers
    /// - Input validation
    /// - CSRF protection
    /// - Request size limits
    /// - Anti-tampering (optional)
    /// </summary>
    public class UnifiedSecurityMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<UnifiedSecurityMiddleware> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly MobileSecurityConfiguration _securityConfig;
        private readonly ISecurityMonitoringService _securityMonitoring;
        private readonly IInputSanitizationService _sanitizationService;

        // Comprehensive suspicious patterns for input validation
        private static readonly string[] SuspiciousPatterns = new[]
        {
            // XSS patterns
            "<script", "javascript:", "vbscript:", "onload=", "onerror=", "onmouseover=", "onclick=",
            "eval(", "alert(", "prompt(", "confirm(", "document.cookie", "document.location", "window.location",
            "innerHTML", "outerHTML", "document.write", "document.writeln",

            // Path traversal patterns
            "../", "..\\", "%2e%2e", "..%2f", "..%5c", "%2e%2e%2f", "%2e%2e%5c",

            // SQL injection patterns
            "union select", "union all select", "drop table", "drop database", "delete from", "insert into",
            "exec sp_", "xp_cmdshell", "sp_executesql", "bulk insert", "openrowset", "opendatasource",
            "1=1", "'or'", "or 1=1", "' or '1'='1", "\" or \"1\"=\"1", "admin'--", "admin\"--",

            // Command injection patterns
            "&&", "||", ";", "|", "`", "$(", "${", "cmd.exe", "powershell", "/bin/bash", "/bin/sh",
            "system(", "exec(", "shell_exec", "passthru", "proc_open",

            // LDAP injection patterns
            ")(", "*)(",  ")(cn=", ")(uid=", ")(mail=",

            // XML injection patterns
            "<!DOCTYPE", "<!ENTITY", "SYSTEM", "PUBLIC", "<?xml",

            // Additional web attack patterns
            "data:", "file:", "ftp:", "gopher:", "ldap:", "dict:", "sftp:", "tftp:",
            "base64,", "iframe", "embed", "object", "applet", "form", "meta"
        };

        // Public paths that don't require authentication
        private static readonly string[] PublicPaths = new[]
        {
            "/auth/login", "/mobile/auth/login", "/auth/refresh", "/mobile/auth/refresh",
            "/swagger", "/health", "/api-docs", "/csrf/token"
        };

        public UnifiedSecurityMiddleware(
            RequestDelegate next,
            ILogger<UnifiedSecurityMiddleware> logger,
            IWebHostEnvironment environment,
            IOptions<MobileSecurityConfiguration> securityConfig,
            ISecurityMonitoringService securityMonitoring,
            IInputSanitizationService sanitizationService)
        {
            _next = next;
            _logger = logger;
            _environment = environment;
            _securityConfig = securityConfig?.Value ?? new MobileSecurityConfiguration();
            _securityMonitoring = securityMonitoring;
            _sanitizationService = sanitizationService;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                // 1. Apply Security Headers
                ApplySecurityHeaders(context);

                // 2. Validate Request
                if (!await ValidateRequest(context))
                {
                    return; // Response already written
                }

                // 3. CSRF Protection (for non-safe methods)
                if (!IsSafeMethod(context.Request.Method) && !IsPublicPath(context.Request.Path))
                {
                    if (!await ValidateCSRF(context))
                    {
                        return; // Response already written
                    }
                }

                // 4. Rate Limiting check (if enabled)
                if (_securityConfig.EnableRateLimiting && !await CheckRateLimit(context))
                {
                    return; // Response already written
                }

                // Continue to next middleware
                await _next(context);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in UnifiedSecurityMiddleware");
                await HandleSecurityError(context, ex);
            }
        }

        private void ApplySecurityHeaders(HttpContext context)
        {
            var headers = context.Response.Headers;

            // Comprehensive security headers
            headers["X-Content-Type-Options"] = "nosniff";

            // X-Frame-Options: Mobile API should allow webview embedding
            headers["X-Frame-Options"] = "SAMEORIGIN";

            headers["X-XSS-Protection"] = "1; mode=block";
            headers["Referrer-Policy"] = "strict-origin-when-cross-origin";
            headers["X-Download-Options"] = "noopen";
            headers["X-Permitted-Cross-Domain-Policies"] = "none";
            headers["X-DNS-Prefetch-Control"] = "off";
            headers["Cross-Origin-Embedder-Policy"] = "require-corp";
            headers["Cross-Origin-Opener-Policy"] = "same-origin";
            headers["Cross-Origin-Resource-Policy"] = "same-origin";

            // Enhanced Permissions Policy
            headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=(), payment=(), usb=(), " +
                                          "accelerometer=(), gyroscope=(), magnetometer=(), fullscreen=(), " +
                                          "picture-in-picture=(), autoplay=(), encrypted-media=(), " +
                                          "web-share=(), clipboard-write=(), display-capture=()";

            // Cache control for security-sensitive responses
            if (IsSecuritySensitiveEndpoint(context))
            {
                headers["Cache-Control"] = "no-cache, no-store, must-revalidate, private";
                headers["Pragma"] = "no-cache";
                headers["Expires"] = "0";
            }

            // Remove information disclosure headers
            headers.Remove("Server");
            headers.Remove("X-Powered-By");
            headers.Remove("X-AspNet-Version");
            headers.Remove("X-AspNetMvc-Version");
            headers.Remove("X-SourceFiles");

            // Content Security Policy
            if (_securityConfig.EnableCSP)
            {
                var cspValue = _environment.IsDevelopment()
                    ? BuildDevelopmentCSP()
                    : BuildProductionCSP();

                headers[_securityConfig.CSPReportOnly ? "Content-Security-Policy-Report-Only" : "Content-Security-Policy"] = cspValue;
            }

            // HSTS only for HTTPS
            if (context.Request.IsHttps && _securityConfig.EnableHSTS)
            {
                headers["Strict-Transport-Security"] = $"max-age={_securityConfig.HSTSMaxAge}; includeSubDomains; preload";
            }

            // Security monitoring headers
            headers["X-Request-ID"] = Guid.NewGuid().ToString();
            headers["X-Security-Version"] = "1.0";
        }

        private string BuildDevelopmentCSP()
        {
            return "default-src 'self'; " +
                   "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                   "style-src 'self' 'unsafe-inline'; " +
                   "img-src 'self' data: https: blob:; " +
                   "font-src 'self' data:; " +
                   "connect-src 'self' http://localhost:* https://localhost:* ws://localhost:* wss://localhost:*; " +
                   "frame-ancestors 'self' capacitor://localhost ionic://localhost; " +
                   "base-uri 'self'; " +
                   "form-action 'self'";
        }

        private string BuildProductionCSP()
        {
            return "default-src 'self'; " +
                   "script-src 'self'; " +
                   "style-src 'self' 'unsafe-inline'; " +
                   "img-src 'self' data: https:; " +
                   "font-src 'self'; " +
                   "connect-src 'self' wss:; " +
                   "frame-ancestors 'self' capacitor://localhost ionic://localhost; " +
                   "base-uri 'self'; " +
                   "form-action 'self'; " +
                   "upgrade-insecure-requests";
        }

        private async Task<bool> ValidateRequest(HttpContext context)
        {
            var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";

            // 1. Validate content length
            if (context.Request.ContentLength > _securityConfig.MaxRequestBodySize)
            {
                await _securityMonitoring.LogSecurityEvent("RequestTooLarge", $"Request size: {context.Request.ContentLength} bytes", remoteIp);
                await WriteJsonResponse(context, 413, "Payload too large", "PAYLOAD_TOO_LARGE");
                return false;
            }

            // 2. Validate headers length and content
            foreach (var header in context.Request.Headers)
            {
                if (header.Value.Any(v => v?.Length > _securityConfig.MaxHeaderSize))
                {
                    await _securityMonitoring.LogSecurityEvent("HeaderTooLarge", $"Header '{header.Key}' exceeds maximum size", remoteIp);
                    await WriteJsonResponse(context, 431, "Request header too large", "HEADER_TOO_LARGE");
                    return false;
                }

                // Check for suspicious header values
                if (await ContainsSuspiciousHeaderValues(header.Key, header.Value))
                {
                    await _securityMonitoring.LogSecurityEvent("SuspiciousHeader", $"Suspicious header detected: {header.Key}", remoteIp);
                    await WriteJsonResponse(context, 400, "Invalid header content", "SUSPICIOUS_HEADER");
                    return false;
                }
            }

            // 3. Validate URL length and structure
            var url = context.Request.GetDisplayUrl();
            if (url.Length > _securityConfig.MaxUrlLength)
            {
                await _securityMonitoring.LogSecurityEvent("UrlTooLong", $"URL length: {url.Length} characters", remoteIp);
                await WriteJsonResponse(context, 414, "Request URL too long", "URL_TOO_LONG");
                return false;
            }

            // 4. Enhanced URL validation
            if (!await ValidateUrlStructure(context))
            {
                await _securityMonitoring.LogSecurityEvent("InvalidUrlStructure", $"Invalid URL structure: {context.Request.Path}", remoteIp);
                await WriteJsonResponse(context, 400, "Invalid URL structure", "INVALID_URL");
                return false;
            }

            // 5. Check for suspicious patterns
            if (await ContainsSuspiciousPatterns(context))
            {
                await _securityMonitoring.LogSecurityEvent("SuspiciousPattern", $"Suspicious pattern detected in path: {context.Request.Path}", remoteIp);
                await WriteJsonResponse(context, 400, "Invalid request", "SUSPICIOUS_PATTERN");
                return false;
            }

            // 6. Validate request body if JSON
            if (context.Request.ContentLength > 0 && IsJsonContent(context))
            {
                context.Request.EnableBuffering();
                try
                {
                    var body = await ReadRequestBody(context);
                    if (!string.IsNullOrEmpty(body))
                    {
                        // Enhanced JSON validation
                        if (!await ValidateJsonContent(body, context))
                        {
                            await _securityMonitoring.LogSecurityEvent("SuspiciousJsonContent", $"Suspicious JSON content detected", remoteIp);
                            await WriteJsonResponse(context, 400, "Invalid JSON content", "SUSPICIOUS_JSON");
                            return false;
                        }
                    }
                }
                catch (JsonException ex)
                {
                    await _securityMonitoring.LogSecurityEvent("InvalidJson", $"Invalid JSON in request to: {context.Request.Path} - {ex.Message}", remoteIp);
                    await WriteJsonResponse(context, 400, "Invalid JSON", "INVALID_JSON");
                    return false;
                }
                finally
                {
                    context.Request.Body.Position = 0;
                }
            }

            // 7. File upload validation
            if (context.Request.HasFormContentType && context.Request.Form.Files.Count > 0)
            {
                if (!await ValidateFileUploads(context))
                {
                    await _securityMonitoring.LogSecurityEvent("InvalidFileUpload", $"Invalid file upload detected", remoteIp);
                    await WriteJsonResponse(context, 400, "Invalid file upload", "INVALID_FILE");
                    return false;
                }
            }

            return true;
        }

        private async Task<bool> ContainsSuspiciousPatterns(HttpContext context)
        {
            // Skip broad pattern validation for authentication endpoints to prevent false positives
            var authPaths = new[] { "/auth/login", "/mobile/auth/login", "/auth/refresh", "/mobile/auth/refresh" };
            bool isAuthEndpoint = authPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));

            if (isAuthEndpoint)
            {
                // For auth endpoints, only check for critical security threats
                return await ContainsCriticalSecurityThreats(context);
            }

            // For non-auth endpoints, use comprehensive validation
            // Check headers (using specialized User-Agent validation)
            foreach (var header in context.Request.Headers)
            {
                var headerValue = string.Join(" ", header.Value.ToArray());

                // Use specialized User-Agent validation
                if (header.Key.Equals("User-Agent", StringComparison.OrdinalIgnoreCase))
                {
                    if (ContainsMaliciousUserAgent(headerValue))
                    {
                        _logger.LogWarning("Suspicious pattern in header {Header}", header.Key);
                        return true;
                    }
                }
                // For other headers, use context-aware validation
                else if (ContainsMaliciousHeaderPattern(header.Key, headerValue))
                {
                    _logger.LogWarning("Suspicious pattern in header {Header}", header.Key);
                    return true;
                }
            }

            // Check query parameters
            foreach (var param in context.Request.Query)
            {
                var paramValue = string.Join(" ", param.Value.ToArray());
                if (ContainsMaliciousPattern(paramValue))
                {
                    _logger.LogWarning("Suspicious pattern in query parameter {Param}", param.Key);
                    return true;
                }
            }

            // Check path
            if (ContainsMaliciousPattern(context.Request.Path.Value ?? ""))
            {
                _logger.LogWarning("Suspicious pattern in path");
                return true;
            }

            return false;
        }

        private bool ContainsMaliciousPattern(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return false;

            var lowerInput = input.ToLowerInvariant();
            return SuspiciousPatterns.Any(pattern => lowerInput.Contains(pattern.ToLowerInvariant()));
        }

        private bool ContainsMaliciousUserAgent(string userAgent)
        {
            if (string.IsNullOrWhiteSpace(userAgent))
                return false;

            var lowerUserAgent = userAgent.ToLowerInvariant();

            // First, check if this looks like a legitimate browser User-Agent
            var legitimateBrowserPatterns = new[]
            {
                "mozilla", "webkit", "chrome", "safari", "firefox", "edge", "opera", "msie", "trident"
            };

            bool looksLikeBrowser = legitimateBrowserPatterns.Any(pattern =>
                lowerUserAgent.Contains(pattern));

            // If it looks like a legitimate browser, be more lenient
            if (looksLikeBrowser)
            {
                // Only check for the most obvious attack patterns in browser User-Agents
                var criticalAttackPatterns = new[]
                {
                    "<script", "javascript:", "eval(", "alert(", "cmd.exe", "powershell",
                    "/bin/bash", "union select", "drop table", "' or '", "\" or \"",
                    "sqlmap", "nmap", "burp", "metasploit"
                };

                return criticalAttackPatterns.Any(pattern => lowerUserAgent.Contains(pattern));
            }

            // For non-browser User-Agents, use stricter validation
            var suspiciousUserAgentPatterns = new[]
            {
                // Script injection attempts
                "<script", "javascript:", "vbscript:", "eval(", "alert(", "prompt(",

                // Command injection attempts
                "cmd.exe", "powershell", "/bin/bash", "/bin/sh", "system(", "exec(",

                // SQL injection attempts
                "union select", "drop table", "' or '", "\" or \"", "1=1",

                // Obvious attack tools
                "sqlmap", "nmap", "nikto", "burp", "metasploit", "nessus",

                // Suspicious protocols (but not in typical browser contexts)
                "file://", "ftp://", "ldap://",

                // Empty or clearly fake agents
                "test", "bot", "crawler", "scanner", "hack", "exploit"
            };

            // Check for actually suspicious patterns
            foreach (var pattern in suspiciousUserAgentPatterns)
            {
                if (lowerUserAgent.Contains(pattern))
                    return true;
            }

            // Additional checks for malformed or suspicious User-Agent structures
            if (userAgent.Length > 1000) // Excessively long User-Agent
                return true;

            if (userAgent.Contains('\0') || userAgent.Contains('\r') || userAgent.Contains('\n')) // Null bytes or CRLF
                return true;

            return false;
        }

        private bool ContainsMaliciousJsonPattern(string jsonContent)
        {
            if (string.IsNullOrWhiteSpace(jsonContent))
                return false;

            var lowerJson = jsonContent.ToLowerInvariant();

            // Define patterns that are specifically malicious in JSON context
            var maliciousJsonPatterns = new[]
            {
                // Script injection
                "<script", "javascript:", "vbscript:", "eval(", "alert(", "prompt(",

                // Command injection
                "cmd.exe", "powershell", "/bin/bash", "/bin/sh", "system(",

                // SQL injection (complete patterns only)
                "union select", "drop table", "delete from", "insert into",
                "exec sp_", "xp_cmdshell", "bulk insert",

                // Path traversal
                "../", "..\\", "%2e%2e",

                // XXE and XML injection
                "<!doctype", "<!entity", "<?xml",

                // Protocol injection
                "javascript:", "data:text/html", "file://",

                // Other injection patterns
                "{{", "}}", "${", "<%", "%>"
            };

            return maliciousJsonPatterns.Any(pattern => lowerJson.Contains(pattern));
        }

        private async Task<bool> ContainsCriticalSecurityThreats(HttpContext context)
        {
            // Only check for the most critical security threats for auth endpoints
            var criticalPatterns = new[]
            {
                // Script injection
                "<script", "javascript:", "eval(", "alert(",

                // Command injection
                "cmd.exe", "powershell", "/bin/bash", "/bin/sh",

                // Obvious SQL injection
                "union select", "drop table", "delete from",

                // Path traversal
                "../", "..\\",

                // XXE
                "<!entity", "<!doctype"
            };

            // Check all request components for critical threats only
            foreach (var header in context.Request.Headers)
            {
                var headerValue = string.Join(" ", header.Value.ToArray()).ToLowerInvariant();
                if (criticalPatterns.Any(pattern => headerValue.Contains(pattern)))
                {
                    _logger.LogWarning("Critical security threat in header {Header}", header.Key);
                    return true;
                }
            }

            // Check path for critical patterns
            var path = (context.Request.Path.Value ?? "").ToLowerInvariant();
            if (criticalPatterns.Any(pattern => path.Contains(pattern)))
            {
                _logger.LogWarning("Critical security threat in path");
                return true;
            }

            return false;
        }

        private bool ContainsMaliciousHeaderPattern(string headerKey, string headerValue)
        {
            if (string.IsNullOrWhiteSpace(headerValue))
                return false;

            var lowerValue = headerValue.ToLowerInvariant();
            var lowerKey = headerKey.ToLowerInvariant();

            // Skip validation for known safe browser headers that might contain quotes/special chars
            var safeBrowserHeaders = new[]
            {
                "sec-ch-ua", "sec-ch-ua-platform", "sec-ch-ua-mobile", "sec-fetch-site",
                "sec-fetch-mode", "sec-fetch-dest", "accept-encoding", "accept-language",
                "cache-control", "connection", "host", "origin", "referer", "cookie"
            };

            if (safeBrowserHeaders.Contains(lowerKey))
                return false;

            // For other headers, check only the most critical patterns
            var criticalHeaderPatterns = new[]
            {
                "<script", "javascript:", "eval(", "alert(",
                "cmd.exe", "powershell", "/bin/bash",
                "union select", "drop table", "../"
            };

            return criticalHeaderPatterns.Any(pattern => lowerValue.Contains(pattern));
        }

        private async Task<bool> ValidateCSRF(HttpContext context)
        {
            // Check if this is a mobile request (mobile uses JWT instead of CSRF)
            if (IsMobileRequest(context))
            {
                // For mobile, just ensure JWT is present
                if (!context.Request.Headers.ContainsKey("Authorization"))
                {
                    _logger.LogWarning("Mobile request without Authorization header for {Path}", context.Request.Path);
                    await WriteJsonResponse(context, 403, "Missing authorization", "NO_AUTH");
                    return false;
                }
                return true;
            }

            // For web requests, validate CSRF token
            var csrfService = context.RequestServices.GetRequiredService<ICSRFService>();
            var csrfToken = context.Request.Headers["X-XSRF-TOKEN"].FirstOrDefault();
            var userId = context.User?.Identity?.Name ?? context.Request.Headers["X-Login-Id"].FirstOrDefault();

            if (string.IsNullOrEmpty(csrfToken))
            {
                _logger.LogWarning("Missing CSRF token for web request to {Path}", context.Request.Path);

                // Generate and provide a token for the client
                if (!string.IsNullOrEmpty(userId))
                {
                    var newToken = await csrfService.GenerateTokenAsync(userId);
                    context.Response.Headers.Add("X-XSRF-TOKEN", newToken);
                }

                await WriteJsonResponse(context, 403, "Missing CSRF token", "NO_CSRF");
                return false;
            }

            if (!await csrfService.ValidateTokenAsync(csrfToken, userId))
            {
                await _securityMonitoring.LogSecurityEvent("InvalidCSRF",
                    $"Invalid CSRF token for user {userId} accessing {context.Request.Path}",
                    context.Connection.RemoteIpAddress?.ToString() ?? "unknown");
                await WriteJsonResponse(context, 403, "Invalid CSRF token", "INVALID_CSRF");
                return false;
            }

            return true;
        }

        private async Task<bool> CheckRateLimit(HttpContext context)
        {
            // Simple rate limiting check - can be enhanced with distributed rate limiting
            var remoteIp = context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
            var userId = context.User?.Identity?.Name ?? "anonymous";
            var key = $"rate_limit:{userId}:{remoteIp}";

            // TODO: Implement actual rate limiting logic
            // For now, just return true
            return true;
        }

        private static bool IsSafeMethod(string method)
        {
            return method.Equals("GET", StringComparison.OrdinalIgnoreCase) ||
                   method.Equals("HEAD", StringComparison.OrdinalIgnoreCase) ||
                   method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsPublicPath(PathString path)
        {
            var pathValue = path.Value?.ToLower() ?? "";
            return PublicPaths.Any(p => pathValue.Contains(p));
        }

        private static bool IsMobileRequest(HttpContext context)
        {
            return context.Request.Headers.ContainsKey("X-From-Mobile-WebView") ||
                   context.Request.Headers.ContainsKey("X-Mobile-App") ||
                   context.Request.Headers.ContainsKey("X-Is-Mobile") ||
                   context.Request.Headers["User-Agent"].ToString().Contains("DigiHR", StringComparison.OrdinalIgnoreCase);
        }

        private static bool IsJsonContent(HttpContext context)
        {
            return context.Request.ContentType?.Contains("application/json", StringComparison.OrdinalIgnoreCase) ?? false;
        }

        private static async Task<string> ReadRequestBody(HttpContext context)
        {
            using var reader = new StreamReader(context.Request.Body, leaveOpen: true);
            var body = await reader.ReadToEndAsync();
            context.Request.Body.Position = 0;
            return body;
        }

        private async Task WriteJsonResponse(HttpContext context, int statusCode, string message, string code)
        {
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";
            var response = JsonSerializer.Serialize(new { error = message, code = code });
            await context.Response.WriteAsync(response);
        }

        private bool IsSecuritySensitiveEndpoint(HttpContext context)
        {
            var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
            return path.Contains("/auth/") ||
                   path.Contains("/csrf/") ||
                   path.Contains("/password") ||
                   path.Contains("/token") ||
                   path.Contains("/secure") ||
                   path.Contains("/admin") ||
                   path.Contains("/api/users") ||
                   path.Contains("/api/user");
        }

        private async Task<bool> ContainsSuspiciousHeaderValues(string headerKey, StringValues headerValues)
        {
            var sensitiveHeaders = new[] { "User-Agent", "Referer", "X-Forwarded-For", "X-Real-IP", "Authorization" };

            if (!sensitiveHeaders.Contains(headerKey, StringComparer.OrdinalIgnoreCase))
                return false;

            foreach (var value in headerValues)
            {
                if (string.IsNullOrEmpty(value))
                    continue;

                // Special handling for User-Agent header to avoid false positives
                if (headerKey.Equals("User-Agent", StringComparison.OrdinalIgnoreCase))
                {
                    if (ContainsMaliciousUserAgent(value))
                    {
                        _logger.LogWarning("Suspicious pattern in header {HeaderKey}: {Value}", headerKey, value.Length > 100 ? value.Substring(0, 100) + "..." : value);
                        return true;
                    }
                }
                else if (ContainsMaliciousPattern(value))
                {
                    _logger.LogWarning("Suspicious pattern in header {HeaderKey}: {Value}", headerKey, value.Length > 100 ? value.Substring(0, 100) + "..." : value);
                    return true;
                }
            }

            return false;
        }

        private async Task<bool> ValidateUrlStructure(HttpContext context)
        {
            var path = context.Request.Path.Value ?? "";
            var query = context.Request.QueryString.Value ?? "";

            // Check for excessive path segments
            var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            if (segments.Length > 10)
            {
                _logger.LogWarning("Excessive path segments: {SegmentCount}", segments.Length);
                return false;
            }

            // Check for suspicious file extensions in path
            var suspiciousExtensions = new[] { ".exe", ".bat", ".cmd", ".com", ".scr", ".vbs", ".js", ".jar", ".zip", ".rar" };
            if (suspiciousExtensions.Any(ext => path.EndsWith(ext, StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogWarning("Suspicious file extension in path: {Path}", path);
                return false;
            }

            // Check for encoded path traversal attempts
            if (query.Contains("%2e%2e") || query.Contains("%2f") || query.Contains("%5c"))
            {
                _logger.LogWarning("Encoded path traversal attempt in query: {Query}", query);
                return false;
            }

            return true;
        }

        private async Task<bool> ValidateJsonContent(string jsonContent, HttpContext context)
        {
            // Skip pattern validation for authentication endpoints to avoid false positives
            var authPaths = new[] { "/auth/login", "/mobile/auth/login", "/auth/refresh", "/mobile/auth/refresh" };
            bool isAuthEndpoint = authPaths.Any(path =>
                context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase));

            if (!isAuthEndpoint)
            {
                // Check for suspicious JSON content (but only for non-auth endpoints)
                if (ContainsMaliciousJsonPattern(jsonContent))
                {
                    _logger.LogWarning("Suspicious pattern in JSON content");
                    return false;
                }
            }

            // Check for excessively nested JSON
            var nesting = 0;
            var maxNesting = 0;
            foreach (var c in jsonContent)
            {
                if (c == '{' || c == '[')
                {
                    nesting++;
                    maxNesting = Math.Max(maxNesting, nesting);
                }
                else if (c == '}' || c == ']')
                {
                    nesting--;
                }
            }

            if (maxNesting > 20)
            {
                _logger.LogWarning("Excessively nested JSON detected: {MaxNesting}", maxNesting);
                return false;
            }

            // Check for suspicious JSON keys
            var suspiciousKeys = new[] { "__proto__", "constructor", "prototype", "eval", "function", "script" };
            if (suspiciousKeys.Any(key => jsonContent.Contains($"\"{key}\"", StringComparison.OrdinalIgnoreCase)))
            {
                _logger.LogWarning("Suspicious JSON key detected");
                return false;
            }

            return true;
        }

        private async Task<bool> ValidateFileUploads(HttpContext context)
        {
            // Dangerous file extensions
            var dangerousExtensions = new[] {
                ".exe", ".bat", ".cmd", ".com", ".scr", ".vbs", ".js", ".jar", ".php", ".asp", ".aspx",
                ".jsp", ".sh", ".ps1", ".dll", ".msi", ".deb", ".rpm", ".dmg", ".app", ".apk"
            };

            // Maximum file size (10MB)
            const long maxFileSize = 10 * 1024 * 1024;

            foreach (var file in context.Request.Form.Files)
            {
                // Check file size
                if (file.Length > maxFileSize)
                {
                    _logger.LogWarning("File upload exceeds maximum size: {FileName} ({FileSize} bytes)", file.FileName, file.Length);
                    return false;
                }

                // Check file extension
                var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
                if (dangerousExtensions.Contains(extension))
                {
                    _logger.LogWarning("Dangerous file extension detected: {FileName} ({Extension})", file.FileName, extension);
                    return false;
                }

                // Check file name for suspicious patterns
                if (ContainsMaliciousPattern(file.FileName))
                {
                    _logger.LogWarning("Suspicious file name pattern: {FileName}", file.FileName);
                    return false;
                }

                // Check for double extensions
                if (file.FileName.Count(c => c == '.') > 1)
                {
                    _logger.LogWarning("Multiple file extensions detected: {FileName}", file.FileName);
                    return false;
                }
            }

            return true;
        }

        private async Task HandleSecurityError(HttpContext context, Exception ex)
        {
            _logger.LogError(ex, "Security middleware error");

            if (!context.Response.HasStarted)
            {
                // In development, include more error details
                if (_environment.IsDevelopment())
                {
                    var errorDetails = new
                    {
                        error = "Security middleware error",
                        code = "SECURITY_ERROR",
                        details = ex.Message,
                        type = ex.GetType().Name,
                        path = context.Request.Path.Value
                    };

                    context.Response.StatusCode = 500;
                    context.Response.ContentType = "application/json";
                    var response = JsonSerializer.Serialize(errorDetails);
                    await context.Response.WriteAsync(response);
                }
                else
                {
                    await WriteJsonResponse(context, 500, "Internal server error", "SECURITY_ERROR");
                }
            }

            await _securityMonitoring.LogSecurityEvent("SecurityMiddlewareError",
                $"Security middleware error: {ex.Message} at {context.Request.Path}",
                context.Connection.RemoteIpAddress?.ToString() ?? "unknown");
        }
    }

    public class MobileSecurityConfiguration
    {
        public bool EnableCSP { get; set; } = true;
        public bool CSPReportOnly { get; set; } = false;
        public bool EnableHSTS { get; set; } = true;
        public int HSTSMaxAge { get; set; } = 31536000; // 1 year
        public bool EnableRateLimiting { get; set; } = true;
        public long MaxRequestBodySize { get; set; } = 10_485_760; // 10MB
        public int MaxHeaderSize { get; set; } = 8192;
        public int MaxUrlLength { get; set; } = 2048;
        public bool EnableAdvancedValidation { get; set; } = true;
        public bool EnableFileUploadValidation { get; set; } = true;
        public bool EnableJsonContentValidation { get; set; } = true;
        public bool EnableHeaderValidation { get; set; } = true;
        public bool EnableUrlStructureValidation { get; set; } = true;
        public long MaxFileUploadSize { get; set; } = 10_485_760; // 10MB
        public int MaxPathSegments { get; set; } = 10;
        public int MaxJsonNesting { get; set; } = 20;
    }
}