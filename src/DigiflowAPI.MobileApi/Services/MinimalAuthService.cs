﻿using DigiflowAPI.MobileApi.Models.Auth;
using DigiflowAPI.MobileApi.Services;
using DigiflowAPI.MobileApi.Interfaces;
// using DigiflowAPI.MobileAPI.Interfaces; // Removed - namespace doesn't exist
using DigiflowMobileAPI.Interfaces;
using DigiflowMobileAPI.Models.Auth;
using DigiflowAPI.Domain.Interfaces.Repositories;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace DigiflowMobileAPI.Services
{
    /// <summary>
    /// Minimal authentication service that only handles auth without business logic
    /// </summary>
    public class MinimalAuthService : IAuthService
    {
        private readonly IWindowsAuthService _windowsAuthService;
        private readonly IUserRepository _userRepository;
        private readonly ILogger<MinimalAuthService> _logger;
        private readonly IConfiguration _configuration;
        private readonly JwtOptions _jwtOptions;
        private readonly TimeSpan _accessTokenExpiration;
        private readonly TimeSpan _refreshTokenExpiration;

        public MinimalAuthService(
            IWindowsAuthService windowsAuthService,
            IUserRepository userRepository,
            ILogger<MinimalAuthService> logger,
            IConfiguration configuration,
            IOptions<JwtOptions> jwtOptions)
        {
            _windowsAuthService = windowsAuthService;
            _userRepository = userRepository;
            _logger = logger;
            _configuration = configuration;
            _jwtOptions = jwtOptions.Value;

            // Read token expiration from configuration
            var accessTokenMinutes = _jwtOptions.ExpiryMinutes;
            var refreshTokenDays = configuration.GetValue<int>("Jwt:RefreshExpiryDays", 90);

            _accessTokenExpiration = TimeSpan.FromMinutes(accessTokenMinutes);
            _refreshTokenExpiration = TimeSpan.FromDays(refreshTokenDays);

            _logger.LogInformation("Minimal auth service configured: Access={AccessMinutes}min, Refresh={RefreshDays}days",
                accessTokenMinutes, refreshTokenDays);
        }

        public async Task<LoginResponse?> LoginAsync(LoginRequest request)
        {
            try
            {
                // Validate Windows/AD credentials
                var isAuthenticated = await _windowsAuthService.ValidateAsync(request.Username, request.Password);

                if (!isAuthenticated)
                {
                    _logger.LogWarning("Authentication failed for user: {Username}", request.Username);
                    return null;
                }

                // Look up the numeric user ID from the database
                var user = await _userRepository.GetByUsernameAsync(request.Username);
                if (user?.LoginId == null)
                {
                    _logger.LogWarning("User not found in database: {Username}", request.Username);
                    return null;
                }

                var numericUserId = user.LoginId.Value.ToString();
                _logger.LogInformation("Found user in database: {Username} -> ID: {UserId}", request.Username, numericUserId);

                // Generate auth response with numeric user ID
                var response = await GenerateAuthResponseAsync(numericUserId, request.Username);

                return MapToLoginResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {Username}", request.Username);
                return null;
            }
        }

        public async Task<LoginResponse?> RefreshTokenAsync(RefreshRequest request)
        {
            try
            {
                // Validate the refresh token
                var principal = ValidateToken(request.AccessToken, validateLifetime: false);
                if (principal == null)
                {
                    _logger.LogWarning("Invalid access token provided for refresh");
                    return null;
                }

                var username = principal.Identity?.Name ?? principal.FindFirst("unique_name")?.Value;
                var userId = principal.FindFirst("sub")?.Value ?? principal.FindFirst("userId")?.Value;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(userId))
                {
                    _logger.LogWarning("Username or userId not found in token");
                    return null;
                }

                // TODO: Validate refresh token from storage
                // For now, generate new tokens
                var response = await GenerateAuthResponseAsync(userId, username);

                return MapToLoginResponse(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token");
                return null;
            }
        }

        public async Task<UserInfo?> GetCurrentUserAsync(ClaimsPrincipal? principal = null)
        {
            if (principal == null || !principal.Identity!.IsAuthenticated)
            {
                return null;
            }

            var username = principal.Identity.Name ?? principal.FindFirst("unique_name")?.Value;
            var userId = principal.FindFirst("sub")?.Value ?? principal.FindFirst("userId")?.Value;

            if (string.IsNullOrEmpty(username))
            {
                return null;
            }

            // Return minimal user info for auth purposes only
            return new UserInfo
            {
                Id = userId ?? username,
                Username = username,
                Name = username,
                Email = principal.FindFirst(ClaimTypes.Email)?.Value ?? "",
                Roles = principal.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList()
            };
        }

        private async Task<LoginResponse> GenerateAuthResponseAsync(string userId, string username)
        {
            var token = GenerateJwtToken(userId, username);
            var refreshToken = GenerateRefreshToken();

            // Get DigiflowAPI base URL for the client to use
            var apiBaseUrl = _configuration["DigiflowApi:BaseUrl"] ?? "https://digiflow.digiturk.com.tr/api";

            return new LoginResponse
            {
                AccessToken = token,
                RefreshToken = refreshToken,
                ExpiresIn = (int)_accessTokenExpiration.TotalSeconds,
                TokenType = "Bearer",
                UserId = userId,
                Username = username,
                IsFirstLogin = false, // This could be determined from AD attributes
                ApiBaseUrl = apiBaseUrl,
                IssuedAt = DateTime.UtcNow
            };
        }

        private string GenerateJwtToken(string userId, string username)
        {
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtOptions.Secret));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim(JwtRegisteredClaimNames.Sub, userId),
                new Claim(JwtRegisteredClaimNames.UniqueName, username),
                new Claim("userId", userId),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
            };

            var token = new JwtSecurityToken(
                issuer: _jwtOptions.Issuer,
                audience: _jwtOptions.Audience,
                claims: claims,
                notBefore: DateTime.UtcNow,
                expires: DateTime.UtcNow.Add(_accessTokenExpiration),
                signingCredentials: creds
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private string GenerateRefreshToken()
        {
            var randomNumber = new byte[32];
            using var rng = System.Security.Cryptography.RandomNumberGenerator.Create();
            rng.GetBytes(randomNumber);
            return Convert.ToBase64String(randomNumber);
        }

        private ClaimsPrincipal? ValidateToken(string token, bool validateLifetime = true)
        {
            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(_jwtOptions.Secret);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = _jwtOptions.Issuer,
                    ValidateAudience = true,
                    ValidAudience = _jwtOptions.Audience,
                    ValidateLifetime = validateLifetime,
                    ClockSkew = TimeSpan.FromMinutes(5)
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out _);
                return principal;
            }
            catch
            {
                return null;
            }
        }

        private LoginResponse MapToLoginResponse(LoginResponse loginResponse)
        {
            // No mapping needed since we're using LoginResponse directly
            return loginResponse;
        }
    }
}