using System;
using System.Collections.Generic;
using System.DirectoryServices;
using System.DirectoryServices.AccountManagement;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using DigiflowAPI.MobileApi.Interfaces;

namespace DigiflowAPI.MobileApi.Services
{
    public class WindowsAuthService : IWindowsAuthService
    {
        private readonly string _ldapPath;
        private readonly string _domain;
        private readonly string _serviceUsername;
        private readonly string _servicePassword;
        private readonly ILogger<WindowsAuthService> _logger;
        private readonly IConfiguration _configuration;

        public WindowsAuthService(IConfiguration configuration, ILogger<WindowsAuthService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _ldapPath = configuration["Ldap:Path"] ?? "LDAP://dtldap.digiturk.local";
            _domain = configuration["Ldap:Domain"] ?? "DIGITURK";
            _serviceUsername = Environment.GetEnvironmentVariable("LDAP_USERNAME");
            _servicePassword = Environment.GetEnvironmentVariable("LDAP_PASSWORD");
            
            _logger.LogInformation("WindowsAuthService configured: LDAP={LdapPath}, Domain={Domain}, ServiceAccount={ServiceUsername}", 
                _ldapPath, _domain, !string.IsNullOrEmpty(_serviceUsername) ? "configured" : "not configured");
        }

        public async Task<bool> ValidateAsync(string username, string password)
        {
            string cleanUsername = RemoveDomainPrefix(username);

            return await Task.Run(() =>
            {
                try
                {
                    // First, try user authentication with their credentials
                    using (var entry = new DirectoryEntry(_ldapPath, $"{_domain}\\{cleanUsername}", password))
                    {
                        var nativeObject = entry.NativeObject;
                        _logger.LogDebug("User authentication successful for {Username}", username);
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug(ex, "Direct user authentication failed for {Username}, trying service account approach", username);
                    
                    // If direct authentication fails and we have service account credentials, try alternative approach
                    if (!string.IsNullOrEmpty(_serviceUsername) && !string.IsNullOrEmpty(_servicePassword))
                    {
                        try
                        {
                            // Connect with service account to verify user exists and then bind with user credentials
                            using (var serviceEntry = new DirectoryEntry(_ldapPath, _serviceUsername, _servicePassword))
                            {
                                using (var searcher = new DirectorySearcher(serviceEntry))
                                {
                                    searcher.Filter = $"(sAMAccountName={cleanUsername})";
                                    var result = searcher.FindOne();
                                    
                                    if (result != null)
                                    {
                                        // User exists, now try to bind with their credentials
                                        var userDn = result.Properties["distinguishedName"][0].ToString();
                                        using (var userEntry = new DirectoryEntry(_ldapPath, userDn, password))
                                        {
                                            var userObject = userEntry.NativeObject;
                                            _logger.LogDebug("Service account assisted authentication successful for {Username}", username);
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception serviceEx)
                        {
                            _logger.LogWarning(serviceEx, "Service account authentication also failed for {Username}", username);
                        }
                    }
                    
                    _logger.LogWarning(ex, "Authentication failed for {Username}", username);
                    return false;
                }
            });
        }

        public async Task<DateTime> GetPasswordLastChangedTimeAsync(string username)
        {
            string cleanUsername = RemoveDomainPrefix(username);

            return await Task.Run(() =>
            {
                try
                {
                    // Use service account credentials if available
                    DirectoryEntry entry;
                    if (!string.IsNullOrEmpty(_serviceUsername) && !string.IsNullOrEmpty(_servicePassword))
                    {
                        entry = new DirectoryEntry(_ldapPath, _serviceUsername, _servicePassword);
                        _logger.LogDebug("Using service account for password last changed lookup for {Username}", username);
                    }
                    else
                    {
                        entry = new DirectoryEntry(_ldapPath);
                        _logger.LogDebug("Using default credentials for password last changed lookup for {Username}", username);
                    }

                    using (entry)
                    {
                        using (var searcher = new DirectorySearcher(entry))
                        {
                            searcher.Filter = $"(sAMAccountName={cleanUsername})";
                            searcher.PropertiesToLoad.Add("pwdLastSet");

                            var result = searcher.FindOne();
                            if (result != null && result.Properties.Contains("pwdLastSet"))
                            {
                                var pwdLastSet = (long)result.Properties["pwdLastSet"][0];
                                return DateTime.FromFileTimeUtc(pwdLastSet);
                            }
                        }
                    }
                    return DateTime.UtcNow.AddDays(-1);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting password last changed time for {Username}", username);
                    return DateTime.UtcNow;
                }
            });
        }

        public async Task<Dictionary<string, List<string>>> GetUserDetailsAsync(string username)
        {
            string cleanUsername = RemoveDomainPrefix(username);

            var userDetails = new Dictionary<string, List<string>>
                {
                    { "Email", new List<string>() },
                    { "Name", new List<string>() },
                    { "Roles", new List<string>() }
                };

            return await Task.Run(() =>
            {
                try
                {
                    // Try with service account credentials if available
                    PrincipalContext context;
                    if (!string.IsNullOrEmpty(_serviceUsername) && !string.IsNullOrEmpty(_servicePassword))
                    {
                        context = new PrincipalContext(ContextType.Domain, _domain, _serviceUsername, _servicePassword);
                        _logger.LogDebug("Using service account for user details lookup for {Username}", username);
                    }
                    else
                    {
                        context = new PrincipalContext(ContextType.Domain, _domain);
                        _logger.LogDebug("Using default credentials for user details lookup for {Username}", username);
                    }

                    using (context)
                    {
                        using (var user = UserPrincipal.FindByIdentity(context, IdentityType.SamAccountName, cleanUsername))
                        {
                            if (user != null)
                            {
                                if (!string.IsNullOrEmpty(user.EmailAddress))
                                    userDetails["Email"].Add(user.EmailAddress);

                                if (!string.IsNullOrEmpty(user.DisplayName))
                                    userDetails["Name"].Add(user.DisplayName);
                                else if (!string.IsNullOrEmpty(user.Name))
                                    userDetails["Name"].Add(user.Name);

                                using (var directoryEntry = user.GetUnderlyingObject() as DirectoryEntry)
                                {
                                    if (directoryEntry != null && directoryEntry.Properties.Contains("memberOf"))
                                    {
                                        foreach (var groupDn in directoryEntry.Properties["memberOf"])
                                        {
                                            var groupDnStr = groupDn.ToString();
                                            var cnMatch = System.Text.RegularExpressions.Regex.Match(groupDnStr, @"CN=([^,]+)");
                                            if (cnMatch.Success)
                                            {
                                                userDetails["Roles"].Add(cnMatch.Groups[1].Value);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (userDetails["Roles"].Count == 0)
                    {
                        userDetails["Roles"].Add("User");
                    }

                    return userDetails;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting user details for {Username}", username);

                    // Fallback values
                    userDetails["Email"].Add($"{cleanUsername}@digiturk.com.tr");
                    userDetails["Name"].Add(cleanUsername);
                    userDetails["Roles"].Add("User");

                    return userDetails;
                }
            });
        }

        private string RemoveDomainPrefix(string username)
        {
            if (string.IsNullOrEmpty(username))
                return username;

            int idx = username.IndexOf('\\');
            if (idx != -1)
            {
                return username.Substring(idx + 1);
            }

            idx = username.IndexOf('@');
            if (idx != -1)
            {
                return username.Substring(0, idx);
            }

            return username;
        }
    }
}