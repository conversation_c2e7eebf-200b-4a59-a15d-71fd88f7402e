using System.ComponentModel.DataAnnotations;

namespace DigiflowMobileAPI.Models.Auth
{
    public class RefreshRequest
    {
        [Required(ErrorMessage = "Refresh token is required")]
        [StringLength(1000, ErrorMessage = "Refresh token is too long")]
        // NOTE: Removed [NoSqlInjection] and [NoXss] - these are inappropriate for base64 tokens
        // Tokens are cryptographically generated and can randomly contain patterns that trigger false positives
        public string RefreshToken { get; set; } = string.Empty;

        [Required(ErrorMessage = "Access token is required")]
        [StringLength(5000, ErrorMessage = "Access token is too long")]
        // NOTE: Removed [NoSqlInjection] and [NoXss] - these are inappropriate for JWT tokens
        // JWT tokens are cryptographically signed and validated, not user input
        public string AccessToken { get; set; } = string.Empty;
    }
}