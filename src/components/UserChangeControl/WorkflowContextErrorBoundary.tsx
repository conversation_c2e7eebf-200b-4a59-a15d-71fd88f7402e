import React, { Component, ReactNode } from 'react'
import UserChangeControl from './UserChangeControl'

interface Props {
  children: ReactNode
  fallbackProps?: any
}

interface State {
  hasError: boolean
}

class WorkflowContextErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    if (error.message.includes('useWorkflowConfig must be used within a WorkflowConfigProvider')) {
      return { hasError: true }
    }
    // Re-throw other errors
    throw error
  }

  componentDidCatch(_error: Error, _errorInfo: React.ErrorInfo) {
    // Log the error if needed
    console.info('WorkflowContext not available, falling back to regular UserChangeControl')
  }

  render() {
    if (this.state.hasError) {
      // Render the fallback component
      return <UserChangeControl {...this.props.fallbackProps} />
    }

    return this.props.children
  }
}

export default WorkflowContextErrorBoundary
