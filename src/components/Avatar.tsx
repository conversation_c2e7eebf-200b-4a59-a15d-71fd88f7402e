import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from 'react-native';
import AuthenticatedImage from './AuthenticatedImage';
import {
  responsiveWidth,
  responsiveFontSize,
  moderateScale,
  moderateVerticalScale,
} from '../utils/responsive';
import { colors } from '../styles/theme';
import Icon from './Icon';

export type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
export type AvatarVariant = 'circle' | 'rounded' | 'square';
export type AvatarBorderStyle = 'none' | 'solid' | 'dashed' | 'dotted';
export type EmojiPosition = 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'center' | 'floating';
export type AvatarStatusIndicator = 'online' | 'offline' | 'away' | 'busy' | 'none';

interface EmojiConfig {
  emoji: string;
  position: EmojiPosition;
  size?: number;
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  offsetX?: number;
  offsetY?: number;
  animate?: boolean;
  pulse?: boolean;
}

interface AvatarProps {
  // Basic props
  size?: AvatarSize;
  variant?: AvatarVariant;

  // Image/Content
  imageUrl?: string;
  fallbackImageUrl?: string;
  name?: string;
  initials?: string;
  
  // Loading state
  loading?: boolean;

  // Emoji features
  emoji?: EmojiConfig;
  showEmoji?: boolean;

  // Status indicator
  status?: AvatarStatusIndicator;
  customStatusColor?: string;
  showStatusIndicator?: boolean;

  // Border and styling
  borderWidth?: number;
  borderColor?: string;
  borderStyle?: AvatarBorderStyle;
  backgroundColor?: string;
  shadowEnabled?: boolean;
  shadowColor?: string;
  shadowOpacity?: number;
  shadowRadius?: number;
  shadowOffset?: { width: number; height: number };

  // Badge/Counter
  badge?: number | string;
  badgeColor?: string;
  badgeTextColor?: string;
  maxBadgeCount?: number;
  showBadge?: boolean;

  // Behavior
  onPress?: () => void;
  onLongPress?: () => void;
  disabled?: boolean;
  pressable?: boolean;

  // Custom styling
  style?: ViewStyle;
  imageStyle?: ImageStyle;
  textStyle?: TextStyle;

  // Accessibility
  accessibilityLabel?: string;
  accessibilityHint?: string;
  testID?: string;

  // Advanced features
  gradientColors?: string[];
  overlayText?: string;
  overlayIcon?: React.ReactNode;
  maskShape?: 'circle' | 'hexagon' | 'star' | 'heart';
  loadingIndicator?: boolean;
  errorFallback?: React.ReactNode;
  placeholder?: React.ReactNode;

  // Animation
  animated?: boolean;
  animationType?: 'pulse' | 'bounce' | 'shake' | 'rotate' | 'scale';
  animationDuration?: number;
  animationDelay?: number;
}

const Avatar: React.FC<AvatarProps> = ({
  size = 'md',
  variant = 'circle',
  imageUrl,
  fallbackImageUrl,
  name = '',
  initials,
  loading = false,
  emoji,
  showEmoji = false,
  status = 'none',
  customStatusColor,
  showStatusIndicator = false,
  borderWidth = 0,
  borderColor = colors.figma.grayscale.border.default,
  borderStyle = 'solid',
  backgroundColor = colors.figma.grayscale.surface.default,
  shadowEnabled = false,
  shadowColor = '#000',
  shadowOpacity = 0.1,
  shadowRadius = 4,
  shadowOffset = { width: 0, height: 2 },
  badge,
  badgeColor = colors.figma.primary.surface.default,
  badgeTextColor = colors.figma.system.white,
  maxBadgeCount = 99,
  showBadge = false,
  onPress,
  onLongPress,
  disabled = false,
  pressable = false,
  style,
  imageStyle,
  textStyle,
  accessibilityLabel,
  accessibilityHint,
  testID,
  gradientColors,
  overlayText,
  overlayIcon,
  maskShape,
  loadingIndicator = false,
  errorFallback,
  placeholder,
  animated = false,
  animationType = 'pulse',
  animationDuration = 1000,
  animationDelay = 0,
}) => {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Size configurations - Updated to match Figma design tokens
  const sizeConfigs = {
    xs: { size: 24, fontSize: 10, iconSize: 12, padding: 2, radius: 4 },
    sm: { size: 40, fontSize: 12, iconSize: 16, padding: 4, radius: 8 }, // Avatar/Small from Figma
    md: { size: 48, fontSize: 20, iconSize: 20, padding: 6, radius: 10 },
    lg: { size: 64, fontSize: 20, iconSize: 24, padding: 8, radius: 12 },
    xl: { size: 82, fontSize: 22, iconSize: 28, padding: 16, radius: 16 }, // Avatar/Big from Figma
    '2xl': { size: 96, fontSize: 28, iconSize: 32, padding: 18, radius: 18 },
    '3xl': { size: 128, fontSize: 36, iconSize: 40, padding: 24, radius: 24 },
  };

  const config = sizeConfigs[size];
  const avatarSize = moderateScale(config.size);
  const fontSize = responsiveFontSize(config.fontSize);

  // Generate initials from name if not provided
  const getInitials = () => {
    if (initials) return initials.toUpperCase();
    if (name) {
      return name
        .split(' ')
        .map(word => word[0])
        .join('')
        .toUpperCase()
        .slice(0, 2);
    }
    return null; // Return null for default icon state
  };

  // Get border radius based on variant
  const getBorderRadius = () => {
    switch (variant) {
      case 'circle':
        return avatarSize / 2;
      case 'rounded':
        return moderateScale(config.radius);
      case 'square':
        return 0;
      default:
        return avatarSize / 2;
    }
  };

  // Get status indicator color
  const getStatusColor = () => {
    if (customStatusColor) return customStatusColor;

    switch (status) {
      case 'online':
        return '#10B981';
      case 'offline':
        return '#6B7280';
      case 'away':
        return '#F59E0B';
      case 'busy':
        return '#EF4444';
      default:
        return '#6B7280';
    }
  };

  // Get badge text
  const getBadgeText = () => {
    if (typeof badge === 'string') return badge;
    if (typeof badge === 'number') {
      return badge > maxBadgeCount ? `${maxBadgeCount}+` : badge.toString();
    }
    return '';
  };

  // Calculate emoji position styles
  const getEmojiPositionStyle = (emojiConfig: EmojiConfig): ViewStyle => {
    const emojiSize = emojiConfig.size || avatarSize * 0.35;
    // Increase offset to place emoji clearly outside avatar boundaries
    const offset = emojiSize * 0.6;

    const baseStyle: ViewStyle = {
      position: 'absolute',
      width: emojiSize,
      height: emojiSize,
      borderRadius: emojiSize / 2,
      backgroundColor: emojiConfig.backgroundColor || '#FFFFFF',
      borderWidth: emojiConfig.borderWidth || 2,
      borderColor: emojiConfig.borderColor || '#FFFFFF',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 10,
      // Add shadow for better visibility
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    };

    switch (emojiConfig.position) {
      case 'top-right':
        return {
          ...baseStyle,
          top: -offset + (emojiConfig.offsetY || 0),
          right: -offset + (emojiConfig.offsetX || 0),
        };
      case 'top-left':
        return {
          ...baseStyle,
          top: -offset + (emojiConfig.offsetY || 0),
          left: -offset + (emojiConfig.offsetX || 0),
        };
      case 'bottom-right':
        return {
          ...baseStyle,
          bottom: -offset + (emojiConfig.offsetY || 0),
          right: -offset + (emojiConfig.offsetX || 0),
        };
      case 'bottom-left':
        return {
          ...baseStyle,
          bottom: -offset + (emojiConfig.offsetY || 0),
          left: -offset + (emojiConfig.offsetX || 0),
        };
      case 'center':
        return {
          ...baseStyle,
          top: '50%',
          left: '50%',
          transform: [
            { translateX: -(emojiSize / 2) + (emojiConfig.offsetX || 0) },
            { translateY: -(emojiSize / 2) + (emojiConfig.offsetY || 0) },
          ],
        };
      case 'floating':
        return {
          ...baseStyle,
          top: -emojiSize + (emojiConfig.offsetY || 0), // Position completely above avatar
          left: avatarSize / 2 - emojiSize / 2 + (emojiConfig.offsetX || 0),
        };
      default:
        return baseStyle;
    }
  };

  // Main avatar container style (no overflow hidden to allow emojis to show)
  const containerStyle: ViewStyle = {
    width: avatarSize,
    height: avatarSize,
    position: 'relative',
    alignSelf: 'flex-start', // Prevent container from expanding
    ...style,
  };

  // Determine background color based on content
  const getBackgroundColor = () => {
    // Loading state uses gray background
    if (loading) {
      return colors.figma.grayscale.surface.default;
    }
    
    const initials = getInitials();
    if (initials) {
      // User has name - use purple background
      return colors.figma.primary.surface.subtle;
    }
    // User has no name - use gray background
    return backgroundColor;
  };

  // Inner avatar style for the actual avatar content
  const avatarStyle: ViewStyle = {
    width: avatarSize,
    height: avatarSize,
    borderRadius: getBorderRadius(),
    borderWidth: moderateScale(borderWidth),
    borderColor,
    backgroundColor: getBackgroundColor(),
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden', // This clips the image content, not the emojis
    position: 'relative',
    ...(shadowEnabled && {
      shadowColor,
      shadowOffset: shadowOffset,
      shadowOpacity,
      shadowRadius: moderateScale(shadowRadius),
      elevation: shadowRadius,
    }),
    ...(gradientColors && {
      // Gradient would require react-native-linear-gradient
      backgroundColor: gradientColors[0],
    }),
  };

  // Image style
  const imageStyles: ImageStyle = {
    width: '100%',
    height: '100%',
    borderRadius: getBorderRadius(),
    ...imageStyle,
  };

  // Text style for initials
  const initialsStyle: TextStyle = {
    fontSize,
    fontWeight: '600',
    color: colors.figma.primary.surface.lighter,
    textAlign: 'center',
    fontFamily: 'Onest',
    ...textStyle,
  };

  // Render the main avatar content
  const renderAvatarContent = () => {
    // Show loading state when loading prop is true
    if (loading) {
      return (
        <Icon 
          name="essentials/User Light" 
          size={avatarSize * 0.6} 
          color={colors.figma.grayscale.text.light.lighter}
          style={{ opacity: 0.6 }}
        />
      );
    }
    
    if (loadingIndicator) {
      return placeholder || <Text style={initialsStyle}>...</Text>;
    }

    if (imageUrl && !imageError) {
      if (imageUrl.includes('digiflowtest.digiturk.com.tr') || imageUrl.startsWith('/')) {
        return (
          <AuthenticatedImage
            imageUrl={imageUrl}
            placeholderName={name}
            style={imageStyles}
            onError={() => setImageError(true)}
          />
        );
      } else {
        return (
          <Image
            source={{ uri: imageUrl }}
            style={imageStyles}
            onError={() => setImageError(true)}
            onLoadStart={() => setIsLoading(true)}
            onLoadEnd={() => setIsLoading(false)}
          />
        );
      }
    }

    if (fallbackImageUrl && imageError) {
      return (
        <Image
          source={{ uri: fallbackImageUrl }}
          style={imageStyles}
          onError={() => setImageError(true)}
        />
      );
    }

    if (errorFallback && imageError) {
      return errorFallback;
    }

    // Check if we have a name for initials
    const initials = getInitials();
    if (initials) {
      // User has name but no image - show initials with purple background
      return <Text style={initialsStyle}>{initials}</Text>;
    }
    
    // User has no name and no image - show user icon with gray background
    return (
      <Icon 
        name="essentials/User Light" 
        size={avatarSize * 0.6} 
        color={colors.figma.grayscale.text.light.lighter}
      />
    );
  };

  // Render emoji overlay
  const renderEmoji = () => {
    if (!showEmoji || !emoji) return null;

    return (
      <View style={getEmojiPositionStyle(emoji)}>
        <Text style={{ fontSize: (emoji.size || avatarSize * 0.4) * 0.6 }}>
          {emoji.emoji}
        </Text>
      </View>
    );
  };

  // Render status indicator
  const renderStatusIndicator = () => {
    if (!showStatusIndicator || status === 'none') return null;

    const indicatorSize = avatarSize * 0.25;
    const indicatorStyle: ViewStyle = {
      position: 'absolute',
      bottom: moderateScale(2),
      right: moderateScale(2),
      width: indicatorSize,
      height: indicatorSize,
      borderRadius: indicatorSize / 2,
      backgroundColor: getStatusColor(),
      borderWidth: moderateScale(2),
      borderColor: '#FFFFFF',
    };

    return <View style={indicatorStyle} />;
  };

  // Render badge
  const renderBadge = () => {
    if (!showBadge || !badge) return null;

    const badgeText = getBadgeText();
    const badgeSize = avatarSize * 0.35;
    const badgeStyle: ViewStyle = {
      position: 'absolute',
      top: -moderateScale(4),
      right: -moderateScale(4),
      minWidth: badgeSize,
      height: badgeSize,
      borderRadius: badgeSize / 2,
      backgroundColor: badgeColor,
      borderWidth: moderateScale(2),
      borderColor: '#FFFFFF',
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: moderateScale(4),
    };

    const badgeTextStyle: TextStyle = {
      fontSize: responsiveFontSize(config.fontSize * 0.6),
      fontWeight: '600',
      color: badgeTextColor,
      textAlign: 'center',
    };

    return (
      <View style={badgeStyle}>
        <Text style={badgeTextStyle}>{badgeText}</Text>
      </View>
    );
  };

  // Render overlay content
  const renderOverlay = () => {
    if (!overlayText && !overlayIcon) return null;

    return (
      <View style={styles.overlay}>
        {overlayIcon && <View style={styles.overlayIcon}>{overlayIcon}</View>}
        {overlayText && (
          <Text style={[styles.overlayText, { fontSize: fontSize * 0.7 }]}>
            {overlayText}
          </Text>
        )}
      </View>
    );
  };

  // Main component
  const avatar = (
    <View
      style={containerStyle}
      testID={testID}
      accessible={true}
      accessibilityLabel={accessibilityLabel || `Avatar for ${name}`}
      accessibilityHint={accessibilityHint}
      accessibilityRole="image"
    >
      {/* Inner avatar container with clipped content */}
      <View style={avatarStyle}>
        {renderAvatarContent()}
        {renderOverlay()}
        {renderStatusIndicator()}
      </View>
      {/* Outer positioned elements that can extend beyond avatar */}
      {renderEmoji()}
      {renderBadge()}
    </View>
  );

  // Wrap with TouchableOpacity if pressable
  if (pressable || onPress || onLongPress) {
    return (
      <TouchableOpacity
        onPress={onPress}
        onLongPress={onLongPress}
        disabled={disabled}
        activeOpacity={0.7}
        style={{ alignSelf: 'flex-start' }}
      >
        {avatar}
      </TouchableOpacity>
    );
  }

  return avatar;
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 999, // Will be overridden by container borderRadius
  },
  overlayIcon: {
    marginBottom: moderateVerticalScale(2),
  },
  overlayText: {
    color: '#FFFFFF',
    fontWeight: '600',
    textAlign: 'center',
  },
});

// Export common avatar configurations
export const AvatarPresets = {
  // Size presets
  small: { size: 'sm' as AvatarSize },
  medium: { size: 'md' as AvatarSize },
  large: { size: 'lg' as AvatarSize },

  // Status presets
  online: {
    showStatusIndicator: true,
    status: 'online' as AvatarStatusIndicator
  },
  offline: {
    showStatusIndicator: true,
    status: 'offline' as AvatarStatusIndicator
  },

  // Emoji presets
  withReaction: {
    showEmoji: true,
    emoji: {
      emoji: '😊',
      position: 'bottom-right' as EmojiPosition,
      backgroundColor: '#FFFFFF',
      borderColor: '#E5E7EB',
    }
  },

  // Badge presets
  withNotification: {
    showBadge: true,
    badgeColor: '#EF4444',
  },

  // Style presets
  bordered: {
    borderWidth: 2,
    borderColor: '#E5E7EB',
    shadowEnabled: true,
  },

  // Interactive presets
  pressable: {
    pressable: true,
    shadowEnabled: true,
  }
};

export default Avatar;