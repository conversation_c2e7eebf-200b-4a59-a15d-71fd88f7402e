import React, { useState, useEffect, useLayoutEffect, useRef, forwardRef, useMemo, useCallback, useImperativeHandle } from 'react'
import './SelectBox.css'
import { IOption } from '@/types'
import { useTranslation } from 'react-i18next'
import debounce from 'lodash/debounce'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import { WPopper } from 'wface'
import { SxProps } from '@mui/material'
import { Theme } from 'react-select'

interface SelectBoxProps {
  id?: string
  label: string
  name?: string
  value: IOption | IOption[] | null
  options: IOption[]
  error?: string
  onChange: (option: IOption | IOption[] | null) => void
  fullWidth?: boolean
  multiple?: boolean
  disabled?: boolean
  isLoading?: boolean
  searchable?: boolean
  defaultText?: string | null
  defaultItem?: IOption
  size?: 'small' | 'medium' | 'default'
  style?: React.CSSProperties
  inputStyle?: React.CSSProperties
  dropdownStyle?: React.CSSProperties
  itemStyle?: React.CSSProperties
  labelStyle?: React.CSSProperties
  variant?: 'outlined' | 'filled' | 'standard'
  sx?: SxProps<Theme> // Add sx prop
  containerSx?: SxProps<Theme> // Additional sx prop for container
  dropdownSx?: SxProps<Theme> // Additional sx prop for dropdown
  inputSx?: SxProps<Theme> // Additional sx prop for input
  ref?: React.Ref<HTMLDivElement> // Add ref prop for React 19 compatibility
}

const ITEMS_PER_PAGE = 20 // Number of items to load at once

const SelectBox = forwardRef<HTMLDivElement, SelectBoxProps>(
  (
    {
      id = 'select-box',
      label,
      disabled = false,
      value,
      options = [],
      name,
      error,
      onChange,
      fullWidth = true,
      multiple = false,
      isLoading = false,
      searchable = false,
      defaultText = ' ',
      defaultItem = null,
      size = 'small',
      style,
      inputStyle,
      dropdownStyle,
      itemStyle,
      labelStyle,
      variant = 'outlined',
      sx,
      containerSx,
      dropdownSx,
      inputSx,
    },
    ref,
  ) => {
    const { i18n } = useTranslation()
    const [isOpen, setIsOpen] = useState(false)
    const [searchInput, setSearchInput] = useState('')
    const containerRef = useRef<HTMLDivElement>(null)
    const inputRef = useRef<HTMLInputElement>(null)
    const dropdownRef = useRef<HTMLDivElement>(null)
    const optionRefs = useRef<(HTMLDivElement | null)[]>([])
    const [highlightedIndex, setHighlightedIndex] = useState<number>(0)
    const [dropdownPlacement, setDropdownPlacement] = useState<'bottom-start' | 'top-start'>('bottom-start')
    const typedStringRef = useRef<string>('')
    const initialCalculationDone = useRef(false)
    const [visibleOptionsCount, setVisibleOptionsCount] = useState(ITEMS_PER_PAGE)

    // Forward ref to the container div for React 19 compatibility
    useImperativeHandle(ref, () => containerRef.current!, [])

    const updateDropdownPosition = useCallback(() => {
      if (!inputRef.current) return

      const inputRect = inputRef.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const viewportMiddle = viewportHeight / 1.5

      // If selectbox is below the middle of the screen, open above
      // If selectbox is above the middle of the screen, open below
      if (inputRect.top > viewportMiddle) {
        setDropdownPlacement('top-start')
      } else {
        setDropdownPlacement('bottom-start')
      }
    }, [])

    const debouncedResetTypedString = useMemo(
      () =>
        debounce(() => {
          typedStringRef.current = ''
        }, 500),
      [],
    )

    // Consolidated effect for dropdown positioning and cleanup
    useLayoutEffect(() => {
      if (isOpen && inputRef.current) {
        updateDropdownPosition()
        window.addEventListener('resize', updateDropdownPosition)
        window.addEventListener('scroll', updateDropdownPosition, true)

        return () => {
          window.removeEventListener('resize', updateDropdownPosition)
          window.removeEventListener('scroll', updateDropdownPosition, true)
        }
      }
    }, [isOpen, updateDropdownPosition])

    // Cleanup effect for debounced function
    useEffect(() => {
      return () => {
        debouncedResetTypedString.cancel()
      }
    }, [debouncedResetTypedString])

    const calculateDropdownPlacement = useCallback(() => {
      if (!inputRef.current) return 'bottom-start'

      const inputRect = inputRef.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const viewportMiddle = viewportHeight / 2

      // If selectbox is below the middle of the screen, open above
      // If selectbox is above the middle of the screen, open below
      return inputRect.top > viewportMiddle ? 'top-start' : 'bottom-start'
    }, [])

    const defaultOption: IOption = useMemo(() => {
      if (defaultItem) {
        return defaultItem
      }
      return {
        value: options.find((opt) => opt.value === 0) && defaultText ? -1 : 0,
        label: defaultText?.trim() ?? (i18n.language === 'tr' ? 'Seçiniz' : 'Select'),
        labelEn: defaultText?.trim() ?? 'Select',
      }
    }, [i18n.language, defaultText, defaultItem, options])

    const allOptions = useMemo(() => {
      if (defaultItem || defaultText) {
        return [defaultOption, ...options]
      }
      return options
    }, [defaultOption, defaultItem, defaultText, options])

    const normalizedValue: any = useMemo(() => {
      if (multiple) {
        return Array.isArray(value)
          ? value.map((v) => (typeof v === 'object' ? v : (allOptions.find((opt) => opt.value === v) ?? { value: v, label: String(v) })))
          : []
      } else {
        if (typeof value === 'object' && value !== null) return value
        const option = allOptions.find((opt) => opt.value == value)
        return option ?? null
      }
    }, [value, allOptions, multiple])

    const inputValue = useMemo(() => {
      if (isLoading) return ''
      if (searchable) return searchInput !== '' || isOpen ? searchInput : (normalizedValue?.label ?? defaultOption.label)
      if (normalizedValue && !isOpen) {
        if (multiple) {
          return (
            (normalizedValue as IOption[])
              .map((opt) => (i18n.language === 'tr' ? opt.label : opt.labelEn))
              .filter(Boolean)
              .join(', ') || ''
          )
        } else {
          return i18n.language === 'tr'
            ? ((normalizedValue as IOption)?.label ?? (normalizedValue as IOption)?.labelEn ?? '')
            : ((normalizedValue as IOption)?.labelEn ?? (normalizedValue as IOption)?.label ?? '')
        }
      }
      return ''
    }, [isLoading, searchable, searchInput, normalizedValue, multiple, i18n.language, isOpen, defaultOption.label])

    const handleToggleDropdown = useCallback(() => {
      if (!disabled && !isLoading) {
        setIsOpen((prev) => {
          if (!prev) {
            updateDropdownPosition()
          }
          return !prev
        })
        if (!isOpen) {
          if (!searchable) {
            setSearchInput('')
          }
          setHighlightedIndex(0)
        }
      }
    }, [disabled, isLoading, isOpen, searchable, updateDropdownPosition])

    const handleSelectItem = useCallback(
      (selectedValue: string | number) => {
        if (multiple) {
          const selectedOptions = Array.isArray(normalizedValue) ? [...normalizedValue] : []
          const optionIndex = selectedOptions.findIndex((option) => option.value.toString() === selectedValue.toString())

          if (optionIndex > -1) {
            selectedOptions.splice(optionIndex, 1)
          } else {
            const selectedOption = allOptions.find((option) => option.value.toString() === selectedValue.toString())
            if (selectedOption) {
              selectedOptions.push(selectedOption)
            }
          }
          setSearchInput('')
          onChange(selectedOptions.length ? selectedOptions : null)
        } else {
          const selectedOption = allOptions.find((option) => option.value.toString() === selectedValue.toString())
          setSearchInput('')
          setIsOpen(false)
          onChange(selectedOption ?? null)
        }
      },
      [multiple, normalizedValue, allOptions, onChange],
    )

    const updateSearchInput = useCallback(() => {
      if (multiple && Array.isArray(normalizedValue)) {
        setSearchInput(
          normalizedValue
            .map((opt) => (i18n.language === 'tr' ? opt.label : opt.labelEn))
            .filter(Boolean)
            .join(', '),
        )
      } else if (!multiple && normalizedValue) {
        setSearchInput('')
      } else {
        setSearchInput('')
      }
    }, [normalizedValue, multiple, i18n.language])

    const filteredOptions = useMemo(() => {
      if (searchable && searchInput.trim() !== '') {
        const labelKey = i18n.language === 'tr' ? 'label' : 'labelEn'
        const searchValue = searchInput.toLowerCase()

        return allOptions.filter((option) => {
          if (option.isDisabled) return false
          const labelValue = option[labelKey]?.toLowerCase() ?? ''
          return labelValue.includes(searchValue)
        })
      }
      return allOptions
    }, [searchInput, allOptions, searchable, i18n.language])

    const sortedOptions = useMemo(() => {
      if (multiple) {
        return [...filteredOptions].sort((a, b) => {
          const isASelected = Array.isArray(normalizedValue) && normalizedValue.some((item) => item.value === a.value)
          const isBSelected = Array.isArray(normalizedValue) && normalizedValue.some((item) => item.value === b.value)
          return isASelected === isBSelected ? 0 : isASelected ? -1 : 1
        })
      } else {
        return filteredOptions
      }
    }, [filteredOptions, normalizedValue, multiple])

    const handleKeyDown = useCallback(
      (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (!isOpen) {
          if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            setIsOpen(true)
            setHighlightedIndex(0)
            e.preventDefault()
          }
          return
        }

        switch (e.key) {
          case 'ArrowDown':
            setHighlightedIndex((prev) => (prev < sortedOptions.length - 1 ? prev + 1 : 0))
            e.preventDefault()
            break
          case 'ArrowUp':
            setHighlightedIndex((prev) => (prev > 0 ? prev - 1 : sortedOptions.length - 1))
            e.preventDefault()
            break
          case 'Enter':
            if (sortedOptions[highlightedIndex] && !sortedOptions[highlightedIndex].isDisabled) {
              handleSelectItem(sortedOptions[highlightedIndex].value)
              e.preventDefault()
              setIsOpen(multiple)
            }
            break
          case 'Escape':
            setIsOpen(false)
            break
          default:
            if (!searchable) {
              typedStringRef.current += e.key
              debouncedResetTypedString()

              const labelKey = i18n.language === 'tr' ? 'label' : 'labelEn'
              const searchString = typedStringRef.current.toLowerCase()

              const startIndex = highlightedIndex + 1
              let index = sortedOptions.findIndex((option, idx) => {
                if (idx < startIndex) return false
                return option[labelKey]?.toLowerCase().startsWith(searchString)
              })
              if (index === -1) {
                index = sortedOptions.findIndex((option) => option[labelKey]?.toLowerCase().startsWith(searchString))
              }
              if (index !== -1) {
                setHighlightedIndex(index)
                optionRefs.current[index]?.scrollIntoView({
                  block: 'nearest',
                })
              }
            }
            break
        }
      },
      [isOpen, sortedOptions, highlightedIndex, handleSelectItem, i18n.language, searchable, debouncedResetTypedString, multiple],
    )

    const handleMouseOver = (index: number) => {
      setHighlightedIndex(index)
    }

    const handleClickAway = () => {
      setIsOpen(false)
      updateSearchInput()
    }

    const handleScroll = useCallback(
      (e: React.UIEvent<HTMLDivElement>) => {
        const target = e.target as HTMLDivElement
        const { scrollTop, scrollHeight, clientHeight } = target

        // If scrolled near the bottom (within 20px), load more items
        if (scrollHeight - scrollTop - clientHeight < 20) {
          setVisibleOptionsCount((prev) => Math.min(prev + ITEMS_PER_PAGE, sortedOptions.length))
        }
      },
      [sortedOptions.length],
    )

    // Consolidated effect for dropdown state management
    useEffect(() => {
      if (!isOpen) {
        updateSearchInput()
      }
      setVisibleOptionsCount(ITEMS_PER_PAGE)
      setHighlightedIndex(0)
    }, [isOpen, searchInput, updateSearchInput])

    // Get the subset of options to display
    const visibleOptions = useMemo(() => {
      return sortedOptions.slice(0, visibleOptionsCount)
    }, [sortedOptions, visibleOptionsCount])

    // Separate effect for scrolling highlighted option into view
    useLayoutEffect(() => {
      if (isOpen && optionRefs.current[highlightedIndex]) {
        optionRefs.current[highlightedIndex]?.scrollIntoView({
          block: 'nearest',
        })
      }
    }, [highlightedIndex, isOpen])

    // Initial dropdown placement calculation
    useLayoutEffect(() => {
      if (!initialCalculationDone.current && inputRef.current) {
        const initialPlacement = calculateDropdownPlacement()
        setDropdownPlacement(initialPlacement)
        initialCalculationDone.current = true
      }
    }, [calculateDropdownPlacement])

    return (
      <>
        <div
          className={`select-box ${fullWidth ? 'full-width' : ''} ${disabled ? 'disabled' : ''} ${size} ${variant}`}
          ref={containerRef}
          style={{
            ...(containerSx as React.CSSProperties),
            ...style,
          }}
        >
          {isLoading && <div className="select-box-spinner" />}
          <input
            ref={inputRef}
            id={id}
            name={name}
            type="text"
            className={`select-box-select ${size} ${variant}`}
            style={{
              ...(inputSx as React.CSSProperties),
              ...inputStyle,
            }}
            value={inputValue}
            placeholder=" "
            readOnly={!searchable}
            onClick={handleToggleDropdown}
            onChange={(e) => {
              setSearchInput(e.target.value)
            }}
            onKeyDown={handleKeyDown}
            disabled={disabled || isLoading || options.length === 0}
            autoComplete="off"
            aria-haspopup="listbox"
            aria-expanded={isOpen}
          />
          <label
            className={`select-box-label ${size} ${variant}`}
            style={{
              ...(disabled || isLoading ? { color: '#999' } : {}),
              ...labelStyle,
            }}
          >
            {label}
          </label>
          {!isLoading && !disabled && options.length > 0 && (
            <div
              className={`select-box-arrow ${isOpen ? 'open' : ''} ${dropdownPlacement === 'top-start' ? 'upward' : ''} ${size}`}
              onClick={handleToggleDropdown}
              aria-hidden="true"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M7 10l5 5 5-5H7z" fill="currentColor" />
              </svg>
            </div>
          )}
          {!isLoading && (options.length === 0 || disabled) && (
            <div className={`select-box-disabled-icon ${size}`}>
              <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path
                  d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 12.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12 17 15.59z"
                  fill="currentColor"
                />
              </svg>
            </div>
          )}
        </div>
        <WPopper
          open={isOpen}
          anchorEl={inputRef.current}
          placement={dropdownPlacement}
          style={{
            ...(sx as React.CSSProperties),
            fontFamily: "'Roboto', 'Helvetica', 'Arial', sans-serif",
            fontSize: '14px',
            zIndex: 1111300,
            width: containerRef.current?.offsetWidth,
          }}
        >
          <ClickAwayListener onClickAway={handleClickAway}>
            <div
              ref={dropdownRef}
              className={`select-box-dropdown ${dropdownPlacement === 'top-start' ? 'dropdown-upward' : ''} ${size} ${variant}`}
              style={{
                ...(dropdownSx as React.CSSProperties),
                ...dropdownStyle,
              }}
              role="listbox"
              aria-labelledby={id}
              onScroll={handleScroll}
            >
              {visibleOptions
                .filter((val) => !val.isDisabled || (val.isDisabled && searchInput === ''))
                .map((option, index) => (
                  <div
                    key={option.value}
                    ref={(el) => {
                      optionRefs.current[index] = el
                    }}
                    className={`select-box-item ${size} ${variant} ${option.isDisabled ? 'disabled' : ''} ${
                      Array.isArray(normalizedValue)
                        ? normalizedValue.some((item) => item.value === option.value)
                          ? 'selected'
                          : ''
                        : normalizedValue?.value === option.value
                          ? 'selected'
                          : ''
                    } ${index === highlightedIndex ? 'highlighted' : ''} ${
                      Array.isArray(normalizedValue) && normalizedValue.some((item) => item.value === option.value) && index === highlightedIndex
                        ? 'selected-highlighted'
                        : ''
                    } ${size}`}
                    style={itemStyle}
                    onClick={() => !option.isDisabled && handleSelectItem(option.value)}
                    onMouseOver={() => handleMouseOver(index)}
                    role="option"
                    aria-selected={
                      Array.isArray(normalizedValue)
                        ? normalizedValue.some((item) => item.value === option.value)
                        : normalizedValue?.value === option.value
                    }
                  >
                    {i18n.language === 'tr' ? option.label : option.labelEn}
                  </div>
                ))}

              {visibleOptionsCount < sortedOptions.length && (
                <div className="select-box-loading-more" style={{ textAlign: 'center', padding: '8px', color: '#666' }}>
                  Loading more...
                </div>
              )}
            </div>
          </ClickAwayListener>
        </WPopper>
        {error && <div className={`select-box-error ${size}`}>{error}</div>}
      </>
    )
  },
)

SelectBox.displayName = 'SelectBox'

// Custom comparison function for React.memo
const areEqual = (
  prevProps: Readonly<Omit<SelectBoxProps, 'ref'> & React.RefAttributes<HTMLDivElement>>,
  nextProps: Readonly<Omit<SelectBoxProps, 'ref'> & React.RefAttributes<HTMLDivElement>>,
) => {
  // Check value changes
  const prevValue = prevProps.value
  const nextValue = nextProps.value

  // Handle array values (multiple select)
  if (Array.isArray(prevValue) && Array.isArray(nextValue)) {
    if (prevValue.length !== nextValue.length) return false
    // Deep comparison of array values
    for (let i = 0; i < prevValue.length; i++) {
      if (prevValue[i]?.value !== nextValue[i]?.value) return false
    }
  } else if (!Array.isArray(prevValue) && !Array.isArray(nextValue)) {
    // Both are single values (IOption or null)
    const prevOption = prevValue
    const nextOption = nextValue
    if (prevOption?.value !== nextOption?.value) return false
  } else {
    // One is array, other is not - definitely changed
    return false
  }

  // Check other important props
  return (
    prevProps.disabled === nextProps.disabled &&
    prevProps.isLoading === nextProps.isLoading &&
    prevProps.error === nextProps.error &&
    prevProps.options === nextProps.options && // Reference equality check
    prevProps.onChange === nextProps.onChange && // Function reference check
    prevProps.label === nextProps.label
  )
}

export default React.memo(SelectBox, areEqual)
