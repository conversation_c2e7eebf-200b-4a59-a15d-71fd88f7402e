import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { colors, spacing, fontSizes } from '../styles/theme';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../navigation/types';

type WorkflowNavigationProp = StackNavigationProp<RootStackParamList>;

interface WorkflowSummarySheetProps {
  isVisible: boolean;
  onClose: () => void;
}

const WindowHeight = Dimensions.get('window').height;

const WorkflowSummarySheet: React.FC<WorkflowSummarySheetProps> = ({
  isVisible,
  onClose,
}) => {
  const navigation = useNavigation<WorkflowNavigationProp>();

  if (!isVisible) {return null;}

  const workflowCategories = [
    { id: 'surveys', title: 'Anketlerim', count: 4, badge: 1 },
    { id: 'tasks', title: 'Üzerim<PERSON><PERSON>', count: 7, badge: 3 },
    { id: 'history', title: '<PERSON><PERSON><PERSON> Geçmişi', count: 85 },
    { id: 'pending', title: 'Askıda Bekleyenler', count: 1 },
    {
      id: 'delegation',
      title: 'Delegasyon Talebi',
      count: 2,
      url: 'https://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation',
    },
    { id: 'delegationCancel', title: 'Delegasyon İptali', count: 0 },
    { id: 'viewing', title: 'Görüntüleme Talebi', count: 0 },
    { id: 'viewingCancel', title: 'Görüntüleme İptali', count: 0 },
  ];

  const handleCategoryPress = async (category: any) => {
    if (category.id === 'delegation' && category.url) {
      onClose(); // Close the sheet
      
      // Get user ID for loginId parameter
      try {
        const { useAuthStore } = await import('../store/authStore');
        const user = useAuthStore.getState().user;
        const { getNumericUserId } = await import('../services/api');
        const numericUserId = await getNumericUserId();
        
        // Add loginId to URL
        let finalUrl = category.url;
        if (numericUserId || user?.id) {
          const urlObj = new URL(category.url);
          urlObj.searchParams.set('loginId', numericUserId || user?.id || '');
          finalUrl = urlObj.toString();
        }
        
        // Navigate to WebView with Windows Authentication
        navigation.navigate('WebView', {
          url: finalUrl,
          title: category.title,
        });
      } catch (error) {
        console.error('Error getting user ID for delegation:', error);
        // Fallback to URL without loginId
        navigation.navigate('WebView', {
          url: category.url,
          title: category.title,
        });
      }
    }
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.backdrop}
        activeOpacity={1}
        onPress={onClose}
      />
      <View style={styles.sheet}>
        <View style={styles.handleContainer}>
          <View style={styles.handle} />
        </View>

        <Text style={styles.sheetTitle}>İş Akışları Özeti</Text>

        <View style={styles.summaryContainer}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryNumber}>7</Text>
            <Text style={styles.summaryLabel}>Bekleyen</Text>
          </View>
          <View style={styles.divider} />
          <View style={styles.summaryItem}>
            <Text style={styles.summaryNumber}>85</Text>
            <Text style={styles.summaryLabel}>Tamamlanan</Text>
          </View>
        </View>

        <ScrollView style={styles.listContainer}>
          {workflowCategories.map((category) => (
            <TouchableOpacity
              key={category.id}
              style={styles.listItem}
              onPress={() => handleCategoryPress(category)}
            >
              <View style={styles.listItemTitleContainer}>
                <Text style={styles.listItemText}>{category.title}</Text>
                {category.badge && (
                  <View style={styles.badgeContainer}>
                    <Text style={styles.badgeText}>{category.badge}</Text>
                  </View>
                )}
              </View>
              <View style={styles.countContainer}>
                <Text style={styles.countText}>{category.count}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'flex-end',
    zIndex: 999,
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  sheet: {
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 30,
    maxHeight: WindowHeight * 0.8,
  },
  handleContainer: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  handle: {
    width: 36,
    height: 4,
    backgroundColor: '#E0E0E0',
    borderRadius: 2,
  },
  sheetTitle: {
    fontSize: 22,
    fontWeight: '600',
    textAlign: 'center',
    color: '#333',
    marginBottom: 24,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#F6F7F9',
    marginHorizontal: 16,
    borderRadius: 16,
    padding: 16,
    marginBottom: 20,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 32,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#8A94A6',
  },
  divider: {
    width: 1,
    backgroundColor: '#E0E0E0',
    height: '100%',
  },
  listContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  listItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  listItemTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItemText: {
    fontSize: 16,
    color: '#333',
  },
  badgeContainer: {
    backgroundColor: '#E53935',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  countContainer: {
    backgroundColor: '#F6F7F9',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    minWidth: 40,
    alignItems: 'center',
  },
  countText: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
});

export default WorkflowSummarySheet;
