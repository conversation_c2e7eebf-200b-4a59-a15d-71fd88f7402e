import React, { forwardRef, useCallback, useState, useMemo, useEffect } from 'react'
import { WTable, WBox } from 'wface'
import { recursiveTableTitleGenerator, removeItemFromMemory } from '@/utils/helpers/wface/WTableHelpers/recursiveTableTitleGenerator'
import { useTranslation } from 'react-i18next'
import { Loading } from '@/components/Loading/Loading'
import './DigiTable.css'
// Dynamic imports for heavy libraries - loaded on demand
// import { jsPDF } from 'jspdf'
// import { autoTable } from 'jspdf-autotable'
import debounce from 'lodash/debounce'

import AddBox from '@mui/icons-material/AddBox'
import Check from '@mui/icons-material/Check'
import Clear from '@mui/icons-material/Clear'
import Remove from '@mui/icons-material/Remove'
import DeleteOutline from '@mui/icons-material/DeleteOutline'
import ChevronRight from '@mui/icons-material/ChevronRight'
import Edit from '@mui/icons-material/Edit'
import SaveAlt from '@mui/icons-material/SaveAlt'
import FilterList from '@mui/icons-material/FilterList'
import FirstPage from '@mui/icons-material/FirstPage'
import LastPage from '@mui/icons-material/LastPage'
import ChevronLeft from '@mui/icons-material/ChevronLeft'
import ArrowDownward from '@mui/icons-material/ArrowDownward'
import ViewColumn from '@mui/icons-material/ViewColumn'
import dayjs from 'dayjs'
import { exportTableToExcel } from '@/utils/helpers/wface/WTableHelpers/exportTableToExcel'
import FilterButton from '@/components/filters/FilterButton/FilterButton'
import isBetween from 'dayjs/plugin/isBetween'
import { tableFieldValueGetters } from '@/utils/helpers/workflow/tableHelpers'
import { Column, MaterialTableProps } from '@/types/WFaceTypes'
import TablePaginationWrapper from './TablePaginationWrapper'
import useMediaQuery from '@/hooks/useMediaQuery'
import MobileTable from './MobileTable'

// Extend dayjs with the isBetween plugin
dayjs.extend(isBetween)

// Define columns to be excluded from PDF export
export const excludedColumns = ['copy', 'bypass', 'remind']

const replaceTurkishChars = (str: string): string => {
  return str
    .replace(/ı/g, 'i')
    .replace(/İ/g, 'I')
    .replace(/ğ/g, 'g')
    .replace(/Ğ/g, 'G')
    .replace(/ü/g, 'u')
    .replace(/Ü/g, 'U')
    .replace(/ş/g, 's')
    .replace(/Ş/g, 'S')
    .replace(/ö/g, 'o')
    .replace(/Ö/g, 'O')
    .replace(/ç/g, 'c')
    .replace(/Ç/g, 'C')
}

const tableIcons = {
  Add: forwardRef<SVGSVGElement, any>((props, ref) => <AddBox {...props} ref={ref} />),
  Check: forwardRef<SVGSVGElement, any>((props, ref) => <Check {...props} ref={ref} />),
  Clear: forwardRef<SVGSVGElement, any>((props, ref) => <Clear {...props} ref={ref} />),
  Remove: forwardRef<SVGSVGElement, any>((props, ref) => <Remove {...props} ref={ref} />),
  Delete: forwardRef<SVGSVGElement, any>((props, ref) => <DeleteOutline {...props} ref={ref} />),
  DetailPanel: forwardRef<SVGSVGElement, any>((props, ref) => <ChevronRight {...props} ref={ref} />),
  Edit: forwardRef<SVGSVGElement, any>((props, ref) => <Edit {...props} ref={ref} />),
  Export: forwardRef<SVGSVGElement, any>((props, ref) => <SaveAlt {...props} ref={ref} />),
  Filter: forwardRef<SVGSVGElement, any>((props, ref) => <FilterList {...props} ref={ref} />),
  FirstPage: forwardRef<SVGSVGElement, any>((props, ref) => <FirstPage {...props} ref={ref} />),
  LastPage: forwardRef<SVGSVGElement, any>((props, ref) => <LastPage {...props} ref={ref} />),
  NextPage: forwardRef<SVGSVGElement, any>((props, ref) => <ChevronRight {...props} ref={ref} />),
  PreviousPage: forwardRef<SVGSVGElement, any>((props, ref) => <ChevronLeft {...props} ref={ref} />),
  ResetSearch: forwardRef<SVGSVGElement, any>((props, ref) => <Clear {...props} ref={ref} />),
  SortArrow: forwardRef<SVGSVGElement, any>((props, ref) => <ArrowDownward {...props} ref={ref} />),
  ViewColumn: forwardRef<SVGSVGElement, any>((props, ref) => <ViewColumn {...props} ref={ref} />),
}

interface PageNumberOptions {
  fontSize?: number
  font?: string
  fontStyle?: string
  color?: string
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  margin?: number
}

interface MobileTableConfig {
  titleFields?: string[]
  subtitleFields?: string[]
  rightFields?: string[]
}

interface DigiTableProps extends MaterialTableProps<any> {
  pageSize?: number
  pageSizeOptions?: number[]
  exportButton?: boolean
  filtering?: boolean
  search?: boolean
  tableType?: string
  grouping?: boolean
  selection?: boolean
  loading?: boolean // external loading control
  draggable?: boolean
  pageNumberOptions?: PageNumberOptions
  sorting?: boolean
  languageFile?: any
  actions?: any[]
  toolbar?: boolean
  paging?: boolean
  headerStyle?: React.CSSProperties
  titleFields?: string[]
  subtitleFields?: string[]
  mobileConfig?: MobileTableConfig
  useMobileTable?: boolean
}

const DigiTable = React.memo(
  ({
    data,
    columns,
    pageSize: initialPageSize,
    pageSizeOptions: initialPageSizeOptions,
    paging,
    exportButton,
    pageNumberOptions,
    onSelectionChange,
    draggable,
    filtering,
    tableType,
    headerStyle,
    toolbar,
    selection,
    grouping,
    languageFile,
    sorting,
    style,
    loading, // parent-based loading
    search,
    title,
    actions,
    mobileConfig,
    useMobileTable,
  }: DigiTableProps) => {
    const { i18n } = useTranslation()
    const [pageSize, setPageSize] = useState<number | undefined>(undefined)
    const [localPageSizeOptions, setLocalPageSizeOptions] = useState<number[] | undefined>(undefined)
    const [filters, setFilters] = useState<{ [key: string]: { type: string; value: any } }>({})
    const [sortConfig, setSortConfig] = useState<{ columnId: string; direction: 'asc' | 'desc' | null }>({ columnId: '', direction: null })
    const [_groupDataChanged, setGroupDataChanged] = useState<number>(0)
    const [searchFilter, setSearchFilter] = useState<string>('')
    const [isLoading, setIsLoading] = useState<boolean>(true)
    const isMobile = useMediaQuery('(max-width: 901px)')

    // Called when a group is removed from the table
    const handleGroupRemoved = useCallback((removedGroupData: any) => {
      if (removedGroupData?.groupBy && removedGroupData.value) {
        removeItemFromMemory({
          field: removedGroupData.groupBy,
          value: removedGroupData.value,
        })
      }
      setGroupDataChanged((prev) => prev + 1)
    }, [])

    // Once data changes, determine pageSize & loading
    useEffect(() => {
      const dataLength = data?.length ?? 0

      if (dataLength > 0) {
        if (dataLength < 10) {
          setPageSize(dataLength)
          setLocalPageSizeOptions([dataLength])
        } else {
          setPageSize(initialPageSize ?? 10)
          setLocalPageSizeOptions(initialPageSizeOptions ?? [5, 10, 20, 50])
        }
      } else {
        // Even if data is empty, set default fallback
        setPageSize(10)
        setLocalPageSizeOptions([5, 10, 20, 50])
      }

      setIsLoading(false)
    }, [data, initialPageSize, initialPageSizeOptions])

    const removeHtmlTags = (html: string): string => {
      return html.replace(/<[^>]*>/g, '')
    }

    // Filter logic
    const applyFilters = useCallback(
      (rowData: any) => {
        for (const [field, filter] of Object.entries(filters)) {
          const value = rowData[field]
          if (value == null) return false

          // For multiselect
          if (filter.type === 'selectMultiple') {
            return Array.isArray(filter.value) && filter.value.includes(value)
          }

          const term = filter.value
          const termLower = typeof term === 'string' ? term.toLowerCase() : term
          const valueLower = typeof value === 'string' ? value.toLowerCase() : value

          switch (filter.type) {
            case 'contains':
              if (!valueLower?.toString().includes(termLower)) return false
              break
            case 'doesntContain':
              if (valueLower?.toString().includes(termLower)) return false
              break
            case 'equals':
              if (valueLower?.toString() !== termLower) return false
              break
            case 'doesntEqual':
              if (valueLower?.toString() === termLower) return false
              break
            case 'startsWith':
              if (!valueLower?.toString().startsWith(termLower)) return false
              break
            case 'endsWith':
              if (!valueLower?.toString().endsWith(termLower)) return false
              break
            case 'isLessThan':
              if (typeof value === 'number') {
                if (value >= Number(term)) return false
              } else if (value instanceof Date) {
                if (!dayjs(value).isBefore(dayjs(term))) return false
              }
              break
            case 'isLessThanOrEqualTo':
              if (typeof value === 'number') {
                if (value > Number(term)) return false
              } else if (value instanceof Date) {
                if (dayjs(value).isAfter(dayjs(term))) return false
              }
              break
            case 'isGreaterThan':
              if (typeof value === 'number') {
                if (value <= Number(term)) return false
              } else if (value instanceof Date) {
                if (!dayjs(value).isAfter(dayjs(term))) return false
              }
              break
            case 'isGreaterThanOrEqualTo':
              if (typeof value === 'number') {
                if (value < Number(term)) return false
              } else if (value instanceof Date) {
                if (dayjs(value).isBefore(dayjs(term))) return false
              }
              break
            case 'on':
              if (!dayjs(value).isSame(dayjs(term), 'day')) return false
              break
            case 'before':
              if (!dayjs(value).isBefore(dayjs(term), 'day')) return false
              break
            case 'after':
              if (!dayjs(value).isAfter(dayjs(term), 'day')) return false
              break
            case 'between':
              if (!dayjs(value).isBetween(dayjs(filter.value.start), dayjs(filter.value.end), 'day', '[]')) return false
              break
            default:
              break
          }
        }
        return true
      },
      [filters],
    )

    // Helper to get field’s value using table type logic
    const getFieldValue = useCallback(
      (rowData: any, field: string, currentTableType?: string) => {
        const isEnglish = i18n.language === 'en'
        currentTableType ??= tableType ?? 'default'
        const fieldValueGetter = tableFieldValueGetters[currentTableType] ?? tableFieldValueGetters.default
        return fieldValueGetter(rowData, field, isEnglish)
      },
      [i18n.language, tableType],
    )

    // applyFilter / clearFilter / handleSort
    const handleApplyFilter = useCallback((columnId: string, filter: { type: string; value: any }) => {
      setFilters((prev) => ({
        ...prev,
        [columnId]: filter,
      }))
    }, [])

    const handleClearFilter = useCallback((columnId: string) => {
      setFilters((prev) => {
        const copy = { ...prev }
        delete copy[columnId]
        return copy
      })
    }, [])

    const handleSort = useCallback((columnId: string, direction: 'asc' | 'desc' | null) => {
      setSortConfig({ columnId, direction })
    }, [])

    // Custom sorting
    const customSort = useCallback(
      (rows: any[]) => {
        if (!sortConfig.columnId || sortConfig.direction === null) {
          return rows
        }

        const isEnglish = i18n.language === 'en'
        const fieldValueGetter = tableFieldValueGetters[tableType ?? 'default'] ?? tableFieldValueGetters.default

        // Helper for empty checks
        const isEmptyValue = (val: any) => {
          return val === null || val === undefined || val === '' || (typeof val === 'string' && val.trim() === '')
        }

        return [...rows].sort((a, b) => {
          const aVal = fieldValueGetter(a, sortConfig.columnId, isEnglish)
          const bVal = fieldValueGetter(b, sortConfig.columnId, isEnglish)

          // both empty
          if (isEmptyValue(aVal) && isEmptyValue(bVal)) return 0
          // only a empty => goes after b
          if (isEmptyValue(aVal)) return 1
          // only b empty => after a
          if (isEmptyValue(bVal)) return -1

          if (typeof aVal === 'string' && typeof bVal === 'string') {
            return sortConfig.direction === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal)
          } else if (typeof aVal === 'number' && typeof bVal === 'number') {
            return sortConfig.direction === 'asc' ? aVal - bVal : bVal - aVal
          } else if (aVal instanceof Date && bVal instanceof Date) {
            return sortConfig.direction === 'asc' ? aVal.getTime() - bVal.getTime() : bVal.getTime() - aVal.getTime()
          } else {
            // fallback: compare as strings
            const aString = String(aVal).toLowerCase()
            const bString = String(bVal).toLowerCase()
            return sortConfig.direction === 'asc' ? aString.localeCompare(bString) : bString.localeCompare(aString)
          }
        })
      },
      [sortConfig, tableType, i18n.language],
    )

    // Text-based search (searchFilter)
    const filterTable = useCallback(
      (rows: any[]) => {
        return rows.filter((row) => {
          return Object.values(row).some((val) => {
            if (!val) return false
            return val.toString().toLowerCase().includes(searchFilter.toLowerCase())
          })
        })
      },
      [searchFilter],
    )

    // combine filter+sort+search
    const filteredAndSortedData = useMemo(() => {
      let result: any = data ?? []
      if (Object.keys(filters).length > 0) {
        result = result.filter(applyFilters)
      }
      if (searchFilter.trim() !== '') {
        result = filterTable(result)
      }
      return customSort(result)
    }, [data, filters, searchFilter, applyFilters, customSort, filterTable])

    // measure column widths
    const calculateMaxWidths = useCallback(() => {
      const font = '14px Arial'
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        return columns.map(() => 150)
      }

      ctx.font = font

      return columns.map((col: any) => {
        let maxWidth = ctx.measureText(col.title).width + 32
        if (data && Array.isArray(data)) {
          data.forEach((row: any) => {
            const cellValue = getFieldValue(row, col.field as string, tableType)
            const cellText = cellValue ? cellValue.toString() : ''
            const cellWidth = ctx.measureText(cellText).width + 32
            if (cellWidth > maxWidth) {
              maxWidth = cellWidth
            }
          })
        }
        maxWidth = Math.min(Math.max(maxWidth, 100), 400)
        return maxWidth
      })
    }, [columns, data, tableType, getFieldValue])

    const maxColumnWidths = calculateMaxWidths()

    // Add FilterButton UI to each column
    const columnsWithCustomFilter: Column<any>[] = useMemo(() => {
      return columns.map((col: any, idx: number) => ({
        ...col,
        cellStyle: {
          ...col.cellStyle,
          maxWidth: maxColumnWidths[idx],
          wordWrap: 'break-word',
          whiteSpace: 'normal',
        },
        headerStyle: {
          ...col.headerStyle,
          maxWidth: maxColumnWidths[idx],
          wordWrap: 'break-word',
          whiteSpace: 'normal',
        },
        title: (
          <div
            style={{
              display: 'flex',
              fontSize: 11,
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <span>{col.title}</span>
            <div className="filterButtonContainer" style={{ display: 'flex', alignItems: 'center' }}>
              <FilterButton
                columnId={col.field as string}
                onApply={(f) => handleApplyFilter(col.field as string, f)}
                onClear={() => handleClearFilter(col.field as string)}
                onSort={(dir) => handleSort(col.field as string, dir)}
                columnType={col?.dateSetting?.type ?? (Array.isArray(data) && data.length > 0 ? typeof data[0][col.field as string] : 'string')}
                currentFilter={filters[col.field as string] ?? null}
                currentSortDirection={sortConfig.columnId === col.field ? sortConfig.direction : null}
                showFilter={(col.filtering !== false && filtering) ?? col.filtering === true}
                sorting={sorting && (col.sorting ?? col.sorting === undefined)}
                data={data as any[]}
              />
            </div>
          </div>
        ),
      }))
    }, [columns, data, maxColumnWidths, filters, filtering, sortConfig, sorting, handleApplyFilter, handleClearFilter, handleSort])

    // Table options
    const tableOptions: any = useMemo(() => {
      return {
        pageSize,
        pageSizeOptions: localPageSizeOptions,
        paginationType: 'stepped',
        exportButton: exportButton ?? false,
        searchAutoFocus: false,
        filtering: false,
        showFirstLastPageButtons: true,
        sortConfig,
        search: search ?? false,
        grouping: grouping ?? false,
        sorting: false, // we do custom sorting
        toolbar: toolbar ?? false,
        selection: selection ?? false,
        draggable: draggable ?? false,
        paging: paging ?? false,
        headerStyle: headerStyle ?? undefined,
        tableLayout: 'auto',
        columnResizable: true,
        groupTitle: (groupData: any) => recursiveTableTitleGenerator(groupData, languageFile),
        exportCsv: async (cols: any[], rows: any[]) => {
          const filteredCols = cols.filter((c) => !excludedColumns.includes(c.field as string) && c.hidden !== true)
          await exportTableToExcel(filteredCols, rows, 'ExportedData', tableType, languageFile)
        },
        exportPdf: async (cols: Column<any>[], rows: any[]) => {
          // Dynamic import for jsPDF - only loaded when exporting PDF
          const { jsPDF } = await import('jspdf')
          const { default: autoTable } = await import('jspdf-autotable')

          const doc = new jsPDF('l', 'pt', 'a4')
          const pageWidth = doc.internal.pageSize.width
          const pageHeight = doc.internal.pageSize.height
          const margins = { top: 10, right: 10, bottom: 10, left: 10 }
          const availableWidth = pageWidth - margins.left - margins.right
          const fontSize = 7
          doc.setFontSize(fontSize)

          // Filter out excluded columns
          const filteredCols = cols.filter((col) => !excludedColumns.includes(col.field as string) && col.hidden !== true)
          const headers = filteredCols.map((col) => {
            if (typeof col.title === 'object' && col.title !== null) {
              const text = col.title.props?.children?.[0]?.props?.children ?? col.title.props?.children
              return languageFile ? replaceTurkishChars(languageFile(text)) : replaceTurkishChars(text)
            }
            return replaceTurkishChars(languageFile ? languageFile(col.title as string) : (col.title as string))
          })

          const body = rows.map((row) =>
            filteredCols.map((col: any) => {
              if (col?.dateSetting?.type === 'date') {
                return dayjs(row[col.field as string]).format('DD.MM.YYYY HH:mm:ss')
              }
              const val = getFieldValue(row, col.field as string, tableType)
              if (typeof val === 'string') {
                return replaceTurkishChars(removeHtmlTags(val))
              }
              return val != null ? String(val) : ''
            }),
          )

          const getTextWidth = (text: string): number => {
            return (doc.getStringUnitWidth(String(text)) * doc.getFontSize()) / doc.internal.scaleFactor
          }

          const columnWidths = filteredCols.map((_, i) => {
            const headerWidth = getTextWidth(headers[i])
            const contentWidths = body.map((row) => getTextWidth(String(row[i])))
            return Math.max(headerWidth, ...contentWidths)
          })

          const totalWidth = columnWidths.reduce((sum, w) => sum + w, 0)
          const scaleFactor = totalWidth > availableWidth ? availableWidth / totalWidth : 1

          const adjustedColumnWidths = columnWidths.map((w) => {
            const adjusted = w * scaleFactor
            return Math.max(adjusted, 30)
          })

          const finalTotalWidth = adjustedColumnWidths.reduce((s, w) => s + w, 0)
          if (finalTotalWidth > availableWidth) {
            const newScaleFactor = availableWidth / finalTotalWidth
            adjustedColumnWidths.forEach((_, idx) => {
              adjustedColumnWidths[idx] *= newScaleFactor
            })
          }

          const defaultPageNumberOptions: PageNumberOptions = {
            fontSize: 8,
            font: 'helvetica',
            fontStyle: 'normal',
            color: '#222222',
            position: 'bottom-right',
            margin: 0,
          }
          const pnOpts = { ...defaultPageNumberOptions, ...pageNumberOptions }

          autoTable(doc, {
            head: [headers],
            body,
            startY: margins.top,
            margin: margins,
            styles: {
              font: 'helvetica',
              fontSize: 7,
              cellPadding: 2,
              overflow: 'linebreak',
              halign: 'left',
              valign: 'middle',
              lineWidth: 0.5,
            },
            headStyles: {
              fillColor: [128, 128, 128],
              textColor: [255, 255, 255],
              fontStyle: 'bold',
              halign: 'left',
            },
            columnStyles: adjustedColumnWidths.reduce(
              (acc, w, index) => {
                acc[index] = {
                  cellWidth: w,
                  minCellWidth: w,
                  maxCellWidth: w,
                }
                return acc
              },
              {} as {
                [key: number]: {
                  cellWidth: number
                  minCellWidth: number
                  maxCellWidth: number
                }
              },
            ),
            didDrawPage: (data: any) => {
              doc.setFont(pnOpts.font!, pnOpts.fontStyle)
              doc.setFontSize(pnOpts.fontSize!)
              doc.setTextColor(pnOpts.color!)

              const pageNumText = `Page ${data.pageNumber} of ${data.pageCount}`
              let x: number, y: number

              switch (pnOpts.position) {
                case 'top-left':
                  x = margins.left
                  y = margins.top - pnOpts.margin! - 5
                  break
                case 'top-center':
                  x = pageWidth / 2
                  y = margins.top - pnOpts.margin! - 5
                  break
                case 'top-right':
                  x = pageWidth - margins.right
                  y = margins.top - pnOpts.margin! - 5
                  break
                case 'bottom-left':
                  x = margins.left
                  y = pageHeight - margins.bottom + pnOpts.margin! + 10
                  break
                case 'bottom-right':
                  x = pageWidth - margins.right
                  y = pageHeight - margins.bottom + pnOpts.margin! + 10
                  break
                case 'bottom-center':
                default:
                  x = pageWidth / 2
                  y = pageHeight - margins.bottom + pnOpts.margin! + 10
                  break
              }

              doc.text(pageNumText, x, y, {
                align: pnOpts.position?.includes('center') ? 'center' : pnOpts.position?.includes('right') ? 'right' : 'left',
              })
            },
            didParseCell: (tableData) => {
              if (tableData.section === 'head') {
                tableData.cell.styles.fillColor = [128, 128, 128]
                tableData.cell.styles.textColor = [255, 255, 255]
                tableData.cell.styles.fontStyle = 'bold'
              }
            },
            willDrawCell: (tableData) => {
              const { cell } = tableData
              cell.styles.lineWidth = 0.5
              cell.styles.lineColor = [211, 211, 211]
            },
          })

          const currentDateTime = dayjs().format('YYYYMMDD_HHmmss')
          const filename = `digiflow_history_${currentDateTime}.pdf`
          doc.save(filename)
        },
        filterCellStyle: {
          padding: '0px 16px',
        },
        filterRowStyle: {
          height: '56px',
        },
      }
    }, [
      pageSize,
      localPageSizeOptions,
      exportButton,
      search,
      grouping,
      toolbar,
      draggable,
      paging,
      headerStyle,
      tableType,
      languageFile,
      getFieldValue,
      pageNumberOptions,
      selection,
      sortConfig,
    ])

    // If parent's loading or local isLoading is true, show spinner
    if (isLoading || loading) {
      return <Loading height={500} show />
    }

    // Debounced search
    const debouncedSearchChange = debounce((searchText: string) => {
      setSearchFilter(searchText)
    }, 500)

    // If mobile or forced mobile table
    if (isMobile || useMobileTable) {
      return (
        <WBox id={exportButton || search || title ? 'digiTableWithHeader' : 'digiTable'} style={style}>
          {search && (
            <div className="digi-table-header">
              <div className="digi-search-container">
                <input
                  type="text"
                  className="digi-search-input"
                  placeholder={i18n.language === 'tr' ? 'Ara...' : 'Search...'}
                  onChange={(e) => debouncedSearchChange(e.target.value)}
                />
                <div className="digi-search-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="11" cy="11" r="8" />
                    <path d="m21 21-4.35-4.35" />
                  </svg>
                </div>
              </div>
            </div>
          )}
          <MobileTable
            paging={paging}
            data={filteredAndSortedData}
            columns={columnsWithCustomFilter}
            filtering={filtering}
            actions={actions}
            title={title}
            onRowClick={onSelectionChange}
            languageFile={languageFile}
            mobileConfig={mobileConfig}
          />
        </WBox>
      )
    }

    // Otherwise render desktop WTable
    return (
      <WBox id={exportButton || search || title ? 'digiTableWithHeader' : 'digiTable'} style={style}>
        <div className="digi-table-container" data-theme="light">
          <div className="digi-table-content">
            <WTable
              onGroupRemoved={handleGroupRemoved}
              columns={columnsWithCustomFilter}
              data={filteredAndSortedData}
              onSelectionChange={onSelectionChange}
              icons={tableIcons}
              title={title ?? ''}
              onSearchChange={(text: string) => debouncedSearchChange(text)}
              actions={actions}
              options={tableOptions}
              components={{
                Pagination: TablePaginationWrapper,
              }}
              localization={{
                error: i18n.language === 'tr' ? 'Hata' : 'Error',
                body: {
                  emptyDataSourceMessage: i18n.language === 'tr' ? 'Gösterilecek kayıt bulunamadı' : 'No record found',
                  filterRow: {
                    filterPlaceHolder: i18n.language === 'tr' ? 'Filtrele' : 'Filter',
                    filterTooltip: i18n.language === 'tr' ? 'Filtrele' : 'Filter',
                  },
                  editRow: {
                    saveTooltip: i18n.language === 'tr' ? 'Kaydet' : 'Save',
                    cancelTooltip: i18n.language === 'tr' ? 'İptal' : 'Cancel',
                    deleteText:
                      i18n.language === 'tr' ? 'Bu kaydı silmek istediğinizden emin misiniz?' : 'Are you sure you want to delete this record?',
                  },
                  addTooltip: i18n.language === 'tr' ? 'Ekle' : 'Add',
                  deleteTooltip: i18n.language === 'tr' ? 'Sil' : 'Delete',
                  editTooltip: i18n.language === 'tr' ? 'Düzenle' : 'Edit',
                },
                header: {
                  actions: i18n.language === 'tr' ? 'İşlemler' : 'Actions',
                },
                grouping: {
                  groupedBy: i18n.language === 'tr' ? 'Gruplandırma:' : 'Grouped By:',
                  placeholder: i18n.language === 'tr' ? 'Sütun başlığını sürükleyerek gruplandırın' : 'Drag headers here to group by',
                },
                pagination: {
                  labelRowsSelect: i18n.language === 'tr' ? 'satır' : 'rows',
                  labelDisplayedRows: i18n.language === 'tr' ? '{from}-{to} / {count}' : '{from}-{to} of {count}',
                  firstTooltip: i18n.language === 'tr' ? 'İlk Sayfa' : 'First Page',
                  previousTooltip: i18n.language === 'tr' ? 'Önceki Sayfa' : 'Previous Page',
                  nextTooltip: i18n.language === 'tr' ? 'Sonraki Sayfa' : 'Next Page',
                  lastTooltip: i18n.language === 'tr' ? 'Son Sayfa' : 'Last Page',
                  firstAriaLabel: i18n.language === 'tr' ? 'İlk Sayfa' : 'First Page',
                  previousAriaLabel: i18n.language === 'tr' ? 'Önceki Sayfa' : 'Previous Page',
                  nextAriaLabel: i18n.language === 'tr' ? 'Sonraki Sayfa' : 'Next Page',
                  lastAriaLabel: i18n.language === 'tr' ? 'Son Sayfa' : 'Last Page',
                  labelRowsPerPage: i18n.language === 'tr' ? 'Sayfa başına satır:' : 'Rows per page:',
                },
                toolbar: {
                  addRemoveColumns: i18n.language === 'tr' ? 'Sütun ekle veya kaldır' : 'Add or remove columns',
                  nRowsSelected: (rowCount: number) => (i18n.language === 'tr' ? `${rowCount} satır seçildi` : `${rowCount} row(s) selected`),
                  showColumnsTitle: i18n.language === 'tr' ? 'Sütunları göster' : 'Show Columns',
                  showColumnsAriaLabel: i18n.language === 'tr' ? 'Sütunları göster' : 'Show Columns',
                  exportTitle: i18n.language === 'tr' ? 'Dışa aktar' : 'Export',
                  exportAriaLabel: i18n.language === 'tr' ? 'Dışa aktar' : 'Export',
                  exportCSVName: i18n.language === 'tr' ? 'Excel olarak dışa aktar' : 'Export as Excel',
                  exportPDFName: i18n.language === 'tr' ? 'PDF olarak dışa aktar' : 'Export as PDF',
                  searchTooltip: i18n.language === 'tr' ? 'Ara' : 'Search',
                  searchPlaceholder: i18n.language === 'tr' ? 'Ara' : 'Search',
                  searchAriaLabel: i18n.language === 'tr' ? 'Ara' : 'Search',
                  clearSearchAriaLabel: i18n.language === 'tr' ? 'Aramayı temizle' : 'Clear Search',
                },
              }}
            />
          </div>
        </div>
      </WBox>
    )
  },
)

export default DigiTable
