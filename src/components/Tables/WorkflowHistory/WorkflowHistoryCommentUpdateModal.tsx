import React, { useState, useCallback, useEffect, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useQueryClient, useMutation } from '@tanstack/react-query'
import { toast } from 'react-hot-toast'
import Modal from '@/components/Modal/Modal'
import { IWorkflowHistory } from '@/types'
import config from '@/config'
import { useUserStore } from '@/stores/userStore'
import { Upload, X, File, User, Workflow, Clock, MessageSquare } from 'lucide-react'

interface IUploadedFile {
  name: string
  size: number
  url: string
}

interface IFile {
  name: string
  size: number
  file: File
}

interface Props {
  data: IWorkflowHistory
  refetch?: () => void
}

interface CommentUpdatePayload {
  comment: string
  uploadedFiles: string[]
}
interface IFileInfo {
  name: string
  url: string
}
let apiUrl = config.VITE_API_URL

if (window.location.hostname === 'digiflowtest') {
  apiUrl = `${config.VITE_API_URL.split('.digiturk.com.tr')[0]}/api`
}
// Headers will be created inside the component where we have access to hooks

const WorkflowHistoryCommentUpdateModal: React.FC<Props> = ({ data, refetch }) => {
  const queryClient = useQueryClient()
  const [isOpen, setIsOpen] = useState(false)
  const userStore = useUserStore()
  const userId = userStore.selectedUser?.value
  const [newComment, setNewComment] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([])
  const [uploadedFiles, setUploadedFiles] = useState<IUploadedFile[]>([])

  const { t, i18n } = useTranslation('workflowHistory')

  const getHeaders = (isFormData: boolean = false) => {
    const language = localStorage.getItem('language') ?? 'en'
    const headers = new Headers()

    if (!isFormData) {
      headers.set('Content-Type', 'application/json')
    }

    headers.set('X-Requested-With', 'XMLHttpRequest')
    headers.set('Accept-Language', language)

    if (userId) {
      headers.set('X-Login-Id', String(userId))
    }

    const wfInstanceId = new URLSearchParams(window.location.search).get('wfInstanceId')
    if (wfInstanceId) {
      headers.set('X-Workflow-Instance-Id', wfInstanceId)
    }

    return headers
  }

  const parseCommentAndFiles = (commentHtml: string): { message: string; files: IFileInfo[] } => {
    // Handle case where comment starts with a dash (no message)
    const trimmedComment = commentHtml.trim()
    if (trimmedComment.startsWith('- <a')) {
      const message = ''
      const files: IFileInfo[] = []

      const parts = trimmedComment.split('<br/> -')
      parts.forEach((part) => {
        if (part.includes('href=')) {
          const urlStart = part.indexOf("href='") + 6
          const urlEnd = part.indexOf("'", urlStart)
          const url = part.substring(urlStart, urlEnd)

          const textStart = part.indexOf('>') + 1
          const textEnd = part.indexOf('</a>')
          const fileName = part.substring(textStart, textEnd)

          // Only add if file isn't already in the array
          if (!files.some((f) => f.name === fileName)) {
            files.push({
              name: fileName,
              url,
            })
          }
        }
      })

      return { message, files }
    }

    // Handle normal case with message
    const parts = commentHtml.split('<br/> -')
    const message = parts[0].replace(/^-\s*/, '').trim() // Remove leading dash if present
    const files: IFileInfo[] = []

    if (parts.length > 1) {
      parts.slice(1).forEach((part) => {
        if (part.includes('href=')) {
          const urlStart = part.indexOf("href='") + 6
          const urlEnd = part.indexOf("'", urlStart)
          const url = part.substring(urlStart, urlEnd)

          const textStart = part.indexOf('>') + 1
          const textEnd = part.indexOf('</a>')
          const fileName = part.substring(textStart, textEnd)

          // Only add if file isn't already in the array
          if (!files.some((f) => f.name === fileName)) {
            files.push({
              name: fileName,
              url,
            })
          }
        }
      })
    }

    return { message, files }
  }

  useEffect(() => {
    if (isOpen && data.comments) {
      const { message, files } = parseCommentAndFiles(data.comments)
      setNewComment(message)

      // Only add existing files to uploadedFiles
      const existingFiles = files.map((file) => ({
        name: file.name,
        size: 0,
        url: file.url,
      }))
      setUploadedFiles(existingFiles)

      // Clear selected files when opening modal
      setSelectedFiles([])
    }
  }, [isOpen, data.comments, data.action])

  const uploadFilesMutation = useMutation({
    mutationFn: async (files: IFile[]) => {
      const formData = new FormData()
      files.forEach((file) => {
        if (file.file) {
          formData.append('files', file.file)
        }
      })
      formData.append('pathKey', 'SharePointActionPanelUploadFolder')

      const response = await fetch(`${apiUrl}/api/file/upload-multiple`, {
        method: 'POST',
        headers: getHeaders(true),
        credentials: 'include',
        body: formData,
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      return response.json()
    },
    onError: (error) => {
      console.error('File upload error:', error)
      toast.error(t('fileUploadError'))
    },
  })

  const updateCommentMutation = useMutation({
    mutationFn: async (payload: CommentUpdatePayload) => {
      const response = await fetch(`${apiUrl}/workflows/history/comment/${data.wfHistoryId}`, {
        method: 'PUT',
        headers: getHeaders(),
        credentials: 'include',
        body: JSON.stringify(payload),
      })

      if (!response.ok) {
        throw new Error('Update failed')
      }

      return response.json()
    },
    onSuccess: () => {
      toast.success(t('commentUpdated'))
      void queryClient.invalidateQueries({ queryKey: ['workflowHistory'] })
      refetch?.()
      resetState()
      handleClose()
    },
    onError: (error) => {
      console.error('Comment update error:', error)
      toast.error(t('commentUpdateError'))
    },
  })

  const resetState = useCallback(() => {
    setNewComment('')
    setSelectedFiles([])
    setUploadedFiles([])
    setIsLoading(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [])

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    event.preventDefault()
    event.stopPropagation()

    const files = Array.from(event.target.files ?? []).map((file) => ({
      name: file.name,
      size: file.size,
      file,
    }))

    setSelectedFiles((prev) => [...prev, ...files])
  }, [])

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    setIsDragging(false)

    const files = Array.from(event.dataTransfer.files).map((file) => ({
      name: file.name,
      size: file.size,
      file,
    }))

    setSelectedFiles((prev) => [...prev, ...files])
  }, [])

  const handleDelete = useCallback((fileName: string) => {
    setUploadedFiles((prev) => prev.filter((file) => file.name !== fileName))
    setSelectedFiles((prev) => prev.filter((file) => file.name !== fileName))
  }, [])

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return null

    try {
      setIsLoading(true)
      const response = await uploadFilesMutation.mutateAsync(selectedFiles)
      const newUploadedFiles = selectedFiles.map((file, index) => ({
        name: file.name,
        size: file.size,
        url: response.urls[index],
      }))

      setUploadedFiles((prev) => [...prev, ...newUploadedFiles])
      setSelectedFiles([])
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

      return response.urls
    } catch (error) {
      console.error('Upload error:', error)
      toast.error(t('fileUploadError'))
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const handleConfirm = async () => {
    if (newComment.trim() === '') {
      toast.error(t('commentEmpty'))
      return
    }

    try {
      setIsLoading(true)
      const fileNames: string[] = []

      if (data.action === 'Dosya Yüklendi') {
        // Handle new file uploads
        if (selectedFiles.length > 0) {
          const filesToUpload = selectedFiles.filter((file) => !uploadedFiles.some((uf) => uf.name === file.name))

          if (filesToUpload.length > 0) {
            const uploadedUrls = await handleUpload()
            if (!uploadedUrls) return

            const newFileNames = uploadedUrls.map((url: string) => {
              const parts = url.split('/')
              return parts[parts.length - 1]
            })
            fileNames.push(...newFileNames)
          }
        }

        // Include existing files
        uploadedFiles.forEach((file) => {
          const urlParts = file.url.split('/')
          fileNames.push(urlParts[urlParts.length - 1])
        })
      }

      await updateCommentMutation.mutateAsync({
        comment: newComment.trim(),
        uploadedFiles: fileNames,
      })
    } catch (error) {
      console.error('Confirm error:', error)
      toast.error(t('commentUpdateError'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = useCallback(() => {
    if (!isLoading && !uploadFilesMutation.isPending && !updateCommentMutation.isPending) {
      setIsOpen(false)
      resetState()
    }
  }, [isLoading, uploadFilesMutation.isPending, updateCommentMutation.isPending, resetState])

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return 'N/A'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  return (
    <>
      <button className="workflow-button workflow-button-primary" onClick={() => setIsOpen(true)} type="button">
        <MessageSquare size={16} />
        {t('editCommentButton')}
      </button>

      <Modal
        open={isOpen}
        title={t('editComment')}
        onClose={handleClose}
        onConfirm={handleConfirm}
        loading={isLoading || uploadFilesMutation.isPending || updateCommentMutation.isPending}
        confirmButtonText={t('save')}
        cancelButtonText={t('cancel')}
      >
        <div className="workflow-history-modal-wrapper">
          {/* Workflow Info Grid */}
          <div className="workflow-info-grid">
            <div className="workflow-info-card">
              <div className="workflow-info-item">
                <span className="workflow-info-label">
                  <User size={14} style={{ display: 'inline', marginRight: '4px' }} />
                  {t('user')}
                </span>
                <span className="workflow-info-value">{data.users}</span>
              </div>
            </div>

            <div className="workflow-info-card">
              <div className="workflow-info-item">
                <span className="workflow-info-label">
                  <Workflow size={14} style={{ display: 'inline', marginRight: '4px' }} />
                  {t('workflowNameNumber')}
                </span>
                <span className="workflow-info-value">
                  {data.wfDefName} / {data.wfWorkflowInstanceId}
                </span>
              </div>
            </div>

            <div className="workflow-info-card">
              <div className="workflow-info-item">
                <span className="workflow-info-label">
                  <Clock size={14} style={{ display: 'inline', marginRight: '4px' }} />
                  {t('stateAction')}
                </span>
                <span className="workflow-info-value">
                  {i18n.language === 'tr' ? `${data.state}/${data.action}` : `${data.stateDesc}/${data.actionEng}`}
                </span>
              </div>
            </div>
          </div>

          {/* Old Comment Section */}
          <div className="workflow-info-card" style={{ marginBottom: '24px' }}>
            <div className="workflow-info-item">
              <span className="workflow-info-label">{t('oldComment')}</span>
              <div
                style={{
                  marginTop: '8px',
                  padding: '12px',
                  backgroundColor: 'var(--df-neutral-50)',
                  borderRadius: 'var(--df-radius-md)',
                  minHeight: '60px',
                  maxHeight: '120px',
                  overflowY: 'auto',
                }}
              >
                {data.comments ?? t('noComment')}
              </div>
            </div>
          </div>

          {/* New Comment Input */}
          <div style={{ marginBottom: '24px' }}>
            <textarea
              className="workflow-comment-textarea"
              placeholder={t('enterNewComment')}
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              disabled={isLoading || updateCommentMutation.isPending}
              rows={5}
            />
          </div>

          {/* File Upload Section */}
          {data.action === 'Dosya Yüklendi' && (
            <div>
              <div
                className={`workflow-file-upload ${isDragging ? 'drag-active' : ''}`}
                onDragEnter={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setIsDragging(true)
                }}
                onDragOver={(e) => e.preventDefault()}
                onDragLeave={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  setIsDragging(false)
                }}
                onDrop={handleDrop}
                onClick={() => fileInputRef.current?.click()}
              >
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileSelect}
                  style={{ display: 'none' }}
                  disabled={isLoading || updateCommentMutation.isPending || uploadFilesMutation.isPending}
                />
                <Upload size={48} className="workflow-file-upload-icon" />
                <p className="workflow-file-upload-text">{t('dragAndDropFiles')}</p>
                <p className="workflow-file-upload-hint">{t('orClickToSelect')}</p>
              </div>

              {/* File List */}
              {(selectedFiles.length > 0 || uploadedFiles.length > 0) && (
                <div className="workflow-file-list">
                  {uploadedFiles.map((file) => (
                    <div key={file.url} className="workflow-file-item">
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <File size={16} color="var(--df-primary-500)" />
                        <div>
                          <div className="workflow-file-name">{file.name}</div>
                          <div className="workflow-file-size">{t('existing')}</div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDelete(file.name)}
                        className="workflow-button workflow-button-secondary"
                        style={{ padding: '4px 8px' }}
                        disabled={isLoading}
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                  {selectedFiles.map((file) => (
                    <div key={file.name} className="workflow-file-item">
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <File size={16} color="var(--df-primary-500)" />
                        <div>
                          <div className="workflow-file-name">{file.name}</div>
                          <div className="workflow-file-size">{formatFileSize(file.size)}</div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleDelete(file.name)}
                        className="workflow-button workflow-button-secondary"
                        style={{ padding: '4px 8px' }}
                        disabled={isLoading}
                      >
                        <X size={16} />
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    </>
  )
}

export default WorkflowHistoryCommentUpdateModal
