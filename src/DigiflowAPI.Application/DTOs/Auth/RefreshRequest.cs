using System.ComponentModel.DataAnnotations;
using DigiflowAPI.Application.Validation;

namespace DigiflowAPI.Application.DTOs.Auth
{
    public class RefreshRequest
    {
        [Required(ErrorMessage = "Refresh token is required")]
        [StringLength(1000, ErrorMessage = "Refresh token is too long")]
        [NoSqlInjection]
        [NoXss]
        public string RefreshToken { get; set; } = string.Empty;

        [Required(ErrorMessage = "Access token is required")]
        [StringLength(5000, ErrorMessage = "Access token is too long")]
        [NoSqlInjection]
        [NoXss]
        public string AccessToken { get; set; } = string.Empty;
    }
}