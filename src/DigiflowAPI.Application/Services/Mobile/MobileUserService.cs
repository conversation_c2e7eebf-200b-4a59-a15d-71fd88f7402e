﻿using AutoMapper;
using DigiflowAPI.Application.DTOs.Mobile;
using DigiflowAPI.Application.Interfaces.Services.Mobile;
using DigiflowAPI.Domain.Interfaces.Repositories;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using static System.Net.Mime.MediaTypeNames;

namespace DigiflowAPI.Application.Services.Mobile
{
    /// <summary>
    /// Service implementation for mobile user operations
    /// </summary>
    public class MobileUserService : IMobileUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly IMapper _mapper;
        private readonly IMemoryCache _cache;
        private readonly ILogger<MobileUserService> _logger;

        private const string USER_IMAGE_BASE_URL = "http://dtl1iis4/root/HR_DP/images/users/";
        private const string USER_IMAGE_CACHE_KEY = "user_image_{0}";
        private const string USER_INFO_WITH_IMAGE_CACHE_KEY = "user_info_image_{0}";
        private const int IMAGE_CACHE_DURATION_MINUTES = 30;
        private const int MOBILE_IMAGE_SIZE = 150; // 150x150px for mobile avatars
        private const long IMAGE_QUALITY = 80L; // JPEG quality 80%

        public MobileUserService(
            IUserRepository userRepository,
            IOrganizationRepository organizationRepository,
            IHttpClientFactory httpClientFactory,
            IConfiguration configuration,
            IMapper mapper,
            IMemoryCache cache,
            ILogger<MobileUserService> logger)
        {
            _userRepository = userRepository;
            _organizationRepository = organizationRepository;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _mapper = mapper;
            _cache = cache;
            _logger = logger;
        }

        public async Task<MobileUserInfo?> GetUserInfoAsync(long userId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(userId);

                if (user == null)
                {
                    _logger.LogWarning("User not found for ID: {UserId}", userId);
                    return null;
                }

                var userInfo = new MobileUserInfo
                {
                    Id = user.LoginId ?? userId,
                    Name = user.NameSurname ?? string.Empty,
                    Email = user.EMail ?? string.Empty,
                    Department = user.DeptName,
                    DepartmentId = user.DeptId.HasValue ? (long)user.DeptId.Value : 0,
                    Position = user.PersonelTipi ?? string.Empty,
                    PhoneNumber = string.Empty, // No phone number in VwUserInformation
                    Title = user.PersonelTipi ?? string.Empty,
                    IsActive = user.IsDeleted != "Y"
                };

                // Add work anniversary data if start date is available
                AddWorkAnniversaryData(userInfo, user.StartDate);

                return userInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user info for ID: {UserId}", userId);
                return null;
            }
        }

        public async Task<MobileUserImageData?> GetUserImageAsync(string email)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                {
                    _logger.LogWarning("Email is null or empty for user image request");
                    return null;
                }

                // Extract username from email
                var emailParts = email.Split('@');
                if (emailParts.Length == 0 || string.IsNullOrEmpty(emailParts[0]))
                {
                    _logger.LogWarning("Invalid email format: {Email}", email);
                    return null;
                }

                var username = emailParts[0];
                return await FetchUserImageFromServer(username);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user image for email: {Email}", email);
                return null;
            }
        }

        public async Task<MobileUserImageData?> GetUserImageByLoginIdAsync(long loginId)
        {
            try
            {
                var user = await _userRepository.GetByIdAsync(loginId);
                if (user == null)
                {
                    _logger.LogWarning("User not found for login ID: {LoginId}", loginId);
                    return null;
                }

                string? username = null;

                // Try to get username from email first
                if (!string.IsNullOrEmpty(user.EMail))
                {
                    var emailParts = user.EMail.Split('@');
                    username = emailParts.Length > 0 && !string.IsNullOrEmpty(emailParts[0])
                        ? emailParts[0]
                        : user.Username;
                }
                else
                {
                    username = user.Username;
                }

                if (string.IsNullOrEmpty(username))
                {
                    _logger.LogWarning("No username found for login ID: {LoginId}", loginId);
                    return null;
                }

                var imageData = await FetchUserImageFromServer(username);
                if (imageData != null)
                {
                    imageData.UserId = loginId;
                }

                return imageData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user image for login ID: {LoginId}", loginId);
                return null;
            }
        }

        public async Task<MobileOrgTreeInfo?> GetUserOrganizationAsync(long userId)
        {
            try
            {
                // Get user's department ID first
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null || !user.DeptId.HasValue)
                {
                    _logger.LogWarning("User or department not found for user ID: {UserId}", userId);
                    return null;
                }

                var departmentId = (long)user.DeptId.Value;

                // Get department details using GetDepartmentAsync
                var departments = await _organizationRepository.GetDepartmentAsync(departmentId);
                var department = departments?.FirstOrDefault();

                if (department == null)
                {
                    _logger.LogWarning("Department not found for ID: {DepartmentId}", departmentId);
                    return null;
                }

                // TODO: Implement proper hierarchy retrieval
                // For now, return basic organization info
                return new MobileOrgTreeInfo
                {
                    DepartmentId = department.Id,
                    DepartmentName = department.Bolum ?? string.Empty,
                    ManagerName = department.Manager ?? string.Empty,
                    ParentDepartmentName = string.Empty, // TODO: Get parent department
                    Hierarchy = new List<string> { department.Bolum ?? string.Empty },
                    TeamSize = 0, // TODO: Calculate team size
                    Level = 1 // TODO: Calculate actual level
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching organization info for user: {UserId}", userId);
                return null;
            }
        }

        public async Task<bool> UpdateUserImageAsync(long userId, Stream imageStream, string contentType)
        {
            try
            {
                // This would typically save to a file server or blob storage
                // For now, log the attempt
                _logger.LogInformation("Attempting to update user image for user: {UserId}", userId);

                // TODO: Implement actual image upload logic
                // This might involve:
                // 1. Validating the image
                // 2. Resizing/optimizing
                // 3. Saving to file system or blob storage
                // 4. Updating database with image URL

                return await Task.FromResult(false); // Not implemented yet
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user image for user: {UserId}", userId);
                return false;
            }
        }

        public async Task<string?> GetUserImageUrlAsync(string email)
        {
            try
            {
                if (string.IsNullOrEmpty(email))
                    return null;

                var emailParts = email.Split('@');
                if (emailParts.Length == 0 || string.IsNullOrEmpty(emailParts[0]))
                    return null;

                var username = emailParts[0];
                var imageUrl = $"{USER_IMAGE_BASE_URL}{username}.jpg";

                // Check if image exists
                if (await CheckImageExistsAsync(imageUrl))
                {
                    // Return the API endpoint URL for security (not direct file URL)
                    return "/api/mobile/user/image";
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error checking user image URL for email: {Email}", email);
                return null;
            }
        }

        private async Task<MobileUserImageData?> FetchUserImageFromServer(string username)
        {
            var imageFileName = $"{username}.jpg";
            var imageUrl = $"{USER_IMAGE_BASE_URL}{imageFileName}";

            _logger.LogInformation("Attempting to fetch image from: {ImageUrl}", imageUrl);

            try
            {
                // Create HTTP client with Windows authentication
                var httpClient = _httpClientFactory.CreateClient();

                // Configure for Windows auth if in domain environment
                var handler = new HttpClientHandler
                {
                    UseDefaultCredentials = true,
                    PreAuthenticate = true
                };

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };

                var response = await client.GetAsync(imageUrl);

                if (response.IsSuccessStatusCode)
                {
                    var imageBytes = await response.Content.ReadAsByteArrayAsync();
                    var contentType = response.Content.Headers.ContentType?.MediaType ?? "image/jpeg";

                    _logger.LogInformation("Successfully fetched user image for {Username}, size: {Size} bytes",
                        username, imageBytes.Length);

                    return new MobileUserImageData
                    {
                        ImageBytes = imageBytes,
                        ContentType = contentType,
                        FileName = imageFileName
                    };
                }
                else
                {
                    _logger.LogWarning("User image not found for {Username} at {ImageUrl}, status: {StatusCode}",
                        username, imageUrl, response.StatusCode);
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error occurred while fetching user image for {Username} from {ImageUrl}",
                    username, imageUrl);
                return null;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Timeout occurred while fetching user image for {Username} from {ImageUrl}",
                    username, imageUrl);
                return null;
            }
        }

        private async Task<bool> CheckImageExistsAsync(string imageUrl)
        {
            try
            {
                var handler = new HttpClientHandler
                {
                    UseDefaultCredentials = true,
                    PreAuthenticate = true
                };

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(10)
                };

                var response = await client.SendAsync(new HttpRequestMessage(HttpMethod.Head, imageUrl));
                return response.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error checking image existence at {ImageUrl}", imageUrl);
                return false;
            }
        }

        /// <summary>
        /// Gets user information with embedded Base64 image for optimal mobile performance
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="includeImage">Whether to include embedded image (default: true)</param>
        /// <returns>User info with embedded image data</returns>
        public async Task<MobileUserInfo?> GetUserInfoWithImageAsync(long userId, bool includeImage = true)
        {
            var cacheKey = string.Format(USER_INFO_WITH_IMAGE_CACHE_KEY, userId);

            // Try cache first for complete user info with image
            if (_cache.TryGetValue<MobileUserInfo>(cacheKey, out var cachedUserInfo))
            {
                _logger.LogDebug("Returning cached user info with image for user {UserId}", userId);
                return cachedUserInfo;
            }

            try
            {
                // Get basic user info (which now includes work anniversary data)
                var userInfo = await GetUserInfoAsync(userId);
                if (userInfo == null)
                {
                    return null;
                }

                // Add embedded image if requested and email is available
                if (includeImage && !string.IsNullOrEmpty(userInfo.Email))
                {
                    var imageData = await GetOptimizedUserImageAsync(userInfo.Email);
                    if (imageData != null)
                    {
                        userInfo.Base64Image = imageData.Base64Data;
                        userInfo.ImageFormat = imageData.Format;
                        userInfo.ImageSize = imageData.SizeBytes;
                        userInfo.Avatar = "/api/mobile/user/image"; // Keep for backwards compatibility
                    }
                }

                // Cache the complete user info with image for faster subsequent requests
                var userInfoCacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(IMAGE_CACHE_DURATION_MINUTES),
                    Size = 5 // Estimated 5KB for user info data
                };
                _cache.Set(cacheKey, userInfo, userInfoCacheOptions);

                return userInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching user info with image for ID: {UserId}", userId);
                return await GetUserInfoAsync(userId); // Fallback to basic user info without image
            }
        }

        /// <summary>
        /// Gets optimized user image data for mobile display
        /// </summary>
        private async Task<OptimizedImageData?> GetOptimizedUserImageAsync(string email)
        {
            if (string.IsNullOrEmpty(email))
                return null;

            var emailParts = email.Split('@');
            if (emailParts.Length == 0 || string.IsNullOrEmpty(emailParts[0]))
                return null;

            var username = emailParts[0];
            var imageCacheKey = string.Format(USER_IMAGE_CACHE_KEY, username);

            // Check cache for processed image
            if (_cache.TryGetValue<OptimizedImageData>(imageCacheKey, out var cachedImage))
            {
                _logger.LogDebug("Returning cached optimized image for user {Username}", username);
                return cachedImage;
            }

            try
            {
                // Fetch original image with reduced timeout for better performance
                var originalImageData = await FetchUserImageFromServerOptimized(username);
                if (originalImageData == null)
                {
                    return null;
                }

                // Process and optimize image for mobile
                var optimizedImage = ProcessImageForMobile(originalImageData.ImageBytes);
                if (optimizedImage == null)
                {
                    return null;
                }

                // Cache the optimized image
                var imageCacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(IMAGE_CACHE_DURATION_MINUTES),
                    Size = 10 // Estimated 10KB for optimized image data
                };
                _cache.Set(imageCacheKey, optimizedImage, imageCacheOptions);

                _logger.LogInformation("Successfully processed and cached optimized image for {Username}, " +
                    "original: {OriginalSize} bytes, optimized: {OptimizedSize} bytes",
                    username, originalImageData.ImageBytes.Length, optimizedImage.SizeBytes);

                return optimizedImage;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting optimized image for user {Username}", username);
                return null;
            }
        }

        /// <summary>
        /// Fetches user image from server with optimized timeout for mobile performance
        /// </summary>
        private async Task<MobileUserImageData?> FetchUserImageFromServerOptimized(string username)
        {
            var imageFileName = $"{username}.jpg";
            var imageUrl = $"{USER_IMAGE_BASE_URL}{imageFileName}";

            _logger.LogDebug("Fetching optimized image from: {ImageUrl}", imageUrl);

            try
            {
                var handler = new HttpClientHandler
                {
                    UseDefaultCredentials = true,
                    PreAuthenticate = true
                };

                using var client = new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(10) // Reduced from 30s for better mobile performance
                };

                var response = await client.GetAsync(imageUrl);

                if (response.IsSuccessStatusCode)
                {
                    var imageBytes = await response.Content.ReadAsByteArrayAsync();
                    var contentType = response.Content.Headers.ContentType?.MediaType ?? "image/jpeg";

                    return new MobileUserImageData
                    {
                        ImageBytes = imageBytes,
                        ContentType = contentType,
                        FileName = imageFileName
                    };
                }
                else
                {
                    _logger.LogDebug("User image not found for {Username} at {ImageUrl}, status: {StatusCode}",
                        username, imageUrl, response.StatusCode);
                    return null;
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogWarning(ex, "HTTP error fetching image for {Username} from {ImageUrl}", username, imageUrl);
                return null;
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogWarning(ex, "Timeout fetching image for {Username} from {ImageUrl}", username, imageUrl);
                return null;
            }
        }

        /// <summary>
        /// Processes and optimizes image for mobile display
        /// </summary>
        private OptimizedImageData? ProcessImageForMobile(byte[] originalImageBytes)
        {
            try
            {
                using var originalStream = new MemoryStream(originalImageBytes);
                using var originalImage = System.Drawing.Image.FromStream(originalStream);

                // Calculate new dimensions maintaining aspect ratio
                var newSize = CalculateOptimalSize(originalImage.Width, originalImage.Height, MOBILE_IMAGE_SIZE);

                using var resizedImage = new Bitmap(newSize.Width, newSize.Height);
                using var graphics = Graphics.FromImage(resizedImage);

                // High quality resizing
                graphics.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceCopy;
                graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                graphics.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;

                graphics.DrawImage(originalImage, 0, 0, newSize.Width, newSize.Height);

                // Convert to JPEG with quality compression
                using var outputStream = new MemoryStream();
                var jpegEncoder = GetJpegEncoder();
                var encoderParams = new EncoderParameters(1);
                encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, IMAGE_QUALITY);

                resizedImage.Save(outputStream, jpegEncoder, encoderParams);
                var optimizedBytes = outputStream.ToArray();

                // Convert to Base64
                var base64String = Convert.ToBase64String(optimizedBytes);

                return new OptimizedImageData
                {
                    Base64Data = base64String,
                    Format = "jpeg",
                    SizeBytes = optimizedBytes.Length,
                    Width = newSize.Width,
                    Height = newSize.Height
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing image for mobile optimization");
                return null;
            }
        }

        private static Size CalculateOptimalSize(int originalWidth, int originalHeight, int maxSize)
        {
            if (originalWidth <= maxSize && originalHeight <= maxSize)
                return new Size(originalWidth, originalHeight);

            var ratioX = (double)maxSize / originalWidth;
            var ratioY = (double)maxSize / originalHeight;
            var ratio = Math.Min(ratioX, ratioY);

            return new Size((int)(originalWidth * ratio), (int)(originalHeight * ratio));
        }

        private static ImageCodecInfo? GetJpegEncoder()
        {
            var codecs = ImageCodecInfo.GetImageEncoders();
            return codecs.FirstOrDefault(codec => codec.FormatID == ImageFormat.Jpeg.Guid);
        }

        /// <summary>
        /// Optimized image data for mobile display
        /// </summary>
        private class OptimizedImageData
        {
            public string Base64Data { get; set; } = string.Empty;
            public string Format { get; set; } = string.Empty;
            public int SizeBytes { get; set; }
            public int Width { get; set; }
            public int Height { get; set; }
        }

        /// <summary>
        /// Adds work anniversary data to user info based on employment start date
        /// </summary>
        /// <param name="userInfo">User info object to populate</param>
        /// <param name="startDate">Employment start date from DP_HR_USERS.GIRIS_TARIHI</param>
        private void AddWorkAnniversaryData(MobileUserInfo userInfo, DateTime? startDate)
        {
            if (!startDate.HasValue)
            {
                _logger.LogDebug("No start date available for user {UserId}, skipping work anniversary calculation", userInfo.Id);
                return;
            }

            try
            {
                var currentDate = DateTime.UtcNow.Date;
                var employmentStartDate = startDate.Value.Date;

                // Handle edge case where start date is in the future
                if (employmentStartDate > currentDate)
                {
                    _logger.LogWarning("Start date {StartDate} is in the future for user {UserId}", employmentStartDate, userInfo.Id);
                    return;
                }

                // Calculate calendar days (not business days)
                var daysWorking = (int)(currentDate - employmentStartDate).TotalDays;

                // Populate work anniversary fields
                userInfo.StartDate = startDate.Value;
                userInfo.DaysWorking = daysWorking;
                userInfo.WorkAnniversaryMessage = FormatWorkAnniversaryMessage(daysWorking);

                _logger.LogDebug("Calculated work anniversary for user {UserId}: {Days} days since {StartDate}",
                    userInfo.Id, daysWorking, employmentStartDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating work anniversary for user {UserId} with start date {StartDate}",
                    userInfo.Id, startDate);
            }
        }

        /// <summary>
        /// Formats the Turkish work anniversary message with proper localization
        /// Examples:
        /// - 0 days: "Bugün başladık! #biraradayız"
        /// - 1 day: "1 gündür #biraradayız!"
        /// - 365 days: "365 gündür #biraradayız!"
        /// - 1247 days: "1.247 gündür #biraradayız!" (Turkish number formatting)
        /// </summary>
        /// <param name="days">Number of days worked</param>
        /// <returns>Formatted Turkish message with number formatting</returns>
        private static string FormatWorkAnniversaryMessage(int days)
        {
            return days switch
            {
                0 => "Bugün başladık! #biraradayız",
                1 => "1 gündür #biraradayız!",
                _ => $"{days.ToString("N0", CultureInfo.CreateSpecificCulture("tr-TR"))} gündür #biraradayız!"
            };
        }
    }
}