﻿using AutoMapper;
using DigiflowAPI.Application.DTOs.Mobile;
using DigiflowAPI.Application.Interfaces.Services.Mobile;
using DigiflowAPI.Domain.Interfaces.Repositories;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace DigiflowAPI.Application.Services.Mobile
{
    /// <summary>
    /// Service implementation for mobile dashboard operations
    /// </summary>
    public class MobileDashboardService : IMobileDashboardService
    {
        private readonly IUserRepository _userRepository;
        private readonly IOrganizationRepository _organizationRepository;
        private readonly ISlideRepository _slideRepository;
        private readonly IInboxRepository _inboxRepository;
        private readonly IHistoryRepository _historyRepository;
        private readonly IMobileUserService _mobileUserService;
        private readonly IMapper _mapper;
        private readonly IMemoryCache _cache;
        private readonly ILogger<MobileDashboardService> _logger;

        private const string DASHBOARD_CACHE_KEY = "mobile_dashboard_{0}";
        private const int CACHE_DURATION_MINUTES = 5;

        public MobileDashboardService(
            IUserRepository userRepository,
            IOrganizationRepository organizationRepository,
            ISlideRepository slideRepository,
            IInboxRepository inboxRepository,
            IHistoryRepository historyRepository,
            IMobileUserService mobileUserService,
            IMapper mapper,
            IMemoryCache cache,
            ILogger<MobileDashboardService> logger)
        {
            _userRepository = userRepository;
            _organizationRepository = organizationRepository;
            _slideRepository = slideRepository;
            _inboxRepository = inboxRepository;
            _historyRepository = historyRepository;
            _mobileUserService = mobileUserService;
            _mapper = mapper;
            _cache = cache;
            _logger = logger;
        }

        public async Task<MobileDashboardResponse> GetDashboardDataAsync(long userId)
        {
            var cacheKey = string.Format(DASHBOARD_CACHE_KEY, userId);

            // Try to get from cache first
            if (_cache.TryGetValue<MobileDashboardResponse>(cacheKey, out var cachedResponse))
            {
                _logger.LogDebug("Returning cached dashboard data for user {UserId}", userId);
                return cachedResponse!;
            }

            try
            {
                // Fetch all data in parallel for better performance
                // Using optimized method that includes embedded image data
                var userInfoTask = _mobileUserService.GetUserInfoWithImageAsync(userId, includeImage: true);
                var orgInfoTask = _mobileUserService.GetUserOrganizationAsync(userId);
                var slidesTask = GetSlidesAsync();
                var inboxTask = GetInboxItemsAsync(userId, 3);
                var historyTask = GetHistoryItemsAsync(userId, 3);

                await Task.WhenAll(userInfoTask, orgInfoTask, slidesTask, inboxTask, historyTask);

                var userInfo = await userInfoTask;
                var orgInfo = await orgInfoTask;

                var response = new MobileDashboardResponse
                {
                    UserInfo = userInfo ?? new MobileUserInfo { Id = userId, Name = "Unknown User" },
                    OrgInfo = orgInfo ?? new MobileOrgTreeInfo(),
                    Slides = (await slidesTask).ToList(),
                    InboxItems = (await inboxTask).ToList(),
                    HistoryItems = (await historyTask).ToList(),
                    GeneratedAt = DateTime.UtcNow
                };

                // Cache the response with size specification
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(CACHE_DURATION_MINUTES),
                    Size = 50 // Estimated 50KB for dashboard data
                };
                _cache.Set(cacheKey, response, cacheOptions);

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching dashboard data for user {UserId}", userId);

                // Return a minimal response on error
                return new MobileDashboardResponse
                {
                    UserInfo = new MobileUserInfo { Id = userId, Name = "Error loading user" },
                    OrgInfo = new MobileOrgTreeInfo(),
                    Slides = new List<MobileSlideItem>(),
                    InboxItems = new List<MobileInboxItem>(),
                    HistoryItems = new List<MobileHistoryItem>(),
                    GeneratedAt = DateTime.UtcNow
                };
            }
        }

        public async Task<IEnumerable<MobileInboxItem>> GetInboxItemsAsync(long userId, int limit = 10)
        {
            try
            {
                var (inbox, delegated, commented) = await _inboxRepository.GetAllAsync(userId);

                // Combine all inbox items and take the most recent ones
                var allItems = inbox.Concat(delegated).Concat(commented)
                    .OrderByDescending(i => i.WfDate)
                    .Take(limit);

                return allItems.Select(item => new MobileInboxItem
                {
                    Id = item.WfInsId,
                    Title = item.FlowName ?? string.Empty,
                    Description = item.StateName ?? string.Empty,
                    Type = item.FlowName ?? string.Empty,
                    Status = item.StateName ?? string.Empty,
                    CreatedDate = item.WfDate ?? DateTime.Now,
                    LastLoginEmail = string.Empty, // Inbox entity doesn't have this property
                    LastLoginId = item.LastLoginId,
                    LastLoginNameSurname = item.WfLastModifiedByNom?.ToString() ?? string.Empty,
                    IsUrgent = false, // Inbox entity doesn't have Priority property
                    SenderName = item.WfOwner?.ToString() ?? item.WfLastModifiedByNom?.ToString(),
                    IsRead = false, // TODO: Implement read status tracking
                    Priority = "Normal", // Default priority as Inbox doesn't have this property
                    WorkflowInstanceId = item.WfInsId.ToString(),
                    StateCode = string.Empty // Inbox entity doesn't have StateCode property
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching inbox items for user {UserId}", userId);
                return Enumerable.Empty<MobileInboxItem>();
            }
        }

        public async Task<IEnumerable<MobileHistoryItem>> GetHistoryItemsAsync(long userId, int limit = 10)
        {
            try
            {
                // Since GetUserHistoryAsync doesn't exist, we'll use GetSuspendedWorkflowsAsync 
                // or return empty list for now. This needs to be implemented properly.
                // TODO: Implement proper history retrieval method in IHistoryRepository

                _logger.LogWarning("GetHistoryItemsAsync is not fully implemented - IHistoryRepository doesn't have GetUserHistoryAsync method");

                // For now, return empty list to avoid compilation errors
                return Enumerable.Empty<MobileHistoryItem>();

                // Alternative: You could use GetSuspendedWorkflowsAsync and map those
                // var suspendedWorkflows = await _historyRepository.GetSuspendedWorkflowsAsync(userId);
                // return suspendedWorkflows.Take(limit).Select(item => new MobileHistoryItem
                // {
                //     Id = item.WfInsId,
                //     Title = item.FlowName ?? string.Empty,
                //     Description = item.StateName ?? string.Empty,
                //     Type = item.FlowName ?? string.Empty,
                //     LastLoginEmail = string.Empty,
                //     LastLoginId = item.LastLoginId,
                //     LastLoginNameSurname = item.WfLastModifiedByNom,
                //     Action = string.Empty,
                //     ActionDate = item.WfDate ?? DateTime.Now,
                //     RelatedTo = item.EntityRefId?.ToString(),
                //     Timestamp = item.WfDate ?? DateTime.Now,
                //     Module = item.FlowName ?? string.Empty,
                //     Status = item.WfActionStatusTypeCd ?? string.Empty,
                //     WorkflowInstanceId = item.WfInsId.ToString()
                // }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching history items for user {UserId}", userId);
                return Enumerable.Empty<MobileHistoryItem>();
            }
        }

        public async Task<IEnumerable<MobileSlideItem>> GetSlidesAsync()
        {
            const string SLIDES_CACHE_KEY = "mobile_slides_active";

            // Try cache first
            if (_cache.TryGetValue<IEnumerable<MobileSlideItem>>(SLIDES_CACHE_KEY, out var cachedSlides))
            {
                return cachedSlides!;
            }

            try
            {
                var slides = await _slideRepository.GetActiveSlidesAsync();

                var mobileSlides = slides.Select(slide => new MobileSlideItem
                {
                    Id = slide.Id,
                    Title = slide.Title ?? string.Empty,
                    Description = slide.Description,
                    ImageUrl = slide.ImageUrl,
                    LinkUrl = slide.LinkUrl,
                    Order = slide.Order,
                    IsActive = slide.IsActive == '1',
                    CreatedDate = slide.CreatedDate,
                    ExpiryDate = slide.ValidDateEnd
                })
                .OrderBy(s => s.Order)
                .ToList();

                // Cache for longer as slides don't change often
                var slideCacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30),
                    Size = 20 // Estimated 20KB for slides data
                };
                _cache.Set(SLIDES_CACHE_KEY, mobileSlides, slideCacheOptions);

                return mobileSlides;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching slides");
                return Enumerable.Empty<MobileSlideItem>();
            }
        }

        public async Task RefreshDashboardCacheAsync(long userId)
        {
            var cacheKey = string.Format(DASHBOARD_CACHE_KEY, userId);
            _cache.Remove(cacheKey);

            // Also refresh slides cache
            _cache.Remove("mobile_slides_active");

            _logger.LogInformation("Dashboard cache refreshed for user {UserId}", userId);

            // Optionally pre-populate the cache
            await GetDashboardDataAsync(userId);
        }
    }
}