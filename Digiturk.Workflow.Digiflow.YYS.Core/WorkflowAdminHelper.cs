﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Web.UI.WebControls;

namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// İş Akış Yöneticisi için kullanılan ve kullanılabilecek olan fonksiyonları barındırır.
    /// </summary>
    public class WorkflowAdminHelper
    {
        /// <summary>
        /// Belirtilen id ile eşleşen workflowadmin nesnesini döner
        /// </summary>
        /// <param name="adminId">WorkFlowAdmin Id</param>
        /// <returns>Belirtilen id ile eşleşen workflowadmin nesnesi</returns>
        public static WorkFlowAdmin Get(long adminId)
        {
            //DEĞİŞTİRİLDİ 22.03.2012 YYS_ADMIN tablosundan FULLNAME column ı kaldırıldığı için
            string query = @"SELECT * FROM DT_WORKFLOW.YYS_ADMINS WHERE DT_WORKFLOW.YYS_ADMINS.LOGIN_ID =:LOGIN_ID ";
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGIN_ID", adminId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null && dt.Rows.Count == 0)
            {
                return null;
            }

            return ConvertDataRow(dt.Rows[0]);
        }

        /// <summary>
        /// Verilen datarow'daki değerleri kullanarak bir WorkFlowAdmin nesnesi oluşturur
        /// </summary>
        /// <param name="row">WorkFlowAdmin nesnesinde kullanılacak değerlerin bulunduğu datarow</param>
        public static WorkFlowAdmin ConvertDataRow(DataRow row)
        {
            WorkFlowAdmin wfa = new WorkFlowAdmin();
            wfa.RequestId = ConvertionHelper.ConvertValue<long>(row["WF_ADMIN_ID"]);
            //wfa.FullName = row["FULLNAME"].ToString();
            wfa.LoginId = ConvertionHelper.ConvertValue<long>(row["LOGIN_ID"]);
            //wfa.Sid = row["SID"].ToString();
            wfa.IsSysAdmin = ConvertionHelper.ConvertValue<long>(row["IS_SYS_ADMIN"]);
            wfa.IsActive = ConvertionHelper.ConvertValue<long>(row["IS_ACTIVE"]);
            wfa.Created = ConvertionHelper.ConvertValue<DateTime>(row["CREATED"]);
            //wfa.Email = row["EMAIL"].ToString();
            wfa.AdminLevelTypeId = ConvertionHelper.ConvertValue<long>(row["ADMIN_LEVEL_TYPE_ID"]);
            return wfa;
        }

        /// <summary>
        /// İş akış admininin önceden admin olduğu bütün iş akışlarını siler
        /// </summary>
        /// <param name="adminId"></param>
        public static int RevokePermissions(long adminId)
        {
            string sql = @"DELETE FROM DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS WHERE DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("ADMIN_ID", adminId);
            return Db.ExecuteNonQuery(p, sql, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Yeni bir workflow admin kayıdı oluşturur.
        /// </summary>
        /// <param name="wfa">Kaydedilecek workflow admin nesnesi</param>
        /// <returns>Etkilenen satır sayısı</returns>
        public static int AddNewWorkFlowAdmin(WorkFlowAdmin wfa)
        {
            //DEĞİŞTİRİLDİ 22.03.2012 YYS_ADMIN tablosundan FULLNAME column ı kaldırıldığı için
            //        string query = @"INSERT INTO DT_WORKFLOW.YYS_ADMINS (
            //                       FULLNAME, LOGIN_ID, EMAIL, SID, IS_SYS_ADMIN, IS_ACTIVE, CREATED, CREATED_BY, ADMIN_LEVEL_TYPE_ID)
            //               VALUES (:FULLNAME,:LOGIN_ID,:EMAIL,:SID,:IS_SYS_ADMIN,:IS_ACTIVE,:CREATED,:CREATED_BY,:ADMIN_LEVEL_TYPE_ID)";

            string query = @"INSERT INTO DT_WORKFLOW.YYS_ADMINS (
                        LOGIN_ID, IS_SYS_ADMIN, IS_ACTIVE, CREATED, CREATED_BY, ADMIN_LEVEL_TYPE_ID)
               VALUES (:LOGIN_ID,:IS_SYS_ADMIN,:IS_ACTIVE,:CREATED,:CREATED_BY,:ADMIN_LEVEL_TYPE_ID)";

            //OracleParameter[] p = new OracleParameter[9];
            //p[0] = new OracleParameter("FULLNAME", wfa.FullName);
            //p[1] = new OracleParameter("LOGIN_ID", wfa.LoginId);
            //p[2] = new OracleParameter("EMAIL", wfa.Email);
            //p[3] = new OracleParameter("SID", wfa.Sid);
            //p[4] = new OracleParameter("IS_SYS_ADMIN", wfa.IsSysAdmin);
            //p[5] = new OracleParameter("IS_ACTIVE", wfa.IsActive);
            //p[6] = new OracleParameter("CREATED", wfa.Created);
            //p[7] = new OracleParameter("CREATED_BY", wfa.CreatedBy);
            //p[8] = new OracleParameter("ADMIN_LEVEL_TYPE_ID", wfa.AdminLevelTypeId);

            OracleParameter[] p = new OracleParameter[6];
            // p[0] = new OracleParameter("FULLNAME", wfa.FullName);
            p[0] = new OracleParameter("LOGIN_ID", wfa.LoginId);
            // p[2] = new OracleParameter("EMAIL", wfa.Email);
            // p[3] = new OracleParameter("SID", wfa.Sid);
            p[1] = new OracleParameter("IS_SYS_ADMIN", wfa.IsSysAdmin);
            p[2] = new OracleParameter("IS_ACTIVE", wfa.IsActive);
            p[3] = new OracleParameter("CREATED", wfa.Created);
            p[4] = new OracleParameter("CREATED_BY", wfa.CreatedBy);
            p[5] = new OracleParameter("ADMIN_LEVEL_TYPE_ID", wfa.AdminLevelTypeId);

            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Belirtilen id ile eşleşen WorkFlowAdmin kaydını günceller
        /// </summary>
        /// <param name="adminId">Güncellenecek kayıda ait id</param>
        /// <param name="wfa">Yeni değerleri içeren workflowadmin nesnesi</param>
        /// <returns>Etkilenen satır sayısı</returns>
        public static int Update(WorkFlowAdmin wfa)
        {
            //DEĞİŞTİRİLDİ 22.03.2012 YYS_ADMIN tablosundan FULLNAME column ı kaldırıldığı için
            //        string query = string.Format(@"UPDATE DT_WORKFLOW.YYS_ADMINS
            //                                SET    FULLNAME            = :FULLNAME,
            //                                       LOGIN_ID            = :LOGIN_ID,
            //                                       EMAIL               = :EMAIL,
            //                                       SID                 = :SID,
            //                                       IS_SYS_ADMIN        = :IS_SYS_ADMIN,
            //                                       IS_ACTIVE           = :IS_ACTIVE,
            //                                       LAST_UPDATED        = :LAST_UPDATED,
            //                                       LAST_UPDATED_BY     = :LAST_UPDATED_BY,
            //                                       ADMIN_LEVEL_TYPE_ID = :ADMIN_LEVEL_TYPE_ID
            //                                       WHERE WF_ADMIN_ID   = :WF_ADMIN_ID ");

            //OracleParameter[] p = new OracleParameter[10];
            //p[0] = new OracleParameter("FULLNAME", wfa.FullName);
            //p[1] = new OracleParameter("LOGIN_ID", wfa.LoginId);
            //p[2] = new OracleParameter("EMAIL", wfa.Email);
            //p[3] = new OracleParameter("SID", wfa.Sid);
            //p[4] = new OracleParameter("IS_SYS_ADMIN", wfa.IsSysAdmin);
            //p[5] = new OracleParameter("IS_ACTIVE", wfa.IsActive);
            //p[6] = new OracleParameter("LAST_UPDATED", wfa.LastUpdated);
            //p[7] = new OracleParameter("LAST_UPDATED_BY", wfa.LastUpdatedBy);
            //p[8] = new OracleParameter("ADMIN_LEVEL_TYPE_ID", wfa.AdminLevelTypeId);
            //p[9] = new OracleParameter("WF_ADMIN_ID", wfa.RequestId);

            string query = string.Format(@"UPDATE DT_WORKFLOW.YYS_ADMINS
                                SET    LOGIN_ID            = :LOGIN_ID,
                                       IS_SYS_ADMIN        = :IS_SYS_ADMIN,
                                       IS_ACTIVE           = :IS_ACTIVE,
                                       LAST_UPDATED        = :LAST_UPDATED,
                                       LAST_UPDATED_BY     = :LAST_UPDATED_BY,
                                       ADMIN_LEVEL_TYPE_ID = :ADMIN_LEVEL_TYPE_ID
                                       WHERE WF_ADMIN_ID   = :WF_ADMIN_ID ");

            OracleParameter[] p = new OracleParameter[7];
            // p[0] = new OracleParameter("FULLNAME", wfa.FullName);
            p[0] = new OracleParameter("LOGIN_ID", wfa.LoginId);
            // p[2] = new OracleParameter("EMAIL", wfa.Email);
            // p[3] = new OracleParameter("SID", wfa.Sid);
            p[1] = new OracleParameter("IS_SYS_ADMIN", wfa.IsSysAdmin);
            p[2] = new OracleParameter("IS_ACTIVE", wfa.IsActive);
            p[3] = new OracleParameter("LAST_UPDATED", wfa.LastUpdated);
            p[4] = new OracleParameter("LAST_UPDATED_BY", wfa.LastUpdatedBy);
            p[5] = new OracleParameter("ADMIN_LEVEL_TYPE_ID", wfa.AdminLevelTypeId);
            p[6] = new OracleParameter("WF_ADMIN_ID", wfa.RequestId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Assignment tablosunda atama var mı yok mu kontrolü yapılır
        /// </summary>
        /// <returns></returns>
        public static bool IsAssignmentExist(Digiturk.Workflow.Entities.FWfAssignment Assignment, out long AssignmentId)
        {
            string query = @" Select * from FRAMEWORK.F_WF_ASSIGNMENT  where FRAMEWORK.F_WF_ASSIGNMENT.IS_DEF_ASSIGNMENT=1
                                and FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_TYPE_CD='WFMODIFY' and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNED_OWNER_REF_ID=:ASSIGNED_OWNER_REF_ID
                                and FRAMEWORK.F_WF_ASSIGNMENT.ASSIGNMENT_OWNER_REF_ID=:ASSIGNMENT_OWNER_REF_ID";
            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("ASSIGNED_OWNER_REF_ID", Assignment.AssignedOwnerRefId);
            p[1] = new OracleParameter("ASSIGNMENT_OWNER_REF_ID", Assignment.AssignmentOwnerRefId);
            DataTable dt = Db.ExecuteDataTable(p, ConnectionType.FrameworkConnection, query);

            if (dt.Rows.Count > 0)
            {
                AssignmentId = Convert.ToInt64(dt.Rows[0][0]);
                return true;
            }
            else
            {
                AssignmentId = 0;
                return false;
            }
        }

        /// <summary>
        ///  FRAMEWORK.F_WF_ASSIGNMENT tablosuna kayıt atmak için kullanılır
        /// </summary>
        /// <param name="LoginId">LoginId</param>
        /// <param name="WfDefId">WfdefId</param>
        public static void SaveAssignment(string LoginId, string WfDefId, string AssignmentType)
        {
            Digiturk.Workflow.Entities.FWfAssignment Assignment = GetAssignment(LoginId, WfDefId, AssignmentType);
            Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignment>.SaveEntity(Assignment);
        }

        /// <summary>
        /// Id si verilen Assignment objesinin update eder
        /// </summary>
        /// <param name="AssignmentId"></param>
        /// <returns></returns>
        public static int DeleteAssignment(long AssignmentId)
        {
            string query = @"  delete from FRAMEWORK.F_WF_ASSIGNMENT WHERE  FRAMEWORK.F_WF_ASSIGNMENT.WF_ASSIGNMENT_ID        = :WF_ASSIGNMENT_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_ASSIGNMENT_ID", AssignmentId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.FrameworkConnection);
        }

        /// <summary>
        /// Assignment Nesnesini doldurur
        /// </summary>
        /// <param name="LoginId">AssignedOwnerRefId</param>
        /// <param name="WfDefId">AssignmentOwnerRefId</param>
        /// <returns></returns>
        public static Digiturk.Workflow.Entities.FWfAssignment GetAssignment(string LoginId, string WfDefId, string AssignmentType)
        {
            Digiturk.Workflow.Entities.FWfAssignment Assignment = new Digiturk.Workflow.Entities.FWfAssignment();
            Assignment.WfAssignedType = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignedType>.GetEntity("LOGIN");
            //  Assignment.WfAssignmentType = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignmentType>.GetEntity("WFMODIFY");
            Assignment.WfAssignmentType = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfAssignmentType>.GetEntity(AssignmentType);
            Assignment.AssignmentOwnerRefId = ConvertionHelper.ConvertValue<long>(WfDefId); // X
            Assignment.AssignedOwnerRefId = ConvertionHelper.ConvertValue<long>(LoginId); // X
            Assignment.IsDefAssignment = true;
            Assignment.Deny = false;
            return Assignment;
        }

        /// <summary>
        /// Akış Yöneticisinin belirtilen iş akışı yöneticiliğini kaldırmak için kullanılır
        /// </summary>
        /// <param name="WfOfAdminId">Akışın Yönetici Id si</param>
        /// <returns></returns>
        public static int DeleteTheFlowOfTheAdmin(long WfOfAdminId)
        {
            string query = @"delete FROM DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS where DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_OF_ADMIN_ID=:WF_OF_ADMIN_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_OF_ADMIN_ID", WfOfAdminId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Kulanıcıya ve sistem yöneticisine, kullanıcının akış yöneticisi ve sistem yöneticisi durumlarını bilgilendirme mailleri atar
        /// </summary>
        /// <param name="AdminLevelTypeId">Kullanıcının tipi</param>
        /// <param name="LoginId">Kullanıcının LoginId si</param>
        /// <param name="wfa">Şu anki wfa admin nesnesi</param>
        /// <param name="Mode">Update veya Insert durumu</param>
        public static void SendMailToUsers(long AdminLevelTypeId, WorkFlowAdmin wfa, string Mode, CheckBoxList WorkflowListCheckBoxList, long LoginId)
        {
            //DEĞİŞTİRİLDİ 22.03.2012 YYS_ADMIN tablosundan FULLNAME column ı kaldırıldığı için
            //AdminLevelTypeId = 4;//SystemAndFlowAdmin
            //AdminLevelTypeId = 3;//SystemAdmin
            //AdminLevelTypeId = 2;//FlowAdmin
            //AdminLevelTypeId = 1;//None

            WorkFlowAdmin OldWfa = WorkflowAdminHelper.Get(wfa.LoginId);
            Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
            HrUser SystemAdmin = HrHelper.Get(LoginId);
            ContextList.Add("FullName", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(wfa.LoginId));
            ContextList.Add("SystemAdminFullName", SystemAdmin.FullName);
            string WorkFlows = "";

            foreach (ListItem item in WorkflowListCheckBoxList.Items)
            {
                if (item.Selected)
                {
                    WorkFlows += item.Text + "<br/>";
                }
            }
            ContextList.Add("WorkFlows", WorkFlows);

            if (Mode == "Update")//Varolan admin düzenleniyor ise
            {
                if (wfa.IsSysAdmin != OldWfa.IsSysAdmin)//Sistem Yöneticisi tanımı değişmiş ise
                {
                    if (Convert.ToBoolean(wfa.IsSysAdmin))
                    {
                        ContextList.Add("Active/Passive", "Aktif");
                    }
                    else
                    {
                        ContextList.Add("Active/Passive", "Pasif");
                    }
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4002, wfa.LoginId, ContextList);
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4003, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
                List<Digiturk.Workflow.Digiflow.Entities.Workflow> wfawf = WorkflowHelper.GetWorkflowsOfAdmin(wfa.LoginId);
                List<Digiturk.Workflow.Digiflow.Entities.Workflow> OldWfawf = WorkflowHelper.GetWorkflowsOfAdmin(OldWfa.LoginId);

                bool Result = CompareTwoWorkFlowLists(wfawf, OldWfawf);
                if ((AdminLevelTypeId == 3 || AdminLevelTypeId == 1) && OldWfawf.Count > 0)//SystemAdmin
                {
                    //Kullanıcıya bilgilendirme Akış Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4000, wfa.LoginId, ContextList);
                    //sistem admine bilgilendirme Akış Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4001, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
                else if (AdminLevelTypeId != 1 && Result != true)//SystemAndFlowAdmin
                {
                    ContextList.Add("Active/Passive", "Aktif");
                    //Kullanıcının Akış Adminliği kullanıcıya
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4004, wfa.LoginId, ContextList);
                    //Kullanıcının Akış Adminliği Sistem Adminine
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4005, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
            }
            else if (Mode == "Insert")//Yeni bir admin tanımlanıyor ise
            {
                if (AdminLevelTypeId == 4)//SystemAndFlowAdmin
                {
                    ContextList.Add("Active/Passive", "Aktif");

                    //Kullanıcıya bilgilendirme Sistem Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4002, wfa.LoginId, ContextList);
                    //sistem admine bilgilendirme Sistem Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4003, Convert.ToInt64(SystemAdmin.LoginId), ContextList);

                    //Kullanıcının Akış Adminliği kullanıcıya
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4004, wfa.LoginId, ContextList);
                    //Kullanıcının Akış Adminliği Sistem Adminine
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4005, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
                else if (AdminLevelTypeId == 3)//SystemAdmin
                {
                    //Kullanıcıya bilgilendirme Sistem Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4002, wfa.LoginId, ContextList);
                    //sistem admine bilgilendirme Sistem Adminliği
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4003, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
                else if (AdminLevelTypeId == 2)////FlowAdmin
                {
                    //Kullanıcının Akış Adminliği kullanıcıya
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4004, wfa.LoginId, ContextList);
                    //Kullanıcının Akış Adminliği Sistem Adminine
                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(4005, Convert.ToInt64(SystemAdmin.LoginId), ContextList);
                }
            }
        }

        /// <summary>
        /// Kullanıcının eski iş akışlarını ve yeni iş akışlarını karşılaştırır, aynı ise bu duruma göre mail atmayacak
        /// </summary>
        /// <param name="wfawf">Yeni akış listesi</param>
        /// <param name="OldWfawf">Eski akış listesi</param>
        /// <returns></returns>
        public static bool CompareTwoWorkFlowLists(List<Digiturk.Workflow.Digiflow.Entities.Workflow> wfawf, List<Digiturk.Workflow.Digiflow.Entities.Workflow> OldWfawf)
        {
            int count = 0;
            if (wfawf.Count != OldWfawf.Count)
            {
                return false;
            }
            else
            {
                for (int i = 0; i < wfawf.Count; i++)
                {
                    if (wfawf[i].WorkflowDefId == OldWfawf[i].WorkflowDefId)
                    {
                        count++;
                    }
                }
            }
            if (count == wfawf.Count)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Akış admininin admin i olduğu akışları kaydeder
        /// </summary>
        /// <param name="wfawf"></param>
        /// <returns>kaydedilir ise 1 döner</returns>
        public static int AddWfOfAdmin(WorkflowAdminWorkflow wfawf)
        {
            string query = @"INSERT INTO DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS
                      ( ADMIN_ID, WF_DEF_ID, CREATED, CREATED_BY, LAST_UPDATED, LAST_UPDATED_BY)
               VALUES (:ADMIN_ID,:WF_DEF_ID,:CREATED,:CREATED_BY,:LAST_UPDATED,:LAST_UPDATED_BY)";

            OracleParameter[] p = new OracleParameter[6];
            p[0] = new OracleParameter("ADMIN_ID", wfawf.AdminId);
            p[1] = new OracleParameter("WF_DEF_ID", wfawf.WfDefId);
            p[2] = new OracleParameter("CREATED", wfawf.Created);
            p[3] = new OracleParameter("CREATED_BY", wfawf.CreatedBy);
            p[4] = new OracleParameter("LAST_UPDATED", wfawf.LastUpdated);
            p[5] = new OracleParameter("LAST_UPDATED_BY", wfawf.LastUpdatedBy);

            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }
    }
}