﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.Entities.YYS.LogicalGroup;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    public class LogicalGroupAdMapHelper
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lgm"></param>
        /// <returns></returns>
        public static int AddNewLogicalGroupMapMember(LogicalGroupAdMap lgm)//map tablosuna insert atıyo ad grup Id(sequencele kyıt atcagımız kolon, ad domaın,ad grup adı, logıcal ıd)
        {
            string query = @"INSERT INTO DT_WORKFLOW.YYS_LG_AD_MAPS
                (                  
                  AD_DOMAIN,
                  AD_GROUP,
                  LAST_SENK_DATE,
                  CREATED,
                  CREATED_BY,
                  VERSION_ID,
                  LOGICAL_GROUP_ID,                  
                )
                VALUES (
                  :AD_DOMAIN,
                  :AD_GROUP,
                  :LAST_SENK_DATE,
                  :CREATED,
                  :CREATED_BY,
                  :VERSION_ID,
                  :LOGICAL_GROUP_ID,                  
                )";



            OracleParameter[] p = new OracleParameter[7];
            p[0] = new OracleParameter(":AD_DOMAIN", lgm.AdDomain);
            p[1] = new OracleParameter(":AD_GROUP", lgm.AdGroup);
            p[2] = new OracleParameter(":LAST_SENK_DATE", lgm.LastSenkDate);
            p[3] = new OracleParameter(":CREATED", lgm.Created);
            p[4] = new OracleParameter(":CREATED_BY", lgm.CreatedBy);
            p[5] = new OracleParameter(":VERSION_ID", lgm.VersionId);
            p[6] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="lgm"></param>
        /// <returns></returns>
        public static long AddNewLogicalGroupMapMemberYeni(LogicalGroupAdMap lgm)//map tablosuna insert atıyo ad grup Id(sequencele kyıt atcagımız kolon, ad domaın,ad grup adı, logıcal ıd)
        {
            //string sequence = @"SELECT DT_WORKFLOW.SQ_YYS_LG_AD_MAPS.NEXTVAL FROM DUAL";
            List<CustomParameterList> Param = new List<CustomParameterList>();
            Param.Add(new CustomParameterList(":DUAL", "DUAL"));
            string sequenceVal = DataAccessLayer.ModelWorking.GetOnlyColumnSQL<string>("DefaultConnection", "SELECT DT_WORKFLOW.SQ_YYS_LG_AD_MAPS.NEXTVAL FROM :DUAL", Param);

            string query = @"INSERT INTO DT_WORKFLOW.YYS_LG_AD_MAPS
                (                  
                  AD_DOMAIN,
                  AD_GROUP,
                  LAST_SENK_DATE,
                  CREATED,
                  CREATED_BY,
                  VERSION_ID,
                  LOGICAL_GROUP_ID,
                  YYS_LG_AD_MEMBERS_ID

                )
                VALUES (
                  :AD_DOMAIN,
                  :AD_GROUP,
                  :LAST_SENK_DATE,
                  :CREATED,
                  :CREATED_BY,
                  :VERSION_ID,
                  :LOGICAL_GROUP_ID,
                  :YYS_LG_AD_MEMBERS_ID 
                )";



            OracleParameter[] p = new OracleParameter[8];
            p[0] = new OracleParameter(":AD_DOMAIN", lgm.AdDomain);
            p[1] = new OracleParameter(":AD_GROUP", lgm.AdGroup);
            p[2] = new OracleParameter(":LAST_SENK_DATE", lgm.LastSenkDate);
            p[3] = new OracleParameter(":CREATED", lgm.Created);
            p[4] = new OracleParameter(":CREATED_BY", lgm.CreatedBy);
            p[5] = new OracleParameter(":VERSION_ID", lgm.VersionId);
            p[6] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
            p[7] = new OracleParameter(":YYS_LG_AD_MEMBERS_ID", ConvertionHelper.ConvertValue<long>(sequenceVal.ToString()));
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection) > 0 ? ConvertionHelper.ConvertValue<long>(sequenceVal.ToString()) : 0;

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="lgm"></param>
        /// <returns></returns>
        public static int UpdateLogicalGroupMap(LogicalGroupAdMap lgm)
        {
            string query = @"UPDATE DT_WORKFLOW.YYS_LG_AD_MAPS SET
            NAME=:NAME,
            LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID,
            AD_DOMAIN=:AD_DOMAIN,
            AD_GROUP=:AD_GROUP,
            LAST_SENK_DATE=:LAST_SENK_DATE,
            CREATED=:CREATED,
            CREATED_BY=:CREATED_BY
            WHERE YYS_LG_AD_MEMBERS_ID=:YYS_LG_AD_MEMBERS_ID";
            OracleParameter[] p = new OracleParameter[9];
            //p[0] = new OracleParameter(":NAME", lgm.Name);

            p[0] = new OracleParameter(":AD_DOMAIN", lgm.AdDomain);
            p[1] = new OracleParameter(":AD_GROUP", lgm.AdGroup);
            p[2] = new OracleParameter(":LAST_SENK_DATE", lgm.LastSenkDate);
            p[3] = new OracleParameter(":CREATED", lgm.Created);
            p[4] = new OracleParameter(":CREATED_BY", lgm.CreatedBy);
            p[5] = new OracleParameter(":VERSION_ID", lgm.VersionId);
            p[6] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
            p[7] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static int DeleteLogicalGroupMap(long logicalGroupMapId)
        {
            string query = @"DELETE FROM DT_WORKFLOW.YYS_LG_AD_MAPS WHERE DT_WORKFLOW.YYS_LG_AD_MAPS.YYS_LG_AD_MEMBERS_ID=:YYS_LG_AD_MEMBERS_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", logicalGroupMapId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static int DeleteAllLogicalGroupMap(long LogicalGroupId)
        {
            string query = @"DELETE FROM DT_WORKFLOW.YYS_LG_AD_MAPS WHERE DT_WORKFLOW.YYS_LG_AD_MAPS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", LogicalGroupId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <returns></returns>
        public static List<LogicalGroupAdMap> GetAlllogicalGroupsByLogicalGroup(long LogicalGroupId)
        {
            List<LogicalGroupAdMap> ret = new List<LogicalGroupAdMap>();
            string query = "Select * from DT_WORKFLOW.YYS_LG_AD_MAPS where LOGICAL_GROUP_ID=" + LogicalGroupId;
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);

            if (dt != null)
            {
                foreach (DataRow row in dt.Rows)
                {
                    LogicalGroupAdMap lg = ConvertDataRow(row);

                    if (!ret.Contains(lg))
                    {
                        ret.Add(lg);
                    }
                }
            }

            return ret;
        }

        public static DataTable GetAlllogicalGroupsByLogicalGroupByDtb(long LogicalGroupId)
        {
            string query = "Select * from DT_WORKFLOW.YYS_LG_AD_MAPS where LOGICAL_GROUP_ID=" + LogicalGroupId;
            return Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
        }
        /// <summary>
        /// Mantıksal Gruplar
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <returns></returns>
        public static DataTable GetMapListForLogicalGroup(long LogicalGroupId)
        {
            string SQL = @"Select
                        WF.NAME as FlowName,
                        LG.NAME as LogicalGroupName,
                        MP.AD_DOMAIN as Domain,
                        MP.AD_GROUP as AdGroupName
                        from DT_WORKFLOW.YYS_LG_AD_MAPS MP
                        INNER JOIN  DT_WORKFLOW.YYS_LOGICAL_GROUPS LG ON LG.LOGICAL_GROUP_ID = MP.LOGICAL_GROUP_ID
                        INNER JOIN  FRAMEWORK.F_WF_WORKFLOW_DEF WF ON WF.WF_WORKFLOW_DEF_ID = LG.WF_DEF_ID
                        where MP.LOGICAL_GROUP_ID = " + LogicalGroupId.ToString();
            return Db.ExecuteDataTable(SQL, ConnectionType.DefaultConnection);
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="row"></param>
        /// <returns></returns>
        public static LogicalGroupAdMap ConvertDataRow(DataRow row)
        {
            LogicalGroupAdMap lg = new LogicalGroupAdMap();            
            lg.YYYSAdMemberId = ConvertionHelper.ConvertValue<long>(row["YYS_LG_AD_MEMBERS_ID"]);
            lg.LogicalGroupId = ConvertionHelper.ConvertValue<long>(row["LOGICAL_GROUP_ID"]);            
            lg.AdDomain = ConvertionHelper.ConvertValue<string>(row["AD_DOMAIN"]);
            lg.AdGroup = ConvertionHelper.ConvertValue<string>(row["AD_GROUP"]);
            lg.LastSenkDate = ConvertionHelper.ConvertValue<System.DateTime>(row["LAST_SENK_DATE"]);
            lg.LastUpdated = string.IsNullOrEmpty(row["LAST_UPDATED"].ToString()) ? DateTime.MinValue : ConvertionHelper.ConvertValue<System.DateTime>(row["LAST_UPDATED"]);
            lg.LastUpdatedBy = string.IsNullOrEmpty(row["LAST_UPDATED_BY"].ToString()) ? 0 : ConvertionHelper.ConvertValue<long>(row["LAST_UPDATED_BY"]);
            lg.Created = ConvertionHelper.ConvertValue<System.DateTime>(row["CREATED"]);
            lg.CreatedBy = ConvertionHelper.ConvertValue<long>(row["CREATED_BY"]);
            lg.VersionId = string.IsNullOrEmpty(row["VERSION_ID"].ToString()) ? 0 : ConvertionHelper.ConvertValue<long>(row["VERSION_ID"]);
            return lg;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="YYYSAdMemberId"></param>
        /// <returns></returns>
        public static LogicalGroupAdMap Get(long YYYSAdMemberId)
        {
            LogicalGroupAdMap mp = new LogicalGroupAdMap();
            string query = "Select * from DT_WORKFLOW.YYS_LG_AD_MAPS where YYS_LG_AD_MEMBERS_ID=" + YYYSAdMemberId;
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
            if (dt != null)
            {
                if (dt.Rows.Count > 0)
                {
                    mp = ConvertDataRow(dt.Rows[0]);
                }
            }
            return mp;
        }
        /// <summary>
        /// İLGİLİ LOGİCAL GRUPTA BU DOMAIN VE AD GRUBU MAPLENMIS MI KONTROL EDER
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <param name="AdDomain"></param>
        /// <param name="AdGroup"></param>
        /// <returns></returns>
        public static bool CheckLogicalGroupAdGroup(long LogicalGroupId, string AdDomain, string AdGroup)
        {
            try
            {
                bool result = false;
                string query = "Select * from DT_WORKFLOW.YYS_LG_AD_MAPS where  LOGICAL_GROUP_ID=" + LogicalGroupId + "AND AD_DOMAIN='" + AdDomain + "' AND AD_GROUP='" + AdGroup + "'";
                DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
                if (dt != null)
                {
                    if (dt.Rows.Count > 0)
                    {
                        result = true;
                    }
                }
                return result;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static void SenkAdGroup(long LogicalGroupId, string Name, DataTable dtb)
        {
            //#region  AD YYS Grup Senkronizasyonu İşlemleri

            //#region  AD YYS Ekleme Senkronizasyonu
            for (int i = 0; i < dtb.Rows.Count; i++)
            {
                try
                {
                    if (!CheckLogicalGroupAdGroup(LogicalGroupId, dtb.Rows[i]["DOMAIN"].ToString(), dtb.Rows[i]["ADGROUPNAME"].ToString()))
                    {
                        #region Yeni AD Grubu Ekleme Süreci
                        AddNewLogicalGroupMapMember(new LogicalGroupAdMap()
                        {
                            //Name = Name,
                            LogicalGroupId = LogicalGroupId,
                            AdDomain = dtb.Rows[i]["DOMAIN"].ToString(),
                            AdGroup = dtb.Rows[i]["ADGROUPNAME"].ToString(),
                            LastSenkDate = DateTime.Now,
                            Created = DateTime.Now,
                            CreatedBy = 0
                        });
                        //#endregion

                        //burda mapi halletti sonradadn kullanıcıları eklemesı lazım
                    }


                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            //#endregion

            //#region AD YYS Çıkartma Senkronizasyonu
            DataTable dts = GetAlllogicalGroupsByLogicalGroupByDtb(LogicalGroupId);
            //==> 2. For da Veritabanında Olup da DataTable da olmayan Kayıtları Silelim.
            for (int i = 0; i < dts.Rows.Count; i++)
            {
                //#region Ad Grupta Var Olma Kontrolü Yapılıyor
                try
                {
                    bool result = false;
                    for (int j = 0; j < dtb.Rows.Count; j++)
                    {
                        if ((dtb.Rows[j]["DOMAIN"].ToString() == dts.Rows[i]["AD_DOMAIN"].ToString()) && (dts.Rows[i]["AD_GROUP"].ToString() == dtb.Rows[j]["ADGROUPNAME"].ToString()))
                        {
                            result = true;
                        }
                    }
                    if (!result)
                    {
                        long LgMapId = ConvertionHelper.ConvertValue<long>(dts.Rows[i]["YYS_LG_AD_MEMBERS_ID"]);
                        DeleteLogicalGroupMap(LgMapId);
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                #endregion
            }
            //#endregion

            //#endregion
        }

        public static void SenkAdGroupNewSingle(long LogicalGroupId, string Name, DataTable dtbAdMaps, DataTable dtbAdUsers)
        {
            //#region  AD YYS Grup Senkronizasyonu İşlemleri

            //#region  AD YYS Ekleme Senkronizasyonu
            for (int i = 0; i < dtbAdMaps.Rows.Count; i++)
            {
                try
                {
                    if (!CheckLogicalGroupAdGroup(LogicalGroupId, dtbAdMaps.Rows[i]["DOMAIN"].ToString(), dtbAdMaps.Rows[i]["ADGROUPNAME"].ToString()))
                    {
                        //#region Yeni AD Grubu Ekleme Süreci
                        AddNewLogicalGroupMapMember(new LogicalGroupAdMap()
                        {
                            //Name = Name,                            
                            LogicalGroupId = LogicalGroupId,
                            AdDomain = dtbAdMaps.Rows[i]["DOMAIN"].ToString(),
                            AdGroup = dtbAdMaps.Rows[i]["ADGROUPNAME"].ToString(),
                            LastSenkDate = DateTime.Now,
                            Created = DateTime.Now,
                            CreatedBy = 0
                        });
                        //#endregion

                        //burda mapi halletti sonradadn kullanıcıları eklemesı lazım

                        //#region Ad Grup Bazında Kullanıcı Ekleme 
                        //try
                        //{
                        //    for (int j = 0; j < dtbAdUsers.Rows.Count; j++)
                        //    {
                        //        try
                        //        {
                        //            if (dtbAdUsers.Rows[j]["Flogin"].ToString() != "")
                        //            {
                        //                LogicalGroupMember lgm = new LogicalGroupMember();
                        //                lgm.FullName = dtbAdUsers.Rows[i]["DisplayName"].ToString();
                        //                lgm.LoginId = long.Parse(dtbAdUsers.Rows[i]["Flogin"].ToString());
                        //                lgm.LogicalGroupMemberTypeId = 5;
                        //                lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]);
                        //                lgm.Created = DateTime.Now;
                        //                lgm.CreatedBy = UserInformation.LoginObject.LoginId;
                        //                lgm.LastUpdated = DateTime.Now;
                        //                lgm.LastUpdatedBy = UserInformation.LoginObject.LoginId;
                        //                lgm.IsAdTransfer = 1;
                        //                bool OnePersonCheck = IsOnePerson == 1 && gvLogicalMembers.VisibleRowCount == 1;
                        //                bool ExistFLogin = LogicalGroupMemberHelper.CheckFLoginLogicalGroup(lgm.LogicalGroupId, lgm.LoginId);
                        //                if (!ExistFLogin && !OnePersonCheck)
                        //                {
                        //                    LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
                        //                    GetAllLogicalGroupMembers();
                        //                }
                        //            }
                        //        }
                        //        catch (Exception ex)
                        //        {
                        //            throw ex;
                        //        }
                        //    }
                        //    //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
                        //    GetAllLogicalGroupMembers();
                        //    SendUnDefinedLoginId();
                        //    //UpdateLogicalGrup(ConvertionHelper.ConvertValue<long>(Session["LogicalGroupId"]), DrpDomain.SelectedValue, DrpGrupList.SelectedValue);
                        //    //Kullanıcı seçilmeden mantıksal grup üyesi eklenemez
                        //    // Grupta Tüm Kullanıcılar Var mı kontrolü Yapılacak.
                        //    // Grupta Eklenen Kullanıcı Var mı Kontrolü Yapılacak.
                        //    // Tek Kişilik bir Grupsa ve O Tek Kişi Eklendi mi kontrolü yapılacak.
                        //}
                        //catch (Exception ex)
                        //{
                        //    throw ex;
                        //}
                        //#endregion
                    }


                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            //#endregion

            //#region AD YYS Çıkartma Senkronizasyonu
            //DataTable dts = GetAlllogicalGroupsByLogicalGroupByDtb(LogicalGroupId);
            ////==> 2. For da Veritabanında Olup da DataTable da olmayan Kayıtları Silelim.
            //for (int i = 0; i < dts.Rows.Count; i++)
            //{
            //#region Ad Grupta Var Olma Kontrolü Yapılıyor
            //    try
            //    {
            //        bool result = false;
            //        if ((Domain == dts.Rows[i]["AD_DOMAIN"].ToString()) && (dts.Rows[i]["AD_GROUP"].ToString() == AdGroupName))
            //        {
            //            result = true;
            //        }

            //        if (!result)
            //        {
            //            long LgMapId = ConvertionHelper.ConvertValue<long>(dts.Rows[i]["YYS_LG_AD_MEMBERS_ID"]);
            //            DeleteLogicalGroupMap(LgMapId);
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        throw ex;
            //    }
            //    #endregion
            //}
            //#endregion

            //#endregion
        }

        public static long SenkAdGroup3üncüMapTabloIdDönen(long LogicalGroupId, DataRow adGroupMapRow)
        {
            //#region  AD YYS Grup Senkronizasyonu İşlemleri
            long adGroupId = 0;
            bool cikarmaSenk = true;
            bool adGroupIdSonuc = true;
            //#region  AD YYS Ekleme Senkronizasyonu
            if (!CheckLogicalGroupAdGroup(LogicalGroupId, adGroupMapRow["DOMAIN"].ToString(), adGroupMapRow["ADGROUPNAME"].ToString()))//map tablosunda kayıt varmı dıye bakıyor
            {
                try
                {
                    //#region Yeni AD Grubu Ekleme Süreci
                    adGroupId = AddNewLogicalGroupMapMemberYeni(new LogicalGroupAdMap()
                    {
                        //Name = Name,
                        LogicalGroupId = LogicalGroupId,
                        AdDomain = adGroupMapRow["DOMAIN"].ToString(),
                        AdGroup = adGroupMapRow["ADGROUPNAME"].ToString(),
                        LastSenkDate = DateTime.Now,
                        Created = DateTime.Now,
                        CreatedBy = 0
                    });
                    //#endregion
                }
                catch (Exception ex)
                {
                    adGroupIdSonuc = false;
                }
                try
                {
                    AdYYSCikartmaSenkronizasyonu(LogicalGroupId, adGroupMapRow);
                }
                catch (Exception)
                {
                    cikarmaSenk = false;
                }
                //burda mapi halletti sonradadn senkronizasyon ve sılme yapabılırsın                

                if (cikarmaSenk && adGroupIdSonuc && adGroupId > 0)
                {
                    return adGroupId;
                }
            }

            return adGroupId;
            //#endregion           
        }

        public static void AdYYSCikartmaSenkronizasyonu(long LogicalGroupId, DataRow adGroupMapRow)//bunu debug et gerekırse teke ındır gerek kalmaybılır
        {
            //#region AD YYS Çıkartma Senkronizasyonu
            DataTable dts = GetAlllogicalGroupsByLogicalGroupByDtb(LogicalGroupId);//isim yalnıs map tablosunda o logıcal gruba aıt tum ap gruplarını alıyo
            //==> 2. For da Veritabanında Olup da DataTable da olmayan Kayıtları Silelim.

            for (int i = 0; i < dts.Rows.Count; i++)
            {
                //#region Ad Grupta Var Olma Kontrolü Yapılıyor
                try
                {
                    bool result = false;
                    if ((adGroupMapRow["AD_DOMAIN"].ToString() == dts.Rows[i]["AD_DOMAIN"].ToString()) && (adGroupMapRow["AD_GROUP"].ToString() == dts.Rows[i]["AD_GROUP"].ToString()))
                    {
                        result = true;
                    }
                    if (!result)
                    {
                        long LgMapId = ConvertionHelper.ConvertValue<long>(dts.Rows[i]["YYS_LG_AD_MEMBERS_ID"]);
                        DeleteLogicalGroupMap(LgMapId);
                    }
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                //#endregion
            }
            //#endregion
        }

        //public static void AdYYSCikartmaSenkronizasyonuNew(long LogicalGroupId, DataTable adGroupMapRow)//bunu debug et gerekırse teke ındır gerek kalmaybılır
        //{
        //    #region AD YYS Çıkartma Senkronizasyonu
        //    DataTable dtbMapsFromDB = GetAlllogicalGroupsByLogicalGroupByDtb(LogicalGroupId);//isim yalnıs map tablosunda o logıcal gruba aıt tum ap gruplarını alıyo
        //    //==> 2. For da Veritabanında Olup da DataTable da olmayan Kayıtları Silelim.



        //    for (int i = 0; i < dtbMapsFromDB.Rows.Count; i++)
        //    {
        //        //#region Ad Grupta Var Olma Kontrolü Yapılıyor
        //        try
        //        {
        //            bool result = false;
        //            if ((adGroupMapRow["AD_DOMAIN"].ToString() == dtbMapsFromDB.Rows[i]["AD_DOMAIN"].ToString()) && (adGroupMapRow["AD_GROUP"].ToString() == dtbMapsFromDB.Rows[i]["AD_GROUP"].ToString()))
        //            {
        //                result = true;
        //            }
        //            if (!result)
        //            {
        //                long LgMapId = ConvertionHelper.ConvertValue<long>(dtbMapsFromDB.Rows[i]["YYS_LG_AD_MEMBERS_ID"]);
        //                DeleteLogicalGroupMap(LgMapId);
        //            }
        //        }
        //        catch (Exception ex)
        //        {
        //            throw ex;
        //        }
        //        //#endregion
        //    }
        //    #endregion
        //}

        /// <summary>
        /// Secilen Ad grubunun secilen logical grup icin map tablosundakı Id'sini döner.
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static long GetAdGroupMapId(string domain, string adGroup, long logicalGroupId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LG_AD_MAPS where AD_DOMAIN =:AD_DOMAIN AND AD_GROUP=:AD_GROUP AND LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[3];
            p[0] = new OracleParameter("AD_DOMAIN", domain);
            p[1] = new OracleParameter("AD_GROUP", adGroup);
            p[2] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);


            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            return long.Parse(dt.AsEnumerable().Select(x => x["YYS_LG_AD_MEMBERS_ID"]).FirstOrDefault().ToString());
        }

        /// <summary>
        /// Secilen Ad grubunun secilen logical grup icin map tablosundakı Id'sini döner.
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static DataTable GetAllMappedAdGroupsForLogicalGroup(long logicalGroupId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LG_AD_MAPS where LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];                        
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);


            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            return dt;
        }

        /// <summary>
        /// Mantıksal Grupda Kullanıcı Var mı? Varsa True döner
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool CheckAdGroupMapExistForLogicalGroup(long LogicalGroupId, string adDomain, string adGroup)
        {
            string SQL = string.Format("SELECT * FROM DT_WORKFLOW.YYS_LG_AD_MAPS where LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID AND AD_DOMAIN=:AD_DOMAIN AND AD_GROUP=:AD_GROUP");
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[3];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", LogicalGroupId);
            p[1] = new OracleParameter("AD_DOMAIN", adDomain);
            p[2] = new OracleParameter("AD_GROUP", adGroup);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, SQL);           
            return dt.Rows.Count > 0;
        }


        public static List<LogicalGroupAdMap> GetAllMappedAdGroupsOfLogicalGroups()
        {
            List<LogicalGroupAdMap> ret = new List<LogicalGroupAdMap>();
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LG_AD_MAPS";

            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);

            if (dt != null)
            {
                foreach (DataRow row in dt.Rows)
                {
                    LogicalGroupAdMap lg = ConvertDataRow(row);

                    if (!ret.Contains(lg))
                    {
                        ret.Add(lg);
                    }
                }
            }
            return ret;
        }
        
    }
}
