<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.XtraPrinting.v12.1</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraPrintingLinks.DataGridLink">

            <summary>
                <para>A link to print the <see cref="T:System.Windows.Forms.DataGrid"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the DataGridLink class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface which specifies the owner container of a DataGridLink class instance.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the DataGridLink class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.#ctor">
            <summary>
                <para>Initializes a new instance of the DataGridLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.DataGridLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.DataGridLink.Images">
            <summary>
                <para>A collection of images which can be added to the page's headers and footers. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.DataGridLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.
</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.DataGridLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.DataGridLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link using the specified look and feel settings.
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link as a child of the specified parent window, using the specified look and feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the Print Preview Form, which modally shows the print preview of the document for this link as a child of the specified parent window. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.DataGridLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.RichTextBoxLink">

            <summary>
                <para>A link to print the <see cref="T:System.Windows.Forms.RichTextBox"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextBoxLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the RichTextBoxLink class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the Print Preview Form, which modally shows the print preview of the document for this link as a child of the specified parent window. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link as a child of the specified parent window, using the specified look and feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.CompositeLink">

            <summary>
                <para>A composite link that can be used to combine several printing links together into a composite document.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the CompositeLink class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the CompositeLink class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface which specifies the owner container of a CompositeLink class instance.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.#ctor">
            <summary>
                <para>Initializes a new instance of the CompositeLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the Printing System of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.CompositeLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.CompositeLink.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.CompositeLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.CompositeLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.CompositeLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview form with the document created from this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview form with the document created from this link. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the modal Print Preview form with the document created from this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Print Preview form with the document created from this link. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the modal Print Preview form with the document created from this link. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.CompositeLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.TreeViewLink">

            <summary>
                <para>A link to print the <see cref="T:System.Windows.Forms.TreeView"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.#ctor">
            <summary>
                <para>Initializes a new instance of the TreeViewLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the TreeViewLink class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.TreeViewLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.TreeViewLink.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.TreeViewLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.TreeViewLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.TreeViewLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link as a child of the specified parent window, using the specified look and feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the Print Preview Form, which modally shows the print preview of the document for this link as a child of the specified parent window. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.TreeViewLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.ListViewLink">

            <summary>
                <para>A link to print the <see cref="T:System.Windows.Forms.ListView"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.#ctor">
            <summary>
                <para>Initializes a new instance of the ListViewLink class with the default settings.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.ListViewLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.ListViewLink.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.ListViewLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.ListViewLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.ListViewLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the Print Preview Form, which modally shows the print preview of the document for this link as a child of the specified parent window. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link as a child of the specified parent window, using the specified look and feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.ListViewLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx">

            <summary>
                <para>Represents a Ribbon Print Preview form intended to show the print preview of a document.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintPreviewRibbonFormEx class with the default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx.PrintRibbonController">
            <summary>
                <para>Gets the <b>PrintRibbonController</b> of the Print Preview form with a Ribbon.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintRibbonController"/> object which represents the <b>Print Ribbon Controller</b> of the Print Preview form with a Ribbon.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx.RibbonControl">
            <summary>
                <para>Gets the <b>RibbonControl</b> of the Print Preview form with a Ribbon.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object which represents the <b>Ribbon Control</b> of the Print Preview form with a Ribbon.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx.RibbonStatusBar">
            <summary>
                <para>Gets the <b>RibbonStatusBar</b> of the Print Preview form with a Ribbon.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> object which represents the <b>Ribbon Status Bar</b> of the Print Preview form with a Ribbon.

</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.PrintRibbonController">

            <summary>
                <para>A component that is used to create a Print Preview page inside a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> when creating a Print Preview Form with a Ribbon.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintRibbonController class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.#ctor(System.Object)">
            <summary>
                <para>Initializes a new instance of the PrintRibbonController class with the specified context.
</para>
            </summary>
            <param name="contextSpecifier">
		A <see cref="T:System.Object"/> which contains information about the current context.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the PrintRibbonController class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.BeginInit">
            <summary>
                <para>Starts the PrintRibbonController's initialization. Initialization occurs at runtime.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.EndInit">
            <summary>
                <para>Ends the PrintRibbonController's initialization.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.GetBarItemByCommand(DevExpress.XtraPrinting.PrintingSystemCommand)">
            <summary>
                <para>Gets a bar item within the Ribbon Control by its command.

</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be executed by the bar item to be found.


            </param>
            <returns>A <see cref="T:DevExpress.XtraBars.BarItem"/> object that represents the bar item which executes the specified command.
</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintRibbonController.ImageCollection">
            <summary>
                <para>Gets the collection of images used in the PrintRibbonController UI.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.RibbonImageCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintRibbonController.PreviewRibbonPages">
            <summary>
                <para>Provides access to a collection of Ribbon pages, generated by this PrintRibbonController instance.

</para>
            </summary>
            <value>An object of the <see cref="T:System.Collections.Generic.IEnumerable"/>@#60;T@#62; generic type, representing a collection of Ribbon pages.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintRibbonController.PrintControl">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance to which the <b>Print Ribbon Controller</b> belongs.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance to which the <b>Print Ribbon Controller</b> belongs.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintRibbonController.UpdateCommands">
            <summary>
                <para>Updates Ribbon Control items according to the current visibility state of the corresponding Printing System commands.


</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.RibbonImageCollection">

            <summary>
                <para>Represents the collection of images used in the Ribbon UI.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonImageCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the RibbonImageCollection class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonImageCollection.GetImage(System.String)">
            <summary>
                <para>Gets the specified image from the collection.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the image name.

            </param>
            <returns>An <see cref="T:System.Drawing.Image"/> object.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonImageCollection.SetImage(System.String,System.Drawing.Image)">
            <summary>
                <para>Sets the specified image in the collection.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the image name.

            </param>
            <param name="value">
		An <see cref="T:System.Drawing.Image"/> object.

            </param>


        </member>
        <member name="T:DevExpress.XtraPrinting.PrintTool">

            <summary>
                <para>The base class for the <see cref="T:DevExpress.XtraReports.UI.ReportPrintTool"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.#ctor(DevExpress.XtraPrinting.PrintingSystemBase)">
            <summary>
                <para>Initializes a new instance of the PrintTool class with the specified Printing System.
</para>
            </summary>
            <param name="printingSystem">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> object that represents the Print Tool's Printing System. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.PrintTool.PrintingSystem"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ClosePreview">
            <summary>
                <para>Closes the Print Preview form.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.CloseRibbonPreview">
            <summary>
                <para>Closes the Ribbon Print Preview form.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.Dispose">
            <summary>
                <para>Disposes of the PrintTool object.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.LoadPrinterSettings(System.String)">
            <summary>
                <para>Applies the printer settings restored from the specified file to the system default printer.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> value, specifying the name of the file (with a full path to it) from where the printer settings should be loaded.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.LoadPrinterSettingsFromRegistry(System.String)">
            <summary>
                <para>Applies the printer settings restored from the specified registry to the system default printer.
</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/>, specifying the system registry path from where the printer settings should be loaded.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.LoadPrinterSettingsFromStream(System.IO.Stream)">
            <summary>
                <para>Applies the printer settings restored from the specified stream to the system default printer.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> from where the printer settings should be loaded.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.MakeCommandResponsive(DevExpress.XtraPrinting.PrintingSystemBase)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="printingSystem">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> descendant.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintTool.PreviewForm">
            <summary>
                <para>Provides access to a Print Preview form of the PrintTool.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintPreviewFormEx"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintTool.PreviewRibbonForm">
            <summary>
                <para>Provides access to a Ribbon Print Preview form of the PrintTool.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx"/> object.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.Print">
            <summary>
                <para>Prints the current document. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.Print(System.String)">
            <summary>
                <para>Prints the current document on the specified printer. 
</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> representing the name of the printer on which the current document should be printed. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.PrintDialog">
            <summary>
                <para>Runs a print dialog box used for selecting a printer, setting some print options and printing the document. 
</para>
            </summary>
            <returns><b>true</b> if the user clicks <b>OK</b> in the dialog box; <b>false</b> if the user clicks <b>Cancel</b>; otherwise <b>null</b> (<b>Nothing</b> in Visual Basic).
</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintTool.PrintingSystem">
            <summary>
                <para>Provides access to the Printing System of the PrintTool.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.SavePrinterSettings(System.String)">
            <summary>
                <para>Saves the settings of the system default printer to the specified file.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> value, specifying the name of the file (with a full path to it) to where the printer settings should be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.SavePrinterSettingsToRegistry(System.String)">
            <summary>
                <para>Saves the settings of the system default printer to the specified registry.
</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/>, specifying the system registry path to where the printer settings should be saved.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.SavePrinterSettingsToStream(System.IO.Stream)">
            <summary>
                <para>Saves the settings of the system default printer to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> to where the printer settings should be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPageSetup">
            <summary>
                <para>Displays a <b>Page Setup</b> dialog window. 
</para>
            </summary>
            <returns><b>true</b> if a user clicks <b>OK</b>; <b>false</b> if a user clicks <b>Cancel</b>, otherwise <b>null</b> (<b>Nothing</b> in Visual Basic).
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreview">
            <summary>
                <para>Invokes the standard Print Preview form for the document assigned to the PrintTool. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the standard Print Preview form for the document assigned to the PrintTool, using the specified look-and-feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreview(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview form for the document assigned to the PrintTool as a child of the specified parent window, using the specified look-and-feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Print Preview form for the document assigned to the PrintTool, using the specified look-and-feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreviewDialog">
            <summary>
                <para>Invokes the modal Print Preview form for the document assigned to the PrintTool. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Print Preview form for the document assigned to the PrintTool as a child of the specified parent window, using the specified look-and-feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreview">
            <summary>
                <para>Invokes the Ribbon Print Preview form for the document assigned to the PrintTool. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form for the document assigned to the PrintTool, using the specified look-and-feel settings.
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Ribbon Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreview(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form for the document assigned to the PrintTool as a child of the specified parent window, using the specified look-and-feel settings.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Ribbon Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreviewDialog">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form for the document assigned to the PrintTool. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form for the document assigned to the PrintTool,  using the specified look-and-feel settings.
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Ribbon Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintTool.ShowRibbonPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form for the document assigned to the PrintTool as a child of the specified parent window, using the specified look-and-feel settings.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Ribbon Print Preview form.

            </param>


        </member>
        <member name="T:DevExpress.XtraReports.UI.ReportPrintTool">

            <summary>
                <para>An instrument for report printing.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraReports.UI.ReportPrintTool.#ctor(DevExpress.XtraReports.IReport)">
            <summary>
                <para>Initializes a new instance of the ReportPrintTool class with the specified report.
</para>
            </summary>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface (usually, it is the <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class instance).

            </param>


        </member>
        <member name="P:DevExpress.XtraReports.UI.ReportPrintTool.AutoShowParametersPanel">
            <summary>
                <para>Specifies whether the <b>Parameters</b> panel is visible in the <b>Print Preview</b> window.


</para>
            </summary>
            <value><b>true</b> to always show the <b>Parameters</b> UI; otherwise <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraReports.UI.ReportPrintTool.Dispose">
            <summary>
                <para>Disposes of the ReportPrintTool object. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraReports.UI.ReportPrintTool.Report">
            <summary>
                <para>Provides access to the settings of the report assigned to the ReportPrintTool.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface (which is typically the <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class instance).
</value>


        </member>
        <member name="M:DevExpress.XtraReports.UI.ReportPrintTool.ShowPageSetup">
            <summary>
                <para>Displays the <b>Page Setup</b> dialog window. 
</para>
            </summary>
            <returns><b>true</b> if a user clicks <b>OK</b>; <b>false</b> if a user clicks <b>Cancel</b>, otherwise <b>null</b> (<b>Nothing</b> in Visual Basic).
</returns>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase">

            <summary>
                <para>Represents the base class for the form used to show the Print Preview of a document.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintPreviewFormExBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.PrintControl">
            <summary>
                <para>Gets the <b>Print Control</b> of the <b>Print Preview Form</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object which represents the <b>Print Control</b> of the form.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.SaveState">
            <summary>
                <para>Specifies whether the <b>Print Preview Form</b>'s size, position and zoom setting are saved to the Windows registry.


</para>
            </summary>
            <value><b>true</b> if <b>Print Preview Form</b> settings are saved in the Windows registry; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.SelectedPageIndex">
            <summary>
                <para>Gets or sets the index of the currently selected page in the form's <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.


</para>
            </summary>
            <value>An integer value which represents the index of the selected page.

</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.Show(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Displays the <b>Print Preview Form</b> using the specified Look-and-Feel settings.

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the Look-and-Feel settings to apply to the <b>Print Preview Form</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.Show">
            <summary>
                <para>Displays the <b>Print Preview Form</b>.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.Show(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the PrintPreviewFormExBase as a child of the specified parent window, using the specified look-and-feel settings.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object that is the parent window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object, specifying the look-and-feel settings applied to the Print Preview form.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.ShowDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> form which is shown modally as a <i>child</i> of the specified <i>parent</i> window using the specified look and feel settings.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the <b>Preview Form</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.ShowDialog">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> form which is shown modally.
</para>
            </summary>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.ShowDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> form which is shown modally as a <i>child</i> of the specified <i>parent</i> window.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormExBase.ShowDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> form which is shown modally using the specified look and feel settings.
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the <b>Print Preview Form</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.PrintBarManager">

            <summary>
                <para>A component that is used to create toolbar, status bar and menu elements in a Print Preview form.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintBarManager.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintBarManager class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintBarManager.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the PrintBarManager class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.ColorPopupControlContainer">
            <summary>
                <para>Gets or sets the popup control which is used to specify a color within the printing bar manager.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.ColorPopupControlContainer"/> object which represents the popup control used to select a color.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintBarManager.GetBarItemByCommand(DevExpress.XtraPrinting.PrintingSystemCommand)">
            <summary>
                <para>Gets a bar item within the bar manager by its command.

</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command to be executed by the bar item to be found.


            </param>
            <returns>A <see cref="T:DevExpress.XtraBars.BarItem"/> object that is the bar item which executes the specified command.

</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.Images">
            <summary>
                <para>Gets the source of images that can be displayed within items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Images"/> object which provides images for bar items.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.ImageStream">
            <summary>
                <para>Gets or sets a stream which contains the images used to display <b>Print Bar Manager</b> items.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object which represents the stream of images.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintBarManager.Initialize(DevExpress.XtraPrinting.Control.PrintControl)">
            <summary>
                <para>Performs basic initialization of the created PrintBarManager object, and assigns the specified <b>Print Control</b> to it.


</para>
            </summary>
            <param name="printControl">
		A <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object which specifies the print control to be assigned.


            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.LargeImages">
            <summary>
                <para>Overrides the <see cref="P:DevExpress.XtraBars.BarManager.LargeImages"/> property.

</para>
            </summary>
            <value>A <see cref="T:System.Object"/> which provides large images for bar button items.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.MultiplePagesControlContainer">
            <summary>
                <para>Gets or sets the popup control which is used to specify the number of pages to preview within the printing bar manager.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.MultiplePagesControlContainer"/> object which represents the popup control used to select the number of pages to preview.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.PreviewBar">
            <summary>
                <para>Gets or sets the print preview bar controlled by this preview bar manager.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Bar"/> object which represents the print preview bar of this bar manager.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.PrintControl">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance to which the <b>Print Bar Manager</b> belongs.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance to which the <b>Print Bar Manager</b> belongs.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.ScaleControlContainer">
            <summary>
                <para>Gets or sets the popup control which is used to specify the document scaling options.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.ScaleControlContainer"/> object which represents the popup control used to specify scaling options.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintBarManager.UpdateCommands">
            <summary>
                <para>Updates print bar manager items according to the current visibility state of the corresponding Printing System commands.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintBarManager.ZoomItem">
            <summary>
                <para>Gets or sets the editor which is used to specify the zoom level within the printing bar manager.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.ZoomBarEditItem"/> object which represents the zoom editor.
</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.PrintPreviewFormEx">

            <summary>
                <para>Represents a standard Print Preview form intended to show the print preview of a document.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.PrintPreviewFormEx.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintPreviewFormEx class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.PrintPreviewFormEx.PrintBarManager">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraPrinting.Preview.PrintBarManager"/> of the <b>Print Preview Form</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintBarManager"/> object which represents the <b>Print Bar Manager</b> of the <b>Print Preview Form</b>.

</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Control.PageBorderVisibility">

            <summary>
                <para>Lists the values used to specify the visibility of page borders.


</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPrinting.Control.PageBorderVisibility.All">
            <summary>
                <para>Both standard borders and selection borders are painted around pages.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPrinting.Control.PageBorderVisibility.AllWithoutSelection">
            <summary>
                <para>Only standard page borders are painted.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPrinting.Control.PageBorderVisibility.None">
            <summary>
                <para>Borders are not painted around pages at all.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPrinting.Control.BrickEventHandler">

            <summary>
                <para>Represents a method that will handle all brick events within the <b>XtraPrinting Library</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Control.BrickEventHandler.Invoke(System.Object,DevExpress.XtraPrinting.Control.BrickEventArgs)">
            <summary>
                <para>A method that will handle all brick events within the <b>XtraPrinting Library</b>.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPrinting.Control.BrickEventArgs"/> that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPrinting.Control.BrickEventArgs">

            <summary>
                <para>Provides data for all brick events within the <b>XtraPrinting Library</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Control.BrickEventArgs.#ctor(DevExpress.XtraPrinting.Brick)">
            <summary>
                <para>Initializes a new instance of the BrickEventArgs class with the specified brick.
</para>
            </summary>
            <param name="brick">
		A <see cref="T:DevExpress.XtraPrinting.Brick"/> value, which represents the event's brick. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.BrickEventArgsBase.Brick"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.BrickEventArgs.#ctor(DevExpress.XtraPrinting.Brick,DevExpress.XtraPrinting.Page,System.EventArgs,System.Single,System.Single)">
            <summary>
                <para>Initializes a new instance of the BrickEventArgs class with the specified brick, page, event args, x and y values.
</para>
            </summary>
            <param name="brick">
		A <see cref="T:DevExpress.XtraPrinting.Brick"/> value, which represents the event's brick. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.BrickEventArgsBase.Brick"/> property.

            </param>
            <param name="page">
		A <see cref="T:DevExpress.XtraPrinting.Page"/> object, which represents the event's page. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Page"/> property.

            </param>
            <param name="args">
		A <see cref="T:System.EventArgs"/> object, which represents the event's arguments. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Args"/> property.

            </param>
            <param name="x">
		A <see cref="T:System.Single"/> value, which represents the event's X coordinate. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Control.BrickEventArgs.X"/> property.

            </param>
            <param name="y">
		A <see cref="T:System.Single"/> value, which represents the event's Y coordinate. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Y"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Args">
            <summary>
                <para>Gets actual event arguments.
</para>
            </summary>
            <value>The actual event arguments.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Page">
            <summary>
                <para>Gets the page on which the brick associated with the event is located.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Page"/> object specifying the page on which the brick is located.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.BrickEventArgs.X">
            <summary>
                <para>Gets the X-coordinate offset of the brick associated with the event.

</para>
            </summary>
            <value>A float value specifying the brick's X-offset in three hundredths of an inch.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.BrickEventArgs.Y">
            <summary>
                <para>Gets the Y-coordinate offset of the brick associated with the event.

</para>
            </summary>
            <value>A float value specifying the brick's Y-offset in three hundredths of an inch.

</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.PrintableComponentLink">

            <summary>
                <para>A link to print components that implement the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the PrintableComponentLink class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface which specifies the owner container of a PrintableComponentLink class instance.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintableComponentLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the PrintableComponentLink class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified Printing System. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintableComponentLink.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintableComponentLink.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintableComponentLink.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintableComponentLink.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintableComponentLink.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowPreview">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which shows the print preview of the document for this link using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the Print Preview Form, which modally shows the print preview of the document for this link as a child of the specified parent window. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowPreviewDialog">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Print Preview Form which modally shows the print preview of the document for this link as a child of the specified parent window, using the specified look and feel settings. 
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the parent window for this dialog. 

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings.
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintableComponentLink.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPrinting.XtraPageSettings">

            <summary>
                <para>Represents the class containing properties responsible for report printing.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.#ctor(DevExpress.XtraPrinting.PrintingSystemBase)">
            <summary>
                <para>Initializes a new instance of the XtraPageSettings class with the specified printing system. 
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> object, which identifies the printing system to be used.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.Assign(System.Drawing.Printing.PageSettings)">
            <summary>
                <para>Assigns the specified page settings.
</para>
            </summary>
            <param name="pageSettings">
		A <see cref="T:System.Drawing.Printing.PageSettings"/> object providing the page settings.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.Assign(System.Drawing.Printing.Margins,System.Drawing.Printing.PaperKind,System.String,System.Boolean)">
            <summary>
                <para>Assigns the page margins, paper kind, paper name and page orientation of a document, simultaneously.
</para>
            </summary>
            <param name="margins">
		A <see cref="T:System.Drawing.Printing.Margins"/> object which specifies the margins of the document. 

            </param>
            <param name="paperKind">
		A <see cref="T:System.Drawing.Printing.PaperKind"/> value which specifies one of the standard paper sizes. 

            </param>
            <param name="paperName">
		A <see cref="T:System.String"/> value which specifies the name of the custom paper which is used in the printer that the document is going to be printed on.

            </param>
            <param name="landscape">
		<b>true</b> to print a page in landscape orientation; otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.AssignDefaultPrinterSettings">
            <summary>
                <para>Assigns the default printer's settings to the <see cref="T:DevExpress.XtraPrinting.XtraPageSettings"/> object.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.AssignDefaultPrinterSettings(DevExpress.XtraPrinting.PrinterSettingsUsing)">
            <summary>
                <para>Assigns the default printer settings to the current page settings according to the specified <see cref="T:DevExpress.XtraPrinting.PrinterSettingsUsing"/> value.

</para>
            </summary>
            <param name="settingsUsing">
		A <see cref="T:DevExpress.XtraPrinting.PrinterSettingsUsing"/> object specifying which of the printer settings should be assigned.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.XtraPageSettings.AssignPrinterSettings(System.String,System.String,DevExpress.XtraPrinting.PrinterSettingsUsing)">
            <summary>
                <para>Assigns the specified printer's settings to the XtraPageSettings object. 
</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value specifying the printer name. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.XtraPageSettingsBase.PrinterName"/> property.

            </param>
            <param name="paperName">
		A <see cref="T:System.String"/> value specifying the paper name. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.XtraPageSettingsBase.PaperName"/> property.

            </param>
            <param name="settingsUsing">
		A <see cref="T:DevExpress.XtraPrinting.PrinterSettingsUsing"/> object specifying which of the printer settings should be assigned. 

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.XtraPageSettings.PageSettings">
            <summary>
                <para>Provides access to the current page settings.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Printing.PageSettings"/> object representing the current page settings.
</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Preview.RibbonControllerBase">

            <summary>
                <para>Represents the base class for Ribbon Controller classes that embed their specific functionality into the existing <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonControllerBase.BeginInit">
            <summary>
                <para>Starts the RibbonControllerBase's initialization. Initialization occurs at runtime.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonControllerBase.EndInit">
            <summary>
                <para>Ends the RibbonControllerBase's initialization.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Preview.RibbonControllerBase.Initialize(DevExpress.XtraBars.Ribbon.RibbonControl,DevExpress.XtraBars.Ribbon.RibbonStatusBar)">
            <summary>
                <para>Performs basic initialization of the created <b>Ribbon Controller</b>, and assigns the specified <b>Ribbon Control</b> and <b>Ribbon Status Bar</b> to it.

</para>
            </summary>
            <param name="ribbonControl">
		A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> object, specifying the Ribbon Control to be assigned to the <see cref="P:DevExpress.XtraPrinting.Preview.RibbonControllerBase.RibbonControl"/> property.


            </param>
            <param name="ribbonStatusBar">
		A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> object, specifying the Ribbon status bar to be assigned to the <see cref="P:DevExpress.XtraPrinting.Preview.RibbonControllerBase.RibbonStatusBar"/> property.



            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.RibbonControllerBase.RibbonControl">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> class instance to which the <b>Ribbon Controller</b> belongs.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonControl"/> class instance to which the <b>Ribbon Controller</b> belongs. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Preview.RibbonControllerBase.RibbonStatusBar">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> class instance to which the <b>Ribbon Controller</b> belongs.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Ribbon.RibbonStatusBar"/> class instance to which the <b>Ribbon Controller</b> belongs. 
</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Control.PrintControl">

            <summary>
                <para>A container that renders and displays document pages.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.AutoZoom">
            <summary>
                <para>Gets or sets a value indicating whether the auto-zoom mode is enabled for the PrintControl.
</para>
            </summary>
            <value><b>true</b> if the auto-zoom mode is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.BackColor">
            <summary>
                <para>Gets or sets the background color of a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the background color of the print control.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.BackgroundImage">
            <summary>
                <para>This property is overridden and never used. To customize the PrintControl's background image, you need to create and use custom skins.


</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object.
</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BackgroundImageChanged">
            <summary>
                <para>This event is intended to hide the <see cref="E:System.Windows.Forms.Control.BackgroundImageChanged"/> event of the base class.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.BackgroundImageLayout">
            <summary>
                <para>This property is overridden and never used. To customize the PrintControl's background image and its layout, you need to create and use custom skins.


</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.ImageLayout"/> enumeration value.
</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BackgroundImageLayoutChanged">
            <summary>
                <para>This event is intended to hide the <see cref="E:System.Windows.Forms.Control.BackgroundImageLayoutChanged"/> event of the base class.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.BorderStyle">
            <summary>
                <para>Gets or sets the border style for the control.
</para>
            </summary>
            <value>One of the <see cref="T:System.Windows.Forms.BorderStyle"/> enumeration values representing the border style. 
</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickClick">
            <summary>
                <para>Occurs when the region of a brick within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> is clicked.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickDoubleClick">
            <summary>
                <para>Occurs when a brick within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> is double-clicked.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickDown">
            <summary>
                <para>Occurs when the mouse pointer is over the region specified by a brick within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> and the mouse button is pressed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickMouseDown">
            <summary>
                <para>Occurs when pressing the mouse button over a brick's region within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickMouseMove">
            <summary>
                <para>Occurs when moving the mouse over a brick's region within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickMouseUp">
            <summary>
                <para>Occurs when releasing the mouse button over a brick's region within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickMove">
            <summary>
                <para>Occurs when the mouse pointer moves over the region specified by a brick within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.BrickUp">
            <summary>
                <para>Occurs when the mouse pointer is over the region specified by a brick within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> and the mouse button is released.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.DockManager">
            <summary>
                <para>Provides access to the Print Control's dock manager. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraBars.Docking.DockManager"/> object, representing the dock manager.
</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.DocumentChanged">
            <summary>
                <para>Occurs when a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance recieves a document change notification.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ExecCommand(DevExpress.XtraPrinting.PrintingSystemCommand)">
            <summary>
                <para>Executes the printing system command without any parameters.
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value representing the command to be executed.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ExecCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[])">
            <summary>
                <para>Executes the specified printing system command and passes the specified parameters.

</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value representing the command to be executed.

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the executing command.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.FindBrick(System.Drawing.Point)">
            <summary>
                <para>Finds a brick based on specific screen coordinates.
</para>
            </summary>
            <param name="screenPoint">
		A screen coordinate point used to locate a brick.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPrinting.Brick"/> class instance to which a specific point belongs.
</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.ForeColor">
            <summary>
                <para>Gets or sets the foreground color of a PrintControl object.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the foreground color of the print control.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.GetBrickScreenBounds(DevExpress.XtraPrinting.Brick,DevExpress.XtraPrinting.Page)">
            <summary>
                <para>Obtains the location and size of the specified brick on the screen.
</para>
            </summary>
            <param name="brick">
		A <see cref="T:DevExpress.XtraPrinting.Brick"/> object representing the brick whose bounds should be determined.

            </param>
            <param name="page">
		A <see cref="T:DevExpress.XtraPrinting.Page"/> object representing the page containing the brick whose bounds should be determined.

            </param>
            <returns>A <see cref="T:System.Drawing.Rectangle"/> object representing brick bounds on the screen. 
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.InvalidateBrick(DevExpress.XtraPrinting.Brick)">
            <summary>
                <para>Redraws the region specified by a brick within a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>
            <param name="brick">
		A <see cref="T:DevExpress.XtraPrinting.Brick"/> class instance whose visual representation needs to be redrawn.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.IsMetric">
            <summary>
                <para>Gets or sets a value indicating whether to use the metric system for measurements in the document's preview.


</para>
            </summary>
            <value><b>true</b> to use the metric system for measurements made in the document's preview; otherwise, <b>false</b>. The default value is set to the <see cref="P:System.Globalization.RegionInfo.CurrentRegion.IsMetric"/> property value.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings that control the print control's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the print control's look and feel.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.MaxPageColumns">
            <summary>
                <para>Gets the maximum number of columns for multi-page reports displayed within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.

</para>
            </summary>
            <value>The maximum number of columns for a multi-page report.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.MaxPageRows">
            <summary>
                <para>Gets the maximum number of rows for a multi-page report displayed within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.

</para>
            </summary>
            <value>The maximum number of rows for a multi-page report.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.MaxZoom">
            <summary>
                <para>Gets the maximum zoom factor of a report page within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> control.
</para>
            </summary>
            <value>The maximum zoom factor of a report page within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> control.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.MinZoom">
            <summary>
                <para>Gets the minimum zoom factor of a report page within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>
            <value>The minimum zoom factor of a report page within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.PageBorderColor">
            <summary>
                <para>Gets or sets the color used to draw borders around all pages in a Print Control.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> value that represents the color of page borders.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.PageBorderVisibility">
            <summary>
                <para>Gets or sets a value indicating whether borders should be painted around all and selected document pages.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Control.PageBorderVisibility"/> enumeration value, that specifies the visibility of page borders.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.PageBorderWidth">
            <summary>
                <para>Gets or sets the width of the borders around all pages in the Print Control.

</para>
            </summary>
            <value>An integer value that represents the width of the page borders.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ResetBackColor">
            <summary>
                <para>Sets the <see cref="P:DevExpress.XtraPrinting.Control.PrintControl.BackColor"/> property to <see cref="F:System.Drawing.Color.Empty"/>.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ResetForeColor">
            <summary>
                <para>Sets the <see cref="P:DevExpress.XtraPrinting.Control.PrintControl.ForeColor"/> property to <see cref="F:System.Drawing.Color.Empty"/>.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ScrollPageDown">
            <summary>
                <para>Scrolls one page down.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ScrollPageUp">
            <summary>
                <para>Scrolls one page up.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.SelectedPage">
            <summary>
                <para>Obtains the currently selected report page.
</para>
            </summary>
            <value>The currently selected report page.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.SelectedPageBorderColor">
            <summary>
                <para>Gets or sets the color used to draw borders around the currently selected page in a Print Control.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> value that represents a color of the selected page's borders.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.SelectedPageBorderWidth">
            <summary>
                <para>Gets or sets the width of the borders around the currently selected page in the Print Control.

</para>
            </summary>
            <value>An integer value that represents the width of the selected page's borders.

</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.SelectedPageChanged">
            <summary>
                <para>Occurs when the selected page within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.SelectedPageIndex">
            <summary>
                <para>Gets or sets the selected page index.
</para>
            </summary>
            <value>The selected page index.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SelectFirstPage">
            <summary>
                <para>Moves selection to the first page within a report.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SelectLastPage">
            <summary>
                <para>Moves selection to the last page within report pages.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SelectNextPage">
            <summary>
                <para>Moves selection to the next page within a multipage report.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SelectPrevPage">
            <summary>
                <para>Moves selection to the previous page within a multipage report.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SetCursor(System.Windows.Forms.Cursor)">
            <summary>
                <para>Sets the cursor to be used within the PrintControl control.

</para>
            </summary>
            <param name="cursor">
		A <see cref="T:System.Windows.Forms.Cursor"/> object representing the desired cursor.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SetDocumentMapVisibility(System.Boolean)">
            <summary>
                <para>Sets the visibility of the <b>Document Map</b> which is available if the document, represented by the Print Control, contains at least one bookmark.


</para>
            </summary>
            <param name="value">
		<b>true</b> to show the <b>Document Map</b>; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SetFocus">
            <summary>
                <para>Sets input focus to the current <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> instance.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.SetPageView(System.Int32,System.Int32)">
            <summary>
                <para>Determines the number of columns and rows used to display pages of a multipage report within the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>
            <param name="columns">
		The number of columns. It will not exceed the <see cref="P:DevExpress.XtraPrinting.Control.PrintControl.MaxPageColumns"/> property value.

            </param>
            <param name="rows">
		The number of rows. It will not exceed the <see cref="P:DevExpress.XtraPrinting.Control.PrintControl.MaxPageRows"/> property value.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ShowBrick(DevExpress.XtraPrinting.Brick,DevExpress.XtraPrinting.Page)">
            <summary>
                <para>Moves the print control's focus to the specified brick which is printed on the specified page.
</para>
            </summary>
            <param name="brick">
		A <see cref="T:DevExpress.XtraPrinting.Brick"/> object representing the brick to which focus is moved.


            </param>
            <param name="page">
		A <see cref="T:DevExpress.XtraPrinting.Page"/> object representing the page where the brick is printed. Note that if a brick is split across two or more pages, then only the part of the brick which is printed on the specified page will be focused.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.ShowPageMargins">
            <summary>
                <para>Specifies whether the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> control shows page margin lines.
</para>
            </summary>
            <value><b>true</b> to show page margin lines; otherwise <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.ShowToolTips">
            <summary>
                <para>Gets or sets a value which specifies whether the Print Control can display hints.
</para>
            </summary>
            <value><b>true</b> if the Print Control can display hints; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.TooltipBackColor">
            <summary>
                <para>Gets or sets the background color of all the tooltips shown in a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the background color of the tooltips.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.TooltipFont">
            <summary>
                <para>Gets or sets the font of all the tooltips shown in a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object that defines the text's format including font face, size, and style attributes.

</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.TooltipForeColor">
            <summary>
                <para>Gets or sets the foreground color of all the tooltips shown in a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> object.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the foreground color of the tooltips.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.UpdatePageView">
            <summary>
                <para>Redraws the current page view in the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Control.PrintControl.Zoom">
            <summary>
                <para>Gets or sets the current zoom factor of a report page.
</para>
            </summary>
            <value>The current zoom factor of a report page.
</value>


        </member>
        <member name="E:DevExpress.XtraPrinting.Control.PrintControl.ZoomChanged">
            <summary>
                <para>Occurs after the current view zoom has changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ZoomIn">
            <summary>
                <para>Zooms in on the page currently displayed in the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Control.PrintControl.ZoomOut">
            <summary>
                <para>Zooms out the page currently displayed in the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/>.   
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPrinting.PrintingSystem">

            <summary>
                <para>Implements the basic functionality of the XtraPrinting Library.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance and adds it to the form's container.
</para>
            </summary>
            <param name="container">
		An <b>IContainer</b> that contains a <b>XtraPrintingSystem</b> component, if any.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.About">
            <summary>
                <para>Activates the <b>About</b> dialog. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintingSystem.Links">
            <summary>
                <para>Gets the collection of links, defined for the current <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</para>
            </summary>
            <value>The link collection of the current <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintingSystem.PageSettings">
            <summary>
                <para>Gets the current page settings. 
</para>
            </summary>
            <value>Current page settings.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.PageSetup">
            <summary>
                <para>Displays the <b>PageSetup</b> dialog.
</para>
            </summary>
            <returns><b>true</b>, if the "OK" button is clicked in the <b>PageSetup</b> dialog, <b>false</b>, if the "Cancel" button is clicked in the <b>PageSetup</b> dialog.
</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintingSystem.PreviewFormEx">
            <summary>
                <para>Gets the form used to display the document preview.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintPreviewFormEx"/> object which represents the current preview form.
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintingSystem.PreviewRibbonFormEx">
            <summary>
                <para>Gets the Ribbon form used to display the document preview.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Preview.PrintPreviewRibbonFormEx"/> object which represents the current preview form.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.Print">
            <summary>
                <para>Prints the current document. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> representing the name of the printer on which to print the document. 


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.PrintingSystem.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 

</para>
            </summary>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value containing the return value of the print dialog box.

</returns>


        </member>
        <member name="P:DevExpress.XtraPrinting.PrintingSystem.Watermark">
            <summary>
                <para>Provides access to watermark settings of the printing system's document.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.Drawing.Watermark"/> object containing watermark settings.
</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.Link">

            <summary>
                <para>A printing link.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrinting.Link.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the Link class with the specified container.
</para>
            </summary>
            <param name="container">
		An object implementing the <see cref="T:System.ComponentModel.IContainer"/> interface which specifies the owner container of a Link class instance.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.#ctor">
            <summary>
                <para>Initializes a new instance of the Link class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.#ctor(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Initializes a new instance of the Link class with the specified printing system.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrinting.Link.PrintingSystem"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.CanHandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,DevExpress.XtraPrinting.IPrintControl)">
            <summary>
                <para>Indicates whether or not the specified Printing System command can be handled. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value that specifies the command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface that specifies the print control (most typically, it is a <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance).

            </param>
            <returns><b>true</b> if the command can be handled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.CreateDocument(DevExpress.XtraPrinting.PrintingSystem)">
            <summary>
                <para>Generates a report using the specified <b>PrintingSystem</b>.
</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> class instance, specifying the printing system of the link.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.HandleCommand(DevExpress.XtraPrinting.PrintingSystemCommand,System.Object[],DevExpress.XtraPrinting.IPrintControl,System.Boolean@)">
            <summary>
                <para>Handles the specified Printing System command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> enumeration value which specifies the command to be handled. 

            </param>
            <param name="args">
		A collection of <see cref="T:System.Object"/> objects representing the parameters to be passed to the handled command. 

            </param>
            <param name="printControl">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintControl"/> interface (most typically, it is the <see cref="T:DevExpress.XtraPrinting.Control.PrintControl"/> class instance). 

            </param>
            <param name="handled">
		<b>true</b> if the command has been handled by a link; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraPrinting.Link.ImageCollection">
            <summary>
                <para>Provides access to the link's collection of images. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Link.Images">
            <summary>
                <para>A collection of images which can be added to the Page Headers and Footers.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.Images"/> object which represents a collection of images that can be used in the report. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Link.ImageStream">
            <summary>
                <para>For internal use. Specifies a stream which contains images to display in the link's Page Header and Footer. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollectionStreamer"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.Print(System.String)">
            <summary>
                <para>Prints the current document to the specified printer.

</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> representing the name of the printer on which to print the document. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.PrintDlg">
            <summary>
                <para>Displays the standard <b>Print</b> dialog and prints the current document. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPrinting.Link.PrintingSystem">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/> object. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrinting.Link.PrintingSystemBase">
            <summary>
                <para>Gets or sets the Printing System used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> class descendant.
</value>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> which shows the print preview of the document for this link using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Print Preview Form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowPreview">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> which shows the print preview of the document for this link. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowPreviewDialog(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b>, which modally shows the print preview of the document for this link as a <i>child</i> of the specified <i>parent</i> window.

</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowPreviewDialog">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> which modally shows the print preview of the document for this link.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowPreviewDialog(System.Windows.Forms.IWin32Window,DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the <b>Print Preview Form</b> which modally shows the print preview of the document for this link as a <i>child</i> of the specified <i>parent</i> window, using the specified look and feel settings.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the <b>Preview Form</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowRibbonPreview(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 

</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrinting.Link.ShowRibbonPreviewDialog(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Invokes the modal Ribbon Print Preview form with the document created from this link, using the specified look and feel settings. 
</para>
            </summary>
            <param name="lookAndFeel">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which specifies the look and feel settings applied to the Ribbon Print Preview form. 

            </param>


        </member>
    </members>
</doc>
