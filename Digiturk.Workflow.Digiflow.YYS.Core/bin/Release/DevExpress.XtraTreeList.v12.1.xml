<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.XtraTreeList.v12.1</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraTreeList.PopupMenuShowingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.PopupMenuShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.PopupMenuShowingEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.PopupMenuShowingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.PopupMenuShowing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraTreeList.TreeList"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.PopupMenuShowingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.PopupMenuShowingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.PopupMenuShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraTreeList.Menu.TreeListMenu,System.Drawing.Point,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the PopupMenuShowingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="menu">
		A TreeListMenu object that specifies the menu to be displayed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Menu"/> property.

            </param>
            <param name="point">
		A Point value that specifies the point at which the menu is to be displayed. <see cref="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Point"/>

            </param>
            <param name="allow">
		A Boolean value that specifies if display of the menu is allowed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Allow"/> property.


            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Allow">
            <summary>
                <para>Gets or sets if display of the menu is allowed.
</para>
            </summary>
            <value><b>true</b> if display of the menu is allowed; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Menu">
            <summary>
                <para>Gets or sets the control's popup menu that will be shown.
</para>
            </summary>
            <value>A TreeListMenu object that is the menu that will be shown.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.PopupMenuShowingEventArgs.Point">
            <summary>
                <para>Gets the position where the menu is to be invoked.
</para>
            </summary>
            <value>A Point value that specifies the position where the menu is to be invoked.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Data.UnboundColumnType">

            <summary>
                <para>Enumerates data types that columns can have in unbound mode.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.Boolean">
            <summary>
                <para>Indicates that the column contains Boolean values (the <see cref="T:System.Boolean"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.DateTime">
            <summary>
                <para>Indicates that the column contains date-time values (the <see cref="T:System.DateTime"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.Decimal">
            <summary>
                <para>Indicates that the column contains decimal values (the <see cref="T:System.Decimal"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.Integer">
            <summary>
                <para>Indicates that the column contains integer values (the <see cref="T:System.Int32"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.Object">
            <summary>
                <para>Indicates that the column contains values of any type.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Data.UnboundColumnType.String">
            <summary>
                <para>Indicates that the column contains string values (the <see cref="T:System.String"/> type).
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeSetCellValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.#ctor(System.Object,System.Object,System.Object,DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Initializes a new instance of the VirtualTreeSetCellValueInfo class.
</para>
            </summary>
            <param name="oldCellData">
		The current cell's old value. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.OldCellData"/> property.

            </param>
            <param name="newCellData">
		The new value to be assigned to the current cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.NewCellData"/> property.

            </param>
            <param name="node">
		A business object to be processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.Node"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the currently processed column. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.Column"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.Cancel">
            <summary>
                <para>Gets or sets whether the new value is discarded.
</para>
            </summary>
            <value><b>true</b> if the new value is discarded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.Column">
            <summary>
                <para>Gets the column that contains the cell being currently processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column being currently processed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.NewCellData">
            <summary>
                <para>Gets the new data for the current cell.
</para>
            </summary>
            <value>An object that represents the new data for the cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.Node">
            <summary>
                <para>Gets an instance of the business object being currently processed.
</para>
            </summary>
            <value>An object being currently processed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo.OldCellData">
            <summary>
                <para>Gets the current cell's old data.
</para>
            </summary>
            <value>An object that specifies the current cell's old data.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetChildNodes"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo.#ctor(System.Object)">
            <summary>
                <para>Initializes a new instance of the VirtualTreeGetChildNodesInfo class.
</para>
            </summary>
            <param name="node">
		A business object to be processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo.Children">
            <summary>
                <para>Gets or sets the collection of children for the currently processed business object.
</para>
            </summary>
            <value>Initially this property is set to a null reference. Use this property to assign a list of children for the currently processed object.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo.Node">
            <summary>
                <para>Gets an instance of the business object being currently processed.
</para>
            </summary>
            <value>An object being currently processed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetCellValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.#ctor(System.Object,DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Initializes a new instance of the VirtualTreeGetCellValueInfo class.
</para>
            </summary>
            <param name="node">
		A business object to be processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.Node"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the currently processed column. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.Column"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.CellData">
            <summary>
                <para>Gets or sets the current cell's data.
</para>
            </summary>
            <value>Initially this property is set to a null reference. Use this property to supply data for the current cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.Column">
            <summary>
                <para>Gets the column that contains the cell currently being processed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column currently being processed.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo.Node">
            <summary>
                <para>Gets an instance of the business object being currently processed.
</para>
            </summary>
            <value>An object being currently processed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeSetCellValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeSetCellValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeSetCellValueEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo)">
            <summary>
                <para>Represents the method for handling events which take a <see cref="T:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo"/> object as a parameter.
</para>
            </summary>
            <param name="sender">
		The event source. This identifies the TreeList control which fires the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.VirtualTreeSetCellValueInfo"/> object that contains data for the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeGetChildNodesEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetChildNodes"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeGetChildNodesEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo)">
            <summary>
                <para>Represents the method for handling events which take a <see cref="T:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo"/> object as a parameter.
</para>
            </summary>
            <param name="sender">
		The event source. This identifies the TreeList control which fires the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.VirtualTreeGetChildNodesInfo"/> object that contains data for the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.VirtualTreeGetCellValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetCellValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.VirtualTreeGetCellValueEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo)">
            <summary>
                <para>Represents the method for handling events which take a <see cref="T:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo"/> object as a parameter.
</para>
            </summary>
            <param name="sender">
		The event source. This identifies the TreeList control which fires the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.VirtualTreeGetCellValueInfo"/> object that contains data for the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection">

            <summary>
                <para>Represents a style condition collection for a TreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.#ctor(DevExpress.XtraTreeList.TreeList)">
            <summary>
                <para>Initializes a new instance of the StyleFormatConditionCollection class.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> control that will own the new collection. The value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.TreeListControl"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.Add(DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition)">
            <summary>
                <para>Adds the specified style condition object to the end of the collection. 
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.StyleFormatCondition"/> object to be added to the collection. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.AddRange(DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition[])">
            <summary>
                <para>Adds an array of style condition objects. 
</para>
            </summary>
            <param name="conditions">
		An array of <see cref="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition"/> objects to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.CompareValues(System.Object,System.Object)">
            <summary>
                <para>Compares the two specified values.
</para>
            </summary>
            <param name="val1">
		The first value to compare.

            </param>
            <param name="val2">
		The second value to compare.

            </param>
            <returns><b>0</b> if the values are equal; <b>-1</b> if the first value is less than the second value; <b>1</b> if the first value is greater than the second value.

</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.IsLoading">
            <summary>
                <para>Gets whether the control that owns the current collection is being initialized.

</para>
            </summary>
            <value><b>true</b> if the control that owns the current collection is being initialized; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.Item(System.Object)">
            <summary>
                <para>Provides access to style conditions, using the data that is stored in the <b>Tag</b> property as an indexer.
</para>
            </summary>
            <param name="tag">
		An object to search against the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> properties of style conditions stored in this collection.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition"/> object which contains the specified <i>tag</i> object in the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to style conditions in the current collection.
</para>
            </summary>
            <param name="index">
		An integer that represents the zero-based index of the <see cref="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition"/> object to be returned.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition"/> object with the specified index.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection.TreeListControl">
            <summary>
                <para>Gets a <see cref="T:DevExpress.XtraTreeList.TreeList"/> control that owns the current collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object which is the owner of the current collection.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition">

            <summary>
                <para>Represents a style condition object that can be applied to a TreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object,System.Object,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class with the specified settings.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.FormatConditionEnum"/> enumeration value that specifies the type of the comparison operation. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Condition"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column whose values are involved in conditional formatting. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Column"/> property.

            </param>
            <param name="tag">
		Any object to be associated with the created style condition. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property.

            </param>
            <param name="val1">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value1"/> property.

            </param>
            <param name="val2">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value2"/> property.

            </param>
            <param name="applyToRow">
		<b>true</b> to apply the specified style to entire nodes that contain cells matching the defined criteria; <b>false</b> to apply the style to individual cells. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.ApplyToRow"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,System.Object,DevExpress.Utils.AppearanceObject,System.Object,System.Object,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class with the specified settings.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.FormatConditionEnum"/> enumeration value that specifies the type of the comparison operation. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Condition"/> property.

            </param>
            <param name="tag">
		Any object to be associated with the created style condition. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property.

            </param>
            <param name="appearance">
		A <see cref="T:DevExpress.Utils.AppearanceObject"/> descendant which provides the appearance settings used to paint the cells that meet the specified condition. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Appearance"/> property.

            </param>
            <param name="val1">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value1"/> property.

            </param>
            <param name="val2">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value2"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column whose values are involved in conditional formatting. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Column"/> property.

            </param>
            <param name="applyToRow">
		<b>true</b> to apply the specified style to entire nodes that contain cells matching the defined criteria; <b>false</b> to apply the style to individual cells. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.ApplyToRow"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class with the specified settings.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.FormatConditionEnum"/> enumeration value that specifies the type of the comparison operation. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Condition"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column whose values are involved in conditional formatting. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Column"/> property.

            </param>
            <param name="tag">
		Any object to be associated with the created style condition. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property.

            </param>
            <param name="val1">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value1"/> property.

            </param>
            <param name="val2">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value2"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum)">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class with the specified comparison operation type.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.FormatConditionEnum"/> enumeration value that specifies the type of the comparison operation. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Condition"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the StyleFormatCondition class with the specified settings.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraGrid.FormatConditionEnum"/> enumeration value that specifies the type of the comparison operation. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Condition"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that represents the column whose values are involved in conditional formatting. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Column"/> property.

            </param>
            <param name="tag">
		Any object to be associated with the created style condition. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property.

            </param>
            <param name="val1">
		An object that is compared to cell values as specified by the <i>condition</i> parameter. This value is assigned to the <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Value1"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.ApplyToRow">
            <summary>
                <para>Gets or sets whether the appearance settings are to be applied to entire nodes or individual cells.
</para>
            </summary>
            <value><b>true</b> to apply the appearance settings to entire nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Assign(DevExpress.XtraEditors.StyleFormatConditionBase)">
            <summary>
                <para>Copies the properties of the specified object to the current object.
</para>
            </summary>
            <param name="source">
		The <see cref="T:DevExpress.XtraEditors.StyleFormatConditionBase"/> object whose properties should be copied.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.Column">
            <summary>
                <para>Gets or sets the column whose values take part in conditional formatting.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column whose values are used in conditional formatting. <b>null</b> (<b>Nothing</b> in Visual Basic) if all columns' values are involved.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatCondition.ColumnName">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>The name of the column to which the current condition is applied.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn">

            <summary>
                <para>Provides column options.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowEdit">
            <summary>
                <para>Gets or sets whether end-users can invoke editors for the column's cells. 
</para>
            </summary>
            <value><b>true</b> if end-users are allowed to invoke cell editors; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowFocus">
            <summary>
                <para>Gets or sets whether end-users can move focus to the column using either the mouse or keyboard.
</para>
            </summary>
            <value><b>true</b> if end-users can move focus to the column using either the mouse or keyboard; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowMove">
            <summary>
                <para>Gets or sets whether end-users can drag the column's header. 
</para>
            </summary>
            <value><b>true</b> if end-users can drag the column's header; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowMoveToCustomizationForm">
            <summary>
                <para>Gets or sets whether end-users can drag the column's header to the customization form.
</para>
            </summary>
            <value><b>true</b> to allow end-users to drag the column's header to the customization form; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowSize">
            <summary>
                <para>Gets or sets whether end-users can drag the column header's right edge to change the column's width.
</para>
            </summary>
            <value><b>true</b> if end-users can change the column's width; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowSort">
            <summary>
                <para>Gets or sets whether end-users can sort data by the column's values. 
</para>
            </summary>
            <value><b>true</b> if end-users can sort data by the column's values; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter to the current object. 
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.FixedWidth">
            <summary>
                <para>Gets or sets whether the column's width remains the same when the column's auto width feature is enabled.
</para>
            </summary>
            <value><b>true</b> if the column's width remains the same when the column's auto width feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.ReadOnly">
            <summary>
                <para>Gets or sets whether end-users are prevented from editing the column's cell values.
</para>
            </summary>
            <value><b>true</b> if end-users are prevented from modifying the column's cell values; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.ShowInCustomizationForm">
            <summary>
                <para>Gets or sets whether the column's header is displayed within the customization form when the column is hidden. 
</para>
            </summary>
            <value><b>true</b> if the column's header is displayed within the customization form when the column is hidden; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection">

            <summary>
                <para>Provides the appearance settings used to paint the elements in a tree list when it's printed and exported.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.#ctor(DevExpress.Utils.IAppearanceOwner)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection"/> class.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object or its descendant which represents the tree list that will own the created collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.EvenRow">
            <summary>
                <para>Gets the appearance settings used to paint the even nodes when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the even nodes when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.FooterPanel">
            <summary>
                <para>Gets the appearance settings used to paint the summary footer when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the summary footer when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.GroupFooter">
            <summary>
                <para>Gets the appearance settings used to paint the group footer when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the group footer when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.HeaderPanel">
            <summary>
                <para>Gets the appearance settings used to paint the column headers when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the column headers when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.Lines">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal and vertical lines when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the horizontal and vertical lines when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.OddRow">
            <summary>
                <para>Gets the appearance settings used to paint the odd nodes when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint odd nodes when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.Preview">
            <summary>
                <para>Gets the appearance settings used to paint the preview sections when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the preview sections when the tree list is printed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection.Row">
            <summary>
                <para>Gets the appearance settings used to paint the data cells when the tree list is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the data cells when the tree list is printed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsView">

            <summary>
                <para>Provides view options for the XtraTreeList controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsView.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListOptionsView"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.AllowHtmlDrawHeaders">
            <summary>
                <para>Gets or sets whether HTML formatting can be applied to column captions. 
</para>
            </summary>
            <value><b>true</b> if HTML formatting can be applied to column captions; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsView.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.AutoCalcPreviewLineCount">
            <summary>
                <para>Gets or sets whether the number of text lines within preview sections are calculated automatically depending upon their contents.
</para>
            </summary>
            <value><b>true</b> to enable automatic calculation of the height of preview sections; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.AutoWidth">
            <summary>
                <para>Gets or sets whether the widths of column is automatically adjusted so that the total width of all the columns matches the tree list's width.
</para>
            </summary>
            <value><b>true</b> to enable the column auto width feature; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.EnableAppearanceEvenRow">
            <summary>
                <para>Gets or sets whether even nodes are painted using the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.EvenRow"/> property.
</para>
            </summary>
            <value><b>true</b> to paint even nodes using the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.EvenRow"/> property; <b>false</b> to use the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.Row"/> property.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.EnableAppearanceOddRow">
            <summary>
                <para>Gets or sets whether odd nodes are painted using the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.OddRow"/> property.
</para>
            </summary>
            <value><b>true</b> to paint odd nodes using the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.OddRow"/> property; <b>false</b> to use the appearance settings provided by the <see cref="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.Row"/> property.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ExpandButtonCentered">
            <summary>
                <para>Gets or sets whether the expand button is vertically centered within the node.
</para>
            </summary>
            <value><b>true</b> if the expand button is vertically centered within the node; <b>false</b> if the button is top-aligned.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowAutoFilterRow">
            <summary>
                <para>Gets or sets whether the Auto Filter Row is displayed.
</para>
            </summary>
            <value><b>true</b> to display the Auto Filter Row; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowButtons">
            <summary>
                <para>Gets or sets whether the expand buttons are displayed.
</para>
            </summary>
            <value><b>true</b> to display the expand buttons; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowCheckBoxes">
            <summary>
                <para>Gets or sets whether nodes display check boxes.
</para>
            </summary>
            <value><b>true</b> to show check boxes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowColumns">
            <summary>
                <para>Gets or sets whether column headers are displayed.
</para>
            </summary>
            <value><b>true</b> to display column headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowFilterPanelMode">
            <summary>
                <para>Gets or sets a value that specifies when the Filter Panel is shown.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.ShowFilterPanelMode"/> enumeration value which specifies when the filter panel is shown.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowFocusedFrame">
            <summary>
                <para>Gets or sets whether a focus frame is displayed around the focused cell.
</para>
            </summary>
            <value><b>true</b> to display a focus frame around the focused cell; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowHorzLines">
            <summary>
                <para>Gets or sets whether horizontal lines are displayed.
</para>
            </summary>
            <value><b>true</b> to display horizontal lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowIndentAsRowStyle">
            <summary>
                <para>Gets or sets whether tree indents are painted using the appearance settings of their corresponding nodes.
</para>
            </summary>
            <value><b>true</b> to paint tree indents using the appearance settings of their corresponding nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowIndicator">
            <summary>
                <para>Gets or sets whether the node indicator panel is displayed.
</para>
            </summary>
            <value><b>true</b> to display the node indicator panel; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowPreview">
            <summary>
                <para>Gets or sets whether preview sections are displayed.
</para>
            </summary>
            <value><b>true</b> to display preview sections; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowRoot">
            <summary>
                <para>Gets or sets whether tree lines between root nodes are displayed.
</para>
            </summary>
            <value><b>true</b> to display tree lines between root nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowRowFooterSummary">
            <summary>
                <para>Gets or sets whether group footers are displayed.
</para>
            </summary>
            <value><b>true</b> to display group footers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowSummaryFooter">
            <summary>
                <para>Gets or sets whether the summary footer is displayed.
</para>
            </summary>
            <value><b>true</b> to display the summary footer; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowVertLines">
            <summary>
                <para>Gets or sets whether vertical lines are displayed.
</para>
            </summary>
            <value><b>true</b> to display vertical lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsSelection">

            <summary>
                <para>Provides selection options for XtraTreeList controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsSelection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListOptionsSelection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsSelection.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsSelection.EnableAppearanceFocusedCell">
            <summary>
                <para>Gets or sets whether the appearance settings for the focused cell are enabled.
</para>
            </summary>
            <value><b>true</b> if the appearance settings for the focused cell are enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsSelection.EnableAppearanceFocusedRow">
            <summary>
                <para>Gets or sets whether the appearance settings for the focused node are enabled.
</para>
            </summary>
            <value><b>true</b> if the appearance settings for the focused node are enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsSelection.InvertSelection">
            <summary>
                <para>Gets or sets whether the focused style is applied to the focused cell only or to all the cells except for the focused one.
</para>
            </summary>
            <value><b>true</b> to apply the focused style to the focused cell only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsSelection.MultiSelect">
            <summary>
                <para>Gets or sets whether multiple nodes can be selected.
</para>
            </summary>
            <value><b>true</b> to allow multiple nodes to be selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsSelection.UseIndicatorForSelection">
            <summary>
                <para>Gets or sets whether nodes can be selected via the node indicator when the tree list is in multiple selection mode.
</para>
            </summary>
            <value><b>true</b> if nodes can be selected via the node indicator; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsPrint">

            <summary>
                <para>Provides options that control how the TreeList control is printed and exported.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsPrint.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListOptionsPrint"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsPrint.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.AutoRowHeight">
            <summary>
                <para>Gets or sets whether the height of nodes is calculated automatically when the tree list is printed.
</para>
            </summary>
            <value><b>true</b> if the height of nodes is calculated automatically; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.AutoWidth">
            <summary>
                <para>Gets or sets whether columns are automatically stretched when the tree list is printed so that it fits the page's width (except margins).

</para>
            </summary>
            <value><b>true</b> to force the tree list to fit the printed page horizontally; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintAllNodes">
            <summary>
                <para>Gets or sets whether the tree list is printed with all its nodes expanded.
</para>
            </summary>
            <value><b>true</b> to expand all the collapsed nodes when the tree list is printed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintFilledTreeIndent">
            <summary>
                <para>Gets or sets whether the tree indents are painted using the appearance settings of their corresponding nodes when the tree list is printed.
</para>
            </summary>
            <value><b>true</b> to paint the tree indents using the appearance settings of their corresponding nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintHorzLines">
            <summary>
                <para>Gets or sets whether horizontal lines are printed.
</para>
            </summary>
            <value><b>true</b> to print horizontal lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintImages">
            <summary>
                <para>Gets or sets whether the state and select images are printed.
</para>
            </summary>
            <value><b>true</b> to print node images; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintPageHeader">
            <summary>
                <para>Gets or sets whether column headers are printed.
</para>
            </summary>
            <value><b>true</b> to print column headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintPreview">
            <summary>
                <para>Gets or sets whether preview sections are printed.
</para>
            </summary>
            <value><b>true</b> to print preview sections; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintReportFooter">
            <summary>
                <para>Gets or sets whether the summary footer is printed.
</para>
            </summary>
            <value><b>true</b> to print the summary footer; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintRowFooterSummary">
            <summary>
                <para>Gets or sets whether group footers are printed.
</para>
            </summary>
            <value><b>true</b> to print group footers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintTree">
            <summary>
                <para>Gets or sets whether the tree lines are printed.
</para>
            </summary>
            <value><b>true</b> to print tree lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintTreeButtons">
            <summary>
                <para>Gets or sets whether the node expand buttons are printed.
</para>
            </summary>
            <value><b>true</b> to print node expand buttons; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.PrintVertLines">
            <summary>
                <para>Gets or sets whether vertical lines are printed.
</para>
            </summary>
            <value><b>true</b> to print vertical lines; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsPrint.UsePrintStyles">
            <summary>
                <para>Gets or sets whether the print appearances are used when the tree list is printed and exported.
</para>
            </summary>
            <value><b>true</b> to use the print appearances; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsMenu">

            <summary>
                <para>Provides menu options for XtraTreeList controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsMenu.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListOptionsMenu"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsMenu.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all settings from the options object passed as the parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsMenu.EnableColumnMenu">
            <summary>
                <para>Gets or sets whether end-users can invoke the column header context menu.

</para>
            </summary>
            <value><b>true</b> if end-users can right-click column headers to invoke their context menus; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsMenu.EnableFooterMenu">
            <summary>
                <para>Gets or sets whether end-users can invoke footer context menus.
</para>
            </summary>
            <value><b>true</b> if end-users can click the summary footer and group footers to invoke their context menus; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsMenu.ShowAutoFilterRowItem">
            <summary>
                <para>Gets whether the 'Show Auto Filter Row' check item is displayed within the Column Header Context Menu.
</para>
            </summary>
            <value><b>true</b> if the 'Show Auto Filter Row' check item is available within a column header's menu; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsBehavior">

            <summary>
                <para>Provides behavior options for XtraTreeList controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsBehavior.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListOptionsBehavior"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AllowCopyToClipboard">
            <summary>
                <para>Gets or sets whether an end-user can copy selected nodes to the clipboard via the CTRL+C shortcut.
</para>
            </summary>
            <value><b>true</b> if an end-user can copy selected nodes to the clipboard via a keyboard shortcut; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AllowExpandOnDblClick">
            <summary>
                <para>Gets or sets whether a node is expanded/collapsed after it has been double clicked.
</para>
            </summary>
            <value><b>true</b> if the node is expanded/collapsed after it has been double clicked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AllowIncrementalSearch">
            <summary>
                <para>Gets or sets whether end-users can locate nodes by typing the desired column value.
</para>
            </summary>
            <value><b>true</b> if incremental searching is allowed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AllowIndeterminateCheckState">
            <summary>
                <para>Gets or sets whether a node can be set to the indeterminate check state.
</para>
            </summary>
            <value><b>true</b> if a node's check state can be set to the indeterminate state; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AllowRecursiveNodeChecking">
            <summary>
                <para>Gets or sets whether child nodes are automatically checked/unchecked when a parent node is checked/unchecked and vice versa.
</para>
            </summary>
            <value><b>true</b> if child nodes are automatically checked/unchecked when a parent node is checked/unchecked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsBehavior.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter to the current object.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoChangeParent">
            <summary>
                <para>Gets or sets whether the node's parent field value is automatically set to the key field value of its parent node.
</para>
            </summary>
            <value><b>true</b> if the node's parent field value is automatically set to the key field value of its parent node; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoFocusNewNode">
            <summary>
                <para>Gets or sets whether a newly added node is automatically focused.
</para>
            </summary>
            <value><b>true</b> if a newly added node is automatically focused; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoMoveRowFocus">
            <summary>
                <para>Gets or sets a value specifying whether horizontal navigation keys move focus to the next/previous node when the current node's last/first cell is focused.
</para>
            </summary>
            <value><b>true</b> if horizontal navigation keys can move focus between nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoNodeHeight">
            <summary>
                <para>Gets or sets whether the height of each node is automatically adjusted to completely display the contents of its cells.
</para>
            </summary>
            <value><b>true</b> to enable automatic calculation of the heights of nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoPopulateColumns">
            <summary>
                <para>Gets or sets whether columns should be automatically created for all the fields in the underlying data source when the TreeList doesn't contain any columns.

</para>
            </summary>
            <value><b>true</b> if the columns are created automatically when the TreeList doesn't contain any columns; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoSelectAllInEditor">
            <summary>
                <para>Gets or sets whether activating a cell editor using the mouse or ENTER or F2 keys selects the entire editor's content.
</para>
            </summary>
            <value><b>true</b> to select the entire cell's content when editing starts; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.CanCloneNodesOnDrop">
            <summary>
                <para>Gets or sets whether end-users are allowed to clone nodes using drag-and-drop.
</para>
            </summary>
            <value><b>true</b> to allow end-users to clone nodes using drag-and-drop; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.CloseEditorOnLostFocus">
            <summary>
                <para>Gets or sets whether the active editor is automatically closed when the tree list loses focus.
</para>
            </summary>
            <value><b>true</b> if the active editor is automatically closed when the tree list loses focus and any changes made to the edited cell's content are saved; otherwise, the cell's editor isn't closed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.CopyToClipboardWithColumnHeaders">
            <summary>
                <para>Gets or sets whether column captions are copied to the clipboard.
</para>
            </summary>
            <value><b>true</b> if column captions are copied to the clipboard along with selected nodes; <b>false</b> if only selected nodes are copied to the clipboard.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.CopyToClipboardWithNodeHierarchy">
            <summary>
                <para>Gets or sets whether data is copied to the clipboard with the hierarchy information included or in a flat form.
</para>
            </summary>
            <value><b>true</b> if data is copied to the clipboard with the hierarchy information included; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.DragNodes">
            <summary>
                <para>Gets or sets whether end-users are allowed to move or copy nodes using drag-and-drop.
</para>
            </summary>
            <value><b>true</b> to allow end-users to move or copy nodes using drag-and-drop; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.Editable">
            <summary>
                <para>Gets or sets whether end-users are allowed to invoke cell editors.
</para>
            </summary>
            <value><b>true</b> if end-users are allowed to invoke cell editors; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.EnableFiltering">
            <summary>
                <para>Gets or sets a value which specifies whether nodes can be filtered.
</para>
            </summary>
            <value><b>true</b> to allow nodes to be filtered; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.EnterMovesNextColumn">
            <summary>
                <para>Gets or sets whether the ENTER key can be used to move focus between cells.
</para>
            </summary>
            <value><b>true</b> to enable focus moving using the ENTER key; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ExpandNodeOnDrag">
            <summary>
                <para>Gets or sets whether collapsed nodes are automatically expanded when the node currently being dragged is hovered over them.
</para>
            </summary>
            <value><b>true</b> if collapsed nodes are automatically expanded when a dragged node is hovered over them; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ExpandNodesOnIncrementalSearch">
            <summary>
                <para>Gets or sets a value that specifies whether collapsed nodes are expanded to display the node if it matches the search criteria.
</para>
            </summary>
            <value><b>true</b> to expand the collapsed node if it matches the search criteria; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ImmediateEditor">
            <summary>
                <para>Gets or sets whether an in-place editor is automatically activated when a cell is clicked.
</para>
            </summary>
            <value><b>true</b> if an in-place editor is automatically activated when a cell is clicked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.KeepSelectedOnClick">
            <summary>
                <para>Gets or sets whether the selected nodes remain selected when their values are being edited.
</para>
            </summary>
            <value><b>true</b> if the selected nodes remain selected when their values are being edited; <b>false</b> if the nodes are automatically deselected after the selected cell's editor has been invoked.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.MoveOnEdit">
            <summary>
                <para>Gets or sets whether end-users can navigate through cells using arrow keys while editing.
</para>
            </summary>
            <value><b>true</b> to allow end-users to navigate through cells using arrow keys while editing; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.PopulateServiceColumns">
            <summary>
                <para>Gets or sets whether the <see cref="M:DevExpress.XtraTreeList.TreeList.PopulateColumns"/> method creates columns that are bound to the fields specified by the <see cref="P:DevExpress.XtraTreeList.TreeList.KeyFieldName"/>, <see cref="P:DevExpress.XtraTreeList.TreeList.ParentFieldName"/> and <see cref="P:DevExpress.XtraTreeList.TreeList.ImageIndexFieldName"/> properties.
</para>
            </summary>
            <value><b>true</b> to create service columns when calling the <see cref="M:DevExpress.XtraTreeList.TreeList.PopulateColumns"/> method; <b>false</b>, service columns are not created.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ResizeNodes">
            <summary>
                <para>Gets or sets whether end-users are allowed to change the heights of nodes.
</para>
            </summary>
            <value><b>true</b> to allow end-users to change the heights of nodes; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ShowEditorOnMouseUp">
            <summary>
                <para>Gets or sets whether a cell's editor is activated when the mouse button is released from a click within the cell.
</para>
            </summary>
            <value><b>true</b> to activate a cell's editor when the mouse button is released from a click within the cell; <b>false</b> to activate a cell's editor when the mouse button is initially pressed within the cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ShowToolTips">
            <summary>
                <para>Gets or sets whether tooltips are displayed for the cells and column headers with truncated content.
</para>
            </summary>
            <value><b>true</b> to show tooltips for the cells and column headers with truncated content; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.SmartMouseHover">
            <summary>
                <para>Gets or sets whether the regions occupied by the scroll bars and active cell editors are considered to be a part of the tree list's area.
</para>
            </summary>
            <value><b>true</b> if the regions occupied by the scroll bars and active cell editors are considered to be a part of the tree list's area; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.UseTabKey">
            <summary>
                <para>Gets or sets whether the TAB/SHIFT + TAB key combinations move focus to the next/previous node cell or to the next/previous control in the tab order.
</para>
            </summary>
            <value><b>true</b> if the TAB/SHIFT+TAB key combinations are processed by the tree list; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListAppearanceCollection">

            <summary>
                <para>Provides the appearance settings used to paint the tree list.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListAppearanceCollection.#ctor(DevExpress.Utils.IAppearanceOwner)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListAppearanceCollection"/> class with the specified owner.
</para>
            </summary>
            <param name="treeList">
		An object which implements the <b>IAppearanceOwner</b> interface (the <see cref="T:DevExpress.XtraTreeList.TreeList"/> or descendant) that will own the created collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.CustomizationFormHint">
            <summary>
                <para>Gets or sets the appearance of the hint text displayed within an empty Customization Form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains corresponding style settings.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.Empty">
            <summary>
                <para>Gets the appearance settings used to paint the tree list's empty space.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the tree list's empty space.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.EvenRow">
            <summary>
                <para>Gets the appearance settings used to paint even nodes.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint even nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.FilterPanel">
            <summary>
                <para>Gets the appearance settings used to paint the Filter Panel.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains appearance settings used to paint the filter panel.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.FixedLine">
            <summary>
                <para>Gets the appearance settings used to paint the fixed lines.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the fixed lines.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.FocusedCell">
            <summary>
                <para>Gets the appearance settings used to paint the currently focused cell.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the currently focused cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.FocusedRow">
            <summary>
                <para>Gets the appearance settings used to paint the currently focused node.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the currently focused node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.FooterPanel">
            <summary>
                <para>Gets the appearance settings used to paint the summary footer.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the summary footer.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.GroupButton">
            <summary>
                <para>Gets the appearance settings used to paint node expand buttons.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint node expand buttons.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.GroupFooter">
            <summary>
                <para>Gets the appearance settings used to paint the group footer.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the group footer.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.HeaderPanel">
            <summary>
                <para>Gets the appearance settings used to paint the column header and node indicator panels.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the column header and node indicator panels.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.HideSelectionRow">
            <summary>
                <para>Gets the appearance settings used to paint the selected node(s) when the tree list isn't focused. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the selected node(s) when the tree list isn't focused.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.HorzLine">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal lines.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the horizontal lines.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.OddRow">
            <summary>
                <para>Gets the appearance settings used to paint odd nodes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint odd nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.Preview">
            <summary>
                <para>Gets the appearance settings used to paint the preview section.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the preview section.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.Row">
            <summary>
                <para>Gets the appearance settings used to paint data cells within tree list nodes.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint data cells within tree list nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.SelectedRow">
            <summary>
                <para>Gets the appearance settings used to paint the selected nodes.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the selected nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.TreeLine">
            <summary>
                <para>Gets the appearance settings used to paint the tree lines.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the tree lines.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListAppearanceCollection.VertLine">
            <summary>
                <para>Gets the appearance settings used to paint the vertical lines.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the vertical lines.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawFooterEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooter"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawFooterEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawFooterEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted footer's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting a footer using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CreateCustomNodeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CreateCustomNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CreateCustomNodeEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CreateCustomNodeEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CreateCustomNode"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender. Identifies the tree list that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CreateCustomNodeEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CreateCustomNodeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CreateCustomNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.#ctor(System.Int32,DevExpress.XtraTreeList.Nodes.TreeListNodes,System.Object)">
            <summary>
                <para>Initializes a new instance of the CreateCustomNodeEventArgs class with the specified settings.
</para>
            </summary>
            <param name="nodeID">
		A zero-based integer specifying the unique identifier of the created node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.NodeID"/> property.

            </param>
            <param name="owner">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> collection which owns the created node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.Owner"/> property.

            </param>
            <param name="tag">
		A custom object to be associated with the created node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.Tag"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.Node">
            <summary>
                <para>Gets or sets a value which represents the created node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> descendant which represents the created node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.NodeID">
            <summary>
                <para>Gets the node's unique identifier.
</para>
            </summary>
            <value>A zero-based integer specifying the unique identifier of the created node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.Owner">
            <summary>
                <para>Gets the collection of nodes which owns the created node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> collection which owns the created node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CreateCustomNodeEventArgs.Tag">
            <summary>
                <para>Gets the data associated with the tree list node via the constructor.
</para>
            </summary>
            <value>An object containing custom information associated with the tree list node.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Columns.AutoFilterCondition">

            <summary>
                <para>Enumerates the comparison operator types for the filter conditions created for specific columns via the Auto Filter Row.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.Columns.AutoFilterCondition.Contains">
            <summary>
                <para>The <b>Contains</b> operator selects records whose values in the corresponding column contain the entered string. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Columns.AutoFilterCondition.Default">
            <summary>
                <para><para>
For columns being filtered by their display text (see <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.FilterMode"/>), the <b>Default</b> option acts identically to the <b>Like</b> option.
</para>

<para>
The <b>Default</b> option acts like the <b>Equals</b> option for the columns that have any of the following in-place editors or any of their descendants: <see cref="T:DevExpress.XtraEditors.CheckEdit"/>, <see cref="T:DevExpress.XtraEditors.DateEdit"/>, <see cref="T:DevExpress.XtraEditors.LookUpEditBase"/> or <see cref="T:DevExpress.XtraEditors.ImageComboBoxEdit"/>.
</para>

<para>
For other columns, the <b>Default</b> option acts identically to the <b>Like</b> option. 
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Columns.AutoFilterCondition.Equals">
            <summary>
                <para>The <b>Equals</b> comparison operator selects records whose values in the corresponding column match the entered value.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Columns.AutoFilterCondition.Like">
            <summary>
                <para><para>
The <b>Like</b> comparison operator selects records whose values in the corresponding column start with the entered string.
</para>

For the columns that use <see cref="T:DevExpress.XtraEditors.CheckEdit"/>, <see cref="T:DevExpress.XtraEditors.DateEdit"/>, <see cref="T:DevExpress.XtraEditors.LookUpEditBase"/> or <see cref="T:DevExpress.XtraEditors.ImageComboBoxEdit"/> in-place editors, the <b>Equals</b> operator is always used, unless these columns are filtered by display text (see <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.FilterMode"/>).

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterPopupMode">

            <summary>
                <para>Contains values that specify the filter dropdown style for Tree List columns. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.FilterPopupMode.CheckedList">
            <summary>
                <para>The filter dropdown is represented as a checked list of filter items. In this mode, an end-user can select more than one item simultaneously. When the dropdown window is closed by clicking the OK button, the control will display those records that contain the checked values.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.FilterPopupMode.Date">
            <summary>
                <para>This mode is in effect for columns displaying date-time values. The filter dropdown contains a calendar, plus check boxes that allow used date intervals to be selected.
If there is no underlying data that would fall into a specific date range, the corresponding check box is hidden. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.FilterPopupMode.Default">
            <summary>
                <para>For columns displaying date-time values, this option is equivalent to the Date option. For other columns, this option is equivalent to the List option. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.FilterPopupMode.List">
            <summary>
                <para>The filter dropdown is represented as a regular list of filter items. Clicking an item invokes a corresponding action, and automatically closes the dropdown. 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter">

            <summary>
                <para>Contains filter options for columns. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.#ctor">
            <summary>
                <para>Initializes a new instance of the TreeListOptionsColumnFilter class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.AllowAutoFilter">
            <summary>
                <para>Gets or sets whether the column's values can be filtered using the Auto Filter Row.
</para>
            </summary>
            <value><b>true</b> if the column's values can be filtered using the automatic filtering row; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.AllowFilter">
            <summary>
                <para>Gets or sets whether an end-user can filter against the current column using the filter dropdown.


</para>
            </summary>
            <value><b>true</b> if  an end-user can filter against the current column using the filter dropdown; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies the settings from the object passed as the parameter. 
</para>
            </summary>
            <param name="options">
		An object whose settings are copied to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.AutoFilterCondition">
            <summary>
                <para>Gets or sets the type of the comparison operator used to create filter conditions for the current column via the auto filter row.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraTreeList.Columns.AutoFilterCondition"/> value that determines the type of the comparison operator used to create filter conditions for the current column via the auto filter row.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.FilterPopupMode">
            <summary>
                <para>Gets or sets the display mode for the current column's filter dropdown. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterPopupMode"/> value that specifies the display mode for the column's filter dropdown list.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.ImmediateUpdateAutoFilter">
            <summary>
                <para>Gets or sets whether the column's filter condition is updated as soon as an end-user modifies the contents of the auto filter row's cell. 

</para>
            </summary>
            <value><b>true</b> if the column's filter condition is updated each time an end-user modifies the contents of the auto filter row's cell; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.ImmediateUpdatePopupDateFilter">
            <summary>
                <para>This option is in effect for date-time columns whose Filter DropDown contains an embedded calendar with check boxes to select common date intervals. It specifies whether selecting a date or date range via this dropdown window filters Tree List nodes immediately. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether data is filtered immediately when selecting a date or date range via the calendar.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumnFilter.ShowEmptyDateFilter">
            <summary>
                <para>Gets or sets whether a Filter DropDown has a filter used to select records that contain null values in a date-time column.
</para>
            </summary>
            <value><b>true</b> if a corresponding filter is available; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListOptionsFilter">

            <summary>
                <para>Provides filtering options for the <see cref="T:DevExpress.XtraTreeList.TreeList"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsFilter.#ctor">
            <summary>
                <para>Initializes a new instance of the TreeListOptionsFilter class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.AllowColumnMRUFilterList">
            <summary>
                <para>Gets or sets whether recently used filter items are displayed in the Column's Filter DropDown.
</para>
            </summary>
            <value><b>true</b> if the columns' filter dropdown lists can display recently used filter items; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.AllowFilterEditor">
            <summary>
                <para>Gets or sets whether the Filter Editor can be used to build complex filter criteria.
</para>
            </summary>
            <value><b>true</b> to allow using the <b>Filter Editor</b>; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.AllowMRUFilterList">
            <summary>
                <para>Gets or sets whether the TreeList's MRU Filter List is enabled.
</para>
            </summary>
            <value><b>true</b> if the MRU Filter List is enabled; otherwise <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListOptionsFilter.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.ColumnFilterPopupRowCount">
            <summary>
                <para>Gets or sets the maximum number of items that the Column's Filter DropDown can display simultaneously.
</para>
            </summary>
            <value>An integer value specifying the maximum height of regular filter dropdowns. The height is specified in rows.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.FilterMode">
            <summary>
                <para>Gets or sets a filter mode used within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterMode"/> enumerator value that specifies a filter mode used within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.MRUColumnFilterListCount">
            <summary>
                <para>Gets or sets the maximum number of items that the column's MRU Filter Lists can store.
</para>
            </summary>
            <value>An integer specifying the maximum number of items that the column's MRU Filter Lists can store.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.MRUFilterListPopupCount">
            <summary>
                <para>Gets or sets the maximum number of items the TreeList's MRU Filter List can display at once.
</para>
            </summary>
            <value>An integer value that determines the maximum number of items that the TreeList's MRU Filter List can display at once.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListOptionsFilter.ShowAllValuesInFilterPopup">
            <summary>
                <para>Gets or sets whether the Column's Filter DropDown shows all available values.
</para>
            </summary>
            <value><b>true</b> to display all values in the Column's Filter DropDown; otherwise <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Columns.TreeListColumnCollection">

            <summary>
                <para>Represents a column collection in the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.#ctor(DevExpress.XtraTreeList.TreeList)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumnCollection"/> object.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which a new column collection belongs.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.Add">
            <summary>
                <para>Creates a new column and appends it to the end of the collection.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the new column.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.AddField(System.String)">
            <summary>
                <para>Creates a new column which is bound to the specified field and appends it to the collection.
</para>
            </summary>
            <param name="fieldName">
		A <see cref="T:System.String"/> value that specifies the name of the data field to bind the created column to.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the new column. 
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.AddRange(DevExpress.XtraTreeList.Columns.TreeListColumn[])">
            <summary>
                <para>Adds an array of columns to the end of the collection.
</para>
            </summary>
            <param name="columns">
		An array of <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> objects.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.AssignTo(DevExpress.XtraTreeList.Columns.TreeListColumnCollection)">
            <summary>
                <para>Copies the current collection's elements to the collection specified.
</para>
            </summary>
            <param name="columns">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumnCollection"/> object representing the column collection into which columns are copied.

            </param>


        </member>
        <member name="E:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.CollectionChanged">
            <summary>
                <para>Fires when changes are made to the column collection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.ColumnByFieldName(System.String)">
            <summary>
                <para>Returns a column with the field name specified.
</para>
            </summary>
            <param name="fieldName">
		A <see cref="T:System.String"/> value that specifies the name of the field to which the column is bound.


            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column bound to the specified field. <b>null</b> (<b>Nothing</b> in Visual Basic) if the collection has no columns bound to the field.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.ColumnByName(System.String)">
            <summary>
                <para>Returns a column specified by its name.
</para>
            </summary>
            <param name="columnName">
		A string value specifying the column's name.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column with the specified name. <b>null</b> (<b>Nothing</b> in Visual Basic) if the collection doesn't contain the column with the specified name.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.IndexOf(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Returns the specified column's position within the collection.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to locate in the collection.

            </param>
            <returns>A zero-based integer representing the column's position within the collection. <b>-1</b> if the column doesn't belong to the collection.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.Insert(System.Int32)">
            <summary>
                <para>Creates a new column and adds it to the collection at the specified position.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the new column's position within the collection. If negative, the column is inserted as the first element. If it exceeds the last available index, the column is appended to the end of collection.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column inserted.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.Item(System.String)">
            <summary>
                <para>Gets the column bound to the specified field.
</para>
            </summary>
            <param name="fieldName">
		A string value specifying the column's bound field name.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column bound to the specified field. <b>null</b> (<b>Nothing</b> in Visual Basic) if the collection doesn't contain a column bound to the specified field.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual columns.
</para>
            </summary>
            <param name="index">
		A zero-based integer identifying the desired column's position within the collection.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column at the specified position. <b>null</b> (<b>Nothing</b> in Visual Basic) if the collection doesn't contain a column at the specified position.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.Remove(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Removes a column from the collection.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column to remove.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumnCollection.TreeList">
            <summary>
                <para>Gets the control that owns the collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object representing the tree list that owns the column collection.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterMode">

            <summary>
                <para>Provides members that specify a <see cref="T:DevExpress.XtraTreeList.TreeList"/> filtering mode.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.FilterMode.Default">
            <summary>
                <para>A default behavior. Identical to the <see cref="F:DevExpress.XtraTreeList.FilterMode.Standard"/> value.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.FilterMode.Smart">
            <summary>
                <para>Allows a <see cref="T:DevExpress.XtraTreeList.TreeList"/> to display all child nodes that match the filter criteria, whether or not their parent nodes match the criteria.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.FilterMode.Standard">
            <summary>
                <para>Requires all node parents to match the filter criteria to show a particular child node.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListState">

            <summary>
                <para>Contains values indicating all the available states for the tree list.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.ColumnButtonPressed">
            <summary>
                <para>The column button is clicked.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.ColumnDragging">
            <summary>
                <para>A column's header is being dragged. The column's <see cref="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowMove"/> option must be enabled to allow column header dragging.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.ColumnPressed">
            <summary>
                <para>A column's header is pressed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.ColumnSizing">
            <summary>
                <para>A column's right edge is being dragged to change the column's width. The column's <see cref="P:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn.AllowSize"/> option must be enabled to allow such resizing.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.Design">
            <summary>
                <para>The tree list is currently in design mode.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.Editing">
            <summary>
                <para>A cell editor is currently active.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.IncrementalSearch">
            <summary>
                <para>An incremental search is being performed.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.MultiSelection">
            <summary>
                <para>Multiple nodes are being selected using drag and drop. The <see cref="P:DevExpress.XtraTreeList.TreeListOptionsSelection.MultiSelect"/> option must be set to <b>true</b> to enable this feature.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.NodeDragging">
            <summary>
                <para>A node is being dragged. The tree list nodes can be dragged if the <see cref="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.DragNodes"/> option is enabled.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.NodePressed">
            <summary>
                <para>A node is currently pressed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.NodeSizing">
            <summary>
                <para><para>A node is being resized by an end-user. This implies that the node's edge is being dragged. This operation is only available for nodes whose <see cref="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.ResizeNodes"/> option is enabled.</para>

<para>You can prevent the node from being resized by enabling the <see cref="P:DevExpress.XtraTreeList.TreeListOptionsBehavior.AutoNodeHeight"/> option.</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.OuterDragging">
            <summary>
                <para>An external object, not one of the tree list's elements, is being dragged from outside over the tree list control.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeListState.Regular">
            <summary>
                <para>The tree list is in its normal state. No specific action is being performed by an end-user. 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeChangeTypeEnum">

            <summary>
                <para>Contains values indicating how the node has been changed.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.Add">
            <summary>
                <para>A new node has been added to the tree list's <see cref="P:DevExpress.XtraTreeList.TreeList.Nodes"/> collection. This can be performed by calling the <see cref="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Add"/> method.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.CheckedState">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Checked"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.Expanded">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Expanded"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.HasChildren">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.HasChildren"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.ImageIndex">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.ImageIndex"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.Remove">
            <summary>
                <para>A node has been removed from the tree list's <see cref="P:DevExpress.XtraTreeList.TreeList.Nodes"/> collection. This can be performed using the <see cref="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Remove"/> method.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.SelectImageIndex">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.SelectImageIndex"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.StateImageIndex">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.StateImageIndex"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.Tag">
            <summary>
                <para>The node's <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Tag"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.User1">
            <summary>
                <para>The corresponding user's property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.User2">
            <summary>
                <para>The corresponding user's property has been changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.NodeChangeTypeEnum.User3">
            <summary>
                <para>The corresponding user's property has been changed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.OptionsLayoutTreeList">

            <summary>
                <para>Contains options that specify how a control's layout is restored from a data store (a stream, xml file or the system registry). 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.OptionsLayoutTreeList.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsLayoutTreeList class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.OptionsLayoutTreeList.AddNewColumns">
            <summary>
                <para>Gets or sets whether the columns that exist in the current control but do not exist in a layout when it's restored should be retained.
</para>
            </summary>
            <value><b>true</b> to retain the columns that exist in the current control's layout but don't exist in the layout being restored; <b>false</b> to destroy such columns.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.OptionsLayoutTreeList.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies the settings from the object passed as the parameter. 
</para>
            </summary>
            <param name="options">
		An object whose settings are copied to the current object.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.OptionsLayoutTreeList.RemoveOldColumns">
            <summary>
                <para>Gets or sets whether the columns that exist in a layout when it's restored, but that don't exist in the current control, should be discarded or added to the control.


</para>
            </summary>
            <value><b>true</b> to discard the columns that exist in the layout being restored, but don't exist in the current control; <b>false</b> to add these columns to the control.


</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.OptionsLayoutTreeList.StoreAppearance">
            <summary>
                <para>Gets or sets whether the control's appearance settings are also stored when the layout is saved to storage, and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the control's appearance settings are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.ScrollVisibility">

            <summary>
                <para>Lists values specifying the availability of the control's elements that enable data scrolling. 

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.ScrollVisibility.Always">
            <summary>
                <para>The scrolling element is always visible.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ScrollVisibility.Auto">
            <summary>
                <para>The scrolling element is only visible when data scrolling can be performed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ScrollVisibility.Never">
            <summary>
                <para>The scrollbar is invisible regardless of whether data scrolling can be performed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.ValidateNodeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ValidateNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.ValidateNodeEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.ValidateNodeEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ValidateNode"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object which raised the event).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.ValidateNodeEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.ValidateNodeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.ValidateNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.ValidateNodeEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraTreeList.ValidateNodeEventArgs"/> object.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.ValidateNodeEventArgs.ErrorText">
            <summary>
                <para>Gets or sets the error description.
</para>
            </summary>
            <value>A string representing the error description.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.ValidateNodeEventArgs.Valid">
            <summary>
                <para>Gets or sets whether the node's validation succeeds.
</para>
            </summary>
            <value><b>true</b> to accept node cell values; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeChangedEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.NodeChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeChanged"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object which raised the event).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.NodeChangedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeChangedEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.NodeChangeTypeEnum)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraTreeList.NodeChangedEventArgs"/> object.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the processed node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="changeType">
		A <see cref="T:DevExpress.XtraTreeList.NodeChangeTypeEnum"/> enumeration member specifying the way the node has been changed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeChangedEventArgs.ChangeType"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.NodeChangedEventArgs.ChangeType">
            <summary>
                <para>Gets the way the node has been changed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.NodeChangeTypeEnum"/> enumeration member specifying the way the node has been changed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.InvalidNodeExceptionEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.InvalidNodeException"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.InvalidNodeExceptionEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.InvalidNodeException"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object which raised the event). 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.InvalidNodeException"/>  event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs.#ctor(System.Exception,System.String,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs"/> object.
</para>
            </summary>
            <param name="except">
		A <see cref="T:System.Exception"/> object representing the exception that caused the event. This value is assigned to the <see cref="P:DevExpress.XtraEditors.Controls.ExceptionEventArgs.Exception"/> property.

            </param>
            <param name="windowText">
		A string value specifying the error description. This value is assigned to the <see cref="P:DevExpress.XtraEditors.Controls.ExceptionEventArgs.ErrorText"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that failed validation. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.InvalidNodeExceptionEventArgs.Node">
            <summary>
                <para>Gets the node that failed validation.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that failed validation.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawEmptyArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawEmptyArea"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object which raised the event). 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawEmptyArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Drawing.Region)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs"/> class with the specified settings.
</para>
            </summary>
            <param name="cache">
		 A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the pens, fonts and brushes used. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the boundaries of the painted element. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint an empty area. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="emptyRows">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the bounding rectangle occupied by the empty rows. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.EmptyRows"/> property.

            </param>
            <param name="bc">
		A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the drawing area under the blank column's header. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.BehindColumn"/> property.

            </param>
            <param name="emptyAreaRegion">
		A <see cref="T:System.Drawing.Region"/> object which represents the interior of the empty area. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.EmptyAreaRegion"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.BehindColumn">
            <summary>
                <para>Gets the blank column's bounding rectangle.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure specifying the drawing area under the blank column header.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.EmptyAreaRegion">
            <summary>
                <para>Gets the region which is occupied by the empty area.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Region"/> object which represents the interior of the empty area.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEmptyAreaEventArgs.EmptyRows">
            <summary>
                <para>Gets the bounding rectangle occupied by the empty rows.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the bounding rectangle occupied by the empty rows.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterConditionCollection">

            <summary>
                <para>Represents a collection of filter conditions for the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollection.#ctor(DevExpress.XtraTreeList.TreeList)">
            <summary>
                <para>Initializes a new instance of the FilterConditionCollection class.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> control that will own the new collection. The value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionCollection.TreeListControl"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollection.Add(DevExpress.XtraTreeList.FilterCondition)">
            <summary>
                <para>Adds the specified filter condition object to the end of the collection. 
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraTreeList.FilterCondition"/> object to be added to the collection. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollection.AddRange(DevExpress.XtraTreeList.FilterCondition[])">
            <summary>
                <para>Adds an array of filter condition objects. 
</para>
            </summary>
            <param name="conditions">
		An array of <see cref="T:DevExpress.XtraTreeList.FilterCondition"/> objects to be added to the collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to filter conditions in the current collection.
</para>
            </summary>
            <param name="index">
		An integer that represents the zero-based index of the <see cref="T:DevExpress.XtraTreeList.FilterCondition"/> object to be returned.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterCondition"/> object with the specified index.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionCollection.TreeListControl">
            <summary>
                <para>Gets the control that owns the current filter condition collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> control that owns the current filter condition collection.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterConditionCollectionBase">

            <summary>
                <para>The base class for filter condition collections.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.#ctor">
            <summary>
                <para>Initializes a new instance of the FilterConditionCollectionBase class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.Add(DevExpress.XtraTreeList.FilterConditionBase)">
            <summary>
                <para>Adds the specified <see cref="T:DevExpress.XtraTreeList.FilterConditionBase"/> object to the current collection.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraTreeList.FilterConditionBase"/> object to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.Assign(DevExpress.XtraTreeList.FilterConditionCollectionBase)">
            <summary>
                <para>Copies settings of the specified object to the current object.
</para>
            </summary>
            <param name="source">
		An object whose settings are copied to the current object.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.BeginUpdate">
            <summary>
                <para>Locks the FilterConditionCollectionBase by preventing change notifications from being fired, preventing visual updates until the <b>EndUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.FilterConditionCollectionBase.CollectionChanged">
            <summary>
                <para>Fires when the collection is modified.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.Contains(DevExpress.XtraTreeList.FilterConditionBase)">
            <summary>
                <para>Returns a Boolean value that specifies whether the collection contains the specified element.
</para>
            </summary>
            <param name="condition">
		An element to be located in the collection.

            </param>
            <returns>A Boolean value that specifies whether the collection contains the specified element.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.EndUpdate">
            <summary>
                <para>Unlocks the FilterConditionCollectionBase object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.IndexOf(DevExpress.XtraTreeList.FilterConditionBase)">
            <summary>
                <para>Returns the index of the specified object within the collection.
</para>
            </summary>
            <param name="condition">
		An object to be located in the collection.

            </param>
            <returns>The index of the specified object within the collection.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionCollectionBase.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to items in the current collection.
</para>
            </summary>
            <param name="index">
		An integer that represents the zero-based index of the <see cref="T:DevExpress.XtraTreeList.FilterConditionBase"/> object to be returned.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterConditionBase"/> object with the specified index.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionCollectionBase.Remove(DevExpress.XtraTreeList.FilterConditionBase)">
            <summary>
                <para>Removes the specified condition from the collection.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraTreeList.FilterConditionBase"/> object to be removed from the collection. 

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterConditionBase">

            <summary>
                <para>The base class for filter conditions.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionBase.#ctor(DevExpress.XtraTreeList.FilterConditionEnum,System.Object,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the FilterConditionBase class with the specified column, comparison operator, and condition values.
</para>
            </summary>
            <param name="condition">
		A FilterConditionEnum value that specifies the comparison operator. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Condition"/> property.

            </param>
            <param name="column">
		The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which the condition is applied. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterCondition.Column"/> property.

            </param>
            <param name="value1">
		The first value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value1"/> property.

            </param>
            <param name="value2">
		The second value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value2"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionBase.#ctor">
            <summary>
                <para>Initializes a new instance of the FilterConditionBase class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionBase.Assign(DevExpress.XtraTreeList.FilterConditionBase)">
            <summary>
                <para>Clears the contents of the current collection and then copies the contents of the specified collection to the current collection.
</para>
            </summary>
            <param name="source">
		A collection whose contents must be copied to the current collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterConditionBase.CheckValue(System.Object)">
            <summary>
                <para>Checks whether the specified value matches the current condition.
</para>
            </summary>
            <param name="val">
		A value to be tested.

            </param>
            <returns><b>true</b> if the specified value matches the current condition; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionBase.Collection">
            <summary>
                <para>Gets the collection to which the current condition belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterConditionCollectionBase"/> object that owns the current condition.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionBase.Condition">
            <summary>
                <para>Gets or sets the comparison operator.
</para>
            </summary>
            <value>A FilterConditionEnum value that represents the comparison operator.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionBase.Value1">
            <summary>
                <para>Gets or sets a value that is compared to a column's values. 
</para>
            </summary>
            <value>A value that is compared to a column's values. 
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterConditionBase.Value2">
            <summary>
                <para>Gets or sets a value that is compared to a column's values.
</para>
            </summary>
            <value>A value that is compared to a column's values. 
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FilterCondition">

            <summary>
                <para>Represents a filter condition for the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class.
</para>
            </summary>
            <param name="condition">
		@nbsp;

            </param>
            <param name="column">
		@nbsp;

            </param>
            <param name="val1">
		@nbsp;

            </param>
            <param name="val2">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class.
</para>
            </summary>
            <param name="condition">
		@nbsp;

            </param>
            <param name="column">
		@nbsp;

            </param>
            <param name="val1">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class.
</para>
            </summary>
            <param name="condition">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraTreeList.FilterConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class with the specified column, comparison operator, and condition values.
</para>
            </summary>
            <param name="condition">
		A FilterConditionEnum value that specifies the comparison operator. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Condition"/> property.

            </param>
            <param name="column">
		The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which the condition is applied. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterCondition.Column"/> property.

            </param>
            <param name="val1">
		The first value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value1"/> property.

            </param>
            <param name="val2">
		The second value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value2"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraTreeList.FilterConditionEnum)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class with the specified comparison operator.
</para>
            </summary>
            <param name="condition">
		A FilterConditionEnum value that specifies the comparison operator. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Condition"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraGrid.FormatConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class.
</para>
            </summary>
            <param name="condition">
		@nbsp;

            </param>
            <param name="column">
		@nbsp;

            </param>
            <param name="val1">
		@nbsp;

            </param>
            <param name="val2">
		@nbsp;

            </param>
            <param name="visible">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor">
            <summary>
                <para>Initializes a new instance of the FilterCondition class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraTreeList.FilterConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class with the specified column, comparison operator, and condition value.
</para>
            </summary>
            <param name="condition">
		A FilterConditionEnum value that specifies the comparison operator. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Condition"/> property.

            </param>
            <param name="column">
		The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which the condition is applied. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterCondition.Column"/> property.

            </param>
            <param name="val1">
		The first value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value1"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.#ctor(DevExpress.XtraTreeList.FilterConditionEnum,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Object,System.Object,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the FilterCondition class with the specified column, comparison operator, and condition values.
</para>
            </summary>
            <param name="condition">
		A FilterConditionEnum value that specifies the comparison operator. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Condition"/> property.

            </param>
            <param name="column">
		The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which the condition is applied. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterCondition.Column"/> property.

            </param>
            <param name="val1">
		The first value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value1"/> property.

            </param>
            <param name="val2">
		The second value that is used to create the filter condition. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterConditionBase.Value2"/> property.

            </param>
            <param name="visible">
		A Boolean value that specifies whether nodes that match the created condition must remain visible or be hidden. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FilterCondition.Visible"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.FilterCondition.CheckValue(System.Object)">
            <summary>
                <para>Checks whether the specified value matches the current condition.
</para>
            </summary>
            <param name="val">
		A value to be tested.

            </param>
            <returns><b>true</b> if the specified value matches the current condition; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterCondition.Column">
            <summary>
                <para>Gets or sets the column to which the current filter condition is applied.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which the current filter condition is applied.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.FilterCondition.Visible">
            <summary>
                <para>Gets or sets whether a node that matches the current condition must remain visible or be hidden.

</para>
            </summary>
            <value><b>true</b> if a node that matches the current condition must remain visible; <b>false</b> if this node must be hidden.

</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeDragImageIndex"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeDragImageIndex"/> event.
</para>
            </summary>
            <param name="sender">
		The event source (typically, the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeDragImageIndex"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32,System.Drawing.Point,System.Windows.Forms.DragEventArgs)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs"/> class.
</para>
            </summary>
            <param name="overNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the target node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="imageIndex">
		An integer value specifying the index of the image in the source collection. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.ImageIndex"/> property.

            </param>
            <param name="ptClient">
		A <see cref="T:System.Drawing.Point"/> structure representing the mouse pointer's coordinates relative to the top-left corner of the control. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.PtClient"/> property.

            </param>
            <param name="dragArgs">
		A <see cref="T:System.Windows.Forms.DragEventArgs"/> object which provides data for the drag-and-drop events. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.DragArgs"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.DragArgs">
            <summary>
                <para>Gets an object which provides information for drag-and-drop events.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.DragEventArgs"/> object which provides data for drag-and-drop events.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.ImageIndex">
            <summary>
                <para>Gets or sets the index of the image to be displayed in front of nodes when dragging.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the desired image within the <b>TreeList.Painter.NodeDragImages</b> collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CalcNodeDragImageIndexEventArgs.PtClient">
            <summary>
                <para>Gets the current mouse pointer's coordinates relative to the top-left corner of the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure representing the current mouse pointer's coordinates.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListVisibleNodeOperation">

            <summary>
                <para>Serves as the base for classes that represent operations performed on visible nodes.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.Operations.TreeListVisibleNodeOperation.NeedsFullIteration">
            <summary>
                <para>Returns a value that specifies whether all nodes or only those that have children are to be visited.
</para>
            </summary>
            <value><b>true</b> always.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListVisibleNodeOperation.NeedsVisitChildren(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Gets a value specifying whether children of the specified node are to be visited.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose child nodes are to be visited.

            </param>
            <returns><b>true</b> always.
</returns>


        </member>
        <member name="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation">

            <summary>
                <para>Serves as the base for classes specifying operations performed on nodes.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation.CanContinueIteration(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns a value indicating whether the iteration must be stopped.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the processed node.

            </param>
            <returns><b>true</b> to continue iteration; <b>false</b> to stop the iteration.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation.Execute(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Must be implemented to perform an operation on the visited node.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node against which the operation is to be performed.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation.FinalizeOperation">
            <summary>
                <para>Can be overridden to perform final operations and free allocated resources.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation.NeedsFullIteration">
            <summary>
                <para>Gets a value indicating whether all or only parent nodes must be processed by the operation.
</para>
            </summary>
            <value><b>true</b> if all nodes must be processed by the operation; <b>false</b> if only nodes that have children are to be processed.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation.NeedsVisitChildren(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Gets a value specifying whether the operation must be performed on the specified node's children.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose child nodes are to be visited.

            </param>
            <returns><b>true</b> if the operation must be performed on the specified node's children; otherwise <b>false</b>.
</returns>


        </member>
        <member name="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator">

            <summary>
                <para>Enables you to perform specified operations over nodes from the predefined set.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.DoLocalOperation(DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation,DevExpress.XtraTreeList.Nodes.TreeListNodes)">
            <summary>
                <para>Performs the specified operation over the predefined set of nodes and their children.
</para>
            </summary>
            <param name="operation">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation"/> descendant representing the operation to be performed.

            </param>
            <param name="nodes">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> object representing the collection of nodes across which the operation is performed.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.DoLocalOperation(DevExpress.XtraTreeList.Nodes.Operations.TreeListOperationDelegate,DevExpress.XtraTreeList.Nodes.TreeListNodes)">
            <summary>
                <para>Performs the specified method over the predefined set of nodes and their children.
</para>
            </summary>
            <param name="operation">
		A TreeListOperationDelegate object that represents the method to be called for the specified nodes.

            </param>
            <param name="nodes">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> object representing the collection of nodes across which the operation is performed.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.DoOperation(DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation)">
            <summary>
                <para>Performs the specified operation across all nodes.
</para>
            </summary>
            <param name="operation">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListOperation"/> descendant representing the operation to be performed.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.DoOperation(DevExpress.XtraTreeList.Nodes.Operations.TreeListOperationDelegate)">
            <summary>
                <para>Performs the specified method across all nodes.
</para>
            </summary>
            <param name="operation">
		A TreeListOperationDelegate object that represents the method to be called for the specified nodes.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.DoVisibleNodesOperation(DevExpress.XtraTreeList.Nodes.Operations.TreeListVisibleNodeOperation,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Performs the specified operation over a range of nodes that are not hidden within collapsed groups.
</para>
            </summary>
            <param name="operation">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListVisibleNodeOperation"/> descendant that specifies the operation to be performed.

            </param>
            <param name="visibleNodeFrom">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node from which the iteration begins.

            </param>
            <param name="visibleNodeTo">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node at which the iteration ends.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.GetNextVisible(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns the visible node located after the specified one. 
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the visible node whose next visible node is returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the visible node located after the specified one. <b>null</b> (<b>Nothing</b> in Visual Basic) if the node specified by the parameter is the last visible node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator.GetPrevVisible(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns the visible node which is located prior to the specified one.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the visible node whose previous visible node is returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the visible node which is located prior to the specified one. <b>null</b> (<b>Nothing</b> in Visual Basic) if the node specified by the parameter is the first visible node.

</returns>


        </member>
        <member name="T:DevExpress.XtraTreeList.Menu.TreeListMenuType">

            <summary>
                <para>Contains values identifying menu types available in the <b>TreeList</b> control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.Menu.TreeListMenuType.Column">
            <summary>
                <para>Identifies the column header context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Menu.TreeListMenuType.Node">
            <summary>
                <para>Identifies a menu for a treelist node. By default, the menu is empty. Use the <see cref="E:DevExpress.XtraTreeList.TreeList.PopupMenuShowing"/> event to add items to the menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Menu.TreeListMenuType.Summary">
            <summary>
                <para>Identifies the summary footer or row footer context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.Menu.TreeListMenuType.User">
            <summary>
                <para>Corresponds to the user defined context menu.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListMenuItemClickEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.TreeListMenuItemClick"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMenuItemClickEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.TreeListMenuItemClick"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.TreeListMenuItemClick"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,System.Boolean,DevExpress.XtraTreeList.SummaryItemType,System.String,DevExpress.XtraTreeList.Menu.TreeListMenuType,DevExpress.Utils.Menu.DXMenuItem)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column for which the context menu has been activated. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.Column"/> property.

            </param>
            <param name="isFooter">
		<b>true</b> if the summary footer's menu item has been clicked; <b>false</b> if either the column header or row footer's menu item has been clicked. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.IsFooter"/> property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value specifying the type of summary which is about to be applied to the column. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.SummaryType"/> property.

            </param>
            <param name="format">
		The format string applied to the column's summary value. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.SummaryFormat"/> property.

            </param>
            <param name="mtype">
		A <see cref="T:DevExpress.XtraTreeList.Menu.TreeListMenuType"/> enumeration value indicating the type of menu whose item has been clicked. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.MenuType"/> property.

            </param>
            <param name="menuItem">
		A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object representing the clicked menu item. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.MenuItem"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.Column">
            <summary>
                <para>Gets the column against whose header or footer the context menu has been activated.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column for which the context menu has been activated.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.Handled">
            <summary>
                <para>Gets or sets a value indicating whether default menu item click processing is prohibited.
</para>
            </summary>
            <value><b>true</b> if default menu item click processing is not performed; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.IsFooter">
            <summary>
                <para>Gets a value indicating whether a summary footer's menu item has been clicked.
</para>
            </summary>
            <value><b>true</b> if the summary footer's menu item has been clicked; <b>false</b> if either the column header or row footer's menu item has been clicked.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.MenuItem">
            <summary>
                <para>Gets the clicked menu item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object representing the clicked menu item.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.MenuType">
            <summary>
                <para>Gets the type of menu whose item has been clicked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Menu.TreeListMenuType"/> enumeration value indicating the type of menu whose item has been clicked.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.SummaryFormat">
            <summary>
                <para>Gets or sets the format of summary value displayed for the column.
</para>
            </summary>
            <value>The format string applied to the column's summary value.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs.SummaryType">
            <summary>
                <para>Gets or sets the summary type which is about to be applied to the column.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value specifying the type of summary which is about to be applied to the column.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ShowTreeListMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMenuEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.TreeListMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ShowTreeListMenu"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.TreeListMenuEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.ShowTreeListMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMenuEventArgs.#ctor(DevExpress.XtraTreeList.Menu.TreeListMenu,System.Drawing.Point,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.TreeListMenuEventArgs"/> class.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.XtraTreeList.Menu.TreeListMenu"/> object representing the tree list's context menu. This value is assigned to the Menu property.

            </param>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure that contains the current mouse pointer coordinates. This value is assigned to the Point property.

            </param>
            <param name="allow">
		<b>true</b> if the context menu will be displayed; otherwise <b>false</b>. This value is assigned to the Allow property.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.AfterExpand"/>, <see cref="E:DevExpress.XtraTreeList.TreeList.AfterCollapse"/>, <see cref="E:DevExpress.XtraTreeList.TreeList.AfterDragNode"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.AfterFocusNode"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.NodeEventArgs)">
            <summary>
                <para>Represents a method that will handle events that require a node as the parameter.
</para>
            </summary>
            <param name="sender">
		The sender of an event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.NodeEventArgs"/> object that contains event related data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeClickEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.StateImageClick"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.SelectImageClick"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeClickEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.NodeClickEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.StateImageClick"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.SelectImageClick"/> events.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.NodeClickEventArgs"/> object that contains the event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeClickEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.SelectImageClick"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.StateImageClick"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeClickEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Drawing.Point)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.NodeClickEventArgs"/> class.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the clicked node.

            </param>
            <param name="point">
		A <b>System.Drawing.Point</b> structure that contains the current mouse pointer position.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.NodeClickEventArgs.Point">
            <summary>
                <para>Gets the mouse pointer's location.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that contains the current mouse pointer's location.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetNodeDisplayValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetNodeDisplayValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetNodeDisplayValueEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetNodeDisplayValue"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs"/> object that contains the event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.GetNodeDisplayValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column which owns the cell being processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents a tree list node whose cell is being processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetNodeDisplayValueEventArgs.Value">
            <summary>
                <para>Gets or sets the value contained within the processed cell.
</para>
            </summary>
            <value>An object representing the processed cell's value.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeCellStyle"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeCellStyle"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.NodeCellStyle"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the cell's owning column. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node in which the processed cell resides. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the cell's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs.Appearance"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomNodeCellStyleEventArgs.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the cell currently being processed.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the cell.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomNodeCellEditEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomNodeCellEdit"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomNodeCellEditEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomNodeCellEdit"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomNodeCellEdit"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraEditors.Repository.RepositoryItem)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column whose cell is processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose cell is processed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="repositoryItem">
		A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object representing the editor used to edit the processed cell's values. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs.RepositoryItem"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomNodeCellEditEventArgs.RepositoryItem">
            <summary>
                <para>Gets or sets the editor assigned to the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object representing the currently assigned editor.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FocusedColumnChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedColumnChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FocusedColumnChangedEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.FocusedColumnChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedColumnChanged"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedColumnChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the currently focused column. <b>null</b> (<b>Nothing</b> in Visual Basic) if none of the columns is currently focused. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.ColumnChangedEventArgs.Column"/> property.

            </param>
            <param name="oldColumn">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the previously focused column. <b>null</b> (<b>Nothing</b> in Visual Basic) if none of the columns was previously focused. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs.OldColumn"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.FocusedColumnChangedEventArgs.OldColumn">
            <summary>
                <para>Gets the previously focused column.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the previously focused column. <b>null</b> (<b>Nothing</b> in Visual Basic) if none of the columns was previously focused.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawRowFooterEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooter"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawRowFooterEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooter"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooter"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.Utils.Drawing.ObjectPainter,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting an element using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents a node containing the painted row footer. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawRowFooterEventArgs.Node">
            <summary>
                <para>Gets the node for whose children the painted row footer is displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node for whose children the row footer is displayed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooterCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooterCell"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooterCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.SummaryItemType,System.String,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column whose footer cell is painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node for whose children the row footer is displayed. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs.Node"/> property.

            </param>
            <param name="itemType">
		A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value specifying the type of summary whose value is displayed within the painted footer cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.ItemType"/> property.

            </param>
            <param name="text">
		A string value specifying the text to be displayed within the painted row footer cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Text"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting an element using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawRowFooterCellEventArgs.Node">
            <summary>
                <para>Gets the node for whose children the row footer is displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object specifying the node for whose children the row footer is displayed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodePreviewEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodePreview"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodePreviewEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodePreview"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodePreview"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraTreeList.Nodes.TreeListNode,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node whose preview section is being painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs.Node"/> property.

            </param>
            <param name="previewText">
		A string value representing the text of the preview section. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs.PreviewText"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs.Node">
            <summary>
                <para>Gets the node whose preview section is being painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose preview section is being painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodePreviewEventArgs.PreviewText">
            <summary>
                <para>Gets or sets the painted preview section's text.
</para>
            </summary>
            <value>A string value representing the text of the preview section being painted.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeIndicator"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeIndicator"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeIndicator"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,System.Boolean,System.Int32,System.Boolean,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs"/> class with the specified parameters.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="isNodeIndicator">
		<b>true</b> if the painted cell corresponds to a node; <b>false</b> if the painted cell corresponds to a group footer. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.IsNodeIndicator"/> property.

            </param>
            <param name="imageIndex">
		An integer value representing the painted indicator cell's image index. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.ImageIndex"/> property.

            </param>
            <param name="topMost">
		<b>true</b> if the painted indicator corresponds to the top most node; otherwise, <b>false</b>. This value is assigned to the <b>DevExpress.Utils.Drawing.IndicatorObjectInfoArgs.IsTopMost</b> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node whose indicator cell is painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.Node"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object which provides the default painting mechanism for the painted element. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.ImageIndex">
            <summary>
                <para>Gets or sets the painted indicator cell's image index.
</para>
            </summary>
            <value>An integer value representing the painted indicator cell's image index. <b>-1</b> if no image is about to be displayed within the cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.IsNodeIndicator">
            <summary>
                <para>Gets a value indicating whether the painted indicator cell corresponds to a node or to a row footer.
</para>
            </summary>
            <value><b>true</b> if the painted cell corresponds to a node; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeIndicatorEventArgs.Node">
            <summary>
                <para>Gets the painted indicator cell's associated node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the painted indicator cell's associated node.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeImagesEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeImages"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeImagesEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeImages"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeImages"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32,System.Int32,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Drawing.Point,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node whose images are being painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.Node"/> property.

            </param>
            <param name="selectImageIndex">
		An integer value specifying the zero-based index of the image displayed within the node when it's focused. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectImageIndex"/> property.

            </param>
            <param name="stateImageIndex">
		An integer value specifying the zero-based index of the node's state image. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateImageIndex"/> property.

            </param>
            <param name="selectRect">
		A <see cref="T:System.Drawing.Rectangle"/> structure that represents the bounding rectangle of the select image. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectRect"/> property.

            </param>
            <param name="stateRect">
		A <see cref="T:System.Drawing.Rectangle"/> structure that represents the state image's bounding rectangle. This value is assigned to <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateRect"/> property.

            </param>
            <param name="selectImageLocation">
		A <see cref="T:System.Drawing.Point"/> structure that represents the top-left point of the select image. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectImageLocation"/> property.

            </param>
            <param name="stateImageLocation">
		A <see cref="T:System.Drawing.Point"/> structure that represents the top-left corner of the state image. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateImageLocation"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.Node">
            <summary>
                <para>Gets the node whose images are being painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose images are being painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectImageIndex">
            <summary>
                <para>Gets or sets the index of the image displayed within the painted node when it is focused.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the image displayed within the node when it is focused.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectImageLocation">
            <summary>
                <para>Gets the location of the select image's top-left corner.
</para>
            </summary>
            <value>A <b>System.Drawing.Point</b> structure that represents the top-left point of the select image.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.SelectRect">
            <summary>
                <para>Gets the bounding rectangle of the select image.
</para>
            </summary>
            <value>A <b>System.Drawing.Rectangle</b> structure representing the bounding rectangle of the select image.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateImageIndex">
            <summary>
                <para>Gets or sets the index of the state image displayed within the node.
</para>
            </summary>
            <value>An integer value representing the node's state image's index.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateImageLocation">
            <summary>
                <para>Gets the location of the state image's top-left corner.
</para>
            </summary>
            <value>A <b>System.Drawing.Point</b> structure specifying the top-left corner of the state image.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeImagesEventArgs.StateRect">
            <summary>
                <para>Gets the bounding rectangle of the state image.
</para>
            </summary>
            <value>A <b>System.Drawing.Rectangle</b> structure that represents the state image's bounding rectangle.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeCellEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeCellEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeCell"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraEditors.Drawing.BaseEditPainter,DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraEditors.ViewInfo.BaseEditViewInfo,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted cell's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="painter">
		A <see cref="T:DevExpress.XtraEditors.Drawing.BaseEditPainter"/> object that provides facilities for painting a cell using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column containing the painted cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node in which the painted cell resides This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Node"/> property.

            </param>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraEditors.ViewInfo.BaseEditViewInfo"/> object which provides view information on the painted cell's editor. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.EditViewInfo"/> property.

            </param>
            <param name="focused">
		<b>true</b> if the painted cell has focus; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Focused"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.CellText">
            <summary>
                <para>Gets the painted cell's display text.
</para>
            </summary>
            <value>A string value representing the painted cell's display text.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.CellValue">
            <summary>
                <para>Gets the painted cell's value.
</para>
            </summary>
            <value>An object representing the painted cell's value.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Column">
            <summary>
                <para>Gets the painted cell's column.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column that contains the painted cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.EditPainter">
            <summary>
                <para>Gets the editor's painter object used to paint a cell.
</para>
            </summary>
            <value>A <b>BaseEditPainter</b> object providing a painting mechanism for the painted editor.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.EditViewInfo">
            <summary>
                <para>Gets the editor's view information.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ViewInfo.BaseEditViewInfo"/> object providing view information on the painted cell's editor.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Focused">
            <summary>
                <para>Gets a value indicating whether the painted cell has focus.
</para>
            </summary>
            <value><b>true</b> if the painted cell has focus; otherwise. <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeCellEventArgs.Node">
            <summary>
                <para>Gets the painted cell's node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that contains the painted cell.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeButtonEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeButton"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeButtonEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeButton"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeButton"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted expand button's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the node that contains the button being painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs.Node"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting the node's expand button using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs.Expanded">
            <summary>
                <para>Gets a value indicating whether the painted button's corresponding node is expanded.
</para>
            </summary>
            <value><b>true</b> if the painted button's corresponding node is expanded; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawNodeButtonEventArgs.Node">
            <summary>
                <para>Gets the painted button's corresponding node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node corresponding to the painted button.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawFooterEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooter"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawFooterEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooter"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawFooterCellEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooterCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawFooterCellEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooterCell"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs"/> that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooterCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.SummaryItemType,System.String,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted footer cell's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column whose footer cell is being painted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Column"/> property.


            </param>
            <param name="itemType">
		A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value specifying the type of summary whose value is displayed within the painted cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.ItemType"/> property.

            </param>
            <param name="text">
		A string value specifying the text to be displayed within the painted footer cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Text"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting a footer cell using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Column">
            <summary>
                <para>Gets a column whose footer cell is painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column whose footer cell is painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.ItemType">
            <summary>
                <para>Gets the type of summary whose value that is displayed within the painted cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumeration value specifying the type of summary whose value is displayed within the painted cell.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawFooterCellEventArgs.Text">
            <summary>
                <para>Gets or sets the text intended to be displayed within the painted footer cell.
</para>
            </summary>
            <value>A string value specifying the text to be displayed within the painted footer cell (a string representation of the resulting summary value). An empty string if summary is not applied to a column whose footer cell is being painted.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawEventArgs">

            <summary>
                <para>Provides basic data required to perform custom painting of an element.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject,DevExpress.Utils.Drawing.ObjectPainter)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawEventArgs"/> class with the specified <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted element's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting an element using the default mechanism. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance">
            <summary>
                <para>Gets the painted element's appearance settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object specifying the painted element's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds">
            <summary>
                <para>Gets the painted element's bounding rectangle.
</para>
            </summary>
            <value>A <b>System.Drawing.Rectangle</b> structure specifying the boundaries of the painted element.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache">
            <summary>
                <para>Gets an object specifying the storage for the most  used pens, fonts and brushes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Graphics">
            <summary>
                <para>Gets an object used to paint.
</para>
            </summary>
            <value>A <b>System.Drawing.Graphics</b> object used to paint.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Handled">
            <summary>
                <para>Gets or sets a value specifying whether default painting is prohibited.
</para>
            </summary>
            <value><b>true</b> if default painted is prohibited; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.ObjectArgs">
            <summary>
                <para>Gets an object containing information about the painted element.
</para>
            </summary>
            <value>An <b>ObjectInfoArgs</b> object providing information about the painted element.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Painter">
            <summary>
                <para>Gets the painter object that provides the default element's painting mechanism.
</para>
            </summary>
            <value>An <b>ObjectPainter</b> descendant providing the default painting mechanism for the painted element.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawColumnHeader"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawColumnHeader"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically, the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs"/> object that contains the event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CustomDrawColumnHeader"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted element's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Bounds"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which specifies the painted column header's appearance settings. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CustomDrawEventArgs.Appearance"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.Caption">
            <summary>
                <para>Gets or sets the text displayed within the painted column header.
</para>
            </summary>
            <value>A string value representing the painted column header's text. An empty string if the column button or the space not occupied by column headers is painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.CaptionRect">
            <summary>
                <para>Gets the rectangle where the column caption is intended to be drawn.
</para>
            </summary>
            <value>A <b>System.Drawing.Rectangle</b> structure specifying the bounding rectangle of the header's text.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.Column">
            <summary>
                <para>Gets the column whose header is painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column whose header is painted. <b>null</b> (<b>Nothing</b> in Visual Basic) if the column button or the space not occupied by column headers is currently painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.ColumnType">
            <summary>
                <para>Gets a value indicating the type of element to be painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.HitInfoType"/> enumeration value indicating the type of element to be painted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.HotTrack">
            <summary>
                <para>Gets a value indicating whether the painted column header is hot tracked.
</para>
            </summary>
            <value><b>true</b> if the painted column header is hot tracked; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.Pressed">
            <summary>
                <para>Gets a value indicating whether the painted element is pressed.
</para>
            </summary>
            <value><b>true</b> if the painted element is pressed by an end-user; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CustomDrawColumnHeaderEventArgs.SortShapeRect">
            <summary>
                <para>Gets the rectangle where the image indicating sort order is intended to be drawn.
</para>
            </summary>
            <value>A <b>System.Drawing.Rectangle</b> structure specifying the sort order image's bounding rectangle.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CellValueChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanged"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanging"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CellValueChangedEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CellValueChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanged"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanging"/> events.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CellValueChangedEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CellValueChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanged"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.CellValueChanging"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CellValueChangedEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Object)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.CellValueChangedEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column whose cell's value has been modified. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose cell's value has been modified. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="val">
		An object representing the new value assigned to the cell. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellValueChangedEventArgs.Value"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CellValueChangedEventArgs.Value">
            <summary>
                <para>Gets the new value assigned to a cell.
</para>
            </summary>
            <value>An object representing the newly assigned cell value.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CellEventArgs">

            <summary>
                <para>Provides data for events which require column and node as parameters to identify the cell being processed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CellEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.CellEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column to which the processed cell belongs. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CellEventArgs.Column"/> property.

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to which the processed cell belongs. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CellEventArgs.Column">
            <summary>
                <para>Gets a column to which the cell processed by an event belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column owning the cell being processed.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CalcNodeHeightEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeHeight"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CalcNodeHeightEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CalcNodeHeightEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeHeight"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CalcNodeHeightEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CalcNodeHeightEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CalcNodeHeight"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CalcNodeHeightEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.CalcNodeHeightEventArgs"/> class.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose height is calculated. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>
            <param name="nodeHeight">
		An integer value specifying the node height. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CalcNodeHeightEventArgs.NodeHeight"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CalcNodeHeightEventArgs.NodeHeight">
            <summary>
                <para>Gets or sets the current node's height in pixels.
</para>
            </summary>
            <value>An integer value specifying the node's height in pixels.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeFocusNodeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeFocusNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeFocusNodeEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.BeforeFocusNodeEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeFocusNode"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control).

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.BeforeFocusNodeEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeFocusNodeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeFocusNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeFocusNodeEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.BeforeFocusNodeEventArgs"/> class.
</para>
            </summary>
            <param name="old">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node from which focus is shifting. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs.OldNode"/> property.
 

            </param>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node which is about to obtain focus. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.NodeEventArgs.Node"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.BeforeFocusNodeEventArgs.CanFocus">
            <summary>
                <para>Gets or sets a value indicating whether focus is allowed to be moved.
</para>
            </summary>
            <value><b>true</b> if the focus is allowed to be moved; otherwise <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListMultiSelection">

            <summary>
                <para>Represents a collection of selected nodes for the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.#ctor(DevExpress.XtraTreeList.TreeList)">
            <summary>
                <para>Creates a collection of selected nodes for a specific <see cref="T:DevExpress.XtraTreeList.TreeList"/> object.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which the collection belongs.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Add(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Adds the specified node to the collection of selected nodes.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be added.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Add(System.Collections.IEnumerable)">
            <summary>
                <para>Selects the specified collection of nodes.
</para>
            </summary>
            <param name="nodes">
		An object implementing the <b>IEnumerable</b> interface which holds the collection of nodes to be selected.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.AddRemove(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Toggles the specified node between the selected and deselected states.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be selected or deselected.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Clear">
            <summary>
                <para>Clears the current selection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Contains(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Specifies whether a specific node is in the collection of selected nodes.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object to check.

            </param>
            <returns><b>true</b>, if a node is in the collection of selected nodes; otherwise, <b>false</b>
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.IndexOf(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Determines the index of a specific <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object in the collection of selected nodes.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object for which to return the position within the collection of selected nodes.

            </param>
            <returns>The zero-based index of a specific <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object in the collection of selected nodes.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListMultiSelection.Item(System.Int32)">
            <summary>
                <para>Gets a <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object from the collection of selected nodes referenced by the specified index.
</para>
            </summary>
            <param name="index">
		The index of the selected node to retrieve.

            </param>
            <value>A selected node referenced by the specified index.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Remove(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Removes a specific <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object from the collection of selected nodes.
</para>
            </summary>
            <param name="node">
		The node to remove from the collection of selected nodes.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Set(System.Collections.IEnumerable)">
            <summary>
                <para>Removes all nodes from the collection of selected nodes and then adds a specific group of nodes to the collection.
</para>
            </summary>
            <param name="nodes">
		A group of nodes to select.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListMultiSelection.Set(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Removes all nodes from the collection of selected nodes and then adds a specific node to the collection.
</para>
            </summary>
            <param name="node">
		The node to select.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.Nodes.TreeListNodes">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> objects in the <see cref="T:DevExpress.XtraTreeList.TreeList"/> component.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.#ctor(DevExpress.XtraTreeList.TreeList)">
            <summary>
                <para>Creates a collection of nodes at the root level of the tree list.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> instance to which the node collection belongs.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.#ctor(DevExpress.XtraTreeList.TreeList,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Creates a collection of child nodes for a specific parent node.
</para>
            </summary>
            <param name="treeList">
		A <see cref="T:DevExpress.XtraTreeList.TreeList"/> class instance to which the collection belongs.

            </param>
            <param name="parentNode">
		The node for which to create a collection of child nodes.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Add(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Adds a node to the node collection.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance to add to the node collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Add(System.Object)">
            <summary>
                <para>Appends a new node to the end of the TreeListNodes collection and initializes the node with the specified data.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <returns>The new <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Add(System.Object[])">
            <summary>
                <para>Adds a new child <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> containing the specified values. 
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells. 

            </param>
            <returns>The created <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.AutoFilterNode">
            <summary>
                <para>Gets the Auto Filter Row element. 
</para>
            </summary>
            <value>A TreeListAutoFilterNode object that is the Auto Filter Row.

</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Clear">
            <summary>
                <para>Removes all elements from the current node collection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.CopyTo(System.Array,System.Int32)">
            <summary>
                <para>Copies the entire nodes collection to a one dimensional array starting at the specified index of the target array.
</para>
            </summary>
            <param name="array">
		A one-dimensional array that is the destination of nodes copied from the collection.

            </param>
            <param name="index">
		An integer value specifying the zero-based index in the target array at which copying begins.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.Count">
            <summary>
                <para>Gets the number of nodes in the current node collection, without taking into account children of these nodes.
</para>
            </summary>
            <value>The number of nodes in the current node collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.FirstNode">
            <summary>
                <para>Gets the first element in the collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the first node in the collection. <b>null</b> (<b>Nothing</b> in Visual Basic) if the collection is empty.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.GetEnumerator">
            <summary>
                <para>Gets the enumerator used to iterate through the node collection.
</para>
            </summary>
            <returns>An enumerator through which the node collection can be iterated.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.IndexOf(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns the index of a specified node within the node collection.
</para>
            </summary>
            <param name="node">
		The node to locate in the collection.

            </param>
            <returns>The index of a specific node within the node collection.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.IsSynchronized">
            <summary>
                <para>Gets a value indicating whether access to the nodes collection is synchronized (thread-safe).
</para>
            </summary>
            <value><b>true</b> if access to the nodes collection is synchronized (thread-safe); otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.Item(System.Int32)">
            <summary>
                <para>Obtains the <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object by a specific index in the current node collection. 
</para>
            </summary>
            <param name="index">
		The zero-based index of the node to locate. The index is a value between 0 and <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.Count"/> - 1.

            </param>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> child at the specified index within the node collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.LastNode">
            <summary>
                <para>Gets the last node in the node collection.
</para>
            </summary>
            <value>The last node in the node collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.ParentNode">
            <summary>
                <para>Gets the parent of the node collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance that represents the parent of the node collection.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.Remove(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Removes a specific node from the node collection.
</para>
            </summary>
            <param name="node">
		The node to remove from the node collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNodes.RemoveAt(System.Int32)">
            <summary>
                <para>Removes a node referenced by the specified index from the node collection.
</para>
            </summary>
            <param name="index">
		The index of the node to remove from the node collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.SyncRoot">
            <summary>
                <para>Gets an object that can be used to synchronize access to the nodes collection.
</para>
            </summary>
            <value>An object that can be used to synchronize access to the nodes collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNodes.TreeList">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which the node collection belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which the node collection belongs.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Nodes.TreeListNode">

            <summary>
                <para>Represents a node of the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.CheckAll">
            <summary>
                <para>Checks the current node and all its nested nodes (including all their children).
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Checked">
            <summary>
                <para>Gets or sets whether the node is checked.
</para>
            </summary>
            <value><b>true</b> if the node is checked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.CheckState">
            <summary>
                <para>Gets or sets the node's check state.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.CheckState"/> enumeration value that specifies the node's check state.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.Clone">
            <summary>
                <para>Returns a copy of this node.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing this node's copy.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Data">
            <summary>
                <para>Gets or sets a value that can be freely used as your specific programming needs dictate.
</para>
            </summary>
            <value>A value of the <b>object</b> type bound to the current node.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.ExpandAll">
            <summary>
                <para>Expands all the child nodes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Expanded">
            <summary>
                <para>Specifies whether the current node is expanded.
</para>
            </summary>
            <value><b>true</b> if the current node is expanded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.FirstNode">
            <summary>
                <para>Gets the first child node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the first child node. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node has no child nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Focused">
            <summary>
                <para>Gets whether the node is currently focused.
</para>
            </summary>
            <value><b>true</b> if the node is focused; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.GetDisplayText(System.Object)">
            <summary>
                <para>Returns the text representation of a specific column value.
</para>
            </summary>
            <param name="columnID">
		An object that identifies a column.

            </param>
            <returns>A text string that represents a specific column value.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.GetValue(System.Object)">
            <summary>
                <para>Returns the value for a specific column.
</para>
            </summary>
            <param name="columnID">
		An object that identifies a column.

            </param>
            <returns>The value of a specific column.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.HasAsParent(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Determines whether the current node has a <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance as a parent.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance to check.

            </param>
            <returns><b>true</b> if the current node has a <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance as a parent; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.HasChildren">
            <summary>
                <para>Gets or sets a value indicating whether a node has children.
</para>
            </summary>
            <value><b>true</b>, if a node has children; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Id">
            <summary>
                <para>Gets the index of the corresponding record within the associated data source.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the corresponding data source record.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.ImageIndex">
            <summary>
                <para>Gets or sets the index of the image displayed within the node.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the image displayed within the node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Item(System.Object)">
            <summary>
                <para>Gets or sets a value for a specific column.
</para>
            </summary>
            <param name="columnID">
		An object that identifies a column.

            </param>
            <value>The value for a specific column.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.LastNode">
            <summary>
                <para>Gets the last child node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the last child node. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node has no child nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Level">
            <summary>
                <para>Returns the nesting level of a node.
</para>
            </summary>
            <value>The nesting level of a node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.NextNode">
            <summary>
                <para>Returns the next node in the collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the next node in the collection. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node is the last node in the collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.NextVisibleNode">
            <summary>
                <para>Returns the next visible node after the current node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the next visible node after the current one. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node is the last visible node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Nodes">
            <summary>
                <para>Gets the collection of the node's children.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> object representing the node's child nodes collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.ParentNode">
            <summary>
                <para>Gets the parent node of the current tree node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> instance that represents the parent of the current tree node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.PrevNode">
            <summary>
                <para>Gets the previous node in the collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the previous node in the collection. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node is the first node in the collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.PrevVisibleNode">
            <summary>
                <para>Returns the visible node located prior to the current node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object which represents the visible node located prior to the current one. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current node is the first node in the tree list's <see cref="P:DevExpress.XtraTreeList.TreeList.Nodes"/> collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.RootNode">
            <summary>
                <para>Gets a node at the root level which contains the current node as a child.
</para>
            </summary>
            <value>The node at the root level which contains the current node as a child.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Selected">
            <summary>
                <para>Gets or sets whether the current node is selected.
</para>
            </summary>
            <value><b>true</b> if the node is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.SelectImageIndex">
            <summary>
                <para>Gets or sets the index of the image displayed within the node when it is focused.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the image displayed within the node when it is focused.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.SetValue(System.Object,System.Object)">
            <summary>
                <para>Sets the value for a specific column.
</para>
            </summary>
            <param name="columnID">
		An object that identifies a column.

            </param>
            <param name="val">
		The value to set for a specific column in the current node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.StateImageIndex">
            <summary>
                <para>Gets or sets the index of the state image displayed within the node.
</para>
            </summary>
            <value>An integer value representing the node's state image index.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Tag">
            <summary>
                <para>Gets or sets the data associated with a tree list node. 
</para>
            </summary>
            <value>An object containing information associated with the tree list node.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.TreeList">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which the current node belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object to which the current node belongs.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Nodes.TreeListNode.UncheckAll">
            <summary>
                <para>Unchecks the current node and all its nested nodes (including all their children).
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Visible">
            <summary>
                <para>Gets or sets whether the node is visible.
</para>
            </summary>
            <value><b>true</b> if the node is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.Columns.TreeListColumn">

            <summary>
                <para>Represents a single column for the XtraTreeList.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumn.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> class instance.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.AbsoluteIndex">
            <summary>
                <para>Gets or sets a column's position within the tree list's column collection.
</para>
            </summary>
            <value>An integer value representing the column's zero based index within the collection.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.AllNodesSummary">
            <summary>
                <para>Specifies whether a summary is calculated against all XtraTreeList nodes or against root nodes only.
</para>
            </summary>
            <value><b>true</b> if a summary is calculated against all XtraTreeList nodes; otherwise, it is calculated only against root nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.AllowIncrementalSearch">
            <summary>
                <para>Gets or sets whether incremental searches can be performed against the column.
</para>
            </summary>
            <value><b>true</b> if incremental searches can be performed against the column; otherwise, false.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.AppearanceCell">
            <summary>
                <para>Gets the appearance settings used to paint the column's cells.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> object that provides the appearance settings used to paint the column cells.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.AppearanceHeader">
            <summary>
                <para>Gets the appearance settings used to paint the column header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the column header's appearance settings.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumn.BestFit">
            <summary>
                <para>Adjusts the <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> width to display the contents of a cell as best as possible.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Caption">
            <summary>
                <para>Gets or sets the column caption displayed in the <b>HeaderPanel</b> of the XtraTreeList.
</para>
            </summary>
            <value>Column caption.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ColumnEdit">
            <summary>
                <para>Gets or sets the repository item that specifies the editor used to edit a column's cell values.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ColumnEditName">
            <summary>
                <para>Gets or sets the name of the editor used for this column's cells.
</para>
            </summary>
            <value>A string value specifying the column cell's editor name.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ColumnHandle">
            <summary>
                <para>Gets the position of the associated field within the data source.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the corresponding field within the data source.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ColumnType">
            <summary>
                <para>Gets the column's data type.
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> value that indicates the column's data type.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.CustomizationCaption">
            <summary>
                <para>Gets or sets the column's caption when its header is displayed within the Customization Form. 
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the column's caption when its header is displayed within the Customization form.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.FieldName">
            <summary>
                <para>Gets or sets the field name assigned to the current column from a datasource.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of a data field.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.FilterInfo">
            <summary>
                <para>Gets an object specifying custom filter criteria.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumnFilterInfo"/> object specifying the filter criteria to be applied.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.FilterMode">
            <summary>
                <para>Gets or sets how column values are filtered via the Auto Filter Row and Column's Filter DropDown.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraGrid.ColumnFilterMode"/> enumeration value that specifies how the column values are filtered.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Fixed">
            <summary>
                <para>Gets or sets whether the column is anchored to a tree list's edge.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.FixedStyle"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Format">
            <summary>
                <para>Gets the object that specifies the formatting applied to column values.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.FormatInfo"/> object that specifies formatting settings for column values.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumn.GetCaption">
            <summary>
                <para>Returns the column's display caption.
</para>
            </summary>
            <returns>A string that specifies the column's display caption.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.Columns.TreeListColumn.GetTextCaption">
            <summary>
                <para>Returns the column's plain text caption.
</para>
            </summary>
            <returns>A string value that represents the column's plain text caption.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ImageAlignment">
            <summary>
                <para>Gets or sets the alignment of the column header's image.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.StringAlignment"/> enumeration member which specifies the image's alignment.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ImageIndex">
            <summary>
                <para>Gets or sets the index of the image displayed within the column header.
</para>
            </summary>
            <value>An integer value specifying the image by its zero-based index within the source collection. <b>-1</b> to not display an image.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Images">
            <summary>
                <para>Gets the source of images that can be displayed within column headers.
</para>
            </summary>
            <value>An object which represents the source of the images that can be displayed within column headers.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.MinWidth">
            <summary>
                <para>Gets the minimum column width.
</para>
            </summary>
            <value>An <b>Integer</b> value that determines the minimum column width.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.MRUFilters">
            <summary>
                <para>Provides access to the column's recently used filters.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListFilterInfoCollection"/> object that stores the recently used filters for the column.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Name">
            <summary>
                <para>Gets or sets the column's name.
</para>
            </summary>
            <value>A string value representing the column name.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.OptionsColumn">
            <summary>
                <para>Provides access to the column's options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListOptionsColumn"/> object which contains the column's options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.OptionsFilter">
            <summary>
                <para>Provides access to the column's filter options.
</para>
            </summary>
            <value>An object that contains the column's filter options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.ReadOnly">
            <summary>
                <para>Gets a value indicating whether data within a column can be edited.
</para>
            </summary>
            <value><b>true</b> if column data cannot be changed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.RowFooterSummary">
            <summary>
                <para>Specifies the type of the summary calculated against child nodes of every parent node in a column.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumerator value.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.RowFooterSummaryStrFormat">
            <summary>
                <para>Gets or sets the format to display the <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.RowFooterSummary"/>.
</para>
            </summary>
            <value>A string that specifies the format applied to the <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.RowFooterSummary"/>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SortIndex">
            <summary>
                <para>Gets the index of the column in the sorting columns list.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the column within the sorting columns list.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SortOrder">
            <summary>
                <para>Gets or sets the column's sort order.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.ColumnSortOrder"/> enumeration value specifying the column's sort order.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.StyleName">
            <summary>
                <para>Gets or sets the name of the style for the current column.
</para>
            </summary>
            <value>A string representing the name of the style set for the current column.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SummaryFooter">
            <summary>
                <para>Specifies the type of the summary to calculate against all nodes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.SummaryItemType"/> enumerator value that specifies the summary type applied.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SummaryFooterStrFormat">
            <summary>
                <para>Gets or sets the format to display the <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SummaryFooter"/>.
</para>
            </summary>
            <value>A string that specifies the format applied to the <see cref="P:DevExpress.XtraTreeList.Columns.TreeListColumn.SummaryFooter"/>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Tag">
            <summary>
                <para>Gets or sets the column's associated data.
</para>
            </summary>
            <value>An object representing data associated with the column.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.TreeList">
            <summary>
                <para>Obtains the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control to which a column belongs.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> control to which a column belongs.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.UnboundType">
            <summary>
                <para>Gets or sets the type of data the column displays in Unbound Mode.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Data.UnboundColumnType"/> value that specifies the type of data the column displays.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Visible">
            <summary>
                <para>Gets or sets whether the column is visible.
</para>
            </summary>
            <value><b>true</b> if the column is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.VisibleIndex">
            <summary>
                <para>Gets or sets the position in the tree list where the current column is displayed.
</para>
            </summary>
            <value>An <b>Integer</b> value that specifies the position of the current column in the tree list.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.VisibleWidth">
            <summary>
                <para>Gets the visible width of the column.
</para>
            </summary>
            <value>An integer value representing the visible column width.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.Columns.TreeListColumn.Width">
            <summary>
                <para>Gets or sets the column width in pixels.
</para>
            </summary>
            <value>An integer value representing the column width in pixels.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeListHitInfo">

            <summary>
                <para>Represents a class that contains information about what is located at a specific point within the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeListHitInfo.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.TreeListHitInfo"/> class instance.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListHitInfo.Bounds">
            <summary>
                <para>Gets the limits for the tree list element that contains the test point.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure representing the element's bounding rectangle.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListHitInfo.Column">
            <summary>
                <para>Gets the column located under the test point.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column that contains the test point.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListHitInfo.HitInfoType">
            <summary>
                <para>Gets the visual element located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.HitInfoType"/> enumeration value identifying the type of visual element at the test point.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListHitInfo.MousePoint">
            <summary>
                <para>Gets the coordinates of the point under the mouse cursor relative to the XtraTreeList control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> object specifying the mouse cursor's position relative to the upper left corner of a tree list control.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeListHitInfo.Node">
            <summary>
                <para>Gets the node located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing a node located under the test point.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.TreeList">

            <summary>
                <para>Represents the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.TreeList"/> class instance.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.About">
            <summary>
                <para>Brings up the <b>About</b> dialog.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AccessibleNotifyClients(System.Windows.Forms.AccessibleEvents,System.Int32,System.Int32)">
            <summary>
                <para>Notifies accessibility client applications of the specified <see cref="T:System.Windows.Forms.AccessibleEvents"/> event for the specified child control. 

</para>
            </summary>
            <param name="accEvent">
		An <see cref="T:System.Windows.Forms.AccessibleEvents"/> event being fired.

            </param>
            <param name="objectId">
		The identifier of the AccessibleObject.

            </param>
            <param name="childId">
		A child <see cref="T:System.Windows.Forms.Control"/> which fires the event.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ActiveEditor">
            <summary>
                <para>Gets the currently active cell editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> descendant representing the currently active editor. <b>null</b> (<b>Nothing</b> in Visual Basic) if no cell is being edited at the moment.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ActiveFilterCriteria">
            <summary>
                <para>Gets or sets the current filter criteria.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which is the total filter criteria.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ActiveFilterEnabled">
            <summary>
                <para>Gets or sets whether the filtering functionality is enabled.
</para>
            </summary>
            <value><b>true</b> to enable filtering; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ActiveFilterInfo">
            <summary>
                <para>Gets the total filter expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListFilterInfo"/> object specifying the total filter expression.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ActiveFilterString">
            <summary>
                <para>Gets or sets the total filter expression for the TreeList.
</para>
            </summary>
            <value>A string that specifies the total filter expression applied to the TreeList.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AddFilter(System.String)">
            <summary>
                <para>Adds a predefined filter with a string filtering condition to the current TreeList.
</para>
            </summary>
            <param name="filterString">
		A String value that is the filtering condition.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AddFilter(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Adds a CriteriaOperator-based filter to the current TreeList.
</para>
            </summary>
            <param name="filterCriteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that contains a filtering condition.


            </param>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.AfterCheckNode">
            <summary>
                <para>Occurs after a node's check state has been changed by an end-user.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.AfterCollapse">
            <summary>
                <para>Fires immediately after a node has been collapsed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.AfterDragNode">
            <summary>
                <para>Fires immediately after a drag-and-drop operation on a node has been successfully completed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.AfterExpand">
            <summary>
                <para>Fires immediately after a node has been expanded.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.AfterFocusNode">
            <summary>
                <para>Fires in response to focus being moved between nodes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.AllNodesCount">
            <summary>
                <para>Returns the total number of nodes within the <b>TreeList</b>.
</para>
            </summary>
            <value>An integer value indicating the total number of nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the tree list's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListAppearanceCollection"/> object which provides the appearance settings for the tree list's elements.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.AppearancePrint">
            <summary>
                <para>Provides access to the properties that specify the appearances of the tree list's elements when the tree list is printed and exported.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListPrintAppearanceCollection"/> object which provides the appearance settings used to paint the tree list's elements when the tree list is printed and exported.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Adds a new <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> containing the specified values to the XtraTreeList.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNode">
		A parent node of the added one.


            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the added node. 
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,System.Int32)">
            <summary>
                <para>Appends a new node containing the specified values to the specified node's child collection.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNodeId">
		An integer value specifying the parent node's identifier.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object or descendant representing the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Object)">
            <summary>
                <para>Adds a <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> containing the specified values to the XtraTreeList.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNode">
		A parent node for the added one.

            </param>
            <param name="tag">
		An object that contains information associated with the tree list node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Tag"/> property.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object or descendant representing the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,System.Int32,System.Object)">
            <summary>
                <para>Adds a <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> containing the specified values to the XtraTreeList.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNodeId">
		An integer value specifying the identifier of the parent node.

            </param>
            <param name="tag">
		An object that contains information associated with the tree list node. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Tag"/> property.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object or descendant representing the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,System.Int32,System.Int32,System.Int32,System.Int32,System.Windows.Forms.CheckState)">
            <summary>
                <para>Appends a new node with the specified settings.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNodeId">
		An integer value that identifies the parent node.

            </param>
            <param name="imageIndex">
		A zero-based index of the image displayed within the node.

            </param>
            <param name="selectImageIndex">
		A zero-based index of the image displayed within the node when it is focused or selected.

            </param>
            <param name="stateImageIndex">
		An integer value that specifies the index of the node's state image.

            </param>
            <param name="checkState">
		The node's check state.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that represents the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,System.Int32,System.Windows.Forms.CheckState)">
            <summary>
                <para>Appends a new node with the specified settings.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNodeId">
		An integer value that identifies the parent node.

            </param>
            <param name="checkState">
		The node's check state.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that represents the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Windows.Forms.CheckState)">
            <summary>
                <para>Appends a new node with the specified settings.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that represents the parent node.

            </param>
            <param name="checkState">
		The node's check state.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that represents the added node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.AppendNode(System.Object,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
                <para>Appends a new node with the specified settings.
</para>
            </summary>
            <param name="nodeData">
		An array of values or a <see cref="T:System.Data.DataRow"/> object, used to initialize the created node's cells.

            </param>
            <param name="parentNodeId">
		An integer value specifying the parent node's identifier.

            </param>
            <param name="imageIndex">
		A zero-based index of the image displayed within the node.

            </param>
            <param name="selectImageIndex">
		A zero-based index of the image displayed within the node when it is focused or selected.

            </param>
            <param name="stateImageIndex">
		An integer value that specifies the index of the node's state image.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the added node. 
</returns>


        </member>
        <member name="F:DevExpress.XtraTreeList.TreeList.AutoFilterNodeId">
            <summary>
                <para>Contains a value which specifies the Auto Filter Row's ID. 
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeCheckNode">
            <summary>
                <para>Occurs before a node's check state is changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeCollapse">
            <summary>
                <para>Fires before a node is collapsed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeDragNode">
            <summary>
                <para>Fires when an attempt to drag a node is performed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeExpand">
            <summary>
                <para>Fires before a node is expanded.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeFocusNode">
            <summary>
                <para>Fires before a node is focused.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.BeforeLoadLayout">
            <summary>
                <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.BeginSort">
            <summary>
                <para>Prevents the tree structure from being changed when changing specific options affecting the order of nodes, until the <see cref="M:DevExpress.XtraTreeList.TreeList.EndSort"/> method is called.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.BeginUnboundLoad">
            <summary>
                <para>In unbound mode, prevents updates of 
the tree structure due to adding, deleting and modifying nodes, until the <see cref="M:DevExpress.XtraTreeList.TreeList.EndUnboundLoad"/> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.BeginUpdate">
            <summary>
                <para>Locks the TreeList, preventing visual updates of the object and its elements until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.BestFitColumns">
            <summary>
                <para>Modifies the width of each visible column so that the contents of its cells are not truncated, if possible.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.BestFitColumns(System.Boolean)">
            <summary>
                <para>Modifies the width of each visible column so that the contents of its cells are not truncated, if possible.
</para>
            </summary>
            <param name="applyAutoWidth">
		<b>true</b> if the total width of columns must be equal to the control's width after resizing; otherwise <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.BestFitVisibleOnly">
            <summary>
                <para>Gets or sets a value specifying which nodes take part in calculations when applying best fit to columns.
</para>
            </summary>
            <value><b>true</b> to implement best fit taking into account only nodes not hidden within collapsed groups; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.BorderStyle">
            <summary>
                <para>Gets or sets the border style for the tree list.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value specifying the border style of a tree list.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CalcHitInfo(System.Drawing.Point)">
            <summary>
                <para>Returns information about the tree list's elements which are located at the specified point.
</para>
            </summary>
            <param name="pt">
		A <see cref="T:System.Drawing.Point"/> structure which specifies test point coordinates relative to the tree list's top-left corner.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.TreeListHitInfo"/> object which contains information about the tree list's elements located at the test point.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CalcNodeDragImageIndex">
            <summary>
                <para>Enables you to specify a custom image to be displayed in front of nodes when dragging.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CalcNodeHeight">
            <summary>
                <para>Allows you to assign custom node height.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CancelCurrentEdit">
            <summary>
                <para>Cancels changes made to focused node cells.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CancelUpdate">
            <summary>
                <para>Unlocks the TreeList object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.CanShowEditor">
            <summary>
                <para>Gets a value indicating whether the current cell's editor can be invoked.
</para>
            </summary>
            <value><b>true</b> if the current cell's editor can be invoked; otherwise <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CellValueChanged">
            <summary>
                <para>Fires immediately after a cell editor is closed if a cell's value has been modified.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CellValueChanging">
            <summary>
                <para>Fires when an end-user starts to edit a cell value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CheckAll">
            <summary>
                <para>Checks all nodes in the TreeList control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ClearColumnErrors">
            <summary>
                <para>Removes the error descriptions for a focused node.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ClearColumnFilter(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Removes the filter condition applied to the specified column.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object specifying the column which filter condition is to be discarded.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ClearColumnsFilter">
            <summary>
                <para>Removes the filter criteria applied to the TreeList.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ClearNodes">
            <summary>
                <para>Removes all nodes from the XtraTreeList.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ClearSorting">
            <summary>
                <para>Clears all the sorting that has been applied to an XtraTreeList control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CloseEditor">
            <summary>
                <para>Hides the active editor, saving changes that were made.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CollapseAll">
            <summary>
                <para>Collapses all the nodes in the tree list.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ColumnButtonClick">
            <summary>
                <para>Fires when the column button is clicked.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ColumnChanged">
            <summary>
                <para>Fires immediately after the column collection or an individual column has been modified.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ColumnFilterChanged">
            <summary>
                <para>Occurs when a column's filter condition is changed. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ColumnPanelRowHeight">
            <summary>
                <para>Gets or sets the height of the column header panel, in pixels.
</para>
            </summary>
            <value>An integer value specifying the column panel height in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.Columns">
            <summary>
                <para>Provides access to a tree list's column collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumnCollection"/> object representing a collection of all the columns within a tree list.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ColumnsCustomization">
            <summary>
                <para>Invokes the customization form.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ColumnsCustomization(System.Drawing.Point)">
            <summary>
                <para>Invokes the customization form at the specified screen location.
</para>
            </summary>
            <param name="location">
		A structure which specifies the position of the customization form's top-left corner. The point is set in screen coordinates.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ColumnsImageList">
            <summary>
                <para>Gets or sets the source of images that can be displayed within column headers.
</para>
            </summary>
            <value>An object that is the source of images that can be displayed within column headers.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ColumnWidthChanged">
            <summary>
                <para>Fires after a column's width has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CompareNodeValues">
            <summary>
                <para>Enables you to perform custom sorting.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CopyNode(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Boolean)">
            <summary>
                <para>Copies the specified node to the specified node's child collection.
</para>
            </summary>
            <param name="sourceNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be copied.

            </param>
            <param name="destinationNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to whose child collection the node is copied.

            </param>
            <param name="cloneChildren">
		<b>true</b> if child nodes of the node being copied must be cloned; otherwise <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the copy of the specified node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CopyNode(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Boolean,System.Boolean)">
            <summary>
                <para>Copies the specified node to the specified node's child collection.
</para>
            </summary>
            <param name="sourceNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be copied.

            </param>
            <param name="destinationNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to whose child collection the node is copied.

            </param>
            <param name="cloneChildren">
		<b>true</b> if child nodes of the node being copied must be cloned; otherwise <b>false</b>.

            </param>
            <param name="modifySource">
		<b>true</b> if the destination node's key field value is assigned to the parent field of the source node and all its copies; otherwise <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the copy of the specified node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CopyToClipboard">
            <summary>
                <para>Copies selected nodes to the clipboard.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.CreateCustomization">
            <summary>
                <para>Invokes the customization form.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CreateCustomNode">
            <summary>
                <para>Provides the ability to create custom nodes.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawColumnHeader">
            <summary>
                <para>Provides the ability to custom paint column headers and the column button.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawEmptyArea">
            <summary>
                <para>Enables you to custom paint a tree list's empty area.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawFilterPanel">
            <summary>
                <para>Enables you to custom paint the Filter Panel. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooter">
            <summary>
                <para>Gives you the ability to perform custom painting of the footer panel.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawFooterCell">
            <summary>
                <para>Gives you the ability to perform custom painting of column footer cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeButton">
            <summary>
                <para>Gives you the ability to perform custom painting of expanding/collapsing node buttons.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeCell">
            <summary>
                <para>Provides the ability to perform custom painting of node cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeCheckBox">
            <summary>
                <para>Enables you to manually paint check boxes displayed within nodes.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeImages">
            <summary>
                <para>Provides the ability to perform custom painting of node state and select images.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeIndent">
            <summary>
                <para>Enables tree indents to be painted manually.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodeIndicator">
            <summary>
                <para>Provides the ability to perform custom painting of indicator cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawNodePreview">
            <summary>
                <para>Provides the ability to perform custom painting of preview sections.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooter">
            <summary>
                <para>Provides the ability to perform custom painting of row footers.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomDrawRowFooterCell">
            <summary>
                <para>Provides the ability to perform custom painting of row footer cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomFilterDisplayText">
            <summary>
                <para>Allows the text displayed within the Filter Panel to be customized.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.CustomizationForm">
            <summary>
                <para>Gets the object representing the customization form.
</para>
            </summary>
            <value>A <b>DevExpress.XtraTreeList.Columns.TreeListCustomizationForm</b> object that represents the customization form. <b>null</b> (<b>Nothing</b> in Visual Basic) if the customization form is currently closed.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.CustomizationFormBounds">
            <summary>
                <para>Gets or sets the boundaries of the customization form.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure representing the the customization form's boundaries.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.CustomizationRowCount">
            <summary>
                <para>Gets or sets the number column headers that can be simultaneously visible in the customization form.
</para>
            </summary>
            <value>An integer value specifying the number of column headers that can be displayed in the customization form.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomNodeCellEdit">
            <summary>
                <para>Enables editors to be assigned to cells on an individual basis.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.CustomNodeCellEditForEditing">
            <summary>
                <para>Allows you to assign a custom editor to a column for in-place editing and override the default column editor, which is by default used both in display and edit modes.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.DataMember">
            <summary>
                <para>Gets or sets a specific list in a data source whose data is displayed by the <b>TreeList</b> control.
</para>
            </summary>
            <value>A string value specifying a list in a data source.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.DataSource">
            <summary>
                <para>Gets or sets the object used as the data source for the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.

</para>
            </summary>
            <value>The object used as the data source. 

</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.DefaultPaintHelperChanged">
            <summary>
                <para>Fires when a different paint helper is assigned to the <b>TreeList</b> control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.DeleteNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Removes a specific <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> from the XtraTreeList.
</para>
            </summary>
            <param name="node">
		The removed <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/>.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.DeleteSelectedNodes">
            <summary>
                <para>Deletes the selected nodes in multiple selection mode or focused nodes in single selection mode.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.DestroyCustomization">
            <summary>
                <para>Closes the customization form.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.DoIncrementalSearch(System.Boolean)">
            <summary>
                <para>Continues the incremental search that is in progress in the specified direction.
</para>
            </summary>
            <param name="down">
		<b>true</b> to continue the incremental search forward; <b>false</b> to continue the incremental search backward.

            </param>
            <returns><b>true</b> if another node with the specified search string has been found and focused; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.DragCancelNode">
            <summary>
                <para>Fires immediately after a drag-and-drop operation over a node has been cancelled.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.DragExpandDelay">
            <summary>
                <para>Gets or sets the amount of time (in milliseconds) before the node is expanded when another node is dragged over it.
</para>
            </summary>
            <value>An integer value specifying the delay (in milliseconds) from the time a dragged node begins hovering over a collapsed node to the time that the node is expanded.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.DragNodesMode">
            <summary>
                <para>Gets or sets how end-users can drag and drop nodes.
</para>
            </summary>
            <value>A TreeListDragNodesMode value that specifies the node drag mode.

</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.DragObjectDrop">
            <summary>
                <para>Fires immediately after the drag and drop operation has been completed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.DragObjectOver">
            <summary>
                <para>Enables you to control whether the dragged column header can be dropped at the current position.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.DragObjectStart">
            <summary>
                <para>Fires when an end-user attempts to drag a column header.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.EditingValue">
            <summary>
                <para>Gets or sets the cell value being edited.
</para>
            </summary>
            <value>An object representing the currently edited cell value. <b>null</b> (<b>Nothing</b> in Visual Basic) if editing is not being performed at the moment.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.EndCurrentEdit">
            <summary>
                <para>Posts the focused node's values to the data source.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.EndSort">
            <summary>
                <para>Forces the control to update itself after the <see cref="M:DevExpress.XtraTreeList.TreeList.BeginSort"/> method has been called.


</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.EndSorting">
            <summary>
                <para>Fires after a sorting operation has finished. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.EndUnboundLoad">
            <summary>
                <para>Forces the control to update itself after the <see cref="M:DevExpress.XtraTreeList.TreeList.BeginUnboundLoad"/> method has been called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.EndUpdate">
            <summary>
                <para>Unlocks the TreeList object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExpandAll">
            <summary>
                <para>Expands all nodes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToHtml(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data as HTML and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToHtml(System.String)">
            <summary>
                <para>Exports the tree list's data to the specified file as HTML.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value which contains the full path (including the file name and extension) specifying where the HTML file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in HTML format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in HTML format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToMht(System.String,System.String)">
            <summary>
                <para>Exports the control's data to a file in MHT format using the specified character encoding.

</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document  will be exported


            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. "UTF-8"). 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToMht(System.String,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the control's data to a file in MHT format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.

</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document will be exported .


            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. "UTF-8"). 

            </param>
            <param name="title">
		A <see cref="T:System.String"/> containing the name shown as the title of the created document.

            </param>
            <param name="compressed">
		<b>true</b> if the MHT code is compressed (secondary characters e.g. spaces are removed); otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToMht(System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the control's data to a stream in MHT format using the specified character encoding, with the specified title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document will be exported.


            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> representing the encoding name set in the output document (e.g. "UTF-8"). 

            </param>
            <param name="title">
		A <see cref="T:System.String"/> containing the name shown as the title of the created document.

            </param>
            <param name="compressed">
		<b>true</b> if the MHT code is compressed (secondary characters e.g. spaces are removed); otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in MHT format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in MHT format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToPdf(System.String)">
            <summary>
                <para>Exports the tree list's data to the specified PDF file.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) to where the PDF file will be created.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToPdf(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data to a PDF document and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in PDF format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in PDF format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToRtf(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data as Rich Text and sends it to the specified stream. 
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to. 

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToRtf(System.String)">
            <summary>
                <para>Exports the tree list's data to the specified RTF file.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) to where the RTF file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToText(System.String)">
            <summary>
                <para>Exports the tree list's data to a text file at the specified path. 
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) to where the text file will be created.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToText(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data to a text document and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in Text format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in Text format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXls(System.String)">
            <summary>
                <para>Exports the tree list's data to the specified file as XLS.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value which specifies the full path (including the file name and extension) to where the XLS file will be created.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXls(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data as XLS and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in XLS format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in XLS format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports data to the specified stream in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports data to a file in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXlsx(System.String)">
            <summary>
                <para>Exports data to the specified file in XLSX (MS Excel 2007) format.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> that specifies the full path to the file, to which the created document is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXlsx(System.IO.Stream)">
            <summary>
                <para>Exports data to the specified stream in XLSX (MS Excel 2007) format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXml(System.IO.Stream)">
            <summary>
                <para>Exports the tree list's data as XML and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ExportToXml(System.String)">
            <summary>
                <para>Exports the tree list's data to the specified file as XML.
</para>
            </summary>
            <param name="xmlFile">
		The <see cref="T:System.String"/> that contains the full path (including the file name and extension) specifying where the XML file will be created.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FilterConditions">
            <summary>
                <para>Gets the collection of <b>filter conditions</b> for a tree list.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.FilterConditionCollection"/> object that represents the collection of filter conditions for a tree list.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.FilterEditorCreated">
            <summary>
                <para>Allows you to customize the Filter Editor before it is displayed on screen. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.FilterNode">
            <summary>
                <para>Enables you to hide particular nodes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FilterNodes">
            <summary>
                <para>Forces the XtraTreeList control to re-filter its data.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FilterNodes(DevExpress.XtraTreeList.Nodes.TreeListNodes)">
            <summary>
                <para>Forces data filtration for the specified node collection.
</para>
            </summary>
            <param name="nodes">
		A collection of nodes to be filtered.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FilterPanelText">
            <summary>
                <para>Gets the text displayed within the Filter Panel.
</para>
            </summary>
            <value>The text displayed within the filter panel. 
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FindNodeByFieldValue(System.String,System.Object)">
            <summary>
                <para>Returns a node by its field value.
</para>
            </summary>
            <param name="fieldName">
		A <see cref="T:System.String"/> value which identifies the column.

            </param>
            <param name="cellValue">
		An object which represents the field value of the requested node.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that represents the node which contains the specified value within the specified column. <b>null</b> (<b>Nothing</b> in Visual Basic) if the node isn't found.

</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FindNodeByID(System.Int32)">
            <summary>
                <para>Returns the node specified by its identifier.
</para>
            </summary>
            <param name="nodeID">
		An integer value specifying the identifier of the requested node.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node with the specified identifier.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FindNodeByKeyID(System.Object)">
            <summary>
                <para>Returns the node by its key field value.
</para>
            </summary>
            <param name="keyID">
		An object representing the key field value of the requested node.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node specified by its key field value.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FireChanged">
            <summary>
                <para>Forces an update to the control's properties listed within the property grid at design time.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FixedLineWidth">
            <summary>
                <para>Gets or sets the width of fixed panel dividers.
</para>
            </summary>
            <value>An integer value which specifies the width of vertical lines, in pixels, that separate anchored columns from other columns.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FocusedColumn">
            <summary>
                <para>Gets or sets the focused column.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the focused column.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.FocusedColumnChanged">
            <summary>
                <para>Fires immediately after focus has been moved from a column.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FocusedNode">
            <summary>
                <para>Gets or sets the focused node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node. <b>null</b> (<b>Nothing</b> in Visual Basic) if no node is focused.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.FocusedNodeChanged">
            <summary>
                <para>Fires immediately after changing the focused node.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FooterPanelHeight">
            <summary>
                <para>Gets or sets the height of the Summary Footer.
</para>
            </summary>
            <value>An integer value that is the Summary Footer's height.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ForceInitialize">
            <summary>
                <para>Forces the tree list to finish its initialization. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.FormatConditions">
            <summary>
                <para>Gets the collection of <b>style format conditions</b> for a tree list.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.StyleFormatConditions.StyleFormatConditionCollection"/> object that represents the collection of style format conditions.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FullCollapse">
            <summary>
                <para>Collapses all expanded nodes within the XtraTreeList.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FullExpand">
            <summary>
                <para>Expands all nodes within the XtraTreeList.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.FullExpandNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Expands all child nodes of a specific node.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> to expand.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetColumnByColumnHandle(System.Int32)">
            <summary>
                <para>Gets the column by the bound field's index within the data source.
</para>
            </summary>
            <param name="columnHandle">
		An integer value representing the zero-based index of the field whose bound column is sought for.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column with the specified bound field index.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetColumnByVisibleIndex(System.Int32)">
            <summary>
                <para>Returns a column by its visible index.
</para>
            </summary>
            <param name="visibleIndex">
		An integer value representing the column's index among other visible columns.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object or its descendant representing a column displayed at the position specified among other visible columns. <b>null</b> (<b>Nothing</b> in Visual Basic) if there is no column at the specified position.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetColumnError(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Gets the error description for the focused cell or the entire focused node.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column where the focused cell resides. <b>null</b> (<b>Nothing</b> in Visual Basic) to return the error description for the entire node.

            </param>
            <returns>A string value representing an error description.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetColumnErrorType(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Gets the type of the error associated with the specified cell in the currently focused node.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object identifying the cell within the focused row.

            </param>
            <returns>An <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType"/> value that specifies the type of error.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetCustomNodeCellEdit">
            <summary>
                <para>Provides the ability to assign editors to individual cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetCustomNodeCellStyle">
            <summary>
                <para>Provides the ability to assign custom style to individual cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetCustomSummaryValue">
            <summary>
                <para>Enables you to perform custom summary value calculations.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetDataRecordByNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns a data row associated with the node specified.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose associated data row is to be obtained.

            </param>
            <returns>A data row associated with the node specified. <b>null</b> (<b>Nothing</b> in Visual Basic) if the node is not found.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetGroupSummaryValue(DevExpress.XtraTreeList.Columns.TreeListColumn,DevExpress.XtraTreeList.Nodes.TreeListNodes)">
            <summary>
                <para>Returns a summary value calculated against the specified group of nodes.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column whose values are used for summary calculation.

            </param>
            <param name="nodes">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNodes"/> object which represents the nodes collection containing the nodes against which the summary is calculated.

            </param>
            <returns>An object which represents the summary value calculated against a group of nodes.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetHitInfo(System.Drawing.Point)">
            <summary>
                <para>Returns information on the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control at a specified point.
</para>
            </summary>
            <param name="pt">
		A <b>Point</b> that represents a single (x,y) coordinate.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.TreeListHitInfo"/> object containing information on the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control at a specified point.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetNodeByVisibleIndex(System.Int32)">
            <summary>
                <para>Returns a node with the specified index among the visible nodes.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the node's index.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node with the specified index. <b>null</b> (<b>Nothing</b> in Visual Basic) if the node with the specified index is not found.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetNodeDisplayValue">
            <summary>
                <para>Provides the ability to display custom values within cells.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetNodeIndex(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Gets the specified node's index.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose index is to be obtained.

            </param>
            <returns>An integer value representing the destination position of the node within its parent node's child collection. <b>-1</b> if the specified node is a null reference or doesn't belong to any node collection.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetPreviewText">
            <summary>
                <para>Enables you to supply custom preview text for each node.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetPrintCustomSummaryValue">
            <summary>
                <para>Permits manual calculations of summary values for the printed version of the XtraTreeList control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetPrintPreviewText">
            <summary>
                <para>Enables you to display custom text within preview sections of the printed control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetSelectImage">
            <summary>
                <para>Allows you to assign select images to nodes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetSortColumn(System.Int32)">
            <summary>
                <para>Gets the column involved in sorting by its index within the sort column collection.
</para>
            </summary>
            <param name="sortIndex">
		An integer value representing the zero-based index of the column within the sort column list.

            </param>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the requested column. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified sort column index is negative or exceeds the last available index.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.GetStateImage">
            <summary>
                <para>Allows you to assign state images to nodes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetSummaryValue(DevExpress.XtraTreeList.Columns.TreeListColumn,System.Boolean)">
            <summary>
                <para>Returns a summary value calculated against all the tree list nodes or against the root nodes only.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column whose values are used for summary calculation.

            </param>
            <param name="allNodes">
		<b>true</b> to calculate the summary against all the tree list nodes; <b>false</b> to calculate a summary only against the root nodes.

            </param>
            <returns>An object which represents the summary value calculated against tree list nodes.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetSummaryValue(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Returns a summary value calculated against all the tree list nodes or against the root nodes only.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object which represents the column whose values are used for the summary calculation.

            </param>
            <returns>An object which represents the summary value calculated against the tree list nodes.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.GetVisibleIndexByNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Returns the index of the specified node among the visible nodes.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node whose visible index is obtained.

            </param>
            <returns>An integer value specifying the position of the specified node among the visible nodes.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.GroupButtonSize">
            <summary>
                <para>Gets or sets the size of group buttons.
</para>
            </summary>
            <value>An integer value specifying the size of group buttons in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.HasColumnErrors">
            <summary>
                <para>Indicates whether the focused node or at least a single cell within the focused node has an error assigned.
</para>
            </summary>
            <value><b>true</b> if the focused node has errors; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.HasFocus">
            <summary>
                <para>Gets a value indicating whether the XtraTreeList control is focused.
</para>
            </summary>
            <value><b>true</b> if the tree list has focus; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.HiddenEditor">
            <summary>
                <para>Fires immediately after a cell editor has been closed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.HideCustomizationForm">
            <summary>
                <para>Fires immediately after the customization form has been closed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.HideEditor">
            <summary>
                <para>Switches the <b>TreeList</b> control from edit to browse mode without saving changes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.HorzScrollStep">
            <summary>
                <para>Gets or sets the offset by which the tree list is scrolled horizontally when the scrollbar buttons are clicked.
</para>
            </summary>
            <value>An integer value specifying by how many pixels the tree list is scrolled.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.HorzScrollVisibility">
            <summary>
                <para>Gets or sets a value specifying when the horizontal scrollbar should be displayed. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.ScrollVisibility"/> enumeration value specifying when the horizontal scrollbar should be displayed. 
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.HtmlImages">
            <summary>
                <para>Gets or sets a collection of images to be inserted into column headers using HTML tags. 
</para>
            </summary>
            <value>A ImageCollection object that stores a collection of images.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ImageIndexFieldName">
            <summary>
                <para>Gets or sets the name of the field whose values represent select image indexes for corresponding nodes.
</para>
            </summary>
            <value>A string value specifying the name of the field that serves as the source of select image indexes.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ImportFromXml(System.String)">
            <summary>
                <para>Imports the data to the tree list from the XML file.
</para>
            </summary>
            <param name="xmlFile">
		A <see cref="T:System.String"/> value that specifies the the full path (including the file name and extension) to the file in XML format which data will be imported to the tree list.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ImportFromXml(System.IO.Stream)">
            <summary>
                <para>Imports the data to the tree list from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the tree list's data is imported from.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.IndicatorWidth">
            <summary>
                <para>Gets or sets the node indicator's width.
</para>
            </summary>
            <value>An integer value specifying the width of the node indicator in pixels.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InternalGetService(System.Type)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be called directly from your code.
</para>
            </summary>
            <param name="service">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateCell(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Invalidates the specified cell.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that contains the cell to invalidate.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column to which the invalidated cell belongs.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateColumnHeader(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Invalidates the header of the specified column.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object specifying the column whose header is to be invalidated.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateColumnPanel">
            <summary>
                <para>Invalidates the column header panel.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateFilterPanel">
            <summary>
                <para>Invalidates the Filter Panel.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Invalidates the specified node.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object specifying the node to be invalidated.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateNodes">
            <summary>
                <para>Invalidates all visible nodes and row footers.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvalidateSummaryFooterPanel">
            <summary>
                <para>Invalidates the summary footer panel.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.InvalidNodeException">
            <summary>
                <para>Fires when a node fails validation or when its data cannot be saved to the data source.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.InvalidValueException">
            <summary>
                <para>Enables you to handle exceptions raised as a result of assigning invalid values to cells.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.InvokeMenuItemClick(DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs)">
            <summary>
                <para>This member supports the tree list's infrastructure and should not be used from your code.
</para>
            </summary>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.TreeListMenuItemClickEventArgs"/> object representing data for the <see cref="E:DevExpress.XtraTreeList.TreeList.TreeListMenuItemClick"/> event.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.IsDesignMode">
            <summary>
                <para>Gets a value indicating whether the tree list is currently in design mode. 
</para>
            </summary>
            <value><b>true</b>, if the tree list is in design mode; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.IsNodeVisible(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Indicates whether a specific <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> is currently visible.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> whose visibility is inspected.

            </param>
            <returns><b>true</b>, if a specific node is visible; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.IsPrintingAvailable">
            <summary>
                <para>Indicates whether the tree list can be printed or exported.
</para>
            </summary>
            <value><b>true</b> if the tree list can be printed or exported; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.IsUnboundMode">
            <summary>
                <para>Obtains the value determining whether the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control is in unbound mode.
</para>
            </summary>
            <value><b>true</b>, if the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control is in unbound mode, otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.KeyFieldName">
            <summary>
                <para>Gets or sets a value specifying the key field of the data source bound to the XtraTreeList control.
</para>
            </summary>
            <value>A string value representing the name of the field used as the unique record identifier.

</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.LayoutChanged">
            <summary>
                <para>Called when global changes are applied to the <see cref="T:DevExpress.XtraTreeList.TreeList"/> object.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.LayoutUpdated">
            <summary>
                <para>Fires after a tree list's layout has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.LayoutUpgrade">
            <summary>
                <para>Occurs after a layout the version of which doesn't match the current layout version used has been loaded from storage (a stream, xml file or system registry).
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.LayoutVersion">
            <summary>
                <para>Gets or sets the version of the control's layout.
</para>
            </summary>
            <value>A string representing the version of the control's layout.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.LeftCoord">
            <summary>
                <para>Gets or sets a value specifying the number of pixels by which the control's content is scrolled horizontally.
</para>
            </summary>
            <value>An integer value specifying the horizontal scrolling offset of the control's content in pixels.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.LeftCoordChanged">
            <summary>
                <para>Fires when the control's content is scrolled horizontally.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.LockReloadNodes">
            <summary>
                <para>In bound mode, prevents updates of the tree structure when adding and deleting nodes and changing nodes' parents via the Tree List or data source, until the <see cref="M:DevExpress.XtraTreeList.TreeList.UnlockReloadNodes"/> method is called.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.LookAndFeel">
            <summary>
                <para>Provides access to settings which control the tree list's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the tree list control's look and feel.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MakeNodeVisible(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Makes visible the <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> specified via the parameter.
</para>
            </summary>
            <param name="node">
		The <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> to make visible.

            </param>
            <returns>The found node's index among visible <see cref="T:DevExpress.XtraTreeList.TreeList"/> nodes.
</returns>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.MeasurePreviewHeight">
            <summary>
                <para>Allows you to specify the height of individual preview sections in pixels.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.MinWidth">
            <summary>
                <para>Gets or sets the minimum allowed width of all the columns in a tree list.
</para>
            </summary>
            <value>An integer value that specifies the minimum width of columns, in pixels.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveFirst">
            <summary>
                <para>Moves focus to the first node.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveLast">
            <summary>
                <para>Moves focus to the last node within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/>.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.TreeList"/> object representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveLastVisible">
            <summary>
                <para>Moves focus to the last visible node within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/>.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveNext">
            <summary>
                <para>Moves focus to the node following the currently focused one within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveNextVisible">
            <summary>
                <para>Moves focus to the next visible node following the currently focused one within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveNode(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Moves the <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> specified via the <I>sourceNode</I> parameter to the <see cref="P:DevExpress.XtraTreeList.Nodes.TreeListNode.Nodes"/> collection of the <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> specified by the <I>destinationNode</I> parameter.
</para>
            </summary>
            <param name="sourceNode">
		The moved <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/>. 

            </param>
            <param name="destinationNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> accepting the moved one.

            </param>
            <returns><b>true</b>, if the <I>sourceNode</I> was moved, otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MoveNode(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Boolean)">
            <summary>
                <para>Moves the selected node to the specified node's child collection.
</para>
            </summary>
            <param name="sourceNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be moved.

            </param>
            <param name="destinationNode">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to whose child collection the node is moved. <b>null</b> (<b>Nothing</b> in Visual Basic) if the source node must be moved to the root nodes collection.


            </param>
            <param name="modifySource">
		<b>true</b>, if the parent field value of the source node must be set to the key field value of the destination node; otherwise <b>false</b>.


            </param>
            <returns><b>true</b> if the node has been moved successfully, otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MovePrev">
            <summary>
                <para>Moves focus to the node preceding the currently focused one within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.MovePrevVisible">
            <summary>
                <para>Moves focus to the visible node preceding the currently focused one within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the focused node. 
</returns>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.MRUFilters">
            <summary>
                <para>Provides access to the TreeList's most recently used filters.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListFilterInfoCollection"/> object that stores the recently used filters.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.NodeCellStyle">
            <summary>
                <para>Enables appearances to be assigned to individual cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.NodeChanged">
            <summary>
                <para>Fires after the node's property has changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.Nodes">
            <summary>
                <para>Obtains the collection of nodes within the XtraTreeList.
</para>
            </summary>
            <value>The collection of XtraTreeList nodes.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.NodesIterator">
            <summary>
                <para>Gets an object that enables you to perform specific operations on a set of nodes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.Operations.TreeListNodesIterator"/> object used to perform predefined operations on a set of nodes.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.NodesReloaded">
            <summary>
                <para>Fires when the tree list control reloads all nodes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.NonColumnFilterCriteria">
            <summary>
                <para>Gets a CriteriaOperator-based filter applied to the TreeList and not associated with any column. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that specifies a filter applied to the TreeList and not associated with any column. 


</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.NonColumnFilterString">
            <summary>
                <para>Gets the text representation of the filter applied to the TreeList and not associated with any column. 


</para>
            </summary>
            <value>The text representation of the filter that is not associated with any TreeList column. 

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsBehavior">
            <summary>
                <para>Provides access to the tree list's behavior options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsBehavior"/> object which contains the tree list's behavior options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsFilter">
            <summary>
                <para>Provides access to the TreeList's filtering options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsFilter"/> object containing the filtering options.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsLayout">
            <summary>
                <para>Provides access to options that specify how a control's layout is stored to and restored from a data store (a stream, xml file or the system registry). 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that provides the corresponding options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsMenu">
            <summary>
                <para>Provides access to the tree list's menu options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsMenu"/> object which provides the tree list's menu options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsPrint">
            <summary>
                <para>Provides access to the tree list's options that affect how the control is printed and exported
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsPrint"/> object which contains the print and export options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsSelection">
            <summary>
                <para>Provides access to the tree list's selection options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsSelection"/> object which contains the tree list's selection options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.OptionsView">
            <summary>
                <para>Provides access to the tree list's display options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListOptionsView"/> object which contains the tree list's display options.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.Painter">
            <summary>
                <para>Gets an object that provides the painting functionality of the <b>TreeList</b> control.
</para>
            </summary>
            <value>A <b>TreeListPainter</b> object implementing the control's painting functionality.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ParentFieldName">
            <summary>
                <para>Gets or sets a value representing the data source field identifying the parent record in this data source.
</para>
            </summary>
            <value>The name of the field used as an identifier of a parent record within a data source.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.PopulateColumns">
            <summary>
                <para>Creates columns for all fields in the bound data source.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.PopupMenuShowing">
            <summary>
                <para>Allows you to customize the default menus for column headers, summary footers and nodes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.PostEditor">
            <summary>
                <para>Posts edited cell's value to the associated data source.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.PreviewFieldName">
            <summary>
                <para>Gets or sets the name of the field whose values are displayed in preview sections.
</para>
            </summary>
            <value>The name of the field whose values are displayed in preview sections.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.PreviewLineCount">
            <summary>
                <para>Gets or sets the number of text lines within preview sections.
</para>
            </summary>
            <value>An integer value specifying the number of text lines within preview sections.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.Print">
            <summary>
                <para>Prints the XtraTreeList control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.Refresh">
            <summary>
                <para>Forces the tree list to invalidate its client area and immediately redraw itself.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RefreshCell(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Refreshes the specified cell.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> that contains the specified cell.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object that is the column where the target cell resides.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RefreshDataSource">
            <summary>
                <para>Updates the XtraTreeList control to reflect changes made to a data source.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RefreshNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Refreshes the specified node.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> to be refreshed.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromRegistry(System.String)">
            <summary>
                <para>Restores the control layout stored at the specified system registry path.
</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Restores the control's layout stored at the specified system registry path, using the specified options. 
</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be restored.
O,r a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to restore all the settings.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Loads the control's layout from a stream.
</para>
            </summary>
            <param name="stream">
		A <b>System.IO.Stream</b> object from which the control's settings are read.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Loads the control's layout from a stream, using the specified options. 
</para>
            </summary>
            <param name="stream">
		A System.IO.Stream object from which the control's settings are read. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be restored. 
Or, a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to restore all the settings.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromXml(System.String)">
            <summary>
                <para>Loads a control's layout from the specified XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the XML file from which control settings are read.


            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.RestoreLayoutFromXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Loads a control's layout from the specified XML file, using the specified options. 
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the XML file from which control settings are read. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be restored. 
Or, a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to restore all the settings.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.RootValue">
            <summary>
                <para>Gets or sets the value used to identify the records in the underlying data source that will be represented as root nodes. These records must have the <b>RootValue</b> in the field specified by the <see cref="P:DevExpress.XtraTreeList.TreeList.ParentFieldName"/> property.
</para>
            </summary>
            <value>An object representing the value that root nodes contain, in the field specified by the <see cref="P:DevExpress.XtraTreeList.TreeList.ParentFieldName"/> property.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.RowHeight">
            <summary>
                <para>Gets or sets the height of a node in pixels.
</para>
            </summary>
            <value>An integer value specifying the height of a node in pixels.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToRegistry(System.String)">
            <summary>
                <para>Saves the control's layout to the specified system registry path.
</para>
            </summary>
            <param name="path">
		A string value, specifying the system registry path to which the layout is saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves the control's layout to the specified system registry path, using the specified options. 

</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be saved. 
Or, a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to save all the settings.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves the control's layout to a stream.
</para>
            </summary>
            <param name="stream">
		A <b>System.IO.Stream</b> object to which the control's layout is written.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves the control's layout to a stream, using the specified options.
</para>
            </summary>
            <param name="stream">
		A System.IO.Stream object to which the control's layout is written. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be saved. 
Or, a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to save all the settings.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToXml(System.String)">
            <summary>
                <para>Saves a control's layout to a specified XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the XML file name.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SaveLayoutToXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves a control's layout to a specified XML file, using the specified options.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the XML file name. 

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraTreeList.OptionsLayoutTreeList"/> object that specifies which TreeList options must be saved. 
Or, a <see cref="P:DevExpress.Utils.OptionsLayoutBase.FullLayout"/> object, to save all the settings.

            </param>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.SelectImageClick">
            <summary>
                <para>Fires when the user clicks a node's select image.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.SelectImageList">
            <summary>
                <para>Gets or sets the source of the images displayed within selected nodes.

</para>
            </summary>
            <value>An object which represents the source of the images displayed within selected nodes.

</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.Selection">
            <summary>
                <para>Gets the collection of selected nodes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListMultiSelection"/> instance containing selected nodes. 
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.SelectionChanged">
            <summary>
                <para>Fires in response to changing selection when the <b>TreeList</b> is in multiselect mode.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetColumnError(DevExpress.XtraTreeList.Columns.TreeListColumn,System.String)">
            <summary>
                <para>Sets an error description for a cell within the focused node or for the entire focused node.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing a column that contains an error cell. <b>null</b> (<b>Nothing</b> in Visual Basic) if the error description should be assigned to the entire focused node.

            </param>
            <param name="errorText">
		A string value representing an error description. An empty string to clear the assigned error.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetColumnError(DevExpress.XtraTreeList.Columns.TreeListColumn,System.String,DevExpress.XtraEditors.DXErrorProvider.ErrorType)">
            <summary>
                <para>Sets an error description and an error type for the specified cell within the focused node.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> that identifies the cell to which an error is set.

            </param>
            <param name="errorText">
		A string value representing an error description. An empty string to clear the assigned error.

            </param>
            <param name="errorType">
		An <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType"/> value that specifies the type of the error. Error types correspond to specific error icons.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetDefaultBehaviorOptions">
            <summary>
                <para>Sets the options provided by the <see cref="P:DevExpress.XtraTreeList.TreeList.OptionsBehavior"/> property to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetDefaultOptionsView">
            <summary>
                <para>Sets the options provided by the <see cref="P:DevExpress.XtraTreeList.TreeList.OptionsView"/> property to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetDefaultPrintOptions">
            <summary>
                <para>Sets the options provided by the <see cref="P:DevExpress.XtraTreeList.TreeList.OptionsPrint"/> property to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetDefaultRowHeight">
            <summary>
                <para>Activates automatic row height adjustment.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetDefaultSelectionOptions">
            <summary>
                <para>Sets the options provided by the <see cref="P:DevExpress.XtraTreeList.TreeList.OptionsSelection"/> property to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetFocusedNode(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Sets focus to a specific node within the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <param name="node">
		The node to set focus on.

            </param>
            <returns>The index of the focused node among visible nodes.
</returns>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetNodeCheckState(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Windows.Forms.CheckState,System.Boolean)">
            <summary>
                <para>Sets the node's check state to the specified value. Allows you to set the check state for the node's children.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object whose check state is to be changed.

            </param>
            <param name="state">
		A CheckState value that specifies the node's new check state.

            </param>
            <param name="recursive">
		<b>true</b> to set the check state of the node's children to the specified value; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetNodeCheckState(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Windows.Forms.CheckState)">
            <summary>
                <para>Sets the node's check state to the specified value. 
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object whose check state is to be changed.

            </param>
            <param name="state">
		A CheckState value that specifies the node's new check state.

            </param>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.SetNodeIndex(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32)">
            <summary>
                <para>Moves the specified node to a new position within its parent node's child collection.
</para>
            </summary>
            <param name="node">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node to be moved.

            </param>
            <param name="index">
		An integer value representing the destination position of the node within its parent node's child collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ShowButtonMode">
            <summary>
                <para>Gets or sets a value determining the manner in which the editor buttons of <see cref="T:DevExpress.XtraTreeList.TreeList"/> cells are displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.ShowButtonModeEnum"/> enumerator value that specifies the manner in which editor buttons are displayed. 

</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowCustomizationForm">
            <summary>
                <para>Fires immediately after the customization form has been displayed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ShowEditor">
            <summary>
                <para>Switches the <b>TreeList</b> control to edit mode.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ShowFilterEditor(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Shows the Filter Editor.
</para>
            </summary>
            <param name="defaultColumn">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object to which a filter is applied in the Filter Editor.


            </param>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowFilterPopupCheckedListBox">
            <summary>
                <para>Allows you to customize checked Column's Filter DropDown lists before they are displayed.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowFilterPopupDate">
            <summary>
                <para>Allows you to customize a column's dropdown calendar before it is displayed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowFilterPopupListBox">
            <summary>
                <para>Enables you to customize a particular Column's Filter DropDown.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowingEditor">
            <summary>
                <para>Fires before the <b>TreeList</b> is switched to edit mode.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShownEditor">
            <summary>
                <para>Fires immediately after a cell editor has been invoked.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ShowPrintPreview">
            <summary>
                <para>Opens the Print Preview window with a Bars UI.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.ShowRibbonPrintPreview">
            <summary>
                <para>Displays the Print Preview window with a Ribbon UI.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ShowTreeListMenu">
            <summary>
                <para>Allows you to customize the default menus for column headers, summary footers and nodes.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.SortedColumnCount">
            <summary>
                <para>Gets the number of columns involved in sorting.
</para>
            </summary>
            <value>An integer value representing the number of columns involved in sorting.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.StartIncrementalSearch(System.String)">
            <summary>
                <para>Starts an incremental search for the specified string.
</para>
            </summary>
            <param name="start">
		The text to locate.

            </param>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.StartSorting">
            <summary>
                <para>Fires  before a sorting operation is started.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.State">
            <summary>
                <para>Gets the control's current state.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.TreeListState"/> enumeration member, indicating the tree list's current state.

</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.StateChanged">
            <summary>
                <para>Fires after the tree list's state has changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.StateImageClick">
            <summary>
                <para>Fires when the user clicks a node's state image.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.StateImageList">
            <summary>
                <para>Gets or sets the source of the images that indicate a node's state.

</para>
            </summary>
            <value>An object which represents the source of state images.
</value>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.StopIncrementalSearch">
            <summary>
                <para>Stops the incremental search, if there is one currently in progress.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.TopVisibleNodeIndex">
            <summary>
                <para>Gets or sets the index of the top visible node.
</para>
            </summary>
            <value>An integer value specifying the zero-based index of the top visible node.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.TopVisibleNodeIndexChanged">
            <summary>
                <para>Fires when the control's content is scrolled vertically.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.TreeLevelWidth">
            <summary>
                <para>Gets or sets the width of the level's indent space.

</para>
            </summary>
            <value>An integer which specifies the width of the level's indent space.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.TreeLineStyle">
            <summary>
                <para>Specifies the style for displaying tree lines of the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.LineStyle"/> enumerator value specifying the style for displaying tree lines.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.TreeListDisposing">
            <summary>
                <para>Gets a value indicating whether the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control is being unloaded from memory.
</para>
            </summary>
            <value><b>true</b>, if the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control is being unloaded from memory; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.TreeListMenuItemClick">
            <summary>
                <para>Provides the ability to perform custom handling of a context menu item click.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.UncheckAll">
            <summary>
                <para>Unchecks all nodes in the TreeList control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraTreeList.TreeList.UnlockReloadNodes">
            <summary>
                <para>Updates the control's tree structure after the <see cref="M:DevExpress.XtraTreeList.TreeList.LockReloadNodes"/> method call, and updates it immediately.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.UseDisabledStatePainter">
            <summary>
                <para>Gets or sets whether the control is painted grayed out, when it's in the disabled state.
</para>
            </summary>
            <value><b>true</b> if the control is painted grayed out, when it's in the disabled state; otherwise, <b>false</b>
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ValidateNode">
            <summary>
                <para>Gives you the ability to specify whether a modified node's data is valid, and if this node can lose focus.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.ValidatingEditor">
            <summary>
                <para>Fires when an editor is validated.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.VertScrollVisibility">
            <summary>
                <para>Gets or sets a value specifying when the tree list's vertical scrollbar is visible.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.ScrollVisibility"/> enumeration value specifying when the vertical scrollbar is visible.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.ViewInfo">
            <summary>
                <para>Gets the control's view/display information.
</para>
            </summary>
            <value>A <b>TreeListViewInfo</b> object providing view information on all the tree list's elements.
</value>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetCellValue">
            <summary>
                <para>This event allows you to initialize cells of the processed node, when populating the TreeList control with data dynamically.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.VirtualTreeGetChildNodes">
            <summary>
                <para>This event allows you to provide root and child nodes, when populating the TreeList control with data dynamically.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraTreeList.TreeList.VirtualTreeSetCellValue">
            <summary>
                <para>Allows changes that are made via the TreeListControl's cells to be stored.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.VisibleColumnCount">
            <summary>
                <para>Gets the number of visible columns within the tree list.
</para>
            </summary>
            <value>An integer value representing the number of visible columns.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.VisibleColumns">
            <summary>
                <para>Gets the collection of visible columns.
</para>
            </summary>
            <value>A <b>VisibleColumnsList</b> object storing visible columns.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.TreeList.VisibleNodesCount">
            <summary>
                <para>Gets the number of rows which are not hidden within collapsed groups.
</para>
            </summary>
            <value>An integer value representing the number of rows which are not hidden within collapsed groups.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.NodeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.AfterCollapse"/>, <see cref="E:DevExpress.XtraTreeList.TreeList.AfterExpand"/>, <see cref="E:DevExpress.XtraTreeList.TreeList.AfterDragNode"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.AfterFocusNode"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.NodeEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.NodeEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.NodeEventArgs.Node">
            <summary>
                <para>Gets the current tree list node.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object that specifies the current tree list node.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetStateImageEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetStateImage"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetStateImageEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetStateImageEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetStateImage"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetStateImageEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetStateImageEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.GetStateImage"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetStateImageEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.GetStateImageEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>
            <param name="nodeImageIndex">
		The image index.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetStateImageEventArgs.NodeImageIndex">
            <summary>
                <para>Gets or sets the index of the state image assigned to the node.
</para>
            </summary>
            <value>An integer value representing the zero-based index of the state image assigned to the node.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetSelectImageEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetSelectImage"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetSelectImageEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetSelectImageEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetSelectImage"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetSelectImageEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetSelectImageEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.GetSelectImage"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetSelectImageEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.Int32,System.Boolean)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.GetSelectImageEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>
            <param name="nodeImageIndex">
		The image index.

            </param>
            <param name="focusedNode">
		Indicates whether a node is focused.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetSelectImageEventArgs.FocusedNode">
            <summary>
                <para>Gets or sets a value indicating whether a node is focused.
</para>
            </summary>
            <value><b>true</b>, if a node is focused; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetPreviewTextEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetPreviewText"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.GetPrintPreviewText"/> events of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetPreviewTextEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetPreviewTextEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetPreviewText"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetPreviewTextEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetPreviewTextEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.GetPreviewText"/> and <see cref="E:DevExpress.XtraTreeList.TreeList.GetPrintPreviewText"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetPreviewTextEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,System.String)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.GetPreviewTextEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>
            <param name="previewText">
		Preview text.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetPreviewTextEventArgs.PreviewText">
            <summary>
                <para>Gets or sets preview text.
</para>
            </summary>
            <value>Preview text.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomSummaryValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetCustomSummaryValue"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomSummaryValueEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.GetCustomSummaryValue"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.GetCustomSummaryValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNodes,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Boolean)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs"/> class instance.
</para>
            </summary>
            <param name="nodes">
		The nodes collection to calculate a summary.

            </param>
            <param name="column">
		A tree list column.

            </param>
            <param name="isSummaryFooter">
		Determines the collection contained in the <i>nodes</i> parameter.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs.Column">
            <summary>
                <para>Obtains the tree list column used by the summary calculations.
</para>
            </summary>
            <value>A tree list column used by the summary calculations.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs.CustomValue">
            <summary>
                <para>Gets or sets a value to be displayed within a summary.
</para>
            </summary>
            <value>An object representing the custom summary value.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs.IsSummaryFooter">
            <summary>
                <para>Gets a value indicating whether the total or group summary value is to be calculated.
</para>
            </summary>
            <value><b>true</b>, if the the total summary value is to be calculated; <b>false</b> for the group summary.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.GetCustomSummaryValueEventArgs.Nodes">
            <summary>
                <para>Gets the nodes collection for which to calculate a custom summary.
</para>
            </summary>
            <value>The nodes collection for which to calculate a custom summary.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.FocusedNodeChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedNodeChanged"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FocusedNodeChangedEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.FocusedNodeChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedNodeChanged"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.FocusedNodeChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs"/> class instance.
</para>
            </summary>
            <param name="old">
		The previous focused tree list node.

            </param>
            <param name="node">
		The current focused tree list node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.FocusedNodeChangedEventArgs.OldNode">
            <summary>
                <para>Gets the previously focused tree list node.
</para>
            </summary>
            <value>The previously focused tree list node.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.CompareNodeValuesEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CompareNodeValues"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CompareNodeValuesEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.CompareNodeValuesEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.CompareNodeValues"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.CompareNodeValuesEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.CompareNodeValuesEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.CompareNodeValues"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode,DevExpress.XtraTreeList.Nodes.TreeListNode,System.Object,System.Object,DevExpress.XtraTreeList.Columns.TreeListColumn,System.Windows.Forms.SortOrder,System.Int32)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraTreeList.CompareNodeValuesEventArgs"/> object.
</para>
            </summary>
            <param name="node1">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node containing the first value involved in the comparison. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Node1"/> property.

            </param>
            <param name="node2">
		A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that contains the second value involved in the comparison. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Node2"/> property.

            </param>
            <param name="nodeValue1">
		An object representing the first value involved in the comparison. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.NodeValue1"/> property.

            </param>
            <param name="nodeValue2">
		An object representing the second value involved in the comparison. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.NodeValue2"/> property.

            </param>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column against whose values the data will be sorted. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Column"/> property.

            </param>
            <param name="sortOrder">
		A <see cref="T:System.Windows.Forms.SortOrder"/> enumeration member representing the sort order to be applied. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.SortOrder"/> property.

            </param>
            <param name="result">
		An integer value representing the comparison result. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Result"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Column">
            <summary>
                <para>Gets the column against whose values sorting is performed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column against whose values data is being sorted.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Node1">
            <summary>
                <para>Gets the node containing the first value involved in comparison.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node containing the first value involved in comparison.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Node2">
            <summary>
                <para>Gets the node containing the second value involved in comparison.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraTreeList.Nodes.TreeListNode"/> object representing the node that contains the second value involved in comparison.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.NodeValue1">
            <summary>
                <para>Gets the first value involved in comparison.

</para>
            </summary>
            <value>An object representing the first value involved in comparison.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.NodeValue2">
            <summary>
                <para>Gets the second value involved in comparison.
</para>
            </summary>
            <value>An object representing the second value involved in comparison.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.Result">
            <summary>
                <para>Gets or sets a comparison result.
</para>
            </summary>
            <value>An integer value representing the comparison result.
</value>


        </member>
        <member name="P:DevExpress.XtraTreeList.CompareNodeValuesEventArgs.SortOrder">
            <summary>
                <para>Gets the sort order applied to the column whose values are going to be compared.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.SortOrder"/> enumeration member representing the sort order applied.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.ColumnChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ColumnChanged"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.ColumnChangedEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.ColumnChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.ColumnChanged"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.ColumnChangedEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.ColumnChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.ColumnChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.ColumnChangedEventArgs.#ctor(DevExpress.XtraTreeList.Columns.TreeListColumn)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.XtraTreeList.ColumnChangedEventArgs"/> class.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.XtraTreeList.Columns.TreeListColumn"/> object representing the column related to the event. This value is assigned to the <see cref="P:DevExpress.XtraTreeList.ColumnChangedEventArgs.Column"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.ColumnChangedEventArgs.Column">
            <summary>
                <para>Gets a tree list column.
</para>
            </summary>
            <value>A tree list column.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeExpandEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeExpand"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeExpandEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.BeforeExpandEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeExpand"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.BeforeExpandEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeExpandEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeExpand"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeExpandEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.BeforeExpandEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.BeforeExpandEventArgs.CanExpand">
            <summary>
                <para>Gets or sets whether a tree list node can be expanded.
</para>
            </summary>
            <value><b>true</b> if a tree list node can be expanded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeDragNodeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeDragNode"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeDragNodeEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.BeforeDragNodeEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeDragNode"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.BeforeDragNodeEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeDragNodeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeDragNode"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeDragNodeEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.BeforeDragNodeEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.BeforeDragNodeEventArgs.CanDrag">
            <summary>
                <para>Gets or sets a value indicating whether a tree list node can be dragged.
</para>
            </summary>
            <value><b>true</b> if a tree list node can be dragged; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeCollapseEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeCollapse"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeCollapseEventHandler.Invoke(System.Object,DevExpress.XtraTreeList.BeforeCollapseEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeCollapse"/> event of the <see cref="T:DevExpress.XtraTreeList.TreeList"/> class.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraTreeList.BeforeCollapseEventArgs"/> class instance containing event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraTreeList.BeforeCollapseEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraTreeList.TreeList.BeforeCollapse"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraTreeList.BeforeCollapseEventArgs.#ctor(DevExpress.XtraTreeList.Nodes.TreeListNode)">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.XtraTreeList.BeforeCollapseEventArgs"/> class instance.
</para>
            </summary>
            <param name="node">
		A tree list node.

            </param>


        </member>
        <member name="P:DevExpress.XtraTreeList.BeforeCollapseEventArgs.CanCollapse">
            <summary>
                <para>Gets or sets a value indicating whether a tree list node can collapse.
</para>
            </summary>
            <value><b>true</b> if a tree list node can collapse; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraTreeList.ViewBorderStyle">

            <summary>
                <para>Specifies the border style of the XtraTreeList.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.ViewBorderStyle.Border3D">
            <summary>
                <para><para>Specifies whether the <see cref="T:DevExpress.XtraTreeList.TreeList"/> border style is Border3D.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ViewBorderStyle.Flat">
            <summary>
                <para><para>Specifies whether the <see cref="T:DevExpress.XtraTreeList.TreeList"/> border style is Flat.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ViewBorderStyle.None">
            <summary>
                <para><para>Specifies whether the <see cref="T:DevExpress.XtraTreeList.TreeList"/> border style is none.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ViewBorderStyle.Single">
            <summary>
                <para><para>Specifies whether the <see cref="T:DevExpress.XtraTreeList.TreeList"/> border style is Single.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ViewBorderStyle.ThinFlat">
            <summary>
                <para><para>Specifies whether the <see cref="T:DevExpress.XtraTreeList.TreeList"/> border style is ThinFlat.</para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.SummaryItemType">

            <summary>
                <para>Specifies summary type to calculate against a group of column cells.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Average">
            <summary>
                <para>Calculates the average field value within a group of column cells.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Count">
            <summary>
                <para>Calculates the number of nodes within a group of column cells.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Custom">
            <summary>
                <para>Allows a user to define a custom summary value by handling the <see cref="E:DevExpress.XtraTreeList.TreeList.GetCustomSummaryValue"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Max">
            <summary>
                <para>Retrieves the maximum value within a group of column cells.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Min">
            <summary>
                <para>Retrieves the minimum value within a group of column cells.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.None">
            <summary>
                <para>Summary is not calculated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.SummaryItemType.Sum">
            <summary>
                <para>Calculates the sum of field values within a group of column cells.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.ShowButtonModeEnum">

            <summary>
                <para>Contains settings that define the visibility of editor buttons within a <see cref="T:DevExpress.XtraTreeList.TreeList"/> cell. 

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.ShowButtonModeEnum.ShowAlways">
            <summary>
                <para>Buttons are always displayed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ShowButtonModeEnum.ShowForFocusedCell">
            <summary>
                <para>Buttons are displayed only for the focused <see cref="T:DevExpress.XtraTreeList.TreeList"/> cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ShowButtonModeEnum.ShowForFocusedRow">
            <summary>
                <para>Buttons are displayed for all cell editors in the focused row.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.ShowButtonModeEnum.ShowOnlyInEditor">
            <summary>
                <para>Buttons are displayed only when a cell editor is active.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.LineStyle">

            <summary>
                <para>Specifies the tree line style for the current <see cref="T:DevExpress.XtraTreeList.TreeList"/> control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Dark">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Dark</b>. 
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Large">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Large</b>. 
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Light">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Light</b>. 
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.None">
            <summary>
                <para>Hides tree lines. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Percent50">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Percent50</b>. 
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Solid">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Solid</b>.      
 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.LineStyle.Wide">
            <summary>
                <para>Sets the <see cref="T:DevExpress.XtraTreeList.TreeList"/> control tree line style to <b>Wide</b>.
 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraTreeList.HitInfoType">

            <summary>
                <para>Lists values that identify the different elements of the XtraTreeList control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.AutoFilterRow">
            <summary>
                <para>A point is over the Auto Filter Row.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.BehindColumn">
            <summary>
                <para>A point is over the blank column header.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.Button">
            <summary>
                <para>A point is over an expand button.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.Cell">
            <summary>
                <para>A point is over a cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.Column">
            <summary>
                <para>A point is over a column header.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.ColumnButton">
            <summary>
                <para>A point is over the column button.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.ColumnEdge">
            <summary>
                <para>A point is over a column edge.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.ColumnFilterButton">
            <summary>
                <para>A point is over a Filter Button.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.CustomizationForm">
            <summary>
                <para>A point is over the Customization Form. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.Empty">
            <summary>
                <para>A point is over the empty area.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanel">
            <summary>
                <para>A point is over the Filter Panel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanelActiveButton">
            <summary>
                <para>A point is over the check box displayed within in the Filter Panel and used to enable/disable the filter.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanelCloseButton">
            <summary>
                <para>A point is over the Close Filter Button in the Filter Panel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanelCustomizeButton">
            <summary>
                <para>A point is over the 'Edit Filter' button displayed within the Filter Panel and used to invoke the Filter Editor.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanelMRUButton">
            <summary>
                <para>A point is over the MRU Filter Button in the Filter Panel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FilterPanelText">
            <summary>
                <para>A point is over the filter string displayed within the Filter Panel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FixedLeftDiv">
            <summary>
                <para>The test point belongs to the left fixed line. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.FixedRightDiv">
            <summary>
                <para>The test point belongs to the right fixed line. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.HScrollBar">
            <summary>
                <para>The test point belongs to the tree list's horizontal scroll bar.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.NodeCheckBox">
            <summary>
                <para>The test point belongs to a node's check box.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.None">
            <summary>
                <para>A point is outside the XtraTreeList control.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.Row">
            <summary>
                <para>A point is over a node area not occupied by any of the node's elements.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.RowFooter">
            <summary>
                <para>A point is over a row(group) footer.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.RowIndent">
            <summary>
                <para>A point is over an area that separates a row from its corresponding indicator cell. This value is returned only when the <see cref="P:DevExpress.XtraTreeList.TreeListOptionsView.ShowIndentAsRowStyle"/> option is enabled. Otherwise, an <b>Empty</b> value is returned when a point is over this area.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.RowIndicator">
            <summary>
                <para>A point is over a node indicator cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.RowIndicatorEdge">
            <summary>
                <para>A point is on a row indicator's edge.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.RowPreview">
            <summary>
                <para>A point is over a preview section.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.SelectImage">
            <summary>
                <para>A point is over a node's select image.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.StateImage">
            <summary>
                <para>A point is over a node's state image.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.SummaryFooter">
            <summary>
                <para>A point is over the summary footer.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraTreeList.HitInfoType.VScrollBar">
            <summary>
                <para>The test point belongs to the tree list's vertical scroll bar.
</para>
            </summary>


        </member>
    </members>
</doc>
