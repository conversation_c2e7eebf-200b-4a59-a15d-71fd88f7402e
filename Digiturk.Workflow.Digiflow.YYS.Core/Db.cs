﻿using DataAccessLayer;
using Digiturk.Framework.Repository;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;

namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// YYS için database işlemlerinin bulunduğu ortak fonksiyonları barındırır.
    /// </summary>
    public class Db
    {
        /// <summary>
        /// Uygulamanın Sql Server modunda çalışıp çalışmadığını belirtir. Sql modunda çalışmıyorsa Oracle Client ile çalışır.
        /// </summary>
        public static bool IsInSqlMode
        {
            get
            {
                string val = ConfigurationManager.AppSettings["DatabaseMode"];
                return val.Equals("SQL", StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// SqlConnection instance oluşturur
        /// </summary>
        /// <returns></returns>
        public static SqlConnection GetSqlConnection(ConnectionType ConnType)
        {
            if (ConnType == ConnectionType.DefaultConnection)
            {
                return new SqlConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            }
            else if (ConnType == ConnectionType.FrameworkConnection)
            {
                return new SqlConnection(ConfigurationManager.ConnectionStrings["FrameworkConnection"].ConnectionString);
            }
            else
            {
                return new SqlConnection(Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.ReturnConnStr("DBSConnection"));
                //return new SqlConnection(DataAccessLayer.PasswordBoxConnectionString.ReturnConnStr("DBSConnection");
            }
        }

        /// <summary>
        /// SqlConnection instance oluşturur
        /// </summary>
        /// <returns></returns>
        public static SqlConnection GetSqlConnection()
        {
            return new SqlConnection(ConfigurationManager.ConnectionStrings["SqlConnectionString"].ConnectionString);
        }

        /// <summary>
        /// OracleConnection instance oluşturur
        /// <param name="ConnectionStringTypeId">Bu Id ile bağlantının hangi bağlantı olduğu anlaşılır.</param>
        /// </summary>
        /// <returns></returns>
        public static OracleConnection GetOracleConnection(ConnectionType ConnType)
        {
            if (ConnType == ConnectionType.DefaultConnection)
            {
                return new OracleConnection(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
            }
            else if (ConnType == ConnectionType.FrameworkConnection)
            {
                return new OracleConnection(ConfigurationManager.ConnectionStrings["FrameworkConnection"].ConnectionString);
            }
            else
            {
                return new OracleConnection(Digiturk.Workflow.Digiflow.DataAccessLayer.PasswordBoxConnectionString.ReturnConnStr("DBSConnection"));
            }
        }

        /// <summary>
        /// OracleConnection instance oluşturur
        /// </summary>
        /// <returns></returns>
        public static OracleConnection GetOracleConnection()
        {
            return new OracleConnection(ConfigurationManager.ConnectionStrings["OracleConnectionString"].ConnectionString);
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye datatable döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <param name="ConnTypeId">Hangi bağlantının kullanılacağını belirtir</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteDataTable(string sql, ConnectionType ConnType)
        {
            DataTable dt = new DataTable();

            if (IsInSqlMode)
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, GetSqlConnection(ConnType));
                da.Fill(dt);
            }
            else
            {
                OracleDataAdapter da = new OracleDataAdapter(sql, GetOracleConnection(ConnType));
                da.Fill(dt);
            }

            return dt;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye datatable döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteDataTable(string sql)
        {
            DataTable dt = new DataTable();

            if (IsInSqlMode)
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, GetSqlConnection());
                da.Fill(dt);
            }
            else
            {
                OracleDataAdapter da = new OracleDataAdapter(sql, GetOracleConnection());
                da.Fill(dt);
            }

            return dt;
        }

        /// <summary>
        /// Database e belirlenen parametredeki veriyi almayı sağlar
        /// </summary>
        /// <param name="p">;Oracle parametre dizisi</param>
        /// <param name="ConnType">Connection'ın türü</param>
        /// <param name="sql">sql cümlesi</param>
        /// <returns>Datatable döner</returns>
        public static DataTable ExecuteDataTable(OracleParameter[] p, ConnectionType ConnType, string sql)
        {
            string Conn = ConnType.ToString();
            using (AdoDatabase db = new AdoDatabase(Conn))
            {
                DataSet ds = new DataSet();
                db.Fill(ds, sql, p);
                return ds.Tables[0];
            }
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye datatable döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <param name="ConnTypeId">Hangi bağlantının seçileceği belirlenir</param>
        /// <returns>DataTable</returns>
        public static DataTable ExecuteDataTable(string sql, params object[] args)
        {
            DataTable dt = new DataTable();

            if (IsInSqlMode)
            {
                SqlDataAdapter da = new SqlDataAdapter(sql, GetSqlConnection(ConnectionType.DefaultConnection));
                if (args.Length > 0)
                {
                    for (int i = 0; i < args.Length; i++)
                    {
                        SqlParameter p = args[i] as SqlParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        da.SelectCommand.Parameters.Add(p);
                    }
                }
                da.Fill(dt);
            }
            else
            {
                OracleDataAdapter da = new OracleDataAdapter(sql, GetOracleConnection(ConnectionType.DefaultConnection));
                for (int i = 0; i < args.Length; i++)
                {
                    OracleParameter p = args[i] as OracleParameter;
                    if (p.Value == null)
                    {
                        p.Value = DBNull.Value;
                    }
                    da.SelectCommand.Parameters.Add(p);
                }
                da.Fill(dt);
            }

            return dt;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye etkilenen satır sayısını döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Etkilenen satır sayısı</returns>
        public static int ExecuteNonQuery(string sql)
        {
            int ret = 0;

            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection();
                SqlCommand cmd = new SqlCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection();
                OracleCommand cmd = new OracleCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye etkilenen satır sayısını döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Etkilenen satır sayısı</returns>
        public static int ExecuteNonQuery(string sql, params object[] args)
        {
            int ret = 0;
            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection();
                SqlCommand cmd = new SqlCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        SqlParameter p = item as SqlParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p);
                    }
                }
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection();
                OracleCommand cmd = new OracleCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        OracleParameter p = item as OracleParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p);
                    }
                }
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye etkilenen satır sayısını döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Etkilenen satır sayısı</returns>
        public static int ExecuteNonQuery(string sql, ConnectionType cnnType)
        {
            int ret = 0;
            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection(cnnType);
                SqlCommand cmd = new SqlCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection(cnnType);
                OracleCommand cmd = new OracleCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteNonQuery();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// Parametre array ile belirtilen sql cümlesini çalıştırır
        /// </summary>
        /// <param name="p">Oracle parametre array i</param>
        /// <param name="sql">sql cümlesi</param>
        /// <param name="cnnType">Connection</param>
        /// <param name="ent">Cümle sonucunda etkilenen satırları mı yoksa identity sütununu alacağımızı tutar</param>
        /// <returns>decimal döner</returns>
        public static string ExecuteNonQuery(OracleParameter[] p, string sql, ConnectionType cnnType, ExecuteNonQueryType ent)
        {
            string ret = "0";

            OracleConnection c = GetOracleConnection(cnnType);
            OracleCommand cmd = new OracleCommand(sql, c);
            cmd.Parameters.AddRange(p);
            c.Open();
            cmd.ExecuteNonQuery();
            ret = cmd.Parameters[p.Length - 1].Value.ToString();
            c.Close();
            return ret;
        }

        /// <summary>
        /// Parametre array ile belirtilen sql cümlesini çalıştırır
        /// </summary>
        /// <param name="p">Oracle Parametre dizisi</param>
        /// <param name="sql">sql cümlesi</param>
        /// <param name="cnnType">Connection Tipi</param>
        /// <returns>Etkilenen satır sayısını döner</returns>
        public static int ExecuteNonQuery(OracleParameter[] p, string sql, ConnectionType cnnType)
        {
            int ret = 0;

            OracleConnection c = GetOracleConnection(cnnType);
            OracleCommand cmd = new OracleCommand(sql, c);
            cmd.Parameters.AddRange(p);
            c.Open();
            ret = cmd.ExecuteNonQuery();
            c.Close();
            return ret;
        }

        /// <summary>
        /// Parametre array ile belirtilen sql cümlesini çalıştırır
        /// </summary>
        /// <param name="p">Oracle Parametre dizisi</param>
        /// <param name="sql">sql cümlesi</param>
        /// <param name="cnnType">Connection Tipi</param>
        /// <returns>Etkilenen satır sayısını döner</returns>
        public static int ExecuteNonQueryWithoutConnOpen(OracleParameter[] p, string sql, ConnectionType cnnType)
        {
            int ret = 0;

            OracleConnection c = GetOracleConnection(cnnType);
            OracleCommand cmd = new OracleCommand(sql, c);
            cmd.Parameters.AddRange(p);
            //c.Open();
            ret = cmd.ExecuteNonQuery();
            //c.Close();
            return ret;
        }

        /// <summary>
        /// Execute Non Query İşlemini yapar (Parametresiz).Transaction dışardan alır
        /// </summary>
        /// <param name="komut"></param>
        /// <param name="transaction"></param>
        /// <returns></returns>
        public static string ExecuteNonQuery_Oracle(OracleCommand komut, OracleTransaction transaction)
        {
            if (komut.Transaction == null)
                komut.Transaction = transaction;
            string sonuc = komut.ExecuteNonQuery().ToString();
            return sonuc;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye sorgu sonucu gelen resultset'in ilk satırının ilk sütununun değerini döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Resultset'in ilk satırının ilk sütununun değeri</returns>
        public static object ExecuteScalar(string sql)
        {
            object ret = 0;

            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection();
                SqlCommand cmd = new SqlCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection();
                OracleCommand cmd = new OracleCommand(sql, c);
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye sorgu sonucu gelen resultset'in ilk satırının ilk sütununun değerini döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Resultset'in ilk satırının ilk sütununun değeri</returns>
        public static object ExecuteScalar(string sql, params object[] args)
        {
            object ret = 0;

            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection();
                SqlCommand cmd = new SqlCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        SqlParameter p = item as SqlParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p.ParameterName, p.Value);
                    }
                }
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection();
                OracleCommand cmd = new OracleCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        OracleParameter p = item as OracleParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p.ParameterName, p.Value);
                    }
                }
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// Verilen sorguyu çalıştırarak geriye sorgu sonucu gelen resultset'in ilk satırının ilk sütununun değerini döner
        /// </summary>
        /// <param name="sql">Çalıştırılacak sql ifadesi</param>
        /// <returns>Resultset'in ilk satırının ilk sütununun değeri</returns>
        public static object ExecuteScalar(ConnectionType cnnType, string sql, params object[] args)
        {
            object ret = 0;

            if (IsInSqlMode)
            {
                SqlConnection c = GetSqlConnection(cnnType);
                SqlCommand cmd = new SqlCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        SqlParameter p = item as SqlParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p.ParameterName, p.Value);
                    }
                }
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }
            else
            {
                OracleConnection c = GetOracleConnection(cnnType);
                OracleCommand cmd = new OracleCommand(sql, c);
                if (args.Length > 0)
                {
                    foreach (var item in args)
                    {
                        OracleParameter p = item as OracleParameter;
                        if (p.Value == null)
                        {
                            p.Value = DBNull.Value;
                        }
                        cmd.Parameters.Add(p.ParameterName, p.Value);
                    }
                }
                c.Open();
                ret = cmd.ExecuteScalar();
                c.Close();
            }

            return ret;
        }

        /// <summary>
        /// DataTable Döndüreceği SQL Cümeleleri için çalıştırılır.
        /// </summary>
        /// <param name="ConnectionName">Bağlantı Adı</param>
        /// <param name="CmdText">SQL Cümlesi</param>
        /// <returns></returns>
        public static DataTable GetDataTable(string ConnectionName, string CmdText)
        {
            // WorkFlowTraceWorker.OracleLog("1763", "GetDataTable 3 " + CmdText , "DAL");
            using (AdoDatabase db = new AdoDatabase(PasswordBoxConnectionString.ReturnConnStr(ConnectionName)))
            {
                OracleCommand cmd = new OracleCommand(CmdText);
                DataSet ds = new DataSet();
                db.Fill(cmd, ds);
                cmd = null;
                // WorkFlowTraceWorker.OracleLog("1763", "GetDataTable 3 " + CmdText + " Sonu", "DAL");
                return ds.Tables[0];
            }
        }

        /// <summary>
        /// Oracle Transaction Nesnesi Döndürür
        /// </summary>
        /// <param name="Baglanti"></param>
        /// <returns></returns>
        public static OracleTransaction GetTransaction_Oracle(OracleConnection Baglanti)
        {
            OracleTransaction myTrans = Baglanti.BeginTransaction();
            return myTrans;
        }

        /// <summary>
        /// Verilen Transaction'u commitler
        /// </summary>
        /// <param name="Trans"></param>
        public static void CommitTransaction_Oracle(OracleTransaction Trans)
        {
            try
            {
                Trans.Commit();
            }
            catch (System.Exception ex)
            {
                Trans.Rollback();
                throw ex;
            }
        }

        public static void CloseConnection(OracleConnection connection)
        {
            connection.Close();
            connection = null;
        }

        public static void RollbackTransaction_Oracle(OracleTransaction Trans)
        {
            try
            {
                Trans.Rollback();
            }
            catch (System.Exception ex)
            {
                throw ex;
            }
        }
    }
}