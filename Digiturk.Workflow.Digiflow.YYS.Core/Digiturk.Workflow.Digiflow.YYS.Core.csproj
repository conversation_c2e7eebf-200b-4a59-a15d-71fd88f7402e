﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{FC3CABD6-9696-4EF8-8153-AD999E715B19}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.YYS.Core</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.YYS.Core</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DataAccessLayer, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.v16.2">
      <HintPath>\\dtl1iis3\Deployment\DevExpress 16.2\Components\Bin\Framework\DevExpress.Web.v16.2.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=2.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net2\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionAuthorizationHelper.cs" />
    <Compile Include="ActionTypeHelper.cs" />
    <Compile Include="Common.cs" />
    <Compile Include="Db.cs" />
    <Compile Include="HrHelper.cs" />
    <Compile Include="LogicalGroupAdMapHelper.cs" />
    <Compile Include="LogicalGroupHelper.cs" />
    <Compile Include="LogicalGroupJobHelper.cs" />
    <Compile Include="LogicalGroupMemberHelper.cs" />
    <Compile Include="LogicalGroupMemberTypeHelper.cs" />
    <Compile Include="MonitoringWorkflowAdminHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RaporTalepAdminHelper.cs" />
    <Compile Include="StateAuthorizationHelper.cs" />
    <Compile Include="TopluOnayHelper.cs" />
    <Compile Include="WorkflowAdminHelper.cs" />
    <Compile Include="WorkflowHelper.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\Digiturk.Workflow.Digiflow.YYS.Core\*.* /s</PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>COPY /Y "$(TargetPath)" \\dtl1iis3\Deployment</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>