﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// Kullanıcılarla ilgili kullanılan ve kullanılabilecek fonksiyonları barındırır.
    /// </summary>
    public class HrHelper
    {
        /// <summary>
        /// Tüm aktif HrUser kayıtlarını döner
        /// </summary>
        /// <returns>Tüm aktif HrUser kayıtları</returns>
        public static List<HrUser> Get()
        {
            List<HrUser> ret = new List<HrUser>();

            string sql = "select HRU.NAME_SURNAME, HRU.E_MAIL, FLGN.LOGIN_ID, FLGN.DOMAIN_USER_NAME, FLGN.DOMAIN_USER_SID from HR_USERS as HRU inner join F_LOGIN as FLGN on HRU.E_MAIL = FLGN.EMAIL where HRU.DELETED = 'N' order by HRU.NAME_SURNAME ";
            DataTable dt = Db.ExecuteDataTable(sql);
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }

            return ret;
        }

        /// <summary>
        /// get hrusers
        /// </summary>
        /// <returns></returns>
        public static DataTable GetUsers()
        {
            string query = @"SELECT LOGIN_ID, NAME_SURNAME FROM DT_WORKFLOW.VW_USER_INFORMATION order by NAME_SURNAME asc";
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
            return dt;
        }

        /// <summary>
        /// get hrusers
        /// </summary>
        /// <returns></returns>
        public static DataTable GetUsersAktif()
        {
            string query = @"SELECT LOGIN_ID, NAME_SURNAME FROM DT_WORKFLOW.VW_USER_INFORMATION where IS_DELETED='N' order by NAME_SURNAME asc";
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);
            return dt;
        }

        /// <summary>
        /// Belirtilen email ile eşleşen HrUser kaydını döner
        /// </summary>
        /// <param name="email">Email adresi</param>
        /// <returns>Belirtilen email ile eşleşen HrUser kaydı</returns>
        public static HrUser Get(long id)
        {
            DataTable dt = new DataTable();
            try
            {
                string sql = @"Select FRAMEWORK.F_LOGIN.LOGIN_ID, FRAMEWORK.F_LOGIN.DOMAIN_USER_SID,FRAMEWORK.F_LOGIN.EMAIL,DT_WORKFLOW.DP_HR_USERS.NAME_SURNAME,FRAMEWORK.F_LOGIN.DOMAIN_USER_NAME  from FRAMEWORK.F_LOGIN
                inner join DT_WORKFLOW.DP_HR_USERS ON FRAMEWORK.F_LOGIN.DOMAIN_USER_NAME=DT_WORKFLOW.DP_HR_USERS.USERNAME
                 where LOGIN_ID=:LOGIN_ID";
                OracleParameter[] p = new OracleParameter[1];
                p[0] = new OracleParameter("LOGIN_ID", id);
                dt = Db.ExecuteDataTable(p, ConnectionType.FrameworkConnection, sql);
                if (dt != null && dt.Rows.Count == 0)
                {
                    return null;
                }
            }
            catch (Exception)
            {
                throw;
            }

            return ConvertDataRow(dt.Rows[0]);
        }

        /// <summary>
        /// Verilen datarow'daki değerleri kullanarak bir HrUser nesnesi oluşturur
        /// </summary>
        /// <param name="row"></param>
        /// <returns>HrUser nesnesinde kullanılacak değerlerin bulunduğu datarow</returns>
        public static HrUser ConvertDataRow(DataRow row)
        {
            return new HrUser
            {
                DomainUserName = row["DOMAIN_USER_NAME"].ToString(),
                DomainUserSid = row["DOMAIN_USER_SID"].ToString(),
                Email = row["EMAIL"].ToString(),
                FullName = row["NAME_SURNAME"].ToString(),
                LoginId = ConvertionHelper.ConvertValue<Decimal>(row["LOGIN_ID"])
            };
        }
    }
}