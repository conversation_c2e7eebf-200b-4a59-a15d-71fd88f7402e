﻿using DevExpress.Web;
using Digiturk.Workflow.Digiflow.Entities;
using System.Collections.Generic;
using System.Data;
using System.Web.UI.WebControls;

/// <summary>
/// YYS içerisinde çoğu yerde kullanılan ortak fonksiyonları barındırır.
/// </summary>
namespace Digiturk.Workflow.Digiflow.YYS.Core
{

    public class YYSCommon
    {
        #region Constants

        public const string SessionUserVariable = "__SessionUser";
        public const string SessionErrorTitleVariable = "__ErrorTitle";
        public const string SessionErrorMessageVariable = "__ErrorMessage";
        public const string SessionExceptionInstanceVariable = "__Exception";
        public const string QueryStringLoginIdVariable = "LoginId";
        public const string QueryStringInstanceId = "wfInstanceId";
        public const string QueryStringStart = "start";

        #endregion Constants

        /// <summary>
        /// Verilen string de belirtilen string leri kaldırmaya ya<PERSON>
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static string ReplaceAll(string text)
        {
            //“SELECT”, “ INSERT”, “ UPDATE”, “ DELETE”, “DROP”, “OR”, “,”, “+”

            text = text.Replace("SELECT", "").Replace("INSERT", "").Replace("DELETE", "").Replace("DROP", "").Replace("select", "").Replace("insert", "").Replace("update", "").Replace("drop", "");

            return text;
        }

        /// <summary>
        /// Gridview de bulunan boşluk karakterlerini silmeye yarar
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static string ReplaceSpace(string text)
        {
            text = text.Replace("&amp;", "").Replace("&nbsp;", "").Replace("amp;", "").Replace("nbsp;", "");
            return text;
        }

        /// <summary>
        /// Returns html formatted success message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Success message text</param>
        /// <returns>Html formatted success message</returns>
        public static string GetSuccessMessage(string title, string message)
        {
            message += "<p>&nbsp;</p><a href='default.aspx'><- Anasayfaya dön</a>";
            return ShowInformation(title, message);
        }

        /// <summary>
        /// Returns html formatted error message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Error message text</param>
        /// <returns>Html formatted error message</returns>
        public static string ShowError(string title, string message)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p></div></div> ", title, message);
        }

        /// <summary>
        /// Returns html formatted error message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Error message text</param>
        /// <param name="details">Details of exception</param>
        /// <returns>Html formatted error message</returns>
        public static string ShowError(string title, string message, string details)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p><p>&nbsp;</p><a href=\"#\" OnClick=\"ShowHideDiv('ErrorBoxDiv')\">Hata Detayları</a></div><div id=\"ErrorBoxDiv\" class=\"errorBox\" style=\"text-align: left\">{2}</div> </div>", title, message, details);
        }

        /// <summary>
        /// Returns html formatted information message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Information message text</param>
        /// <returns>Html formatted information message</returns>
        public static string ShowInformation(string title, string message)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-highlight ui-corner-all\" style=\"margin-top: 20px; padding: 0 .7em;\"> <p><span class=\"ui-icon ui-icon-info\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br />{1}</p></div></div>", title, message);
        }

        /// <summary>
        /// Gelen DropDownList nesnesini doldurmaya yarar
        /// </summary>
        /// <param name="TextField"></param>
        /// <param name="ValueField"></param>
        /// <param name="ddl"></param>
        /// <param name="dt"></param>
        /// <returns></returns>
        public static DropDownList FillDropDownList(string TextField, string ValueField, DropDownList ddl, DataTable dt, string FirstItemText, string FirstItemValue)
        {
            ddl.DataTextField = TextField;
            ddl.DataValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListItem(FirstItemText, FirstItemValue));
            return ddl;
        }

        public static DropDownList FillDropDownList(string TextField, string ValueField, DropDownList ddl, List<Digiturk.Workflow.Digiflow.Entities.Workflow> list, string FirstItemText, string FirstItemValue)
        {
            ddl.DataTextField = TextField;
            ddl.DataValueField = ValueField;
            ddl.DataSource = list;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListItem(FirstItemText, FirstItemValue));
            return ddl;
        }



        /// <summary>
        /// Gelen combobox nesnesini istenen değerler ve gelen datatable ile doldurup geri döndürür
        /// </summary>
        /// <param name="TextField">Görünen değer</param>
        /// <param name="ValueField">Değer</param>
        /// <param name="ddl">DropDownList</param>
        /// <param name="dt">Datatable</param>
        /// <param name="FirstItemText">Manuel eklemek istediğin alan görünen değer</param>
        /// <param name="FirstItemValue">Manuel eklemek istediğin alan değer</param>
        /// <returns></returns>
        public static ASPxComboBox FillCombobox(string TextField, string ValueField, ASPxComboBox ddl, DataTable dt, string FirstItemText, string FirstItemValue)
        {
            ddl.TextField = TextField;
            ddl.ValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListEditItem(FirstItemText, FirstItemValue));
            ddl.SelectedIndex = 0;
            return ddl;
        }

        /// <summary>
        /// Gelen combobox nesnesini istenen değerler ile ve gelen Mantıksal Grup listesi ile doldurup geri döndürür
        /// </summary>
        /// <param name="TextField">Görünen değer</param>
        /// <param name="ValueField">Değer</param>
        /// <param name="ddl">DropDownList</param>
        /// <param name="dt">Datatable</param>
        /// <param name="FirstItemText">Manuel eklemek istediğin alan görünen değer</param>
        /// <param name="FirstItemValue">Manuel eklemek istediğin alan değer</param>
        /// <returns></returns>
        public static ASPxComboBox FillComboboxLogicalGroup(string TextField, string ValueField, ASPxComboBox ddl, List<LogicalGroup> dt, string FirstItemText, string FirstItemValue)
        {
            ddl.TextField = TextField;
            ddl.ValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListEditItem(FirstItemText, FirstItemValue));
            ddl.SelectedIndex = 0;
            return ddl;
        }

        public static DropDownList FillDropDownLogicalGroup(string TextField, string ValueField, DropDownList ddl, List<LogicalGroup> dt, string FirstItemText, string FirstItemValue)
        {
            ddl.DataTextField = TextField;
            ddl.DataValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListItem(FirstItemText, FirstItemValue));
            ddl.SelectedIndex = 0;
            return ddl;
        }

        /// <summary>
        /// Gelen combobox nesnesini istenen değerler ile ve gelen İş Akışı ile doldurup geri döndürür
        /// </summary>
        /// <param name="TextField">Görünen değer</param>
        /// <param name="ValueField">Değer</param>
        /// <param name="ddl">DropDownList</param>
        /// <param name="dt">Datatable</param>
        /// <param name="FirstItemText">Manuel eklemek istediğin alan görünen değer</param>
        /// <param name="FirstItemValue">Manuel eklemek istediğin alan değer</param>
        /// <returns></returns>
        public static ASPxComboBox FillComboboxWorkFlow(string TextField, string ValueField, ASPxComboBox ddl, List<Digiturk.Workflow.Digiflow.Entities.Workflow> dt, string FirstItemText, string FirstItemValue)
        {
            ddl.TextField = TextField;
            ddl.ValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListEditItem(FirstItemText, FirstItemValue));
            ddl.SelectedIndex = 0;
            return ddl;
        }

        /// <summary>
        /// Gelen combobox nesnesini istenen değerler ile ve gelen İş Akış Stateleri ile doldurup geri döndürür
        /// </summary>
        /// <param name="TextField">Görünen değer</param>
        /// <param name="ValueField">Değer</param>
        /// <param name="ddl">DropDownList</param>
        /// <param name="dt">Datatable</param>
        /// <param name="FirstItemText">Manuel eklemek istediğin alan görünen değer</param>
        /// <param name="FirstItemValue">Manuel eklemek istediğin alan değer</param>
        /// <returns></returns>
        public static ASPxComboBox FillComboboxWorkFlowState(string TextField, string ValueField, ASPxComboBox ddl, List<WorkflowState> dt, string FirstItemText, string FirstItemValue)
        {
            ddl.TextField = TextField;
            ddl.ValueField = ValueField;
            ddl.DataSource = dt;
            ddl.DataBind();
            ddl.Items.Insert(0, new ListEditItem(FirstItemText, FirstItemValue));
            ddl.SelectedIndex = 0;
            return ddl;
        }
    }
}