﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// Mantıksal grup üye tipleri için kullanılan ve kullanılabilecek olan fonksyionları barındırır.
    /// </summary>
    public class LogicalGroupMemberTypeHelper
    {
        /// <summary>
        /// Tum Logical Group Member Tiplerini listeler.
        /// </summary>
        /// <returns></returns>
        ///
        public static List<LogicalGroupMemberType> GetAllLogicalGroupMemberTypes()
        {
            List<LogicalGroupMemberType> allLgm = new List<LogicalGroupMemberType>();
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES";

            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.DefaultConnection);

            if (dt != null)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    LogicalGroupMemberType lgm = ConvertDataRow(dr);
                    if (!allLgm.Contains(lgm))
                    {
                        allLgm.Add(lgm);
                    }
                }
            }
            return allLgm;
        }

        /// <summary>
        /// Mantıksal Grup Tipini döndürür.
        /// </summary>
        /// <returns></returns>
        ///
        public static string GetLogicalGroupMemberTypeName(long LogicalGroupMemberTypeId)
        {
            List<LogicalGroupMemberType> allLgm = new List<LogicalGroupMemberType>();
            string query = "SELECT DESCRIPTION_TR FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES where DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES.LOGICAL_GROUP_MEMBER_TYPE_ID=:LOGICAL_GROUP_MEMBER_TYPE_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_MEMBER_TYPE_ID", LogicalGroupMemberTypeId);
            DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            return dt.Rows[0][0].ToString();
        }

        /// <summary>
        /// Mantıksal Grup Tip Id yi döndürür.
        /// </summary>
        /// <returns></returns>
        ///
        public static long GetLogicalGroupMemberTypeId(string LogicalGroupMemberTypeName)
        {
            List<LogicalGroupMemberType> allLgm = new List<LogicalGroupMemberType>();
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES where DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBER_TYPES.DESCRIPTION_TR  =:DESCRIPTION_TR";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("DESCRIPTION_TR", LogicalGroupMemberTypeName);
            DataTable dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            return ConvertionHelper.ConvertValue<long>(dt.Rows[0][0].ToString());
        }

        /// <summary>
        /// Verilen DataRow nesnesini LogicalGroupMemberType nesnesine dönüştürür
        /// </summary>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static LogicalGroupMemberType ConvertDataRow(DataRow dr)
        {
            LogicalGroupMemberType lgm = new LogicalGroupMemberType();
            lgm.RequestId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_MEMBER_TYPE_ID"]);
            lgm.Description = dr["DESCRIPTION"].ToString();
            lgm.DescriptionTR = dr["DESCRIPTION_TR"].ToString();
            return lgm;
        }
    }
}