﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;
using System.Linq;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// Mantıksal grup üyeleri için kullanılan ve kullanılabilecek olan fonksiyonları barındırır.
    /// </summary>
    public class LogicalGroupMemberHelper
    {

        public static bool CheckLogicalGroupMemberCount(long LogicalGroupId)
        {
            string SQL = string.Format("Select * from DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS where LOGICAL_GROUP_ID = {0}", LogicalGroupId);
            DataTable dtb = Db.ExecuteDataTable(SQL, ConnectionType.DefaultConnection);
            return dtb.Rows.Count > 0;
        }
        /// <summary>
        /// Mantıksal Grupda Kullanıcı Var mı? Varsa True döner
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool CheckFLoginLogicalGroup(long LogicalGroupId, long LoginId)
        {
            string SQL = string.Format("Select * from DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS where LOGICAL_GROUP_ID = {0} and LOGIN_ID = {1}", LogicalGroupId, LoginId);
            DataTable dtb = Db.ExecuteDataTable(SQL, ConnectionType.DefaultConnection);
            return dtb.Rows.Count > 0;
        }

        /// <summary>
        /// Mantıksal Grupda Kullanıcı Var mı? Varsa True döner
        /// </summary>
        /// <param name="LogicalGroupId"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public static bool CheckAdGroupMembersExistForLogicalGroup(long LogicalGroupId, long adGroupMapId)
        {
            string SQL = string.Format("Select * from DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS where LOGICAL_GROUP_ID =:LOGICAL_GROUP_ID and YYS_LG_AD_MEMBERS_ID =:YYS_LG_AD_MEMBERS_ID");
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", LogicalGroupId);
            p[1] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", adGroupMapId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, SQL);
            return dt.Rows.Count > 0;
        }

        /// <summary>
        /// Secilen Logical Group Id'ye bagli olan tum Logical Group Memberlari listeler.
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static List<LogicalGroupMember> GetByLogicalGroupId(long logicalGroupId)
        {
            List<LogicalGroupMember> allLgm = new List<LogicalGroupMember>();
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    LogicalGroupMember lgm = ConvertDataRow(dr);

                    if (!allLgm.Contains(lgm))
                    {
                        allLgm.Add(lgm);
                    }
                }
            }
            return allLgm;
        }

        /// <summary>
        /// Secilen Logical Group Id'nin üyeleri arasında tüm kullanıcılar var mı kontrolü yapılır
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static bool HasAllUsersInMembers(long logicalGroupId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID and DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGIN_ID=-1";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null && dt.Rows.Count > 0)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Secilen Logical Group Id'ye bagli olan tum kayitlari siler.
        /// </summary>
        /// <param name="logicalGroupMemberId"></param>
        /// <returns></returns>
        public static int DeleteLogicalGroupMembersByLogicalGroupId(long logicalGroupId)
        {
            string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Secilen username ile mappli ad kullanıcısını member tablosundan siler (batch kullanıyo)
        /// </summary>
        /// <param name="logicalGroupMemberId"></param>
        /// <returns></returns>
        public static int DeleteMappedAdUserByUsername(long mapId, string username)
        {
            string query = @"DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS MEMBER
                 WHERE MEMBER.YYS_LG_AD_MEMBERS_ID = :YYS_LG_AD_MEMBERS_ID
                       AND MEMBER.LOGIN_ID = (SELECT F_LOGIN_ID
                                                FROM DT_WORKFLOW.DP_HR_USERS
                                               WHERE USERNAME = :USERNAME)";
            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", mapId);
            p[1] = new OracleParameter("USERNAME", username);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Sadece gelen primary key e ait olan mantıksal üyeyi siler
        /// </summary>
        /// <param name="logicalGroupMemberId"></param>
        /// <returns></returns>
        public static int DeleteLogicalGroupMember(long logicalGroupMemberId)
        {
            string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_MEMBER_ID=:LOGICAL_GROUP_MEMBER_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_MEMBER_ID", logicalGroupMemberId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        public static int DeleteLogicalGroupMemberWithGroupOfLogin(long logicalGroupId, long LoginId)
        {
            string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS.LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID AND LOGIN_ID=:LOGIN_ID";
            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
            p[1] = new OracleParameter("LOGIN_ID", LoginId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Verilen DataRow'u Logical Group Member nesnesine donusturur.
        /// </summary>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static LogicalGroupMember ConvertDataRow(DataRow dr)
        {
            LogicalGroupMember lgm = new LogicalGroupMember();
            lgm.RequestId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_MEMBER_ID"]);
            lgm.LogicalGroupId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_ID"]);
            lgm.Content = dr["CONTENT"].ToString();
            lgm.Description = dr["DESCRIPTION"].ToString();
            lgm.Email = dr["EMAIL"].ToString();
            lgm.FullName = dr["FULLNAME"].ToString();
            lgm.LogicalGroupMemberTypeId = ConvertionHelper.ConvertValue<long>(dr["LOGICAL_GROUP_MEMBER_TYPE_ID"]);
            lgm.LoginId = ConvertionHelper.ConvertValue<long>(dr["LOGIN_ID"]);
            lgm.YYS_LG_AD_MEMBERS_ID = string.IsNullOrEmpty(dr["YYS_LG_AD_MEMBERS_ID"].ToString()) ? 0 : ConvertionHelper.ConvertValue<long>(dr["YYS_LG_AD_MEMBERS_ID"]);
            return lgm;
        }

        /// <summary>
        /// Yeni bir Logical Group Member kaydi yaratir.
        /// </summary>
        /// <param name="lgm"></param>
        /// <returns></returns>
        public static int AddNewLogicalGroupMember(LogicalGroupMember lgm)
        {
            //string sequence = @"SELECT DT_WORKFLOW.SQ_YYS_LG_AD_MAPS.NEXTVAL FROM DUAL";
            //var sequenceVal = Db.ExecuteScalar(sequence);


            string query = @"INSERT INTO DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS (
                   LOGICAL_GROUP_MEMBER_TYPE_ID, LOGICAL_GROUP_ID, CONTENT, DESCRIPTION, LOGIN_ID, FULLNAME, EMAIL, CREATED, CREATED_BY,YYS_LG_AD_MEMBERS_ID)
          VALUES (:LOGICAL_GROUP_MEMBER_TYPE_ID,:LOGICAL_GROUP_ID,:CONTENT,:DESCRIPTION,:LOGIN_ID,:FULLNAME,:EMAIL,:CREATED,:CREATED_BY,:YYS_LG_AD_MEMBERS_ID )";

            OracleParameter[] p = new OracleParameter[10];
            p[0] = new OracleParameter(":LOGICAL_GROUP_MEMBER_TYPE_ID", lgm.LogicalGroupMemberTypeId);
            p[1] = new OracleParameter(":LOGICAL_GROUP_ID", lgm.LogicalGroupId);
            p[2] = new OracleParameter(":CONTENT", lgm.Content);
            p[3] = new OracleParameter(":DESCRIPTION", lgm.Description);
            p[4] = new OracleParameter(":LOGIN_ID", lgm.LoginId);
            p[5] = new OracleParameter(":FULLNAME", lgm.FullName);
            p[6] = new OracleParameter(":EMAIL", lgm.Email);
            p[7] = new OracleParameter(":CREATED", lgm.Created);
            p[8] = new OracleParameter(":CREATED_BY", lgm.CreatedBy);
            p[9] = new OracleParameter(":YYS_LG_AD_MEMBERS_ID", lgm.YYS_LG_AD_MEMBERS_ID);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);           

        }
        /// <summary>
        /// members tablosundan logical gruop Id ile mantıksal gruba ait tüm kullanıcıları getirir.
        /// LOGICAL_GROUP_MEMBER_TYPE_ID=4 outsource ise members tablosundaki ekrandan girilen name surname, degilse dp_hr_usersan name surname ceker.
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static DataTable GetAllMembersOfLogicalGroup(long logicalGroupId)
        {
            string sql = @"SELECT
            MEMBERS.LOGICAL_GROUP_MEMBER_ID,
            MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID,
            MEMBERS.LOGICAL_GROUP_ID,
            MEMBERS.CONTENT,
            MEMBERS.DESCRIPTION,
            MEMBERS.LOGIN_ID,
            CASE
                WHEN MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID = '1' THEN 'KULLANICI'
                WHEN MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID = '-1' THEN 'TÜM KULLANICILAR'
                WHEN MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID = '3' THEN 'PARAMETRE'
                WHEN MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID = '4' THEN TO_CHAR(MEMBERS.FULLNAME)
                WHEN MEMBERS.LOGICAL_GROUP_MEMBER_TYPE_ID = '5' THEN 'AD KULLANICI'
                ELSE NULL -- İhtiyaca göre bu durumu doldurabilirsiniz
            END AS TIP,
            MEMBERS.FULLNAME,
            MEMBERS.EMAIL,
            MEMBERS.CREATED,
            MEMBERS.CREATED_BY,
            MEMBERS.LAST_UPDATED,
            MEMBERS.LAST_UPDATED_BY,
            MEMBERS.VERSION_ID,
            MEMBERS.YYS_LG_AD_MEMBERS_ID,
            MAPS.AD_DOMAIN,
            MAPS.AD_GROUP,
            USR.USERNAME,
            USR.NAME_SURNAME
        FROM
            DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS MEMBERS
        INNER JOIN
            DT_WORKFLOW.YYS_LG_AD_MAPS MAPS ON MEMBERS.YYS_LG_AD_MEMBERS_ID = MAPS.YYS_LG_AD_MEMBERS_ID(+)
        LEFT JOIN
            DT_WORKFLOW.DP_HR_USERS USR ON USR.F_LOGIN_ID = MEMBERS.LOGIN_ID
        WHERE
            MEMBERS.LOGICAL_GROUP_ID =:LOGICAL_GROUP_ID";


            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, sql);
            return dt;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static List<long> GetAllLogicalGroupMembersByAdMapId(long adMapId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE YYS_LG_AD_MEMBERS_ID =:YYS_LG_AD_MEMBERS_ID ";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", adMapId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            List<long> listOfLoginId = new List<long>();
            if (dt.Rows.Count > 0)
            {
                listOfLoginId = dt.AsEnumerable().Select(x => long.Parse(x["LOGIN_ID"].ToString())).ToList<long>();
            }
            return listOfLoginId;
        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <returns></returns>
        public static List<LogicalGroupMember> GetAllLogicalGroupMembersListByAdMapId(long adMapId)
        {
            List<LogicalGroupMember> memberList = new List<LogicalGroupMember>();
            string query = "SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE YYS_LG_AD_MEMBERS_ID =:YYS_LG_AD_MEMBERS_ID ";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", adMapId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    LogicalGroupMember member = ConvertDataRow(dr);

                    if (!memberList.Contains(member))
                    {
                        memberList.Add(member);
                    }
                }
            }

            return memberList;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name = "adMapId" ></ param >
        /// < returns ></ returns >
        public static int DeleteAllMembersOfMappedAdGroupForLogicalGroup(long adMapId)
        {
            string query = "DELETE FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE YYS_LG_AD_MEMBERS_ID=:YYS_LG_AD_MEMBERS_ID";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", adMapId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        public static string GetUsernameOfMappedUser(long loginId, long mapId)
        {
            string result = "";
            string query = @"SELECT USR.USERNAME
              FROM DT_WORKFLOW.YYS_LG_AD_MAPS MAP
                   INNER JOIN DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS MEMBER
                      ON MAP.YYS_LG_AD_MEMBERS_ID = MEMBER.YYS_LG_AD_MEMBERS_ID
                   INNER JOIN DT_WORKFLOW.DP_HR_USERS USR ON MEMBER.LOGIN_ID = USR.F_LOGIN_ID
                   INNER JOIN DT_WORKFLOW.YYS_LOGICAL_GROUPS LG
                      ON MAP.LOGICAL_GROUP_ID = LG.LOGICAL_GROUP_ID
                   INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF WF_DEF
                      ON LG.WF_DEF_ID = WF_DEF.WF_WORKFLOW_DEF_ID
             WHERE     F_LOGIN_ID = :F_LOGIN_ID
                   AND MEMBER.YYS_LG_AD_MEMBERS_ID = :YYS_LG_AD_MEMBERS_ID";

            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("F_LOGIN_ID", loginId);
            p[1] = new OracleParameter("YYS_LG_AD_MEMBERS_ID", mapId);
            DataTable dtb = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            if (dtb.Rows.Count > 0)
            {
                result = dtb.AsEnumerable().Select(x => x["USERNAME"]).FirstOrDefault().ToString();
            }
            dtb = null;
            return result;
        }

        public static DataTable GetUserInfoByLoginId(string loginId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.DP_HR_USERS WHERE F_LOGIN_ID=:F_LOGIN_ID";

            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("F_LOGIN_ID", loginId);
            return Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
        }

        /// <summary>
        /// LOGICAL_GROUP_MEMBER_TYPE_ID 1 ve 5 yanı KULLANICI VE AD USER OLAN IKI TIP ICIN;
        /// LOGIN IDLERI MEMBER TABLOSU ILE DP HR TABLOSUNDA AYNI OLUP,
        /// NAME SURNAME ALANLARI (EVLİLİK SOYADI DEĞİŞİMİ, ESKİDEN LOGİCAL GRUOP KAYIDI OLAN VS.)
        /// FARKLI OLAN KAIYTLAR ICIN DP HRDAN MEMBERA UPDATE ISLEMI YAPIYOR.
        /// </summary>
        public static void UpdateUsersNameSurnameFromDpHr()
        {
            string query = @"UPDATE DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS lgm
                SET FULLNAME = (
                    SELECT dp_hr.NAME_SURNAME
                    FROM DT_WORKFLOW.DP_HR_USERS dp_hr
                    WHERE dp_hr.F_LOGIN_ID = lgm.LOGIN_ID
                )
                WHERE lgm.LOGIN_ID IN (
                    SELECT dp_hr.F_LOGIN_ID
                    FROM DT_WORKFLOW.DP_HR_USERS dp_hr
                    WHERE dp_hr.NAME_SURNAME <> lgm.fullname
                )
                AND lgm.LOGICAL_GROUP_MEMBER_TYPE_ID IN (1,5) ";
            
            
            Db.ExecuteNonQuery(query,ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Logical group members tablosudna LOGICAL_GROUP_MEMBER_TYPE_ID = 1 yani normal kullanıcı tipinde kaydı varmı kontrolü
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <param name="loginId"></param>
        public static bool IsUserExistInLogicalGroupInUserType(long logicalGroupId, long loginId)
        {
            bool sonuc = false;
            string query = @"SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE LOGIN_ID=:LOGIN_ID AND LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID AND LOGICAL_GROUP_MEMBER_TYPE_ID = '1'";

            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("LOGIN_ID", loginId);
            p[1] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);

            DataTable dtb = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            if (dtb.Rows.Count > 0)
            {
                sonuc = true;
            }
            return sonuc;            
            dtb = null;
        }

        /// <summary>
        /// Logical group members tablosudna LOGICAL_GROUP_MEMBER_TYPE_ID = 5 yani normal kullanıcı tipinde kaydı varmı kontrolü.
        /// 
        /// mapli bir ad grubu userlarını senkronize ederken kişinin baska bir ad grubu ile o mantıksal grup için mapli halde içeride 
        /// olabilecği için dublicate verinin önüne geçerken kullanılır.
        /// </summary>
        /// <param name="logicalGroupId"></param>
        /// <param name="loginId"></param>
        public static bool IsUserExistInLogicalGroupInAdUserType(long logicalGroupId, long loginId)
        {
            bool sonuc = false;
            string query = @"SELECT * FROM DT_WORKFLOW.YYS_LOGICAL_GROUP_MEMBERS WHERE LOGIN_ID=:LOGIN_ID AND LOGICAL_GROUP_ID=:LOGICAL_GROUP_ID AND LOGICAL_GROUP_MEMBER_TYPE_ID = '5'";

            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("LOGIN_ID", loginId);
            p[1] = new OracleParameter("LOGICAL_GROUP_ID", logicalGroupId);

            DataTable dtb = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            if (dtb.Rows.Count > 0)
            {
                sonuc = true;
            }
            return sonuc;
            dtb = null;
        }

    }
}