# Testing Token Refresh Fix

## What Was Fixed
The token refresh was failing because the mobile app was only sending `refreshToken` but the API requires both `accessToken` and `refreshToken`.

## How to Test

### 1. Initial Login
- Open the app
- Login with your credentials
- Verify you see the home screen

### 2. Check Console Logs
Look for these messages in the console:
- `"Login successful, received data structure"`
- `"Token stored successfully with unified storage"`

### 3. Test Token Refresh
To test the refresh flow:

#### Option A: Wait for Natural Expiration
- Wait for the token to expire (default is 30 minutes)
- Try to access any protected screen (like WebView)
- The app should automatically refresh the token

#### Option B: Force Token Refresh
1. Open Chrome DevTools console
2. Look for when the app tries to refresh:
   - You should see: `"Performing token refresh..."`
   - If successful: `"Token refresh successful"`
   - If failed: Check for error messages

### 4. What to Look For in Logs

**Success Case:**
```
Performing token refresh...
Token refresh successful
Token stored successfully with unified storage
```

**Previous Error (Fixed):**
```
Token refresh failed with status: 400 Error: {"errors":{"AccessToken":["Access token is required"]}}
```

### 5. Test App Restart
1. Force close the app
2. Reopen it
3. You should go directly to home screen (not login)
4. WebView pages should load without re-authentication

## Debugging Tips

If token refresh still fails:
1. Check both tokens are stored:
   - Look for: `"Missing tokens for refresh - access token: true refresh token: true"`
2. Verify the request body includes both tokens
3. Check API response for specific errors

## Code Changed
File: `/src/services/api.ts`
Function: `performTokenRefresh`
- Now retrieves both access and refresh tokens
- Sends both in the request body