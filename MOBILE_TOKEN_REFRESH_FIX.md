# Mobile Token Refresh Fix

## Issue
The token refresh is failing with error: `"AccessToken": ["Access token is required"]` because the mobile app is only sending the `refreshToken` field, but the API requires both `accessToken` and `refreshToken`.

## Current Issue in DigiHRApp

In `src/services/api.ts` line 714, the app is sending:
```javascript
body: JSON.stringify({ refreshToken: currentRefreshToken }),
```

## Required Fix

The mobile app needs to send both tokens in the refresh request:

```javascript
// Get both tokens
const currentAccessToken = await AsyncStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
const currentRefreshToken = await AsyncStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);

if (!currentRefreshToken || !currentAccessToken) {
  console.log('Missing tokens for refresh');
  return false;
}

// Send both tokens
const response = await fetch(`${getMobileApiUrl()}${ENDPOINTS.REFRESH}`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-Mobile-App': 'true',
    'X-Is-Mobile': 'true',
  },
  body: JSON.stringify({ 
    accessToken: currentAccessToken,    // Add this line
    refreshToken: currentRefreshToken 
  }),
});
```

## Complete Updated refreshToken Function

Replace the existing `refreshToken` function in `api.ts` with:

```javascript
export const refreshToken = async (): Promise<boolean> => {
  try {
    // Get both tokens
    const currentAccessToken = await AsyncStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
    const currentRefreshToken = await AsyncStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
    
    if (!currentRefreshToken || !currentAccessToken) {
      console.log('Missing tokens for refresh');
      return false;
    }

    console.log('Performing token refresh...');
    const response = await fetch(`${getMobileApiUrl()}${ENDPOINTS.REFRESH}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Mobile-App': 'true',
        'X-Is-Mobile': 'true',
      },
      body: JSON.stringify({ 
        accessToken: currentAccessToken,
        refreshToken: currentRefreshToken 
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Token refresh failed with status:', response.status, 'Error:', errorText);
      return false;
    }

    const data = await response.json();
    console.log('Token refresh successful');

    // Store new tokens using unified storage
    await storeToken(data.accessToken, data.expiresIn);
    if (data.refreshToken) {
      await AsyncStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, data.refreshToken);
    }

    return true;
  } catch (error) {
    console.error('Token refresh error:', error);
    return false;
  }
};
```

## API Endpoint Expectations

The `/auth/refresh` endpoint expects:
```json
{
  "accessToken": "expired-jwt-token",
  "refreshToken": "valid-refresh-token"
}
```

## Why Both Tokens Are Needed

1. **Access Token**: Even if expired, it contains user claims (userId, username) that the API uses to validate the refresh request
2. **Refresh Token**: Used to verify the user has a valid session and generate new tokens

## Testing

After implementing the fix:
1. Login successfully
2. Wait for token to expire (or manually set a shorter expiration)
3. Make any API call
4. The refresh should happen automatically
5. Verify new tokens are stored and subsequent requests work

## Alternative: API Change (Not Recommended)

Alternatively, the API could be modified to only require the refresh token, but this would require:
1. Storing refresh tokens in the database with user associations
2. Additional database lookups during refresh
3. More complex token management

The current approach (sending both tokens) is simpler and more secure.