#!/usr/bin/env pwsh
# Script to test mobile WebView authentication

Write-Host "Testing Mobile WebView Authentication" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan

$baseUrl = "https://localhost:5001"

# Test 1: Mobile WebView request without JWT (should fail)
Write-Host "`nTest 1: Mobile WebView without JWT" -ForegroundColor Yellow
try {
    $headers = @{
        "X-Mobile-App" = "true"
        "X-Is-Mobile" = "true"
        "X-From-Mobile-WebView" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/users/all" -Method Get -Headers $headers -SkipCertificateCheck
    Write-Host "UNEXPECTED: Request succeeded without JWT!" -ForegroundColor Red
    Write-Host "Response: $($response | ConvertTo-Json -Compress)" -ForegroundColor Gray
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 401) {
        Write-Host "SUCCESS: Mobile request without JWT correctly returned 401 Unauthorized" -ForegroundColor Green
    }
    else {
        Write-Host "FAILED: Unexpected error - Status: $statusCode" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 2: Get JWT token first (simulate mobile login)
Write-Host "`nTest 2: Mobile login to get JWT token" -ForegroundColor Yellow
$jwtToken = $null
try {
    # First, we need to simulate a mobile login
    # This would normally be done through the mobile app's login screen
    Write-Host "Note: In a real scenario, the mobile app would authenticate and get a JWT token" -ForegroundColor Gray
    
    # For testing, try to get a token using Windows auth with mobile headers
    $loginHeaders = @{
        "X-Mobile-App" = "true"
        "X-Is-Mobile" = "true"
        "X-DigiflowReact" = "true"  # This triggers JWT generation for Windows auth
        "Accept" = "application/json"
    }
    
    $loginResponse = Invoke-WebRequest -Uri "$baseUrl/auth/windows" -Method Post -Headers $loginHeaders -UseDefaultCredentials -SkipCertificateCheck
    
    # Check for JWT token in response headers
    if ($loginResponse.Headers["X-JWT-Token"]) {
        $jwtToken = $loginResponse.Headers["X-JWT-Token"]
        Write-Host "SUCCESS: Got JWT token from Windows auth" -ForegroundColor Green
        Write-Host "Token prefix: $($jwtToken.Substring(0, 20))..." -ForegroundColor Gray
    }
    else {
        # Try to parse token from response body
        $responseBody = $loginResponse.Content | ConvertFrom-Json
        if ($responseBody.token) {
            $jwtToken = $responseBody.token
            Write-Host "SUCCESS: Got JWT token from response body" -ForegroundColor Green
            Write-Host "Token prefix: $($jwtToken.Substring(0, 20))..." -ForegroundColor Gray
        }
        else {
            Write-Host "WARNING: No JWT token found in response" -ForegroundColor Yellow
        }
    }
}
catch {
    Write-Host "Failed to get JWT token: $_" -ForegroundColor Red
}

# Test 3: Mobile WebView request with JWT
if ($jwtToken) {
    Write-Host "`nTest 3: Mobile WebView with JWT token" -ForegroundColor Yellow
    try {
        $headers = @{
            "X-Mobile-App" = "true"
            "X-Is-Mobile" = "true"
            "X-From-Mobile-WebView" = "true"
            "Authorization" = "Bearer $jwtToken"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/users/all" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: Mobile WebView request with JWT succeeded" -ForegroundColor Green
        Write-Host "Response contains $($response.Count) users" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: Mobile WebView request with JWT failed" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 4: Test new endpoint
Write-Host "`nTest 4: Test new /users/select-options endpoint" -ForegroundColor Yellow
if ($jwtToken) {
    try {
        $headers = @{
            "X-Mobile-App" = "true"
            "X-Is-Mobile" = "true"
            "X-From-Mobile-WebView" = "true"
            "Authorization" = "Bearer $jwtToken"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/users/select-options" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: New endpoint /users/select-options works" -ForegroundColor Green
        Write-Host "Response contains $($response.Count) users" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: New endpoint failed" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 5: Test with only WebView header (simplified)
Write-Host "`nTest 5: Test with only X-From-Mobile-WebView header" -ForegroundColor Yellow
if ($jwtToken) {
    try {
        $headers = @{
            "X-From-Mobile-WebView" = "true"
            "Authorization" = "Bearer $jwtToken"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/users/all" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: Simplified WebView headers work" -ForegroundColor Green
        Write-Host "Response contains $($response.Count) users" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: Simplified WebView headers failed" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 6: Test mobile dashboard endpoint
Write-Host "`nTest 6: Test /mobile/dashboard endpoint" -ForegroundColor Yellow
if ($jwtToken) {
    try {
        $headers = @{
            "X-Mobile-App" = "true"
            "X-Is-Mobile" = "true"
            "X-From-Mobile-WebView" = "true"
            "Authorization" = "Bearer $jwtToken"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/mobile/dashboard" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: Mobile dashboard endpoint works" -ForegroundColor Green
        Write-Host "Response: $($response | ConvertTo-Json -Compress)" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: Mobile dashboard failed" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

Write-Host "`n====================================" -ForegroundColor Cyan
Write-Host "Mobile WebView Authentication Test Complete" -ForegroundColor Cyan
Write-Host "`nKey Points:" -ForegroundColor Yellow
Write-Host "- Mobile WebView requests require JWT authentication" -ForegroundColor White
Write-Host "- Headers X-Mobile-App, X-Is-Mobile, X-From-Mobile-WebView identify mobile requests" -ForegroundColor White
Write-Host "- The /users/all endpoint now works (redirects to /users/select-options)" -ForegroundColor White
Write-Host "- Mobile header validation is now more flexible" -ForegroundColor White