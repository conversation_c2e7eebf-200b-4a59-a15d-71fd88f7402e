#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run ESLint security checks
echo "🔍 Running ESLint security checks..."
npm run lint:security 2>/dev/null || npx eslint . --no-eslintrc -c eslint.config.js --quiet

if [ $? -ne 0 ]; then
  echo "❌ ESLint security check failed!"
  echo "Please fix the security issues before committing."
  exit 1
fi

echo "✅ ESLint security checks passed"

# Run npm audit (non-blocking, just for information)
echo "📊 Running npm audit for vulnerability check..."
npm audit --audit-level=moderate || true

# Run tests
echo "🧪 Running tests..."
npm test

echo "✅ All pre-commit checks completed successfully!"