#!/bin/bash

echo "========================================"
echo "DEBUG LOGIN TEST - Issue Detection"
echo "========================================"
echo ""

# Configuration
USERNAME="DTADPORTALTEST1"
PASSWORD="xF66X6V43DGQ"
API_BASE="https://digiflowtest.digiturk.com.tr"

echo "Testing login with detailed debugging..."
echo "Username: $USERNAME"
echo "API Base: $API_BASE"
echo ""

# Test 1: Check API endpoint accessibility
echo "[1] Testing API endpoint accessibility..."
health_status=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/api/health")
echo "Health endpoint status: $health_status"

if [ "$health_status" != "200" ]; then
    echo "⚠️  API health check failed - this might indicate server issues"
fi
echo ""

# Test 2: Test login endpoint with verbose output
echo "[2] Testing login endpoint with full debugging..."

login_payload=$(cat <<EOF
{
    "Username": "$USERNAME",
    "Password": "$PASSWORD",
    "Domain": "DIGITURK",
    "DeviceId": "1",
    "DeviceName": "2"
}
EOF
)

echo "Login payload:"
echo "$login_payload"
echo ""

echo "Making login request..."
login_response=$(curl -v -X POST "$API_BASE/auth/login" \
    -H "Content-Type: application/json" \
    -H "X-Mobile-App: true" \
    -H "X-Is-Mobile: true" \
    -H "Accept: application/json" \
    -d "$login_payload" 2>&1)

echo ""
echo "Full curl response:"
echo "$login_response"
echo ""

# Extract HTTP status from verbose output
http_status=$(echo "$login_response" | grep "< HTTP" | tail -1 | awk '{print $3}')
echo "HTTP Status: $http_status"

# Extract response body (everything after the last empty line)
response_body=$(echo "$login_response" | sed -n '/^$/,$p' | tail -n +2)
echo "Response Body:"
echo "$response_body"
echo ""

# Test 3: Analyze response
echo "[3] Response Analysis..."

if [[ $http_status == "200" ]]; then
    echo "✅ Login request successful (200)"
    
    # Try to extract token
    token=$(echo "$response_body" | grep -o '"[aA]ccessToken":"[^"]*' | cut -d'"' -f4)
    if [ -n "$token" ]; then
        echo "✅ Access token found: ${token:0:50}..."
    else
        echo "❌ No access token in response"
        echo "Looking for other token fields..."
        echo "$response_body" | grep -i token
    fi
    
elif [[ $http_status == "400" ]]; then
    echo "❌ Bad Request (400) - Check payload format"
    echo "Response indicates validation errors"
    
elif [[ $http_status == "401" ]]; then
    echo "❌ Unauthorized (401) - Check credentials"
    echo "Username/password might be incorrect"
    
elif [[ $http_status == "404" ]]; then
    echo "❌ Not Found (404) - Check endpoint URL"
    echo "The /auth/login endpoint might not exist"
    
elif [[ $http_status == "500" ]]; then
    echo "❌ Server Error (500) - Backend issue"
    echo "Check server logs for details"
    
else
    echo "❌ Unexpected status: $http_status"
fi

echo ""

# Test 4: Alternative endpoint test
echo "[4] Testing alternative endpoints..."

echo "Trying /api/auth/login..."
alt_response=$(curl -s -o /dev/null -w "%{http_code}" -X POST "$API_BASE/api/auth/login" \
    -H "Content-Type: application/json" \
    -d "$login_payload")
echo "Alternative endpoint status: $alt_response"

echo ""

# Test 5: Check for CORS issues
echo "[5] Testing CORS preflight..."
cors_response=$(curl -s -I -X OPTIONS "$API_BASE/auth/login" \
    -H "Origin: https://digiflowtest.digiturk.com.tr" \
    -H "Access-Control-Request-Method: POST" \
    -H "Access-Control-Request-Headers: Content-Type,X-Mobile-App")

echo "CORS preflight response:"
echo "$cors_response"
echo ""

# Test 6: Network connectivity
echo "[6] Network connectivity test..."
ping_result=$(ping -c 1 digiflowtest.digiturk.com.tr 2>&1)
if [[ $ping_result == *"1 packets transmitted, 1 received"* ]]; then
    echo "✅ Network connectivity OK"
else
    echo "⚠️  Network connectivity issues detected"
fi

echo ""
echo "========================================"
echo "DIAGNOSTIC SUMMARY"
echo "========================================"
echo ""

if [[ $http_status == "200" ]]; then
    echo "🎉 LOGIN SUCCESSFUL!"
    echo "The authentication is working correctly."
elif [[ $http_status == "400" ]]; then
    echo "🔧 PAYLOAD ISSUE"
    echo "Check the request format and required fields."
elif [[ $http_status == "401" ]]; then
    echo "🔐 CREDENTIAL ISSUE"
    echo "Verify username and password are correct."
elif [[ $http_status == "404" ]]; then
    echo "🔍 ENDPOINT ISSUE"
    echo "The login endpoint might not be available."
else
    echo "⚠️  UNKNOWN ISSUE"
    echo "Check server status and logs."
fi

echo ""
echo "Next steps:"
echo "1. Review the full curl response above"
echo "2. Check server logs if available"
echo "3. Verify API endpoint configuration"
echo "4. Test with different credentials if needed"
echo ""
