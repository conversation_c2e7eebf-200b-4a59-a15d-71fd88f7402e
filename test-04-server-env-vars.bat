@echo off
echo ========================================
echo TEST 4: Server Environment Variables (Windows)
echo ========================================
echo.
echo Testing if environment variables are set correctly...
echo.

echo [4.1] Checking WebView security environment variables...
echo DIGIFLOW_API_X_FRAME_OPTIONS = %DIGIFLOW_API_X_FRAME_OPTIONS%
echo DIGIFLOW_API_FRAME_ANCESTORS = %DIGIFLOW_API_FRAME_ANCESTORS%
echo.

if "%DIGIFLOW_API_X_FRAME_OPTIONS%"=="SAMEORIGIN" (
    echo ✅ X_FRAME_OPTIONS is correctly set to SAMEORIGIN
) else if "%DIGIFLOW_API_X_FRAME_OPTIONS%"=="" (
    echo ❌ X_FRAME_OPTIONS is NOT SET
) else (
    echo ⚠️ X_FRAME_OPTIONS is set to: %DIGIFLOW_API_X_FRAME_OPTIONS% ^(expected: SAMEORIGIN^)
)

if "%DIGIFLOW_API_FRAME_ANCESTORS%"=="self" (
    echo ✅ FRAME_ANCESTORS is correctly set to self
) else if "%DIGIFLOW_API_FRAME_ANCESTORS%"=="" (
    echo ❌ FRAME_ANCESTORS is NOT SET
) else (
    echo ⚠️ FRAME_ANCESTORS is set to: %DIGIFLOW_API_FRAME_ANCESTORS% ^(expected: self^)
)
echo.

echo [4.2] Checking other important environment variables...
echo ASPNETCORE_ENVIRONMENT = %ASPNETCORE_ENVIRONMENT%
echo DIGIFLOW_API_BASE_URL = %DIGIFLOW_API_BASE_URL%
echo DIGIFLOW_API_DEBUG_MODE = %DIGIFLOW_API_DEBUG_MODE%
echo.

echo [4.3] Checking if IIS needs restart...
echo Last IIS restart time:
wmic process where "name='w3wp.exe'" get CreationDate /format:list | findstr "CreationDate" | head -1
echo.

echo ========================================
echo TEST 4 RESULTS
echo ========================================
echo Please report back:
echo 1. Are X_FRAME_OPTIONS and FRAME_ANCESTORS set correctly?
echo 2. What is the ASPNETCORE_ENVIRONMENT value?
echo 3. When was IIS last restarted?
echo 4. Any error messages?
echo.
echo If environment variables are not set, run:
echo deployment\set-all-env-vars-test.bat
echo Then: iisreset
echo.
pause
