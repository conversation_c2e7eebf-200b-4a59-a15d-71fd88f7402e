#!/bin/bash

echo "========================================"
echo "TEST 3: Workflow URL Test (MacBook)"
echo "========================================"
echo ""

# JWT Authentication Configuration
# FILL IN YOUR CREDENTIALS HERE:
USERNAME="DTADPORTALTEST1"  # e.g., "DTUMKORKMAZ"
PASSWORD="xF66X6V43DGQ"  # Your AD password
API_BASE="https://digiflowtest.digiturk.com.tr"

echo "Testing the specific workflow URL that causes the error..."
echo "Username: $USERNAME"
echo ""

# Function to get JWT token
get_jwt_token() {
    echo "[AUTH] Getting JWT token..."

    login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

    token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

    if [ -n "$token" ]; then
        echo "✅ JWT token acquired"
        return 0
    else
        echo "❌ Failed to get JWT token"
        echo "Response: $login_response"
        return 1
    fi
}

# Get JWT token
if ! get_jwt_token; then
    echo "❌ Cannot proceed without JWT token"
    exit 1
fi
echo ""

WORKFLOW_URL="$API_BASE/react/main/workflow?name=delegation"

# Test 3.1: Direct workflow URL access (no auth - should redirect or show login)
echo "[3.1] Testing workflow URL access (no auth)..."
echo "URL: $WORKFLOW_URL"
workflow_status=$(curl -s -o /dev/null -w "%{http_code}" "$WORKFLOW_URL")
echo "Status Code: $workflow_status"

if [ "$workflow_status" = "200" ]; then
    echo "✅ Workflow URL responds with 200 OK"
elif [ "$workflow_status" = "302" ] || [ "$workflow_status" = "401" ]; then
    echo "⚠️  Workflow URL redirects/requires auth: $workflow_status"
else
    echo "❌ Workflow URL returns status: $workflow_status"
fi
echo ""

# Test 3.2: Test workflow URL with authentication
echo "[3.2] Testing workflow URL with authentication..."
auth_workflow_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $token" \
  "$WORKFLOW_URL")
echo "Auth workflow status: $auth_workflow_status"
echo ""

# Test 3.3: Check for error content (with auth)
echo "[3.3] Testing workflow URL content for errors (with auth)..."
workflow_content=$(curl -s \
  -H "Authorization: Bearer $token" \
  "$WORKFLOW_URL")

# Save content to temp file for analysis
echo "$workflow_content" > /tmp/workflow_response.html

# Check for error patterns
if [[ $workflow_content == *"useWorkflowConfig must be used within a WorkflowConfigProvider"* ]]; then
    echo "❌ Found WorkflowConfigProvider error in content"
elif [[ $workflow_content == *"error"* ]] || [[ $workflow_content == *"Error"* ]]; then
    echo "⚠️  Found potential error content"
else
    echo "✅ No obvious error content detected"
fi

# Check for React content
if [[ $workflow_content == *"react"* ]] || [[ $workflow_content == *"React"* ]] || [[ $workflow_content == *"root"* ]]; then
    echo "✅ React content detected"
else
    echo "❌ No React content detected"
fi

# Check content length
content_length=${#workflow_content}
echo "Content length: $content_length characters"

if [ "$content_length" -lt 1000 ]; then
    echo "⚠️  Content seems very short (possible empty page)"
else
    echo "✅ Content has reasonable length"
fi
echo ""

# Test 3.4: Check workflow URL with webview headers + auth
echo "[3.4] Testing workflow URL with webview headers + auth..."
webview_workflow_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $token" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$WORKFLOW_URL")
echo "WebView + Auth workflow status: $webview_workflow_status"

# Get frame options for this request
webview_auth_frame=$(curl -s -I \
  -H "Authorization: Bearer $token" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$WORKFLOW_URL" | grep -i "x-frame-options")
echo "WebView + Auth X-Frame-Options: $webview_auth_frame"
echo ""

# Test 3.4: Test different workflow URLs
echo "[3.4] Testing alternative workflow URLs..."

# Test base workflow URL
base_workflow="https://digiflowtest.digiturk.com.tr/react/main/workflow"
base_status=$(curl -s -o /dev/null -w "%{http_code}" "$base_workflow")
echo "Base workflow URL status: $base_status"

# Test with trailing slash
slash_workflow="https://digiflowtest.digiturk.com.tr/react/main/workflow/"
slash_status=$(curl -s -o /dev/null -w "%{http_code}" "$slash_workflow")
echo "Workflow URL with slash status: $slash_status"
echo ""

echo "========================================"
echo "TEST 3 RESULTS SUMMARY"
echo "========================================"
echo "Workflow URL: $WORKFLOW_URL"
echo "Direct access status: $workflow_status"
echo "WebView access status: $webview_workflow_status"
echo "Content length: $content_length characters"
echo ""
echo "Content saved to: /tmp/workflow_response.html"
echo "You can check it with: cat /tmp/workflow_response.html"
echo ""
echo "Please report back:"
echo "1. All status codes"
echo "2. Whether errors were found in content"
echo "3. Content length"
echo "4. First few lines of /tmp/workflow_response.html"
echo ""
