# WebView Empty Page Debugging Guide

## Issue: Empty Page in WebView Despite Security Header Fixes

If you're still seeing an empty page in the webview after implementing the security header fixes, here's a systematic approach to diagnose the issue.

## **Step 1: Verify Security Headers Are Applied**

Run the enhanced test script:
```powershell
.\test-webview-headers.ps1 -ApiUrl "https://digiflowtest.digiturk.com.tr" -ReactAppUrl "https://digiflowtest.digiturk.com.tr"
```

**Expected Results:**
- Regular requests: `X-Frame-Options: DENY`
- WebView requests: `X-Frame-Options: SAMEORIGIN`
- React app accessible with 200 status

## **Step 2: Check React App Accessibility**

### Test Direct Access
1. Open the React app URL directly in a browser
2. Verify it loads without errors
3. Check browser console for JavaScript errors

### Common React App Issues:
- **Build/Deployment Problems**: App not properly built or deployed
- **Routing Issues**: React Router not handling the URL correctly
- **Environment Variables**: Missing or incorrect environment configuration
- **Asset Loading**: CSS/JS files not loading properly

## **Step 3: WebView-Specific Debugging**

### Enable WebView Debugging (DigiHRApp)

Add debugging to the WebView component:

```typescript
// In DigiHRApp WebView component
<WebView
  source={{ uri: url }}
  onError={(syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('WebView Error:', nativeEvent);
    Alert.alert('WebView Error', nativeEvent.description);
  }}
  onHttpError={(syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('HTTP Error:', nativeEvent);
    Alert.alert('HTTP Error', `Status: ${nativeEvent.statusCode}`);
  }}
  onLoadStart={() => console.log('WebView load started')}
  onLoadEnd={() => console.log('WebView load ended')}
  onMessage={(event) => {
    console.log('WebView message:', event.nativeEvent.data);
  }}
  // Enable debugging
  webviewDebuggingEnabled={true}
  // Add user agent for better debugging
  userAgent="DigiHRApp-WebView-Debug"
/>
```

## **Step 4: Check React App Error Boundary**

The React app might be hitting an error boundary. Check for:

### Common Error Boundary Triggers:
1. **Authentication Failures**: JWT token issues
2. **API Call Failures**: CORS or network issues
3. **Missing Dependencies**: Required services not available
4. **Configuration Errors**: Wrong environment settings

### Debug Error Boundary:
Add logging to the React app's error boundary:

```typescript
// In DigiflowReact error boundary
componentDidCatch(error: Error, errorInfo: ErrorInfo) {
  console.error('Error Boundary Caught:', error, errorInfo);

  // Send message to WebView if available
  if (window.ReactNativeWebView) {
    window.ReactNativeWebView.postMessage(JSON.stringify({
      type: 'ERROR_BOUNDARY',
      error: error.message,
      stack: error.stack
    }));
  }
}
```

## **Step 5: Network and CORS Issues**

### Check API Connectivity from WebView Context

Test if the React app can reach the API when loaded in webview:

```javascript
// Add to React app for debugging
useEffect(() => {
  const testApiConnection = async () => {
    try {
      const response = await fetch('/api/health', {
        headers: {
          'X-Mobile-App': 'true',
          'X-From-Mobile-WebView': 'true'
        }
      });
      console.log('API Health Check:', response.status);

      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'API_TEST',
          status: response.status,
          success: response.ok
        }));
      }
    } catch (error) {
      console.error('API Test Failed:', error);

      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'API_TEST_ERROR',
          error: error.message
        }));
      }
    }
  };

  testApiConnection();
}, []);
```

## **Step 6: URL and Routing Issues**

### Verify Correct URL
Ensure the WebView is loading the correct URL:

```typescript
// In DigiHRApp - log the URL being loaded
const webViewUrl = 'https://digiflowtest.digiturk.com.tr';
console.log('Loading WebView URL:', webViewUrl);

// Test different URL variations
const urlVariations = [
  'https://digiflowtest.digiturk.com.tr',
  'https://digiflowtest.digiturk.com.tr/',
  'https://digiflowtest.digiturk.com.tr/index.html',
  'http://digiflowtest.digiturk.com.tr' // Test HTTP vs HTTPS
];
```

### React Router Issues
If using React Router, ensure it handles the webview context:

```typescript
// In DigiflowReact routing
const isWebView = useWebView();

return (
  <Router>
    <Routes>
      {/* Ensure routes work in webview context */}
      <Route path="/" element={<HomePage />} />
      <Route path="*" element={<NotFoundPage />} />
    </Routes>
  </Router>
);
```

## **Step 7: Authentication Issues**

### JWT Token Problems
The webview might fail due to authentication issues:

```typescript
// In DigiHRApp - ensure JWT token is passed
const injectedJavaScript = `
  window.WEBVIEW_JWT_TOKEN = '${jwtToken}';
  window.WEBVIEW_USER_ID = '${userId}';
  window.WEBVIEW_SESSION_ID = '${sessionId}';
  true; // Required for injected script
`;

<WebView
  injectedJavaScriptBeforeContentLoaded={injectedJavaScript}
  // ... other props
/>
```

## **Step 8: Debugging Checklist**

### ✅ **Immediate Checks:**
- [ ] Security headers correctly applied (use test script)
- [ ] React app loads directly in browser
- [ ] WebView URL is correct and accessible
- [ ] No JavaScript errors in browser console
- [ ] API endpoints are reachable

### ✅ **WebView-Specific Checks:**
- [ ] WebView error handlers show no errors
- [ ] User agent headers are being sent
- [ ] JWT token is properly injected
- [ ] WebView debugging enabled

### ✅ **React App Checks:**
- [ ] Error boundary not triggered
- [ ] Authentication working in webview context
- [ ] API calls successful from webview
- [ ] Routing works correctly

### ✅ **Network Checks:**
- [ ] CORS headers allow webview origin
- [ ] API responds to webview requests
- [ ] No network timeouts or failures

## **Step 9: Common Solutions**

### **Empty Page = Authentication Failure**
```typescript
// Add auth debugging to React app
useEffect(() => {
  const checkAuth = async () => {
    try {
      const response = await fetch('/api/auth/verify-session');
      if (!response.ok) {
        console.error('Auth check failed:', response.status);
        // Redirect to login or show error
      }
    } catch (error) {
      console.error('Auth check error:', error);
    }
  };

  checkAuth();
}, []);
```

### **Empty Page = Build Issues**
```bash
# Rebuild React app
npm run build

# Check build output
ls -la build/

# Verify index.html exists and is valid
cat build/index.html
```

### **Empty Page = CORS Issues**
```csharp
// In API middleware - add more detailed CORS logging
if (IsWebViewRequest(context))
{
    _logger.LogInformation("WebView request detected from: {Origin}",
        context.Request.Headers["Origin"].FirstOrDefault());
}
```

## **Step 10: Emergency Fallback**

If all else fails, create a simple test page:

```html
<!-- Create test.html in React app public folder -->
<!DOCTYPE html>
<html>
<head>
    <title>WebView Test</title>
</head>
<body>
    <h1>WebView Test Page</h1>
    <p>If you can see this, the webview is working</p>
    <script>
        console.log('Test page loaded in webview');
        if (window.ReactNativeWebView) {
            window.ReactNativeWebView.postMessage('Test page loaded successfully');
        }
    </script>
</body>
</html>
```

Load this test page in the webview to isolate the issue.

## **Next Steps**

1. Run the enhanced test script
2. Check WebView error logs
3. Verify React app loads directly
4. Test with simple HTML page
5. Add debugging to both WebView and React app
6. Check authentication flow in webview context
