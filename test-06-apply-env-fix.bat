@echo off
echo ========================================
echo TEST 6: Apply Environment Variable Fix (Windows)
echo ========================================
echo.
echo This script will apply the webview environment variable fix
echo.

echo WARNING: This will set system-wide environment variables
echo Make sure you are running as Administrator
echo.
set /p "CONFIRM=Do you want to apply the webview fix? (yes/no): "
if /i not "%CONFIRM%"=="yes" (
    echo Fix cancelled by user
    goto :end
)

echo.
echo [6.1] Setting webview-friendly environment variables...
setx DIGIFLOW_API_X_FRAME_OPTIONS "SAMEORIGIN" /M
if %errorlevel% equ 0 (
    echo ✅ DIGIFLOW_API_X_FRAME_OPTIONS set to SAMEORIGIN
) else (
    echo ❌ Failed to set DIGIFLOW_API_X_FRAME_OPTIONS
)

setx DIGIFLOW_API_FRAME_ANCESTORS "self" /M
if %errorlevel% equ 0 (
    echo ✅ DIGIFLOW_API_FRAME_ANCESTORS set to self
) else (
    echo ❌ Failed to set DIGIFLOW_API_FRAME_ANCESTORS
)
echo.

echo [6.2] Verifying environment variables...
echo DIGIFLOW_API_X_FRAME_OPTIONS = %DIGIFLOW_API_X_FRAME_OPTIONS%
echo DIGIFLOW_API_FRAME_ANCESTORS = %DIGIFLOW_API_FRAME_ANCESTORS%
echo.

echo [6.3] Restarting IIS to apply changes...
echo This may take a moment...
iisreset
if %errorlevel% equ 0 (
    echo ✅ IIS restarted successfully
) else (
    echo ❌ IIS restart failed
)
echo.

echo [6.4] Testing the fix...
timeout /t 10 /nobreak >nul
echo Testing API with webview headers...
curl -s -I -H "X-Mobile-App: true" -H "X-From-Mobile-WebView: true" http://localhost/DigiflowAPI/api/health | findstr "X-Frame-Options"
echo.

echo [6.5] Testing external API...
curl -s -I -H "X-Mobile-App: true" -H "X-From-Mobile-WebView: true" https://digiflowtest.digiturk.com.tr/api/health | findstr "X-Frame-Options"
echo.

echo ========================================
echo TEST 6 RESULTS
echo ========================================
echo Environment variables have been set and IIS restarted.
echo.
echo Please report back:
echo 1. Were environment variables set successfully?
echo 2. Did IIS restart without errors?
echo 3. What X-Frame-Options header do you see in the tests?
echo 4. Any error messages?
echo.
echo Expected result: X-Frame-Options: SAMEORIGIN
echo.

:end
pause
