# Mobile WebView Token Persistence Guide

## Overview
This guide explains how to implement JWT token persistence in the mobile WebView to maintain authentication across app restarts and refreshes.

## Problem Solved
Previously, when the mobile app was closed or refreshed, users had to re-authenticate because:
- Windows authentication was used transparently without generating JWT tokens
- No token persistence mechanism existed
- No refresh token implementation for extending sessions

## Solution Architecture

### 1. New Authentication Flow
```
1. Mobile WebView → POST /auth/windows (with Windows credentials)
2. API validates Windows auth → generates JWT + refresh token
3. Mobile app stores tokens securely
4. All subsequent requests use JWT Bearer token
5. When JWT expires → use refresh token to get new tokens
6. Tokens persist across app restarts
```

### 2. New Endpoints

#### Windows Authentication with JWT Generation
```http
POST /auth/windows
Headers:
  X-From-Mobile-WebView: true
  X-Mobile-App: true
  X-Is-Mobile: true
  Authorization: Negotiate <windows-auth-token>

Response:
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "dGhpcyBpcyBhIHJlZnJlc2ggdG9rZW4...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "userId": "12345",
  "username": "DOMAIN\\username",
  "issuedAt": "2025-01-28T10:30:00Z",
  "accessTokenMinutes": 60,
  "refreshTokenDays": 90,
  "autoRefreshEnabled": true,
  "tokenRefreshBufferMinutes": 5
}
```

#### Refresh Token
```http
POST /auth/refresh
Content-Type: application/json

{
  "accessToken": "expired-jwt-token",  // REQUIRED - even if expired
  "refreshToken": "valid-refresh-token"  // REQUIRED - must be valid
}

Response: Same as above with new tokens
```

**IMPORTANT**: Both `accessToken` and `refreshToken` fields are REQUIRED. The API will return a 400 error if either field is missing.

## Mobile App Implementation

### 1. Initial Authentication
```javascript
// In your WebView JavaScript interface
async function authenticateWithWindows() {
  try {
    const response = await fetch('https://digiflow-api/auth/windows', {
      method: 'POST',
      headers: {
        'X-From-Mobile-WebView': 'true',
        'X-Mobile-App': 'true',
        'X-Is-Mobile': 'true',
        'Content-Type': 'application/json'
      },
      credentials: 'include' // Important for Windows auth
    });

    if (response.ok) {
      const tokens = await response.json();
      
      // Store tokens securely in mobile app
      await secureStorage.setItem('accessToken', tokens.accessToken);
      await secureStorage.setItem('refreshToken', tokens.refreshToken);
      await secureStorage.setItem('tokenExpiry', 
        new Date(Date.now() + tokens.expiresIn * 1000).toISOString());
      await secureStorage.setItem('userId', tokens.userId);
      await secureStorage.setItem('username', tokens.username);
      
      // Configure axios/fetch to use the token
      setAuthorizationHeader(tokens.accessToken);
      
      return tokens;
    }
  } catch (error) {
    console.error('Windows auth failed:', error);
    throw error;
  }
}
```

### 2. Token Persistence on App Start
```javascript
// Check for existing tokens when app starts
async function initializeAuthentication() {
  const accessToken = await secureStorage.getItem('accessToken');
  const refreshToken = await secureStorage.getItem('refreshToken');
  const tokenExpiry = await secureStorage.getItem('tokenExpiry');
  
  if (!accessToken || !refreshToken) {
    // No tokens, need to authenticate
    return await authenticateWithWindows();
  }
  
  // Check if token is expired or about to expire
  const expiryTime = new Date(tokenExpiry).getTime();
  const bufferTime = 5 * 60 * 1000; // 5 minutes buffer
  
  if (Date.now() > expiryTime - bufferTime) {
    // Token expired or about to expire, refresh it
    return await refreshAccessToken();
  }
  
  // Token is valid, use it
  setAuthorizationHeader(accessToken);
  return { accessToken, refreshToken };
}
```

### 3. Automatic Token Refresh
```javascript
async function refreshAccessToken() {
  const accessToken = await secureStorage.getItem('accessToken');
  const refreshToken = await secureStorage.getItem('refreshToken');
  
  if (!refreshToken) {
    // No refresh token, need full re-authentication
    return await authenticateWithWindows();
  }
  
  try {
    const response = await fetch('https://digiflow-api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-From-Mobile-WebView': 'true',
        'X-Mobile-App': 'true'
      },
      body: JSON.stringify({ accessToken, refreshToken })
    });

    if (response.ok) {
      const tokens = await response.json();
      
      // Update stored tokens
      await secureStorage.setItem('accessToken', tokens.accessToken);
      await secureStorage.setItem('refreshToken', tokens.refreshToken);
      await secureStorage.setItem('tokenExpiry', 
        new Date(Date.now() + tokens.expiresIn * 1000).toISOString());
      
      setAuthorizationHeader(tokens.accessToken);
      return tokens;
    } else if (response.status === 401) {
      // Refresh token invalid, need full re-authentication
      return await authenticateWithWindows();
    }
  } catch (error) {
    console.error('Token refresh failed:', error);
    // Fallback to full authentication
    return await authenticateWithWindows();
  }
}
```

### 4. API Request Interceptor
```javascript
// Set up axios/fetch interceptor to handle token refresh automatically
function setupApiInterceptor() {
  // For axios
  axios.interceptors.request.use(async (config) => {
    const token = await secureStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add mobile headers
    config.headers['X-From-Mobile-WebView'] = 'true';
    config.headers['X-Mobile-App'] = 'true';
    config.headers['X-Is-Mobile'] = 'true';
    
    return config;
  });

  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response?.status === 401) {
        // Token expired, try to refresh
        try {
          await refreshAccessToken();
          // Retry the original request
          return axios.request(error.config);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          redirectToLogin();
        }
      }
      return Promise.reject(error);
    }
  );
}
```

### 5. WebView Configuration
```javascript
// Configure WebView to inject auth headers
webView.addEventListener('loadstart', async (event) => {
  const accessToken = await secureStorage.getItem('accessToken');
  
  if (accessToken) {
    // Inject authorization header for all API requests
    webView.executeScript({
      code: `
        // Override fetch to add auth headers
        const originalFetch = window.fetch;
        window.fetch = function(...args) {
          let [url, options = {}] = args;
          
          // Only add auth for API requests
          if (url.includes('/api/') || url.includes('digiflow')) {
            options.headers = {
              ...options.headers,
              'Authorization': 'Bearer ${accessToken}',
              'X-From-Mobile-WebView': 'true',
              'X-Mobile-App': 'true',
              'X-Is-Mobile': 'true'
            };
          }
          
          return originalFetch.apply(this, [url, options]);
        };
        
        // Same for XMLHttpRequest if needed
        const originalXhrOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(...args) {
          this.addEventListener('readystatechange', function() {
            if (this.readyState === 1) {
              const url = args[1];
              if (url.includes('/api/') || url.includes('digiflow')) {
                this.setRequestHeader('Authorization', 'Bearer ${accessToken}');
                this.setRequestHeader('X-From-Mobile-WebView', 'true');
                this.setRequestHeader('X-Mobile-App', 'true');
                this.setRequestHeader('X-Is-Mobile', 'true');
              }
            }
          });
          return originalXhrOpen.apply(this, args);
        };
      `
    });
  }
});
```

## Security Considerations

### 1. Secure Token Storage
- Use platform-specific secure storage (iOS Keychain, Android Keystore)
- Never store tokens in plain text or SharedPreferences
- Encrypt tokens if storing in SQLite or other databases

### 2. Token Expiration Handling
- Check token expiration before each API call
- Refresh tokens proactively (5 minutes before expiry)
- Handle refresh token expiration gracefully

### 3. Network Security
- Always use HTTPS for API calls
- Implement certificate pinning for additional security
- Validate SSL certificates

### 4. Token Scope
- Tokens are specific to the user and device
- Implement device fingerprinting if needed
- Revoke tokens on logout or app uninstall

## Testing Token Persistence

### 1. Initial Authentication Test
```bash
# Test Windows auth endpoint
curl -X POST https://localhost:5001/auth/windows \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Mobile-App: true" \
  -H "X-Is-Mobile: true" \
  --negotiate -u : \
  --insecure
```

### 2. Token Refresh Test
```bash
# Test refresh endpoint
curl -X POST https://localhost:5001/auth/refresh \
  -H "Content-Type: application/json" \
  -H "X-From-Mobile-WebView: true" \
  -d '{
    "accessToken": "your-expired-token",
    "refreshToken": "your-refresh-token"
  }' \
  --insecure
```

### 3. App Restart Test
1. Authenticate and get tokens
2. Store tokens securely
3. Force close the app
4. Reopen the app
5. Verify tokens are loaded and API calls work
6. Verify refresh works when token expires

## Troubleshooting

### Common Issues

1. **Windows auth fails**
   - Ensure Windows credentials are passed correctly
   - Check if user exists in the database
   - Verify mobile headers are present

2. **Tokens not persisting**
   - Check secure storage implementation
   - Verify storage permissions
   - Check for storage quota issues

3. **Refresh token fails**
   - Ensure refresh token is valid and not expired
   - Check if both tokens are sent in request
   - Verify token signature and claims

4. **API calls return 401**
   - Check if Authorization header is set correctly
   - Verify token hasn't expired
   - Ensure mobile headers are present

## Environment Variables Required

Make sure these are set in the API environment:
```
DIGIFLOW_JWT_SECRET=your-secret-key
DIGIFLOW_JWT_ISSUER=DigiflowAPI
DIGIFLOW_JWT_AUDIENCE=DigiflowMobile
DIGIFLOW_JWT_ACCESS_TOKEN_MINUTES=60
DIGIFLOW_JWT_REFRESH_TOKEN_DAYS=90
DIGIFLOW_MAX_REFRESH_TOKENS_PER_USER=5
DIGIFLOW_MOBILE_LONG_TERM_AUTH=true
DIGIFLOW_AUTO_REFRESH_ON_RESUME=true
DIGIFLOW_TOKEN_REFRESH_BUFFER_MINUTES=5
```

## Summary

With this implementation:
1. Users authenticate once with Windows credentials
2. JWT tokens are generated and stored securely
3. Tokens persist across app restarts
4. Tokens auto-refresh before expiry
5. Users stay logged in for up to 90 days (refresh token lifetime)
6. Seamless experience without repeated login prompts