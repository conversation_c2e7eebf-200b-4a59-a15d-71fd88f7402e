﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System.Collections.Generic;
using System.Data;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// İş Akış kuralları içinde kullanılan ve kullanılacak olan fonksiyonları barındırır.
    /// </summary>
    public class ActionAuthorizationHelper
    {
        /// <summary>
        /// Secilen WorkflowRuleId bazinda tek bir Workflow Rule dondurur.
        /// </summary>
        /// <param name="workflowRuleId"></param>
        /// <returns></returns>
        public static ActionAuthorization GetByWorkflowRuleID(long workflowRuleId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_AUTHORIZATION_ID=:ACTION_AUTHORIZATION_ID";
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("ACTION_AUTHORIZATION_ID", workflowRuleId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null && dt.Rows.Count == 0)
            {
                return null;
            }

            return ConvertDataRow(dt.Rows[0]);
        }

        /// <summary>
        /// Verilen DataRow parametresini ActionAuthorization nesnesine donusturur.
        /// </summary>
        /// <param name="dr"></param>
        /// <returns></returns>
        public static ActionAuthorization ConvertDataRow(DataRow dr)
        {
            ActionAuthorization wfr = new ActionAuthorization();
            wfr.RequestId = ConvertionHelper.ConvertValue<long>(dr["ACTION_AUTHORIZATION_ID"]);
            wfr.WfDefId = ConvertionHelper.ConvertValue<long>(dr["WF_DEF_ID"]);
            wfr.SourceId = ConvertionHelper.ConvertValue<long>(dr["SOURCE_ID"]);
            wfr.StateDefId = ConvertionHelper.ConvertValue<long>(dr["STATE_DEF_ID"]);
            wfr.ActionId = ConvertionHelper.ConvertValue<long>(dr["ACTION_ID"]);
            wfr.ToGroupId = ConvertionHelper.ConvertValue<long>(dr["TO_GROUP_ID"]);
            wfr.IsActive = ConvertionHelper.ConvertValue<long>(dr["IS_ACTIVE"]);
            return wfr;
        }

        /// <summary>
        /// Secilen WorkflowId bazinda tek bir ActionAuthorization dondurur.
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public static List<ActionAuthorization> GetByWorkflowId(long workflowId)
        {
            string query = "SELECT * FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.WF_DEF_ID=:WF_DEF_ID order by DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.STATE_DEF_ID,DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_ID";
            List<ActionAuthorization> ret = new List<ActionAuthorization>();
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_DEF_ID", workflowId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);
            if (dt.Rows.Count > 0)
            {
                foreach (DataRow item in dt.Rows)
                {
                    ret.Add(ConvertDataRow(item));
                }
            }

            return ret;
        }

        /// <summary>
        /// Secilen WorkflowRuleId'ye bagli olan ActionAuthorization'u siler.
        /// </summary>
        /// <param name="workflowRuleId"></param>
        /// <returns></returns>
        public static long DeleteWorkflowRule(long workflowRuleId)
        {
            string query = "DELETE FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_AUTHORIZATION_ID=:ACTION_AUTHORIZATION_ID";
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("ACTION_AUTHORIZATION_ID", workflowRuleId);
            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Yeni bir ActionAuthorization kayit islemi yapar.
        /// </summary>
        /// <param name="wfr"></param>
        /// <returns></returns>
        public static void AddNewWorkflowRule(ActionAuthorization wfr)
        {
            string query = @"INSERT INTO DT_WORKFLOW.YYS_ACTION_AUTHORIZATION (
                           WF_DEF_ID, STATE_DEF_ID, ACTION_ID, SOURCE_ID, TO_GROUP_ID, CREATED, CREATED_BY, IS_ACTIVE,LAST_UPDATED, LAST_UPDATED_BY)
                   VALUES (:WF_DEF_ID, :STATE_DEF_ID, :ACTION_ID, :SOURCE_ID, :TO_GROUP_ID, :CREATED, :CREATED_BY, :IS_ACTIVE,:LAST_UPDATED, :LAST_UPDATED_BY )";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[10];

            p[0] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
            p[1] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
            p[2] = new OracleParameter("ACTION_ID", wfr.ActionId);
            p[3] = new OracleParameter("SOURCE_ID", wfr.SourceId);
            p[4] = new OracleParameter("TO_GROUP_ID", wfr.ToGroupId);
            p[5] = new OracleParameter("CREATED", wfr.Created);
            p[6] = new OracleParameter("CREATED_BY", wfr.CreatedBy);
            p[7] = new OracleParameter("IS_ACTIVE", wfr.IsActive);
            p[8] = new OracleParameter("LAST_UPDATED", wfr.LastUpdated);
            p[9] = new OracleParameter("LAST_UPDATED_BY", wfr.LastUpdatedBy);

            Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }

        /// <summary>
        /// Bu kuralın tanımlı olup olmadığını kontrol eder
        /// </summary>
        /// <param name="wfr">Action Authorization nesnesi</param>
        /// <returns></returns>
        public static ActionAuthorization IsDefined(ActionAuthorization wfr)
        {
            string query = @"SELECT * FROM DT_WORKFLOW.YYS_ACTION_AUTHORIZATION WHERE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.ACTION_ID=:ACTION_ID
AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.WF_DEF_ID=:WF_DEF_ID AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.STATE_DEF_ID=:STATE_DEF_ID AND DT_WORKFLOW.YYS_ACTION_AUTHORIZATION.SOURCE_ID=:SOURCE_ID";
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[4];
            p[0] = new OracleParameter("ACTION_ID", wfr.ActionId);
            p[1] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
            p[2] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
            p[3] = new OracleParameter("SOURCE_ID", wfr.SourceId);

            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            if (dt != null && dt.Rows.Count == 0)
            {
                return null;
            }

            return ConvertDataRow(dt.Rows[0]);
        }

        /// <summary>
        /// ActionAuthorization nesnesini update eder
        /// </summary>
        /// <param name="wfr">ActionAuthorization nesnesi</param>
        public static int Update(ActionAuthorization wfr)
        {
            string query = @"UPDATE DT_WORKFLOW.YYS_ACTION_AUTHORIZATION
SET    WF_DEF_ID               = :WF_DEF_ID,
       STATE_DEF_ID            = :STATE_DEF_ID,
       ACTION_ID               = :ACTION_ID,
       SOURCE_ID               = :SOURCE_ID,
       TO_GROUP_ID             = :TO_GROUP_ID,
       LAST_UPDATED            = :LAST_UPDATED,
       LAST_UPDATED_BY         = :LAST_UPDATED_BY,
       IS_ACTIVE               = :IS_ACTIVE
WHERE  ACTION_AUTHORIZATION_ID = :ACTION_AUTHORIZATION_ID";

            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[9];

            p[0] = new OracleParameter("WF_DEF_ID", wfr.WfDefId);
            p[1] = new OracleParameter("STATE_DEF_ID", wfr.StateDefId);
            p[2] = new OracleParameter("ACTION_ID", wfr.ActionId);
            p[3] = new OracleParameter("SOURCE_ID", wfr.SourceId);
            p[4] = new OracleParameter("TO_GROUP_ID", wfr.ToGroupId);
            p[5] = new OracleParameter("LAST_UPDATED", wfr.LastUpdated);
            p[6] = new OracleParameter("LAST_UPDATED_BY", wfr.LastUpdatedBy);
            p[7] = new OracleParameter("IS_ACTIVE", wfr.IsActive);
            p[8] = new OracleParameter("ACTION_AUTHORIZATION_ID", wfr.RequestId);

            return Db.ExecuteNonQuery(p, query, ConnectionType.DefaultConnection);
        }
    }
}