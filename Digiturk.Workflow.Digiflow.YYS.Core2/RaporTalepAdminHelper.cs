﻿using System.Collections.Generic;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// Summary description for RaporTalepAdminHelper
    /// </summary>
    public static class RaporTalepAdminHelper
    {
        /// <summary>
        /// Verilen Uygulamanın içeride daha önce kayıt edilip edilmediğini sorgular.Varsa True döner
        /// </summary>
        /// <param name="UygulamaId"></param>
        /// <returns></returns>
        public static bool Uygulama_Var(string UygulamaId, string TalepTipi)
        {
            string Sql = "";
            switch (TalepTipi)
            {
                case "Rapor":
                    Sql = "select  COUNT(RAPOR_TALEP_ID) from  DT_WORKFLOW.WF_DF_RAPOR_TALEP where ILGILI_UYGULAMA =" + UygulamaId;
                    break;

                case "Kurumsal Uygulama":
                    Sql = "select  COUNT(KURUMSAL_TALEP_ID) from  DT_WORKFLOW.WF_DF_KURUMSAL_TALEP where ILGILI_UYGULAMA =" + UygulamaId;
                    break;
            }
            string sonuc = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetOnlyColumnSQL<string>("FrameworkConnection", Sql, new List<Digiturk.Workflow.Digiflow.DataAccessLayer.CustomParameterList>());
            bool netice = false;
            if (sonuc == "0")
            {
                netice = false;
            }
            else
            {
                netice = true;
            }
            return netice;
        }
    }
}