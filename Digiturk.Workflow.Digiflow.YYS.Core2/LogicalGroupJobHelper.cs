﻿using ApPortalServices.AdPortal.LdapServices;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Mail;
using System.Web;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// Summary description for LogicalGroupJobHelper
    /// </summary>
    public class LogicalGroupJobHelper
    {
        public LogicalGroupJobHelper()
        {
            //
            // TODO: Add constructor logic here
            //
        }

        public static DataTable DtbAdUserList { get; set; }
        public static void LogicalGroupJobExecute()
        {
            try
            {
                if (DtbAdUserList == null)
                {
                    DtbAdUserList = GetCheckLoginDataTable();
                }
                else
                {
                    DtbAdUserList.Rows.Clear();
                }
                // 1- Grupları Güncellenecek Mantıksal Gruplar Çekilir. (Tamamlandı)
                // 2- Mantıksal Grup Üzerindeki Ad Portal Kullanıcıları Çekilir. (Tamamlandı)
                // 3- AD Portaldan Kullanıcılar Çekilir. (Tamamlandı)
                // 4- AD Portal Grubundaki Kullanıcılar Mantıksal Gruptan Kontrol Edilir. Eklenecek Varsa eklenir. (Tamamlandı)
                // 5- Mantıksal Gruptan AD Portal Kullanıcıları Kontrol Edilir. Silinecek varsa silinir.
                // 6- Eklenmeden Önce FLogin Kontrolü Yapılır ve FLogin Tanımı Olmayan Kullanıcılar Mail Atılır.
                List<LogicalGroup> lglist = LogicalGroupHelper.GetAdWithlogicalGroups();
                foreach (var item in lglist)
                {
                    string FlowName = LogicalGroupHelper.GetFlowName(item.WfDefId);
                    CompareLogicalGroup(item, FlowName);
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        public static void CompareLogicalGroup(LogicalGroup lg, string FlowName)
        {
            CompareWithAdportalGroupToYYSLgMember(lg, FlowName);
            CompareWithYYSLgMemberToAdLogicalGroup(lg);
        }

        public static void CompareWithAdportalGroupToYYSLgMember(LogicalGroup lg, string FlowName)
        {
            var adUserList = GetAdUserList(lg.AdDomainName, lg.AdGroupName);
            List<LogicalGroupMember> allLgm = LogicalGroupMemberHelper.GetByLogicalGroupId(lg.RequestId);
            foreach (var item in adUserList)
            {
                DataTable dtbFLoginInfo = FormInformationHelper.GetLoginNamewithNameSurname(item.UserName);
                if (dtbFLoginInfo.Rows.Count > 0)
                {
                    var chk = allLgm.Where(t => t.LoginId == long.Parse(dtbFLoginInfo.Rows[0]["F_LOGIN_ID"].ToString())).ToList();
                    if (chk.Count == 0)
                    {
                        // Gruba Ekliyoruz.
                        AddLogicalGroupAdMember(lg.RequestId, long.Parse(dtbFLoginInfo.Rows[0]["F_LOGIN_ID"].ToString()), item.UserName);
                    }
                }
                else
                {
                    DataRow dr = DtbAdUserList.NewRow();
                    dr["UserName"] = item.UserName;
                    dr["DisplayName"] = item.NameSurName;
                    dr["FloginStatus"] = "0";
                    dr["CheckResult"] = "F Login Bulunamadı";
                    DtbAdUserList.Rows.Add(dr);
                }
            }
            SendUnDefinedLoginId(FlowName, lg.Name, lg.AdDomainName + "/" + lg.AdGroupName);
            DtbAdUserList.Rows.Clear();
        }

        public static DataTable GetCheckLoginDataTable()
        {
            DataTable DtbAdUserList = new DataTable();
            DtbAdUserList.Columns.Add("UserName");
            DtbAdUserList.Columns.Add("DisplayName");
            DtbAdUserList.Columns.Add("Flogin");
            DtbAdUserList.Columns.Add("CheckResult");
            DtbAdUserList.Columns.Add("FloginStatus");
            return DtbAdUserList;
        }

        public static void CompareWithYYSLgMemberToAdLogicalGroup(LogicalGroup lg)
        {
            var adUserList = GetAdUserList(lg.AdDomainName, lg.AdGroupName);
            List<LogicalGroupMember> allLgm = LogicalGroupMemberHelper.GetByLogicalGroupId(lg.RequestId);
            // Bu Kısmı Tamamlayacağız.
            foreach (var item in allLgm)
            {
                DataTable dtbFLoginInfo = FormInformationHelper.GetLoginNamewithNameSurnameFlogin(item.LoginId);
                if (dtbFLoginInfo.Rows.Count > 0)
                {
                    string UserName = dtbFLoginInfo.Rows[0]["USERNAME"].ToString();
                    var userlist = adUserList.Where(t => t.UserName == UserName).ToList();
                    if (userlist.Count == 0)
                    {
                        // Kaydı Silelim.
                        DeleteLogicalGroupAdMember(lg.RequestId, item.LoginId);
                    }
                }
                //var adusershort=adUserList.Where(t=>t.UserName)
                // Ad Portal Listesinden Kontrol edilir gelmeyen kayıt silinir.
            }

        }

        public static void AddLogicalGroupAdMember(long LogicalGroupId, long LoginId, string UserName)
        {
            DataTable dtbFLoginInfo = FormInformationHelper.GetLoginNamewithNameSurname(UserName);
            LogicalGroupMember lgm = new LogicalGroupMember();
            lgm.FullName = dtbFLoginInfo.Rows[0]["NAME_SURNAME"].ToString();
            lgm.LoginId = long.Parse(dtbFLoginInfo.Rows[0]["F_LOGIN_ID"].ToString());
            lgm.LogicalGroupMemberTypeId = 5;
            lgm.LogicalGroupId = LogicalGroupId;
            lgm.Created = DateTime.Now;
            lgm.CreatedBy = 0;
            lgm.LastUpdated = DateTime.Now;
            lgm.LastUpdatedBy = 0;
            lgm.IsAdTransfer = 1;
            bool ExistFLogin = LogicalGroupMemberHelper.CheckFLoginLogicalGroup(lgm.LogicalGroupId, lgm.LoginId);
            if (!ExistFLogin)
            {
                LogicalGroupMemberHelper.AddNewLogicalGroupMember(lgm);
            }
        }

        public static void DeleteLogicalGroupAdMember(long LogicalGroupId, long LoginId)
        {
            //LogicalGroupMemberHelper.DeleteLogicalGroupMemberWithGroupOfLogin(LogicalGroupId, LoginId);
        }

        public static List<UserInformation> GetAdUserList(string Domain, string GroupName)
        {
            ApPortalServices.AdPortal.LdapServices.AdPortalServices client = new ApPortalServices.AdPortal.LdapServices.AdPortalServices();
            string Username = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.UserName"];
            string Password = System.Configuration.ConfigurationSettings.AppSettings["Web.Services.Password"];
            client.Credentials = new System.Net.NetworkCredential(Username, Password);
            if (Domain == "DIGITURK")
            {
                client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalServices"];
            }
            else if (Domain == "DIGITURKCC")
            {
                client.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalCCServices"];
            }
            ApPortalServices.AdPortal.LdapServices.UserInformation[] userlist = client.GetGroupOfUserList(GroupName);
            return userlist.ToList();
        }

        public static bool sendMail(string vfrom, string vTo, string vCC, string vBody, string vSubject, string FlowName, string YYSGrupName, string AdGrupName)
        {
            SmtpClient smtpClient = new SmtpClient();
            MailMessage message = new MailMessage();
            string bodyHtmlHeader = "";
            string bodyHtmlFooter = "";
            try
            {
                MailAddress fromAddress = new MailAddress(vfrom, "DIGIPORT");
                message.From = fromAddress;
                if (vCC != "")
                {
                    message.CC.Add(vCC);
                }
                message.To.Add(vTo);

                bodyHtmlHeader = "<html>";
                bodyHtmlHeader += "<head>";
                bodyHtmlHeader += "<meta http-equiv=Content-Type content=text/html; charset=windows-1254>";
                bodyHtmlHeader += "<style>TD{ font-family:verdana; font-size:9pt; }</style>";
                bodyHtmlHeader += "</head>";
                bodyHtmlHeader += "<body>";
                bodyHtmlHeader += "<table><tr><td><font face=verdana size=2>";
                bodyHtmlHeader += " Akışın Adı: " + FlowName + " <br>";
                bodyHtmlHeader += " YYS Grubun Adı: " + YYSGrupName + " <br>";
                bodyHtmlHeader += " AD Grubun Adı: " + AdGrupName + " <br>";

                bodyHtmlFooter = "</font></td></tr></table>";

                message.Subject = vSubject;
                message.IsBodyHtml = true;
                message.Body = bodyHtmlHeader + vBody + bodyHtmlFooter;
                smtpClient.Host = "smtp.digiturk.local";
                //smtpClient.Host = "************";
                smtpClient.Send(message);
                return true;

            }
            catch (Exception ex)
            {
                string hata = ex.ToString();
                hata = hata.Substring(0, hata.Length);
                return false;
            }
        }

        public static string ConvertDataTableToHTML(DataTable dt)
        {
            string html = "<table>";
            //add header row
            html += "<tr>";
            for (int i = 0; i < dt.Columns.Count; i++)
                html += "<td>" + dt.Columns[i].ColumnName + "</td>";
            html += "</tr>";
            //add rows
            for (int i = 0; i < dt.Rows.Count; i++)
            {
                html += "<tr>";
                for (int j = 0; j < dt.Columns.Count; j++)
                    html += "<td>" + dt.Rows[i][j].ToString() + "</td>";
                html += "</tr>";
            }
            html += "</table>";
            return html;
        }

        public static void SendUnDefinedLoginId(string FlowName, string YYSGrupName, string AdGrupName)
        {
            DataView wv = DtbAdUserList.DefaultView;
            wv.RowFilter = "FloginStatus=0";
            if (wv.Count > 0)
            {
                string HtmlTable = ConvertDataTableToHTML(wv.ToTable());
                //public bool sendMail(string vfrom, string vTo, string vCC, string vBody, string vSubject, string FlowName, string YYSGrupName, string AdGrupName)
                sendMail("<EMAIL>", "<EMAIL>", "<EMAIL>", HtmlTable, "AD Portalde Olup FLogin Eşleşmeyen Kullanıcılar", FlowName, YYSGrupName, AdGrupName);
            }
            wv.RowFilter = "";
        }

    }
}