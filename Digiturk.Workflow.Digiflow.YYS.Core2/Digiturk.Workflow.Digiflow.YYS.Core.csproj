﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{D87F7A96-1727-4BB6-B480-CFE9621D917E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.YYS.Core</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.YYS.Core</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DataAccessLayer">
      <HintPath>..\..\Deployment\DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxEditors.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.ASPxEditors.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGridView.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.ASPxGridView.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.ASPxGridView.v12.1.Export, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.ASPxGridView.v12.1.Export.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Web.v12.1, Version=12.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SharedAssemblies\DXperience 12.1\Bin\Framework\DevExpress.Web.v12.1.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Entities">
      <HintPath>..\..\Deployment\Digiturk.Framework.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Framework.Repository">
      <HintPath>..\..\Deployment\Digiturk.Framework.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>..\..\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>..\..\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.121.2.0, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\Deployment\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ActionAuthorizationHelper.cs" />
    <Compile Include="ActionTypeHelper.cs" />
    <Compile Include="Class1.cs" />
    <Compile Include="Common.cs" />
    <Compile Include="Db.cs" />
    <Compile Include="HrHelper.cs" />
    <Compile Include="LogicalGroupHelper.cs" />
    <Compile Include="LogicalGroupJobHelper.cs" />
    <Compile Include="LogicalGroupMemberHelper.cs" />
    <Compile Include="LogicalGroupMemberTypeHelper.cs" />
    <Compile Include="MonitoringWorkflowAdminHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RaporTalepAdminHelper.cs" />
    <Compile Include="StateAuthorizationHelper.cs" />
    <Compile Include="TopluOnayHelper.cs" />
    <Compile Include="WorkflowAdminHelper.cs" />
    <Compile Include="WorkflowHelper.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\Digiturk.Workflow.Digiflow.YYS.Core2\*.* /s</PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>cd  c:\TFS\Deployment
del $(TargetFileName)
COPY /Y "$(TargetPath)" c:\TFS\Deployment</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>