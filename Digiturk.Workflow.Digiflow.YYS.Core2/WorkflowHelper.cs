﻿using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Oracle.DataAccess.Client;
using System;
using System.Collections.Generic;
using System.Data;
namespace Digiturk.Workflow.Digiflow.YYS.Core
{
    /// <summary>
    /// İş Akışları için kullanılan ve kullanılabilecek olan fonksiyonları barındırır.
    /// </summary>
    public class WorkflowHelper
    {
        /// <summary>
        /// Tanımlı iş akışlarının listesini döner
        /// </summary>
        /// <returns>Tanımlı tüm iş akışları</returns>
        public static List<Digiturk.Workflow.Digiflow.Entities.Workflow> Get()
        {
            //  string query = "SELECT * FROM F_WF_WORKFLOW_DEF ORDER BY NAME ASC";
            string query = @"SELECT  FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID AS WF_DEF_ID, FRAMEWORK.F_WF_WORKFLOW_DEF.NAME, FRAMEWORK.F_WF_WORKFLOW_DEF.DESCRIPTION  from FRAMEWORK.F_WF_WORKFLOW_DEF where FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0 and FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER!=5 Order By FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.FrameworkConnection);
            List<Digiturk.Workflow.Digiflow.Entities.Workflow> ret = new List<Digiturk.Workflow.Digiflow.Entities.Workflow>();
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }

            return ret;
        }

        /// <summary>
        /// Statüs ü LIVE olan tüm iş akışlarını getirir
        /// </summary>
        /// <returns></returns>
        public static DataTable GetLiveWorkFlows()
        {
            //  string query = "SELECT * FROM F_WF_WORKFLOW_DEF ORDER BY NAME ASC";
            string query = @"Select * from FRAMEWORK.F_WF_WORKFLOW_DEF where FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0  Order By FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";
            //WHERE WF_VERSION_STATUS_TYPE_CD='LIVE'
            DataTable dt = Db.ExecuteDataTable(query, ConnectionType.FrameworkConnection);
            return dt;
        }

        /// <summary>
        /// Verilen workflow id ile eşleşen stateleri döner
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public static List<WorkflowState> GetStates(long workflowId)
        {
            string query = " Select * from FRAMEWORK.F_WF_STATE_DEF where FRAMEWORK.F_WF_STATE_DEF.WF_WORKFLOW_DEF_ID=:WF_WORKFLOW_DEF_ID AND FRAMEWORK.F_WF_STATE_DEF.WF_STATE_TYPE_CD!='END' ORDER BY FRAMEWORK.F_WF_STATE_DEF.NAME";
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_WORKFLOW_DEF_ID", workflowId);

            dt = Db.ExecuteDataTable(p, ConnectionType.FrameworkConnection, query);

            List<WorkflowState> ret = new List<WorkflowState>();

            if (dt.Rows.Count > 0)
            {
                foreach (DataRow item in dt.Rows)
                {
                    ret.Add(ConvertDataRowToWorkflowState(item));
                }
            }

            return ret;
        }

        /// <summary>
        /// Seçilen akışların yöneticilerini getirir
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public static List<WorkFlowAdmin> GetAdminsOfWorkflow(decimal workflowId)
        {
            //DEĞİŞTİRİLDİ 22.03.2012 YYS_ADMIN tablosundan FULLNAME column ı kaldırıldığı için
            DataTable dt = new DataTable();
            string query = @"SELECT DT_WORKFLOW.YYS_ADMINS .*,DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.*, UIS.NAME_SURNAME FROM DT_WORKFLOW.YYS_ADMINS
LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_ADMINS.LOGIN_ID =DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID
INNER JOIN DT_WORKFLOW.VW_USER_INFORMATION UIS ON UIS.LOGIN_ID =DT_WORKFLOW.YYS_ADMINS.LOGIN_ID
WHERE DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=:WF_DEF_ID
order by UIS.NAME_SURNAME";

            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_DEF_ID", workflowId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            List<WorkFlowAdmin> ret = new List<WorkFlowAdmin>();
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(WorkflowAdminHelper.ConvertDataRow(item));
            }

            return ret;
        }

        /// <summary>
        /// Seçilen akışların yöneticilerini getirir
        /// </summary>
        /// <param name="workflowId"></param>
        /// <returns></returns>
        public static DataTable GetAdminsOfWorkflowDataTable(decimal workflowId)
        {
            DataTable dt = new DataTable();
            string query = @"SELECT UIS.LOGIN_ID,IS_ACTIVE,ADMIN_LEVEL_TYPE_ID,DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.*, UIS.NAME_SURNAME AS FULLNAME  FROM DT_WORKFLOW.YYS_ADMINS
LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_ADMINS.LOGIN_ID =DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID
left JOIN DT_WORKFLOW.VW_USER_INFORMATION UIS ON UIS.LOGIN_ID =DT_WORKFLOW.YYS_ADMINS.LOGIN_ID
WHERE DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=:WF_DEF_ID
order by UIS.NAME_SURNAME";

            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_DEF_ID", workflowId);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, query);

            return dt;
        }

        /// <summary>
        /// Verilen admin'in yönetici olduğu akışları listeler
        /// </summary>
        /// <param name="adminId">Admin Id</param>
        /// <returns>Verilen admin'in yönetici olduğu akışlar</returns>
        public static List<Digiturk.Workflow.Digiflow.Entities.Workflow> GetWorkflowsOfAdmin(long adminId, long IsAdmin)
        {
            string sql = @"SELECT distinct FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID as WF_DEF_ID, NAME, DESCRIPTION FROM FRAMEWORK.F_WF_WORKFLOW_DEF
LEFT JOIN DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS  ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
WHERE (DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID or :ISADMIN=1)
AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0 order by FRAMEWORK.F_WF_WORKFLOW_DEF.NAME";
            DataTable dt = new DataTable();
            List<Digiturk.Workflow.Digiflow.Entities.Workflow> ret = new List<Digiturk.Workflow.Digiflow.Entities.Workflow>();
            OracleParameter[] p = new OracleParameter[2];
            p[0] = new OracleParameter("ADMIN_ID", adminId);
            p[1] = new OracleParameter("ISADMIN", IsAdmin);
            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, sql);
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }

            return ret;
        }

        /// <summary>
        /// Verilen admin'in yönetici olduğu akışları listeler
        /// </summary>
        /// <param name="adminId">Admin Id</param>
        /// <returns>Verilen admin'in yönetici olduğu akışlar</returns>
        public static List<Digiturk.Workflow.Digiflow.Entities.Workflow> GetWorkflowsOfAdmin(long adminId)
        {
            string sql = @"SELECT * FROM DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS
INNER JOIN FRAMEWORK.F_WF_WORKFLOW_DEF ON DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.WF_DEF_ID=FRAMEWORK.F_WF_WORKFLOW_DEF.WF_WORKFLOW_DEF_ID
WHERE DT_WORKFLOW.YYS_WORKFLOWS_OF_ADMINS.ADMIN_ID=:ADMIN_ID
AND FRAMEWORK.F_WF_WORKFLOW_DEF.VERSION_NUMBER>0 order by FRAMEWORK.F_WF_WORKFLOW_DEF.NAME ";
            DataTable dt = new DataTable();
            List<Digiturk.Workflow.Digiflow.Entities.Workflow> ret = new List<Digiturk.Workflow.Digiflow.Entities.Workflow>();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("ADMIN_ID", adminId);

            dt = Db.ExecuteDataTable(p, ConnectionType.DefaultConnection, sql);
            foreach (DataRow item in dt.Rows)
            {
                ret.Add(ConvertDataRow(item));
            }

            return ret;
        }

        /// <summary>
        /// Verilen datarow'daki değerleri kullanarak bir Workflow nesnesi oluşturur
        /// </summary>
        /// <param name="row">Workflow nesnesinde kullanılacak değerlerin bulunduğu datarow</param>
        private static Digiturk.Workflow.Digiflow.Entities.Workflow ConvertDataRow(DataRow row)
        {
            Digiturk.Workflow.Digiflow.Entities.Workflow wfa = new Digiturk.Workflow.Digiflow.Entities.Workflow();
            wfa.WorkflowDefId = ConvertionHelper.ConvertValue<Decimal>(row["WF_DEF_ID"].ToString());
            wfa.Name = row["NAME"].ToString();
            wfa.Description = row["DESCRIPTION"].ToString();
            return wfa;
        }

        /// <summary>
        /// Verilen datarow'daki değerleri kullanarak bir Workflow nesnesi oluşturur
        /// </summary>
        /// <param name="row">Workflow nesnesinde kullanılacak değerlerin bulunduğu datarow</param>
        private static WorkflowState ConvertDataRowToWorkflowState(DataRow row)
        {
            WorkflowState s = new WorkflowState();
            s.Description = row["DESCRIPTION"].ToString();
            s.Name = row["NAME"].ToString();
            s.StateType = row["WF_STATE_TYPE_CD"].ToString();
            s.WorkflowId = ConvertionHelper.ConvertValue<long>(row["WF_WORKFLOW_DEF_ID"].ToString());
            s.WorkflowStateId = ConvertionHelper.ConvertValue<long>(row["WF_STATE_DEF_ID"].ToString());

            return s;
        }

        /// <summary>
        /// StateId si verilen state in ne olduğunu getirir
        /// </summary>
        /// <param name="StateId"></param>
        /// <returns></returns>
        public static WorkflowState GetState(long StateId)
        {
            string query = " Select * from FRAMEWORK.F_WF_STATE_DEF where FRAMEWORK.F_WF_STATE_DEF.WF_STATE_DEF_ID=:WF_STATE_DEF_ID order by FRAMEWORK.F_WF_STATE_DEF.NAME";
            DataTable dt = new DataTable();
            OracleParameter[] p = new OracleParameter[1];
            p[0] = new OracleParameter("WF_STATE_DEF_ID", StateId);
            dt = Db.ExecuteDataTable(p, ConnectionType.FrameworkConnection, query);

            if (dt.Rows.Count > 0)
            {
                return ConvertDataRowToWorkflowState(dt.Rows[0]);
            }

            return null;
        }
    }
}