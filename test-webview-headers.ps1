# Test script to verify webview embedding headers
# This script tests both regular and webview requests to ensure proper header configuration

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$Endpoint = "/api/health"
)

Write-Host "Testing WebView Embedding Headers" -ForegroundColor Green
Write-Host "API URL: $ApiUrl" -ForegroundColor Yellow
Write-Host "Endpoint: $Endpoint" -ForegroundColor Yellow
Write-Host ""

$fullUrl = "$ApiUrl$Endpoint"

# Test 1: Regular web request (should have X-Frame-Options: DENY)
Write-Host "Test 1: Regular Web Request" -ForegroundColor Cyan
Write-Host "Expected: X-Frame-Options: DENY" -ForegroundColor Gray

try {
    $response1 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -UseBasicParsing
    $frameOptions1 = $response1.Headers["X-Frame-Options"]
    $csp1 = $response1.Headers["Content-Security-Policy"]
    
    Write-Host "Status: $($response1.StatusCode)" -ForegroundColor Green
    Write-Host "X-Frame-Options: $frameOptions1" -ForegroundColor $(if ($frameOptions1 -eq "DENY") { "Green" } else { "Red" })
    
    if ($csp1 -and $csp1.Contains("frame-ancestors 'none'")) {
        Write-Host "CSP frame-ancestors: 'none' ✓" -ForegroundColor Green
    } else {
        Write-Host "CSP frame-ancestors: Not 'none' ⚠️" -ForegroundColor Yellow
        Write-Host "CSP: $csp1" -ForegroundColor Gray
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: WebView request (should have X-Frame-Options: SAMEORIGIN)
Write-Host "Test 2: WebView Request" -ForegroundColor Cyan
Write-Host "Expected: X-Frame-Options: SAMEORIGIN" -ForegroundColor Gray

try {
    $headers = @{
        "X-Mobile-App" = "true"
        "X-From-Mobile-WebView" = "true"
        "X-Request-Source" = "DigiHRApp"
        "User-Agent" = "ReactNative-WebView-DigiHR-App"
    }
    
    $response2 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -Headers $headers -UseBasicParsing
    $frameOptions2 = $response2.Headers["X-Frame-Options"]
    $csp2 = $response2.Headers["Content-Security-Policy"]
    
    Write-Host "Status: $($response2.StatusCode)" -ForegroundColor Green
    Write-Host "X-Frame-Options: $frameOptions2" -ForegroundColor $(if ($frameOptions2 -eq "SAMEORIGIN") { "Green" } else { "Red" })
    
    if ($csp2 -and ($csp2.Contains("frame-ancestors 'self'") -or $csp2.Contains("capacitor://localhost"))) {
        Write-Host "CSP frame-ancestors: Allows webview ✓" -ForegroundColor Green
    } else {
        Write-Host "CSP frame-ancestors: May not allow webview ⚠️" -ForegroundColor Yellow
        Write-Host "CSP: $csp2" -ForegroundColor Gray
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Check other security headers
Write-Host "Test 3: Security Headers Check" -ForegroundColor Cyan

try {
    $response3 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -UseBasicParsing
    
    $securityHeaders = @{
        "X-Content-Type-Options" = "nosniff"
        "X-XSS-Protection" = "1; mode=block"
        "Referrer-Policy" = "strict-origin-when-cross-origin"
    }
    
    foreach ($header in $securityHeaders.GetEnumerator()) {
        $actualValue = $response3.Headers[$header.Key]
        if ($actualValue -eq $header.Value) {
            Write-Host "$($header.Key): ✓" -ForegroundColor Green
        } else {
            Write-Host "$($header.Key): ⚠️ (Expected: $($header.Value), Got: $actualValue)" -ForegroundColor Yellow
        }
    }
    
    # Check if server headers are removed
    $serverHeader = $response3.Headers["Server"]
    if (-not $serverHeader) {
        Write-Host "Server header removed: ✓" -ForegroundColor Green
    } else {
        Write-Host "Server header present: ⚠️ ($serverHeader)" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test Summary:" -ForegroundColor Green
Write-Host "1. Regular requests should have X-Frame-Options: DENY" -ForegroundColor Gray
Write-Host "2. WebView requests should have X-Frame-Options: SAMEORIGIN" -ForegroundColor Gray
Write-Host "3. CSP should allow webview embedding for mobile requests" -ForegroundColor Gray
Write-Host "4. Other security headers should remain intact" -ForegroundColor Gray

Write-Host ""
Write-Host "Usage Examples:" -ForegroundColor Yellow
Write-Host "  .\test-webview-headers.ps1 -ApiUrl 'https://digiflowtest.digiturk.com.tr'" -ForegroundColor Gray
Write-Host "  .\test-webview-headers.ps1 -ApiUrl 'http://localhost:5000' -Endpoint '/api/auth/verify-session'" -ForegroundColor Gray
