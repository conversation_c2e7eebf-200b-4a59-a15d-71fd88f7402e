# Test script to verify webview embedding headers and diagnose empty page issues
# This script tests both regular and webview requests to ensure proper header configuration

param(
    [Parameter(Mandatory=$true)]
    [string]$ApiUrl,

    [Parameter(Mandatory=$false)]
    [string]$Endpoint = "/api/health",

    [Parameter(Mandatory=$false)]
    [string]$ReactAppUrl = ""
)

Write-Host "Testing WebView Embedding Headers" -ForegroundColor Green
Write-Host "API URL: $ApiUrl" -ForegroundColor Yellow
Write-Host "Endpoint: $Endpoint" -ForegroundColor Yellow
Write-Host ""

$fullUrl = "$ApiUrl$Endpoint"

# Test 1: Regular web request (should have X-Frame-Options: DENY)
Write-Host "Test 1: Regular Web Request" -ForegroundColor Cyan
Write-Host "Expected: X-Frame-Options: DENY" -ForegroundColor Gray

try {
    $response1 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -UseBasicParsing
    $frameOptions1 = $response1.Headers["X-Frame-Options"]
    $csp1 = $response1.Headers["Content-Security-Policy"]

    Write-Host "Status: $($response1.StatusCode)" -ForegroundColor Green
    Write-Host "X-Frame-Options: $frameOptions1" -ForegroundColor $(if ($frameOptions1 -eq "DENY") { "Green" } else { "Red" })

    if ($csp1 -and $csp1.Contains("frame-ancestors 'none'")) {
        Write-Host "CSP frame-ancestors: 'none' ✓" -ForegroundColor Green
    } else {
        Write-Host "CSP frame-ancestors: Not 'none' ⚠️" -ForegroundColor Yellow
        Write-Host "CSP: $csp1" -ForegroundColor Gray
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: WebView request (should have X-Frame-Options: SAMEORIGIN)
Write-Host "Test 2: WebView Request" -ForegroundColor Cyan
Write-Host "Expected: X-Frame-Options: SAMEORIGIN" -ForegroundColor Gray

try {
    $headers = @{
        "X-Mobile-App" = "true"
        "X-From-Mobile-WebView" = "true"
        "X-Request-Source" = "DigiHRApp"
        "User-Agent" = "ReactNative-WebView-DigiHR-App"
    }

    $response2 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -Headers $headers -UseBasicParsing
    $frameOptions2 = $response2.Headers["X-Frame-Options"]
    $csp2 = $response2.Headers["Content-Security-Policy"]

    Write-Host "Status: $($response2.StatusCode)" -ForegroundColor Green
    Write-Host "X-Frame-Options: $frameOptions2" -ForegroundColor $(if ($frameOptions2 -eq "SAMEORIGIN") { "Green" } else { "Red" })

    if ($csp2 -and ($csp2.Contains("frame-ancestors 'self'") -or $csp2.Contains("capacitor://localhost"))) {
        Write-Host "CSP frame-ancestors: Allows webview ✓" -ForegroundColor Green
    } else {
        Write-Host "CSP frame-ancestors: May not allow webview ⚠️" -ForegroundColor Yellow
        Write-Host "CSP: $csp2" -ForegroundColor Gray
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Check other security headers
Write-Host "Test 3: Security Headers Check" -ForegroundColor Cyan

try {
    $response3 = Invoke-WebRequest -Uri $fullUrl -Method HEAD -UseBasicParsing

    $securityHeaders = @{
        "X-Content-Type-Options" = "nosniff"
        "X-XSS-Protection" = "1; mode=block"
        "Referrer-Policy" = "strict-origin-when-cross-origin"
    }

    foreach ($header in $securityHeaders.GetEnumerator()) {
        $actualValue = $response3.Headers[$header.Key]
        if ($actualValue -eq $header.Value) {
            Write-Host "$($header.Key): ✓" -ForegroundColor Green
        } else {
            Write-Host "$($header.Key): ⚠️ (Expected: $($header.Value), Got: $actualValue)" -ForegroundColor Yellow
        }
    }

    # Check if server headers are removed
    $serverHeader = $response3.Headers["Server"]
    if (-not $serverHeader) {
        Write-Host "Server header removed: ✓" -ForegroundColor Green
    } else {
        Write-Host "Server header present: ⚠️ ($serverHeader)" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test Summary:" -ForegroundColor Green
Write-Host "1. Regular requests should have X-Frame-Options: DENY" -ForegroundColor Gray
Write-Host "2. WebView requests should have X-Frame-Options: SAMEORIGIN" -ForegroundColor Gray
Write-Host "3. CSP should allow webview embedding for mobile requests" -ForegroundColor Gray
Write-Host "4. Other security headers should remain intact" -ForegroundColor Gray

Write-Host ""

# Test 4: React App Accessibility (if URL provided)
if ($ReactAppUrl) {
    Write-Host "Test 4: React App Accessibility" -ForegroundColor Cyan
    Write-Host "Testing React app URL: $ReactAppUrl" -ForegroundColor Gray

    try {
        $reactResponse = Invoke-WebRequest -Uri $ReactAppUrl -Method GET -UseBasicParsing -TimeoutSec 10
        Write-Host "React App Status: $($reactResponse.StatusCode)" -ForegroundColor $(if ($reactResponse.StatusCode -eq 200) { "Green" } else { "Yellow" })

        # Check for common React error indicators
        $content = $reactResponse.Content
        if ($content -match "error|Error|ERROR") {
            Write-Host "⚠️ Potential errors found in React app content" -ForegroundColor Yellow
        }

        if ($content -match "<!DOCTYPE html>") {
            Write-Host "✓ Valid HTML document" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Response doesn't appear to be HTML" -ForegroundColor Yellow
        }

        # Check for React-specific content
        if ($content -match "react|React|REACT") {
            Write-Host "✓ React content detected" -ForegroundColor Green
        } else {
            Write-Host "⚠️ No React content detected" -ForegroundColor Yellow
        }

        # Check X-Frame-Options on React app
        $reactFrameOptions = $reactResponse.Headers["X-Frame-Options"]
        if ($reactFrameOptions) {
            Write-Host "React App X-Frame-Options: $reactFrameOptions" -ForegroundColor $(if ($reactFrameOptions -eq "SAMEORIGIN" -or $reactFrameOptions -eq "ALLOWALL") { "Green" } else { "Red" })
        } else {
            Write-Host "React App X-Frame-Options: Not set (allows embedding)" -ForegroundColor Green
        }

    }
    catch {
        Write-Host "React App Error: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "This could indicate the React app is not accessible or has issues" -ForegroundColor Yellow
    }

    Write-Host ""
}

Write-Host ""
Write-Host "Diagnostic Recommendations:" -ForegroundColor Green
Write-Host "1. If API headers are correct but webview is empty:" -ForegroundColor Gray
Write-Host "   - Check React app URL accessibility" -ForegroundColor Gray
Write-Host "   - Verify React app doesn't have its own frame restrictions" -ForegroundColor Gray
Write-Host "   - Check browser console for JavaScript errors" -ForegroundColor Gray
Write-Host "   - Test React app error boundary" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Common empty page causes:" -ForegroundColor Gray
Write-Host "   - React app build/deployment issues" -ForegroundColor Gray
Write-Host "   - Authentication failures" -ForegroundColor Gray
Write-Host "   - CORS issues with API calls" -ForegroundColor Gray
Write-Host "   - JavaScript runtime errors" -ForegroundColor Gray
Write-Host ""
Write-Host "Usage Examples:" -ForegroundColor Yellow
Write-Host "  .\test-webview-headers.ps1 -ApiUrl 'https://digiflowtest.digiturk.com.tr'" -ForegroundColor Gray
Write-Host "  .\test-webview-headers.ps1 -ApiUrl 'http://localhost:5000' -Endpoint '/api/auth/verify-session'" -ForegroundColor Gray
Write-Host "  .\test-webview-headers.ps1 -ApiUrl 'https://digiflowtest.digiturk.com.tr' -ReactAppUrl 'https://digiflowtest.digiturk.com.tr'" -ForegroundColor Gray
