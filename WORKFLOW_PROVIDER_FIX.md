# Workflow Provider Context Fix

## Issue
The webview shows an empty page due to the error:
```
useWorkflowConfig must be used within a WorkflowConfigProvider
```

This happens when accessing workflow URLs directly (like `http://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation`) because the `WorkflowProvider` is not properly wrapping the component.

## Root Cause
The workflow screens (`DelegationScreen`, `MonitoringScreen`, etc.) use `useWorkflow()` hook which internally calls `useWorkflowConfig()`, but when accessed directly via URL, they're not wrapped with the required `WorkflowProvider`.

## Solution Options

### Option 1: Add Default WorkflowProvider Wrapper (Recommended)

Create a wrapper component that ensures WorkflowProvider is always available:

```typescript
// src/components/workflow/WorkflowProviderWrapper.tsx
import React, { ReactNode } from 'react'
import { useLocation } from 'react-router-dom'
import { WorkflowProvider } from '@/contexts/workflow/WorkflowProvider'

interface WorkflowProviderWrapperProps {
  children: ReactNode
}

export const WorkflowProviderWrapper: React.FC<WorkflowProviderWrapperProps> = ({ children }) => {
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  
  // Extract workflow parameters from URL
  const workflowName = searchParams.get('name') || ''
  const wfInstanceId = searchParams.get('wfInstanceId') ? Number(searchParams.get('wfInstanceId')) : 0
  const refInstanceId = searchParams.get('refInstanceId') ? Number(searchParams.get('refInstanceId')) : null
  const copyInstanceId = searchParams.get('copyInstanceId') ? Number(searchParams.get('copyInstanceId')) : null
  
  // Default schemas - you might need to adjust this based on your workflow registry
  const schemas = {}

  return (
    <WorkflowProvider
      workflowName={workflowName}
      wfInstanceId={wfInstanceId}
      refInstanceId={refInstanceId}
      copyInstanceId={copyInstanceId}
      schemas={schemas}
    >
      {children}
    </WorkflowProvider>
  )
}
```

### Option 2: Update Route Configuration

Ensure all workflow routes are properly wrapped:

```typescript
// In your routing configuration
import { WorkflowProviderWrapper } from '@/components/workflow/WorkflowProviderWrapper'

// Wrap workflow routes
<Route 
  path="/main/workflow" 
  element={
    <WorkflowProviderWrapper>
      <WorkflowSelectorScreen />
    </WorkflowProviderWrapper>
  } 
/>
```

### Option 3: Add Safe Hook (Quick Fix)

Create a safe version of the workflow hook:

```typescript
// src/hooks/useSafeWorkflow.ts
import { useWorkflow } from '@/contexts/workflow/WorkflowProvider'

export const useSafeWorkflow = () => {
  try {
    return useWorkflow()
  } catch (error) {
    console.warn('WorkflowProvider not found, returning default values')
    return {
      workflowName: '',
      wfInstanceId: 0,
      refInstanceId: null,
      copyInstanceId: null,
      schemas: {},
      definitionId: 0,
      historyId: 0,
      readOnly: false,
      setWorkflowName: () => {},
      setDefinitionId: () => {},
      setHistoryId: () => {},
      setReadOnly: () => {},
      // Add other default values as needed
    }
  }
}
```

Then update the workflow screens to use `useSafeWorkflow` instead of `useWorkflow`.

## Implementation Steps

### Step 1: Create the WorkflowProviderWrapper
Create the file `src/components/workflow/WorkflowProviderWrapper.tsx` with the code from Option 1.

### Step 2: Update Routing
Find where the workflow routes are defined and wrap them with the `WorkflowProviderWrapper`.

### Step 3: Test the Fix
1. Access the workflow URL directly: `http://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation`
2. Verify no more context errors
3. Test in webview

## Alternative Quick Fix (If routing is complex)

If the routing is too complex to modify quickly, you can patch the existing screens:

```typescript
// In DelegationScreen.tsx and other workflow screens
import { useSafeWorkflow } from '@/hooks/useSafeWorkflow'

// Replace:
// const workflow = useWorkflow()
// With:
const workflow = useSafeWorkflow()
```

## Testing

After implementing the fix:

1. **Direct URL Test**: Open `http://digiflowtest.digiturk.com.tr/react/main/workflow?name=delegation` directly in browser
2. **WebView Test**: Load the same URL in DigiHRApp webview
3. **Console Check**: Verify no more "useWorkflowConfig must be used within a WorkflowConfigProvider" errors

## Expected Result

- No more context provider errors
- Workflow screens load properly in webview
- Empty page issue resolved
- Authentication flow works correctly

## Notes

- The `WorkflowProviderWrapper` extracts parameters from the URL, so make sure the URL format matches your expectations
- You might need to adjust the schemas parameter based on your workflow registry implementation
- Consider adding error boundaries around workflow components for better error handling
