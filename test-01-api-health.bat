@echo off
echo ========================================
echo TEST 1: API Health Check
echo ========================================
echo.
echo Testing basic API connectivity...
echo.

REM Test API health endpoint
echo [1.1] Testing API health endpoint...
curl -s -o nul -w "Status: %%{http_code} | Time: %%{time_total}s" https://digiflowtest.digiturk.com.tr/api/health
echo.
echo.

REM Test with verbose headers
echo [1.2] Testing API health with headers...
curl -I https://digiflowtest.digiturk.com.tr/api/health
echo.

echo ========================================
echo TEST 1 COMPLETE
echo ========================================
echo.
echo Please report:
echo - Did you see "Status: 200" in test 1.1?
echo - Did you see security headers in test 1.2?
echo - Any error messages?
echo.
pause
