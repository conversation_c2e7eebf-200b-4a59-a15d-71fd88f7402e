#!/usr/bin/env pwsh
# Test script to verify token refresh works with both tokens

Write-Host "Testing Token Refresh with Both Tokens" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

$baseUrl = "https://localhost:5001"

# First, get a valid token via Windows auth
Write-Host "`nStep 1: Get initial tokens via Windows auth" -ForegroundColor Yellow
try {
    $headers = @{
        "X-From-Mobile-WebView" = "true"
        "X-Mobile-App" = "true"
        "X-Is-Mobile" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/windows" -Method Post -Headers $headers -UseDefaultCredentials -SkipCertificateCheck
    
    if ($response.accessToken -and $response.refreshToken) {
        Write-Host "SUCCESS: Got initial tokens" -ForegroundColor Green
        $accessToken = $response.accessToken
        $refreshToken = $response.refreshToken
        Write-Host "Access Token (first 50 chars): $($accessToken.Substring(0, 50))..." -ForegroundColor Gray
        Write-Host "Refresh Token (first 20 chars): $($refreshToken.Substring(0, 20))..." -ForegroundColor Gray
    }
    else {
        Write-Host "FAILED: No tokens received" -ForegroundColor Red
        exit
    }
}
catch {
    Write-Host "FAILED: Windows auth failed" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit
}

# Test 1: Refresh with both tokens (correct way)
Write-Host "`nTest 1: Refresh with BOTH tokens (correct)" -ForegroundColor Yellow
try {
    $body = @{
        accessToken = $accessToken
        refreshToken = $refreshToken
    } | ConvertTo-Json
    
    $headers = @{
        "Content-Type" = "application/json"
        "X-From-Mobile-WebView" = "true"
        "X-Mobile-App" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/refresh" -Method Post -Headers $headers -Body $body -SkipCertificateCheck
    
    if ($response.accessToken -and $response.refreshToken) {
        Write-Host "SUCCESS: Refresh worked with both tokens!" -ForegroundColor Green
        Write-Host "New Access Token (first 50 chars): $($response.accessToken.Substring(0, 50))..." -ForegroundColor Gray
    }
}
catch {
    Write-Host "FAILED: Refresh with both tokens failed" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
}

# Test 2: Refresh with only refresh token (incorrect - what mobile app is doing)
Write-Host "`nTest 2: Refresh with ONLY refresh token (incorrect)" -ForegroundColor Yellow
try {
    $body = @{
        refreshToken = $refreshToken
    } | ConvertTo-Json
    
    $headers = @{
        "Content-Type" = "application/json"
        "X-From-Mobile-WebView" = "true"
        "X-Mobile-App" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/refresh" -Method Post -Headers $headers -Body $body -SkipCertificateCheck
    Write-Host "UNEXPECTED: Request succeeded without access token!" -ForegroundColor Red
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 400) {
        Write-Host "EXPECTED: Got 400 error when access token is missing" -ForegroundColor Green
        
        # Try to get error details
        if ($_.Exception.Response) {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $responseBody = $reader.ReadToEnd()
            Write-Host "Error response: $responseBody" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "FAILED: Got unexpected error code: $statusCode" -ForegroundColor Red
    }
}

# Test 3: Test with missing accessToken field but empty value
Write-Host "`nTest 3: Refresh with empty access token" -ForegroundColor Yellow
try {
    $body = @{
        accessToken = ""
        refreshToken = $refreshToken
    } | ConvertTo-Json
    
    $headers = @{
        "Content-Type" = "application/json"
        "X-From-Mobile-WebView" = "true"
        "X-Mobile-App" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/refresh" -Method Post -Headers $headers -Body $body -SkipCertificateCheck
    Write-Host "UNEXPECTED: Request succeeded with empty access token!" -ForegroundColor Red
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    Write-Host "EXPECTED: Got error with empty access token - Status: $statusCode" -ForegroundColor Green
}

Write-Host "`n======================================" -ForegroundColor Cyan
Write-Host "Summary:" -ForegroundColor Yellow
Write-Host "- The API requires BOTH accessToken and refreshToken fields" -ForegroundColor White
Write-Host "- The mobile app is currently only sending refreshToken" -ForegroundColor White
Write-Host "- This is why token refresh is failing" -ForegroundColor White
Write-Host "`nMobile team needs to update their code to send both tokens!" -ForegroundColor Yellow