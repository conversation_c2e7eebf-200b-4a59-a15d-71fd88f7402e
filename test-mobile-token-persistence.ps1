#!/usr/bin/env pwsh
# Script to test mobile WebView token persistence implementation

Write-Host "Testing Mobile WebView Token Persistence" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

$baseUrl = "https://localhost:5001"
$global:accessToken = $null
$global:refreshToken = $null

# Test 1: Windows Authentication to get JWT token
Write-Host "`nTest 1: Windows Auth for Mobile WebView" -ForegroundColor Yellow
try {
    $headers = @{
        "X-From-Mobile-WebView" = "true"
        "X-Mobile-App" = "true"
        "X-Is-Mobile" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/windows" -Method Post -Headers $headers -UseDefaultCredentials -SkipCertificateCheck
    
    if ($response.accessToken -and $response.refreshToken) {
        Write-Host "SUCCESS: Got JWT tokens from Windows auth" -ForegroundColor Green
        Write-Host "Access Token (first 50 chars): $($response.accessToken.Substring(0, 50))..." -ForegroundColor Gray
        Write-Host "Refresh Token (first 20 chars): $($response.refreshToken.Substring(0, 20))..." -ForegroundColor Gray
        Write-Host "Token expires in: $($response.expiresIn) seconds" -ForegroundColor Gray
        Write-Host "User ID: $($response.userId)" -ForegroundColor Gray
        Write-Host "Username: $($response.username)" -ForegroundColor Gray
        Write-Host "Auto-refresh enabled: $($response.autoRefreshEnabled)" -ForegroundColor Gray
        
        # Store tokens globally
        $global:accessToken = $response.accessToken
        $global:refreshToken = $response.refreshToken
    }
    else {
        Write-Host "FAILED: No tokens received" -ForegroundColor Red
    }
}
catch {
    Write-Host "FAILED: Windows auth failed" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    
    # Try to get more details
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response: $responseBody" -ForegroundColor Red
    }
}

# Test 2: Use JWT token to access protected endpoint
if ($global:accessToken) {
    Write-Host "`nTest 2: Access Protected Endpoint with JWT" -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $($global:accessToken)"
            "X-From-Mobile-WebView" = "true"
            "X-Mobile-App" = "true"
            "X-Is-Mobile" = "true"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/users/all" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: Accessed protected endpoint with JWT" -ForegroundColor Green
        Write-Host "Response contains $($response.Count) users" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: Could not access protected endpoint" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 3: Refresh token flow
if ($global:accessToken -and $global:refreshToken) {
    Write-Host "`nTest 3: Refresh Token Flow" -ForegroundColor Yellow
    
    # Wait a moment to ensure we can distinguish new tokens
    Start-Sleep -Seconds 2
    
    try {
        $body = @{
            accessToken = $global:accessToken
            refreshToken = $global:refreshToken
        } | ConvertTo-Json
        
        $headers = @{
            "Content-Type" = "application/json"
            "X-From-Mobile-WebView" = "true"
            "X-Mobile-App" = "true"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/refresh" -Method Post -Headers $headers -Body $body -SkipCertificateCheck
        
        if ($response.accessToken -and $response.refreshToken) {
            Write-Host "SUCCESS: Tokens refreshed successfully" -ForegroundColor Green
            Write-Host "New Access Token (first 50 chars): $($response.accessToken.Substring(0, 50))..." -ForegroundColor Gray
            Write-Host "Token different from original: $($response.accessToken -ne $global:accessToken)" -ForegroundColor Gray
            
            # Update global tokens
            $global:accessToken = $response.accessToken
            $global:refreshToken = $response.refreshToken
        }
        else {
            Write-Host "FAILED: No new tokens received" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "FAILED: Token refresh failed" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 4: Use refreshed token
if ($global:accessToken) {
    Write-Host "`nTest 4: Use Refreshed Token" -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $($global:accessToken)"
            "X-From-Mobile-WebView" = "true"
            "X-Mobile-App" = "true"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/users/select-options" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: Refreshed token works correctly" -ForegroundColor Green
        Write-Host "Response contains $($response.Count) user options" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: Refreshed token didn't work" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

# Test 5: Invalid refresh token handling
Write-Host "`nTest 5: Invalid Refresh Token Handling" -ForegroundColor Yellow
try {
    $body = @{
        accessToken = "invalid.jwt.token"
        refreshToken = "invalid-refresh-token"
    } | ConvertTo-Json
    
    $headers = @{
        "Content-Type" = "application/json"
        "X-From-Mobile-WebView" = "true"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$baseUrl/auth/refresh" -Method Post -Headers $headers -Body $body -SkipCertificateCheck
    Write-Host "UNEXPECTED: Invalid tokens were accepted!" -ForegroundColor Red
}
catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    if ($statusCode -eq 401) {
        Write-Host "SUCCESS: Invalid tokens correctly rejected with 401" -ForegroundColor Green
    }
    else {
        Write-Host "PARTIAL: Got error but not 401 - Status: $statusCode" -ForegroundColor Yellow
    }
}

# Test 6: Test auth/me endpoint with JWT
if ($global:accessToken) {
    Write-Host "`nTest 6: Test /auth/me with JWT Token" -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $($global:accessToken)"
            "X-From-Mobile-WebView" = "true"
            "Accept" = "application/json"
        }
        
        $response = Invoke-RestMethod -Uri "$baseUrl/auth/me" -Method Get -Headers $headers -SkipCertificateCheck
        Write-Host "SUCCESS: /auth/me works with JWT" -ForegroundColor Green
        Write-Host "Username: $($response.username)" -ForegroundColor Gray
        Write-Host "Auth Type: $($response.authenticationType)" -ForegroundColor Gray
        Write-Host "Claims count: $($response.claims.Count)" -ForegroundColor Gray
    }
    catch {
        Write-Host "FAILED: /auth/me failed with JWT" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
    }
}

Write-Host "`n=======================================" -ForegroundColor Cyan
Write-Host "Mobile Token Persistence Test Complete" -ForegroundColor Cyan
Write-Host "`nKey Points:" -ForegroundColor Yellow
Write-Host "- Windows auth generates JWT tokens for mobile" -ForegroundColor White
Write-Host "- Tokens can be refreshed without re-authentication" -ForegroundColor White
Write-Host "- Mobile app should store tokens securely" -ForegroundColor White
Write-Host "- Tokens should be refreshed before expiry" -ForegroundColor White
Write-Host "- All API calls should include mobile headers" -ForegroundColor White

# Display token storage simulation
Write-Host "`nToken Storage Simulation:" -ForegroundColor Yellow
Write-Host "In a real mobile app, you would store:" -ForegroundColor Gray
Write-Host "- accessToken: <secure storage>" -ForegroundColor Gray
Write-Host "- refreshToken: <secure storage>" -ForegroundColor Gray
Write-Host "- tokenExpiry: $(([DateTime]::UtcNow.AddSeconds(3600)).ToString('o'))" -ForegroundColor Gray
Write-Host "- userId: <from response>" -ForegroundColor Gray
Write-Host "- username: <from response>" -ForegroundColor Gray