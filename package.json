{"name": "digiflow", "private": true, "version": "0.0.0", "author": "<PERSON><PERSON>", "license": "UNLICENSED", "type": "module", "homepage": "/main/inbox", "scripts": {"dev": "cross-env NODE_ENV=development vite", "build": "yarn lint && yarn type-check && vite build", "build:skip-checks": "vite build", "build:dev": "cross-env NODE_ENV=development vite build --mode development && node modify-assets-path.js", "build:test": "cross-env NODE_ENV=test vite build --mode test && node modify-assets-path.js", "build:production": "cross-env NODE_ENV=production vite build --mode production && node modify-assets-path.js", "lint": "eslint src --ext .ts,.tsx --fix && eslint *.ts *.js --fix", "lint:check": "eslint src --ext .ts,.tsx && eslint *.ts *.js", "lint:security": "eslint . --no-eslintrc -c eslint.config.js --quiet", "security:scan": "./scripts/weekly-security-scan.sh", "security:audit": "npm audit", "security:audit-fix": "npm audit fix", "type-check": "tsc --noEmit", "prettify": "prettier --write .", "prettify:check": "prettier --check .", "preview": "vite preview", "clean": "rm -rf dist react node_modules/.vite", "clean:cache": "rm -rf node_modules/.vite .vite", "test": "vitest", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest watch", "analyze": "vite build --mode production && npx vite-bundle-analyzer dist", "prepare": "husky", "validate": "yarn lint:check && yarn prettify:check && yarn type-check && yarn test:run", "ci-build": "yarn validate && yarn clean && yarn build:production"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^5.16.0", "@mui/lab": "^5.0.0-alpha.173", "@mui/material": "^5.16.0", "@mui/system": "^5.16.0", "@mui/types": "7.1.4", "@mui/x-date-pickers": "^6.20.0", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/lodash": "^4.17.18", "axios": "^1.10.0", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "framer-motion": "^12.23.6", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lucide-react": "^0.522.0", "moment": "^2.30.1", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-router-dom": "6.2.2", "react-select": "^5.10.1", "reflect-metadata": "^0.2.2", "vite-tsconfig-paths": "^5.1.4", "wface": "file:./wface.tgz", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@axe-core/react": "^4.10.2", "@faker-js/faker": "^9.9.0", "@playwright/test": "^1.54.1", "@reduxjs/toolkit": "^2.8.2", "@rollup/plugin-image": "^3.0.3", "@tanstack/eslint-plugin-query": "^5.81.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/axios-mock-adapter": "^1.10.4", "@types/file-saver": "^2.0.7", "@types/jest-axe": "^3.5.9", "@types/node": "^24.0.3", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.5.0", "@vitejs/plugin-react-swc": "^3.10.2", "axe-core": "^4.10.3", "axios-mock-adapter": "^2.1.0", "cross-env": "^7.0.3", "dotenv": "^17.2.0", "eslint": "^9.31.0", "eslint-config-prettier": "10.1.8", "eslint-plugin-no-secrets": "^2.2.1", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-security": "^3.0.1", "globals": "^16.3.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "jest-axe": "^10.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "msw": "^2.10.4", "pre-commit": "^1.2.2", "prettier": "^3.6.0", "react-error-boundary": "^6.0.0", "rollup-plugin-visualizer": "^6.0.3", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3", "vitest": "^3.2.4"}, "resolutions": {"react": "18.3.1", "react-dom": "18.3.1", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "d3-color": "^3.1.0", "dompurify": "^3.2.6", "jspdf": "^3.0.1", "axios": "^1.10.0", "@babel/runtime": "^7.26.10"}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}, "msw": {"workerDirectory": ["public"]}}