{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-29 00:45:02.215"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-29 00:45:02.216"}
{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-29 08:30:00.536"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-29 08:30:00.537"}
{"level":"info","message":"Starting MCP server","service":"mcp-puppeteer","timestamp":"2025-07-29 08:30:30.694"}
{"level":"info","message":"MCP server started successfully","service":"mcp-puppeteer","timestamp":"2025-07-29 08:30:30.695"}
{"level":"info","message":"Puppeteer MCP Server closing","service":"mcp-puppeteer","timestamp":"2025-07-29 08:30:33.308"}
