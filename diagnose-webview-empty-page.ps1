# Quick diagnostic script for empty webview page issues
param(
    [Parameter(Mandatory=$true)]
    [string]$ReactAppUrl,
    
    [Parameter(Mandatory=$false)]
    [string]$ApiUrl = ""
)

Write-Host "=== WebView Empty Page Diagnostic ===" -ForegroundColor Cyan
Write-Host "React App URL: $ReactAppUrl" -ForegroundColor Yellow
if ($ApiUrl) {
    Write-Host "API URL: $ApiUrl" -ForegroundColor Yellow
}
Write-Host ""

$issues = @()
$warnings = @()

# Test 1: Basic React App Accessibility
Write-Host "1. Testing React App Accessibility..." -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri $ReactAppUrl -UseBasicParsing -TimeoutSec 10
    
    if ($response.StatusCode -eq 200) {
        Write-Host "   ✓ React app responds with 200 OK" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ React app responds with status: $($response.StatusCode)" -ForegroundColor Yellow
        $warnings += "React app returns non-200 status: $($response.StatusCode)"
    }
    
    # Check content type
    $contentType = $response.Headers["Content-Type"]
    if ($contentType -and $contentType.Contains("text/html")) {
        Write-Host "   ✓ Content-Type is HTML" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Content-Type is not HTML: $contentType" -ForegroundColor Red
        $issues += "React app not serving HTML content"
    }
    
    # Check for HTML structure
    $content = $response.Content
    if ($content -match "<!DOCTYPE html>") {
        Write-Host "   ✓ Valid HTML document structure" -ForegroundColor Green
    } else {
        Write-Host "   ❌ Invalid HTML document structure" -ForegroundColor Red
        $issues += "React app not serving valid HTML"
    }
    
    # Check for React content
    if ($content -match "react|React|REACT|root") {
        Write-Host "   ✓ React content detected" -ForegroundColor Green
    } else {
        Write-Host "   ⚠️ No obvious React content detected" -ForegroundColor Yellow
        $warnings += "No React content patterns found"
    }
    
    # Check for error indicators
    if ($content -match "error|Error|ERROR|exception|Exception") {
        Write-Host "   ⚠️ Potential error content detected" -ForegroundColor Yellow
        $warnings += "Error patterns found in content"
    }
    
} catch {
    Write-Host "   ❌ Failed to access React app: $($_.Exception.Message)" -ForegroundColor Red
    $issues += "React app not accessible: $($_.Exception.Message)"
}

Write-Host ""

# Test 2: Frame Options Check
Write-Host "2. Checking Frame Options..." -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri $ReactAppUrl -UseBasicParsing -TimeoutSec 10
    $frameOptions = $response.Headers["X-Frame-Options"]
    
    if (-not $frameOptions) {
        Write-Host "   ✓ No X-Frame-Options header (allows embedding)" -ForegroundColor Green
    } elseif ($frameOptions -eq "SAMEORIGIN" -or $frameOptions -eq "ALLOWALL") {
        Write-Host "   ✓ X-Frame-Options allows embedding: $frameOptions" -ForegroundColor Green
    } elseif ($frameOptions -eq "DENY") {
        Write-Host "   ❌ X-Frame-Options blocks embedding: DENY" -ForegroundColor Red
        $issues += "React app has X-Frame-Options: DENY"
    } else {
        Write-Host "   ⚠️ Unknown X-Frame-Options value: $frameOptions" -ForegroundColor Yellow
        $warnings += "Unknown X-Frame-Options: $frameOptions"
    }
    
    # Check CSP frame-ancestors
    $csp = $response.Headers["Content-Security-Policy"]
    if ($csp -and $csp.Contains("frame-ancestors 'none'")) {
        Write-Host "   ❌ CSP blocks embedding with frame-ancestors 'none'" -ForegroundColor Red
        $issues += "CSP frame-ancestors blocks embedding"
    } elseif ($csp -and $csp.Contains("frame-ancestors")) {
        Write-Host "   ✓ CSP frame-ancestors configured" -ForegroundColor Green
    }
    
} catch {
    Write-Host "   ⚠️ Could not check frame options" -ForegroundColor Yellow
}

Write-Host ""

# Test 3: API Connectivity (if provided)
if ($ApiUrl) {
    Write-Host "3. Testing API Connectivity..." -ForegroundColor Green
    try {
        $apiResponse = Invoke-WebRequest -Uri "$ApiUrl/api/health" -UseBasicParsing -TimeoutSec 10
        
        if ($apiResponse.StatusCode -eq 200) {
            Write-Host "   ✓ API health endpoint responds" -ForegroundColor Green
        } else {
            Write-Host "   ⚠️ API health endpoint status: $($apiResponse.StatusCode)" -ForegroundColor Yellow
            $warnings += "API health endpoint returns: $($apiResponse.StatusCode)"
        }
        
        # Test with webview headers
        $webviewHeaders = @{
            "X-Mobile-App" = "true"
            "X-From-Mobile-WebView" = "true"
            "User-Agent" = "DigiHRApp-WebView"
        }
        
        $webviewResponse = Invoke-WebRequest -Uri "$ApiUrl/api/health" -Headers $webviewHeaders -UseBasicParsing -TimeoutSec 10
        
        if ($webviewResponse.StatusCode -eq 200) {
            Write-Host "   ✓ API responds to webview requests" -ForegroundColor Green
        } else {
            Write-Host "   ❌ API fails for webview requests: $($webviewResponse.StatusCode)" -ForegroundColor Red
            $issues += "API rejects webview requests"
        }
        
    } catch {
        Write-Host "   ❌ API not accessible: $($_.Exception.Message)" -ForegroundColor Red
        $issues += "API not accessible: $($_.Exception.Message)"
    }
    
    Write-Host ""
}

# Test 4: Common URL Issues
Write-Host "4. Checking URL Configuration..." -ForegroundColor Green

# Check for HTTPS vs HTTP
if ($ReactAppUrl.StartsWith("https://")) {
    Write-Host "   ✓ Using HTTPS" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ Using HTTP (may cause issues in production)" -ForegroundColor Yellow
    $warnings += "Using HTTP instead of HTTPS"
}

# Check for trailing slash
if ($ReactAppUrl.EndsWith("/")) {
    Write-Host "   ✓ URL has trailing slash" -ForegroundColor Green
} else {
    Write-Host "   ℹ️ URL without trailing slash (testing with slash...)" -ForegroundColor Cyan
    try {
        $slashResponse = Invoke-WebRequest -Uri "$ReactAppUrl/" -UseBasicParsing -TimeoutSec 5
        if ($slashResponse.StatusCode -eq 200) {
            Write-Host "   ✓ URL with trailing slash also works" -ForegroundColor Green
        }
    } catch {
        Write-Host "   ⚠️ URL with trailing slash fails" -ForegroundColor Yellow
    }
}

Write-Host ""

# Test 5: DNS and Network
Write-Host "5. Testing Network Connectivity..." -ForegroundColor Green

$uri = [System.Uri]$ReactAppUrl
$hostname = $uri.Host

try {
    $dnsResult = Resolve-DnsName -Name $hostname -ErrorAction Stop
    Write-Host "   ✓ DNS resolution successful for $hostname" -ForegroundColor Green
} catch {
    Write-Host "   ❌ DNS resolution failed for $hostname" -ForegroundColor Red
    $issues += "DNS resolution failed for $hostname"
}

# Test port connectivity
$port = if ($uri.Port -eq -1) { if ($uri.Scheme -eq "https") { 443 } else { 80 } } else { $uri.Port }
try {
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.ConnectAsync($hostname, $port).Wait(5000)
    if ($tcpClient.Connected) {
        Write-Host "   ✓ Port $port is accessible on $hostname" -ForegroundColor Green
        $tcpClient.Close()
    } else {
        Write-Host "   ❌ Port $port is not accessible on $hostname" -ForegroundColor Red
        $issues += "Port $port not accessible"
    }
} catch {
    Write-Host "   ❌ Cannot connect to $hostname`:$port" -ForegroundColor Red
    $issues += "Cannot connect to $hostname`:$port"
}

Write-Host ""

# Summary
Write-Host "=== DIAGNOSTIC SUMMARY ===" -ForegroundColor Cyan
Write-Host ""

if ($issues.Count -eq 0 -and $warnings.Count -eq 0) {
    Write-Host "✅ No issues detected! The problem might be:" -ForegroundColor Green
    Write-Host "   - WebView-specific JavaScript errors" -ForegroundColor Gray
    Write-Host "   - Authentication/session issues" -ForegroundColor Gray
    Write-Host "   - React app error boundary triggered" -ForegroundColor Gray
    Write-Host "   - Mobile app WebView configuration" -ForegroundColor Gray
} else {
    if ($issues.Count -gt 0) {
        Write-Host "❌ CRITICAL ISSUES FOUND:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "   • $issue" -ForegroundColor Red
        }
        Write-Host ""
    }
    
    if ($warnings.Count -gt 0) {
        Write-Host "⚠️ WARNINGS:" -ForegroundColor Yellow
        foreach ($warning in $warnings) {
            Write-Host "   • $warning" -ForegroundColor Yellow
        }
        Write-Host ""
    }
}

Write-Host "NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Fix any critical issues listed above" -ForegroundColor Gray
Write-Host "2. Test React app directly in mobile browser" -ForegroundColor Gray
Write-Host "3. Check WebView error logs in mobile app" -ForegroundColor Gray
Write-Host "4. Add debugging to React app error boundary" -ForegroundColor Gray
Write-Host "5. Test with simple HTML page first" -ForegroundColor Gray
Write-Host ""
Write-Host "For detailed debugging steps, see: WEBVIEW_EMPTY_PAGE_DEBUGGING.md" -ForegroundColor Cyan
