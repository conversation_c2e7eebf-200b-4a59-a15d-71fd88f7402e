﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Net;
using System.Text.RegularExpressions;

namespace Digiturk.Workflow.Digiflow.GenericMailHelper
{
    //<summary>
    //ActionType For Mail Nodification
    //</summary>
    public enum ActionType
    {
        ONWFSTARTOWNER,
        ONWFSTARTASSIGNER,
        ONWFAPPROVEOWNER,
        ONWFAPPROVEASSIGNER,
        ONWFREJECTASSIGNER,
        ONWFREJECTOWNER,
        ON<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>APPROVEENDOWNER,
        ONWF<PERSON><PERSON><PERSON>FLOWREJECTENDOWNER,
        ONFLOWENDINFORMATION,
        ONFLOWENDINFORMATIONLOGICALGRP,
        ONFLOWCUSTOMMAILINGLOGICALGRP,
        ONFLOWCUSTOM<PERSON>IL<PERSON>MPLA<PERSON>,
        ONSENDTO<PERSON>CK,
        ONW<PERSON><PERSON><PERSON><PERSON>TFLOWUSERS,
        ON<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORMATIONGROUP,
        ONWFAPP<PERSON><PERSON><PERSON><PERSON>ORMA<PERSON><PERSON>GRO<PERSON>,
        ONWFPRIVATEAPPROVEOWNER,
        ONWFPRIVATEAPPROVE,
        ONFLOWPRIVATEENDINFORMATION,
        ONWFRELATEDUSERINFORMATION,
        ONLASTSTATE,
        ONFLOWENDINFORMATIONTOLIST
    }

    /// <summary>
    /// Generic Mail Templatelerini kullanarak mail atılacak fonksiyondur.
    /// </summary>
    //CCLoginIdLogicalGroup
    public class GenericMailHelper
    {
        /// <summary>
        /// Bir İş Akışında Bilgilendirme maili istenip istenmediğini kontrol eder
        /// </summary>
        /// <param name="WfDefId">Akış Türünün DefinitionId si</param>
        /// <param name="UserId">Kullanıcının LoginId si</param>
        /// <returns></returns>
        public static bool IsDenyInformationMail(long WfDefId, long UserId)
        {
            string SQL = "Select * from DT_WORKFLOW.WF_DENY_MAIL where WF_DEF_ID=" + WfDefId.ToString() + " and LOGIN_ID=" + UserId.ToString() + "";
            DataTable DtbDeny = DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL);
            if (DtbDeny.Rows.Count > 0)
            {
                DtbDeny = null;
                return true; // Kullanıcı Mail İstemiyor
            }
            else
            {
                DtbDeny = null;
                return false;// Kullanıcı Mail İstiyor
            }
        }

        /// <summary>
        /// Akış Sonunda Atılacak Spesifik Maillerde Kullanılır.
        /// </summary>
        /// <param name="TemplateId"></param>
        /// <param name="wfInstanceId"></param>
        /// <param name="LastActionUserLoginId"></param>
        /// <param name="loginId"></param>
        /// <param name="WfContext"></param>
        /// <param name="ccList"></param>
        public static void SendEmail(long TemplateMailId, long wfInstanceId, long LastActionUserLoginId, long loginId, Digiturk.Workflow.Common.WFContext WfContext, IList<long> ccLongList)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akış İle İlgili Bilgiler Toplanır

                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(wfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(wfInstanceId);

                #endregion Akış İle İlgili Bilgiler Toplanır

                #region Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();
                FLogin ToUser = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(loginId);
                ToList.Add(ToUser);

                foreach (long item in ccLongList)
                {
                    FLogin CCUser = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(item);
                    CcList.Add(CCUser);
                }

                #endregion Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                #region Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(loginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));
                string WorkFlowName = WfIns.WfWorkflowDef.Name;
                //string CreateUserManagerName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(DataAccessLayer.CheckingWorker.GetManager(Convert.ToInt64(WfIns.OwnerLogin.LoginId))); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                string CreateUserManagerName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(DataAccessLayer.CheckingWorker.GetManager(Convert.ToInt64(WfIns.OwnerLogin.LoginId), WfIns.WfWorkflowDef.WfWorkflowDefId));
                string WorkFlowInsId = WfIns.WfWorkflowInstanceId.ToString();

                #endregion Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(wfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region Parametreler WfContex e Yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                WfContext.Parameters.AddOrChangeItem("WorkFlowName", WorkFlowName);
                WfContext.Parameters.AddOrChangeItem("CreateUserManagerName", CreateUserManagerName);
                WfContext.Parameters.AddOrChangeItem("WorkFlowInsId", WorkFlowInsId);

                #endregion Parametreler WfContex e Yüklenir

                #region To ve CC List Testi İçin

                string ToCCList = "";
                foreach (FLogin item in ToList)
                {
                    ToCCList += "To:" + item.DomainUserName + "<br>";
                }
                foreach (FLogin item in CcList)
                {
                    ToCCList += "CC:" + item.DomainUserName + "<br>";
                }

                WfContext.Parameters.AddOrChangeItem("ToCCList", ToCCList);

                #endregion To ve CC List Testi İçin

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + wfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderimi Yapılır

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderimi Yapılır

                #region Object Disposing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disposing
            }
        }

        //public static void (long TemplateId, long wfInstanceId, long LastActionUserLoginId, long loginId, Digiturk.Workflow.Common.WFContext WfContext, IList<long> ccList)
        //GenericMailHelper.SendEmail(EndStateActionType, WfInstanceId, LastActionUserLoginId, LoginId, wfContext);
        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="ActionTypeValue"> Aksiyonun tipi</param>
        /// <param name="WfInstanceId"> Son İşlem yapan Kullanıcı</param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId)
        {
            long AssigtoLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WfInstanceId);
            long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);

            SendEmail(ActionTypeValue, WfInstanceId, LastActionUserLoginId, AssigtoLoginId);
            Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfInstanceId);
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="ActionTypeValue"> Aksiyonun Tipi </param>
        /// <param name="WfInstanceId"> Akış Numarası</param>
        /// <param name="LastActionUserLoginId"> Son İşlem Yapan Kullanıcı </param>
        /// <param name="AssigtoLoginId"> Atanan Kullanıcı </param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Common.WFContext WfContext;
                WfContext = new Digiturk.Workflow.Common.WFContext(WfIns);
                SendEmail(ActionTypeValue, WfInstanceId, LastActionUserLoginId, AssigtoLoginId, WfContext);
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="ActionTypeValue"> Aksiyonun Tipi </param>
        /// <param name="WfInstanceId"> Akış Numarası</param>
        /// <param name="LastActionUserLoginId"> Son İşlem Yapan Kullanıcı </param>
        /// <param name="AssigtoLoginId"> Atanan Kullanıcı </param>
        /// <param name="AssignToIdList"> Atanan Kullanıcıların listesi</param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId, List<long> AssignToIdList)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Common.WFContext WfContext;
                WfContext = new Digiturk.Workflow.Common.WFContext(WfIns);
                WfContext.Parameters.AddOrChangeItem("AssigtoLoginId", AssigtoLoginId);
                WfContext.Parameters.AddOrChangeItem("AssignToIdList", AssignToIdList);
                WfContext.Parameters.AddOrChangeItem("LastActionUserLoginId", LastActionUserLoginId);
                WfContext.Parameters.AddOrChangeItem("ActionTypeValue", ActionTypeValue);
                SendEmail(ActionTypeValue, WfInstanceId, LastActionUserLoginId, AssigtoLoginId, WfContext, AssignToIdList);
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="ActionTypeValue">Aksiyon Tipi-Bu Tip Mail Templateini Belirler</param>
        /// <param name="WfInstanceId">Akış Numarası</param>
        /// <param name="LastActionUserLoginId">Son İşlem yapan kullanıcı</param>
        /// <param name="AssigtoLoginId"> Atanan Kullanıcı </param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId, Digiturk.Workflow.Common.WFContext WfContext)
        {
            //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "-SendEmail Geldi" + WfContext.Parameters.Count, "Hedef");
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akış İle İlgili Bilgiler Toplanır

                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);

                #endregion Akış İle İlgili Bilgiler Toplanır

                #region Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                long TemplateMailId = GetTemplateId(WfIns.WfWorkflowDef.WfWorkflowDefId, ActionTypeValue);
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();
                if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFREJECTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER || ActionTypeValue == ActionType.ONWFWORKFLOWAPPROVEENDOWNER || ActionTypeValue == ActionType.ONWFWORKFLOWREJECTENDOWNER || ActionTypeValue == ActionType.ONFLOWPRIVATEENDINFORMATION)
                {
                    FLogin OwnerLogin = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(WfIns.OwnerLogin.LoginId);
                    ToList.Add(OwnerLogin);
                }
                else
                {
                    if (AssigtoLoginId > 0)
                    {
                        FLogin AssignUser = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(AssigtoLoginId);
                        ToList.Add(AssignUser);
                    }
                }

                //WfContext.Parameters.AddOrChangeItem("MAILTEMPLATE" + DateTime.Now, TemplateMailId);
                //WfContext.Save();
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Kaydetti" + WfContext.Parameters.Count + "--" + TemplateMailId, "Hedef");

                #endregion Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                #region Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);

                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 2" + WfContext.Parameters.Count, "Hedef");

                #region Akışı Oluşturan Kişiye Mail Atılıyorsa Akışın Atandığı Kullanıcılar birden Fazla ise Kullanıcılar listeleniyor

                if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
                {
                    //List<FLogin> AssignToIdList=Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(WfInstanceId);
                    List<long> AssignToIdList = Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(WfInstanceId);
                    if (AssignToIdList.Count > 1)
                    {
                        AssignedUserNameSurname = "";
                    }
                    for (int i = 0; i < AssignToIdList.Count; i++)
                    {
                        if (i != AssignToIdList.Count - 1)
                        {
                            AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]) + ",";
                        }
                        else
                        {
                            AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]);
                        }
                    }
                }
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 3" + WfContext.Parameters.Count, "Hedef");

                #endregion Akışı Oluşturan Kişiye Mail Atılıyorsa Akışın Atandığı Kullanıcılar birden Fazla ise Kullanıcılar listeleniyor

                #region puantaj akisina ozel yaklasim

                if (WfIns.WfWorkflowDef.WfWorkflowDefId == 1312) //Puantaj akışında gmy ise sistem tarafından onaylanıyor denilecek.
                {
                    if (WfContext.Parameters.ContainsKey("ManagerTipi"))
                    {
                        if (WfContext.Parameters["ManagerTipi"].ToString() == "GMY")
                        {
                            AssignedUserNameSurname = "Sistem";
                        }
                    }
                }

                #endregion puantaj akisina ozel yaklasim

                #region Parametreler WfContex e Yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 4" + WfContext.Parameters.Count, "Hedef");

                #endregion Parametreler WfContex e Yüklenir

                #region Akış Adminleri Listeye Eklenir

                string AdminList = "";
                if (WfIns.WfWorkflowDef.WfWorkflowDefId == 1340)
                {
                    Digiflow.Entities.JumpToStateRequest JumpReq = Common.WFRepository<JumpToStateRequest>.GetEntity(WfIns.EntityRefId);
                    FWfWorkflowInstance WfJumpToDef = Common.WFRepository<FWfWorkflowInstance>.GetEntity(JumpReq.FlowInstanceID);
                    AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfJumpToDef.WfWorkflowDef.WfWorkflowDefId);
                }
                else
                {
                    AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                }
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 5" + WfContext.Parameters.Count, "Hedef");

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);
                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 6" + WfContext.Parameters.Count, "Hedef");

                #endregion Akışın Linki Eklenir

                #region Akışın Atandığı Kişinin Delegesi Bulunur

                if (ActionTypeValue == ActionType.ONWFSTARTASSIGNER || ActionTypeValue == ActionType.ONWFAPPROVEASSIGNER)
                {
                    //Todo Delegasyon Change
                    long WfDelegationId = WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(AssigtoLoginId, WfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                    //long WfDelegationId = Digiturk.Workflow.Common.WorkflowDelegationHelper.GetActiveDelegate(AssigtoLoginId, WfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                    string DelegationNameSurName = "";
                    string DelegationByNameSurName = "";
                    if (WfDelegationId > 0)
                    {
                        DelegationNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfDelegationId);
                        DelegationByNameSurName = DelegationNameSurName + "(" + AssignedUserNameSurname + " Delegesi)";
                        WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", DelegationByNameSurName);
                    }
                }

                #endregion Akışın Atandığı Kişinin Delegesi Bulunur

                // Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 7" + WfContext.Parameters.Count, "Hedef");

                #region Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                long SatinalmaTalepOnaylandi = ConvertionHelper.ConvertValue<int>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.StateDefIDBul("SatinalmaTalepOnaylandi"));

                //Email Adresine göre durum değerlendirilecek. Todo
                if (ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
                {
                    if (WfIns.WfCurrentState != null)
                    {
                        if (WfIns.WfCurrentState.WfStateDef.WfStateDefId != SatinalmaTalepOnaylandi) //1651
                        {
                            if (IsDenyInformationMail(WfIns.WfWorkflowDef.WfWorkflowDefId, ToList[0].LoginId))
                            {
                                return;
                            }
                        }
                    }
                }

                #endregion Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                #region Mail Gönderimi Yapılır

                WfContext.Save();
                Digiturk.Workflow.Common.WFContext WfContext2;
                WfContext2 = WfContext;
                WfContext2.Save();
                WfContext = null;
                WfContext = WfContext2;
                WfContext2 = null;

                //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 8" + WfContext.Parameters.Count, "Hedef");

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }
                // Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", WfInstanceId + "SendEmail Geldi 9" + WfContext.Parameters.Count, "Hedef");

                #endregion Mail Gönderimi Yapılır

                #region Object Disposing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disposing
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir. Akışın Birden Fazla kullanıcıya atanma ihtimaline dayanarak atanan kullanıcıları liste olarak alır.
        /// </summary>
        /// <param name="ActionTypeValue">Aksiyon Tipi-Bu Tip Mail Templateini Belirler</param>
        /// <param name="WfInstanceId">Akış Numarası</param>
        /// <param name="LastActionUserLoginId">Son İşlem yapan kullanıcı</param>
        /// <param name="AssigtoLoginId"> Atanan Kullanıcı </param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        /// <param name="AssignToIdList"> Akışın Atandığı Kullanıcıların listesi</param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId, Digiturk.Workflow.Common.WFContext WfContext, List<long> AssignToIdList)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akış İle İlgili Bilgiler Toplanır

                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);

                #endregion Akış İle İlgili Bilgiler Toplanır

                #region Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                long TemplateMailId = GetTemplateId(WfIns.WfWorkflowDef.WfWorkflowDefId, ActionTypeValue);
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();
                if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFREJECTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER || ActionTypeValue == ActionType.ONWFWORKFLOWAPPROVEENDOWNER || ActionTypeValue == ActionType.ONWFWORKFLOWREJECTENDOWNER)
                {
                    FLogin OwnerLogin = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(WfIns.OwnerLogin.LoginId);
                    ToList.Add(OwnerLogin);
                }
                else
                {
                    if (AssigtoLoginId > 0)
                    {
                        FLogin AssignUser = Digiturk.Workflow.Common.WFRepository<FLogin>.GetEntity(AssigtoLoginId);
                        ToList.Add(AssignUser);
                    }
                }

                #endregion Mail Atılacak Template Id Seçilir ve Mail Atılacak Kullanıcılar Belirlenir

                #region Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Akış İçerisinde kullanılacak Parametreler Dönüştürülür/ LoginId'ler NameSurname'e Dönüştürülür

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region Akışı Oluşturan Kişiye Mail Atılıyorsa Akışın Atandığı Kullanıcılar birden Fazla ise Kullanıcılar listeleniyor

                if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
                {
                    if (AssignToIdList.Count > 0)
                    {
                        AssignedUserNameSurname = "";
                    }
                    for (int i = 0; i < AssignToIdList.Count; i++)
                    {
                        if (i != AssignToIdList.Count - 1)
                        {
                            AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]) + ",";
                        }
                        else
                        {
                            AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]);
                        }
                    }
                }

                #endregion Akışı Oluşturan Kişiye Mail Atılıyorsa Akışın Atandığı Kullanıcılar birden Fazla ise Kullanıcılar listeleniyor

                #region Parametreler WfContex e Yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);

                #endregion Parametreler WfContex e Yüklenir

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                long SatinalmaTalepOnaylandi = ConvertionHelper.ConvertValue<int>(Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.StateDefIDBul("SatinalmaTalepOnaylandi"));

                //Email Adresine göre durum değerlendirilecek. Todo
                if (ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
                {
                    if (WfIns.WfCurrentState != null)
                    {
                        if (WfIns.WfCurrentState.WfStateDef.WfStateDefId != SatinalmaTalepOnaylandi)//1651
                        {
                            if (IsDenyInformationMail(WfIns.WfWorkflowDef.WfWorkflowDefId, ToList[0].LoginId))
                            {
                                return;
                            }
                        }
                    }
                }

                #endregion Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                #region Mail Gönderimi Yapılır

                if (TemplateMailId > 0)
                {
                    //Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderimi Yapılır

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="ActionTypeValue"> Aksiyon Tipi</param>
        /// <param name="WfInstanceId"> Akış Numarası</param>
        /// <param name="ToUserNameSurName"> Mail Gönderilecek Kullancının Adı Soyadı</param>
        /// <param name="ToEmailAdress"> Mail Gönderilecek Kullanıcının Email Adresi</param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        public static void SendEmail(ActionType ActionTypeValue, long WfInstanceId, string ToUserNameSurName, string ToEmailAdress, Digiturk.Workflow.Common.WFContext WfContext)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Gerekli Parametreler Güncelleniyor

                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = GetTemplateId(WfIns.WfWorkflowDef.WfWorkflowDefId, ActionTypeValue);
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                IList<string> ToList = new List<string>();
                IList<string> CcList = new List<string>();
                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = ToUserNameSurName;
                ToList.Add(ToEmailAdress);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);

                #endregion Gerekli Parametreler Güncelleniyor

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region Akışı Oluşturan Kişiye Mail Atılıyorsa

                if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
                {
                    List<FLogin> AssignToIdList = Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(WfInstanceId);
                    if (AssignToIdList.Count > 1)
                    {
                        AssignedUserNameSurname = "";
                    }
                    foreach (var Assignitem in AssignToIdList)
                    {
                        AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Assignitem.LoginId);
                    }
                }

                #endregion Akışı Oluşturan Kişiye Mail Atılıyorsa

                #region Parametreler Context e yazılıyor

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);

                #endregion Parametreler Context e yazılıyor

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                //Email Adresine göre durum değerlendirilecek. Todo
                //if (ActionTypeValue == ActionType.ONWFAPPROVEOWNER || ActionTypeValue == ActionType.ONWFSTARTOWNER)
                //{
                //    if (IsDenyInformationMail(WfIns.WfWorkflowDef.WfWorkflowDefId, ToList[0].LoginId))
                //    {
                //        return;
                //    }
                //}

                #endregion Kullanıcı Bilgilendirme Maili İstemiyorsa Gönderme

                #region Mail Gönderme İşlemi Yapılıyor

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme İşlemi Yapılıyor

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın herhangi bir Fonksiyonu kullanıldığında (Yönlendirme - Durdurma - İptal Etme - Geri Alma vs..) kullanılan mail gönderme fonksiyonudur.
        /// </summary>
        /// <param name="SendToLoginId"> İşlem Yapılan Kullanıcı</param>
        /// <param name="ActionOwner"> İşlemi Yapan Kullanıcı </param>
        /// <param name="ActionName"> İşlemin Adı</param>
        /// <param name="Comment"> Açıklama</param>
        /// <param name="CustomTemplateId"> Özel Mail Template Numarası</param>
        /// <param name="WfInstanceId"> Akış Numarası</param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        public static void SendEmailAction(long SendToLoginId, long ActionOwner, string ActionName, string Comment, long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akışla İlgili Bilgiler Toplanır

                long AssigtoLoginId = SendToLoginId;
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);

                #endregion Akışla İlgili Bilgiler Toplanır

                #region Mail template elde edilir

                long TemplateMailId = CustomTemplateId;
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();

                #endregion Mail template elde edilir

                #region Mail Template içerisinde kullanılan sabit bilgilerin Id to NameSurname dönüşümleri yapılır

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string SendToNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(SendToLoginId);
                string ActionOwnerNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(ActionOwner);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Mail Template içerisinde kullanılan sabit bilgilerin Id to NameSurname dönüşümleri yapılır

                #region Mail Template içerisinde kullanılan değişkenler Parametre listesine (WFContex'e) Yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                WfContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwnerNameSurName);
                WfContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString("dd.MM.yyyy hh:mm:ss"));
                WfContext.Parameters.AddOrChangeItem("ActionDescription", Comment);
                WfContext.Parameters.AddOrChangeItem("Action", ActionName);
                WfContext.Parameters.AddOrChangeItem("ActionTo", SendToNameSurName);

                #endregion Mail Template içerisinde kullanılan değişkenler Parametre listesine (WFContex'e) Yüklenir

                #region Mail Gönderme işlemi yapılır.

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme işlemi yapılır.

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın herhangi bir Fonksiyonu kullanıldığında (Yönlendirme - Durdurma - İptal Etme - Geri Alma vs..) kullanılan mail gönderme fonksiyonudur.
        /// </summary>
        /// <param name="SendToLoginId"> İşlem Yapılan Kullanıcı</param>
        /// <param name="ActionOwner"> İşlemi Yapan Kullanıcı </param>
        /// <param name="ActionName"> İşlemin Adı</param>
        /// <param name="Comment"> Açıklama</param>
        /// <param name="CustomTemplateId"> Özel Mail Template Numarası</param>
        /// <param name="WfInstanceId"> Akış Numarası</param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        public static void SendEmailAction(long SendToLoginId, long ActionOwner, string ActionName, string ActionNameEng, string Comment, long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akışla İlgili Bilgiler Toplanır

                long AssigtoLoginId = SendToLoginId;
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);

                #endregion Akışla İlgili Bilgiler Toplanır

                #region Mail template elde edilir

                long TemplateMailId = CustomTemplateId;
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();

                #endregion Mail template elde edilir

                #region Mail Template içerisinde kullanılan sabit bilgilerin Id to NameSurname dönüşümleri yapılır

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string SendToNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(SendToLoginId);
                string ActionOwnerNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(ActionOwner);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Mail Template içerisinde kullanılan sabit bilgilerin Id to NameSurname dönüşümleri yapılır

                #region Mail Template içerisinde kullanılan değişkenler Parametre listesine (WFContex'e) Yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                WfContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwnerNameSurName);
                WfContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString("dd.MM.yyyy hh:mm:ss"));
                WfContext.Parameters.AddOrChangeItem("ActionDescription", Comment);
                WfContext.Parameters.AddOrChangeItem("Action", ActionName);
                WfContext.Parameters.AddOrChangeItem("ActionEng", ActionNameEng);
                WfContext.Parameters.AddOrChangeItem("ActionTo", SendToNameSurName);

                #endregion Mail Template içerisinde kullanılan değişkenler Parametre listesine (WFContex'e) Yüklenir

                #region Mail Gönderme işlemi yapılır.

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme işlemi yapılır.

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir
        /// </summary>
        /// <param name="CustomTemplateId"></param>
        /// <param name="WfInstanceId"></param>
        /// <param name="WfContext"></param>
        public static void SendEmail(long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akışa Ait Bilgiler Toplanıyor.

                long AssigtoLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WfInstanceId);
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = CustomTemplateId;
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();

                #endregion Akışa Ait Bilgiler Toplanıyor.

                #region Akıştaki Kullanıcılara ait dönüştürmeler yapılıyor

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Akıştaki Kullanıcılara ait dönüştürmeler yapılıyor

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region Bilgiler Context e yükleniyor

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);

                #endregion Bilgiler Context e yükleniyor

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderim İşlemi yapılıyor

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderim İşlemi yapılıyor

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın sonunda kullanıcıya spesifik bilgiler toplayarak gönderilmek için kullanılır.
        /// </summary>
        /// <param name="CustomTemplateId">Gönderilecek Mail Template Numarası</param>
        /// <param name="WfInstanceId">Akış Numarası</param>
        /// <param name="WfContext"> Akışa Özgü Liste </param>
        /// <param name="mailParameters"> Maile özgü Template </param>
        /// <param name="ToList"> To List bilgileri </param>
        public static void SendEmail(long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext, IList<string> ToList, long AssigtoLoginId)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                //long AssigtoLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WfInstanceId);
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = CustomTemplateId;

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                IList<string> CcList = new List<string>();
                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);

                #endregion İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderme İşlemi Yapılıyor

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme İşlemi Yapılıyor

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın sonunda kullanıcıya spesifik bilgiler toplayarak gönderilmek için kullanılır.
        /// </summary>
        /// <param name="CustomTemplateId">Gönderilecek Mail Template Numarası</param>
        /// <param name="WfInstanceId">Akış Numarası</param>
        /// <param name="WfContext"> Akışa Özgü Liste </param>
        /// <param name="mailParameters"> Maile özgü Template </param>
        /// <param name="ToList"> To List bilgileri </param>
        public static void SendEmail(long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext, IDictionary<string, string> mailParameters, IList<string> ToList)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                long AssigtoLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WfInstanceId);
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = CustomTemplateId;

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                IList<string> CcList = new List<string>();
                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);

                #endregion İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderme İşlemi Yapılıyor

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendEmailDirect(TemplateMailId, ActIns, WfContext, mailParameters, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme İşlemi Yapılıyor

                #region Object Disponsing

                WfIns = null;
                WfState = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Akışın Geldiği Adıma Göre Atanan yada Akışı Oluşturan kullanıcılara generic mail gönderir. Bu template Akış Admini Tarafından YYS de kullanılır.
        /// </summary>
        /// <param name="TemplateId"> Gönderilecek Mail'in Mail Template i</param>
        /// <param name="WfContext"> Mail Gönderimi sırasında dinamik değişkenleri tutucak Paramtre listesi </param>
        /// <param name="wfa"> Akış Admini Türü</param>
        /// <param name="lWfs"> İş Akışlarının Listesi</param>
        /// <param name="ToList">Gönderilecek To kullanıcıları</param>
        /// <param name="CcList"> Gönderilecek CC kullanıcıları</param>
        /// <param name="SystemAdminFullName"> Sistem Admini Adı Soyadı</param>
        /// <param name="FlowAdminFullName"> Akış Admini Adı Soyadı</param>
        public static void SendEmail(long TemplateId, Common.WFContext WfContext, WorkFlowAdmin wfa, List<Workflow.Digiflow.Entities.Workflow> lWfs, IList<FLogin> ToList, IList<FLogin> CcList, string SystemAdminFullName, string FlowAdminFullName)
        {
            if (ToList.Count == 0) throw new Exception("Gönderilecek Kişi Boş Olamaz");
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = new FWfActionInstance();
                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", SystemAdminFullName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", FlowAdminFullName);
                string ActivePassive = "Aktif";
                if (wfa.IsActive == 0) ActivePassive = "Pasif";
                WfContext.Parameters.AddOrChangeItem("ActivePassive", ActivePassive);
                if (TemplateId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(ActIns.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
                }
                ActIns = null;
            }
        }

        /// <summary>
        ///   Fatih Yazdı.
        /// </summary>
        /// <param name="CustomTemplateId">TeplateId</param>
        /// <param name="WfInstanceId">WfInstanceId</param>
        /// <param name="WfContext">WfContext</param>
        /// <param name="ToList">ToList</param>
        /// <param name="CcList">CcList null veya string mail adresleri</param>
        public static void SendEmail(long CustomTemplateId, long WfInstanceId, Digiturk.Workflow.Common.WFContext WfContext, IList<string> ToList, IList<string> CcList)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                long AssigtoLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WfInstanceId);
                long LastActionUserLoginId = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetLastActionToLoginId(WfInstanceId);
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns;
                ///Eğer currentState null ise yani akış bitmiş ise
                if (ReferenceEquals(WfIns.WfCurrentState, null))
                {
                    ActIns = new FWfActionInstance();
                }
                else //Eğer CurrentSatte null değilse yani bir akış bir adımdaysa
                {
                    Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                    ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                    WfState = null;
                }
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = CustomTemplateId;
                if (ReferenceEquals(CcList, null))//ccList null gelirse cclistin içi boş bırakılıyor.
                {
                    CcList = new List<string>();
                }

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Mailing İçin Gerekli Bilgiler Toplanıyor

                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Mailing İçin Gerekli Bilgiler Toplanıyor

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);

                #endregion İşlem Yapan Kullanıcıların İsimleri Bulunuyor

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderme İşlemi Yapılıyor

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme İşlemi Yapılıyor

                #region Object Disponsing

                WfIns = null;
                ActIns = null;
                ToList = null;
                CcList = null;

                #endregion Object Disponsing
            }
        }

        /// <summary>
        /// Kullanılacak gerekli mail templateini döndüdür
        /// </summary>
        /// <param name="WfDefId"> Akışın Numarası</param>
        /// <param name="ActionTypeValue"> Aksiyonun Tipi</param>
        /// <returns></returns>
        public static long GetTemplateId(long WfDefId, ActionType ActionTypeValue)
        {
            return Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetMailTemplateId(WfDefId.ToString(), ActionTypeValue.ToString());
        }

        /// <summary>
        /// Kişiye Notifikasyon (Hatırlatma) maili göndermek için kullanılır.
        /// </summary>
        /// <param name="WfInstanceId"> Akışın Numarası</param>
        /// <param name="LastActionUserLoginId">Son İşlem Yapan Kullanıcı</param>
        /// <param name="AssigtoLoginId"> Atanan Kullanıcı</param>
        public static void SendNotification(long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                #region Akış İle İlgili Gerekli Bilgileri Toplanır

                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Common.WFContext WfContext;
                WfContext = new Digiturk.Workflow.Common.WFContext(WfIns);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfStateDef WfStateDef = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateDef>.GetEntity(WfState.WfStateDef.WfStateDefId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);

                #endregion Akış İle İlgili Gerekli Bilgileri Toplanır

                #region Notifikasyon Bilgilerini Toplar ve Kullanılacak Bilgileri LoginId NameSurName dönüşümleri yapılır

                long TemplateMailId = ConvertionHelper.ConvertValue<long>(System.Configuration.ConfigurationManager.AppSettings["NotificationOtoTemplateID"]);
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();
                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));

                #endregion Notifikasyon Bilgilerini Toplar ve Kullanılacak Bilgileri LoginId NameSurName dönüşümleri yapılır

                #region Yerine Kontrolü Yapılıyor

                LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);

                #endregion Yerine Kontrolü Yapılıyor

                #region Mail İçerisinde kullanılacak Bilgileri Parametre listesine yüklenir

                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                WfContext.Parameters.AddOrChangeItem("StateName", WfStateDef.Name);

                #endregion Mail İçerisinde kullanılacak Bilgileri Parametre listesine yüklenir

                #region Akış Adminleri Listeye Eklenir

                string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
                WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);

                #endregion Akış Adminleri Listeye Eklenir

                #region Akışın Linki Eklenir

                string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
                string TaskScreenLink = "";
                TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
                WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);

                #endregion Akışın Linki Eklenir

                #region Mail Gönderme işlemi yapılır

                if (TemplateMailId > 0)
                {
                    Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
                }

                #endregion Mail Gönderme işlemi yapılır
            }
        }

        /// <summary>
        /// WfContext den bağımsız içerikler göndermek için kullanılır.
        /// </summary>
        /// <param name="mailTemplateId">Mail Template ID si</param>
        /// <param name="actionInstance"> Mail Atılırken kullanılacak Action Instance </param>
        /// <param name="wfContext"> Mail Atılırken kullanılacak WfContex</param>
        /// <param name="mailParameters"> Özel dinamik Mail İçerik parametresi </param>
        /// <param name="emailToList"> To da gönderilecek kullanıcıların listesi </param>
        /// <param name="emailCcList"> CC de gönderilecek kullanıcıların listesi</param>
        public static void SendEmailDirect(long? mailTemplateId, FWfActionInstance actionInstance, Common.WFContext wfContext, IDictionary<string, string> mailParameters, IList<string> emailToList, IList<string> emailCcList)
        {
            if (mailTemplateId > 0)
            {
                Digiturk.Workflow.Engine.MailHelper.SendEmailDirect(mailTemplateId, actionInstance, wfContext, mailParameters, emailToList, emailCcList);
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(actionInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }
        }

        public static bool IsValidEmail(String Email)
        {
            if (Email != null && Email != "")
                return Regex.IsMatch(Email, @"\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*");
            else
                return false;
        }

        //Tüm onaycılara bilgilendirme maili
        public static void SendEmailAllAcceptLoginList(long LoginId, long WfInstanceId, DataTable CommentSender, long MailTempId, Digiturk.Workflow.Common.WFContext wfContext)
        {
            if (CommentSender.Rows.Count > 0)
            {
                for (int i = 0; i < CommentSender.Rows.Count; i++)
                {
                    if (LoginId == ConvertionHelper.ConvertValue<long>(CommentSender.Rows[i][0].ToString()))
                    {
                        List<FLogin> ToAcceptList = new List<FLogin>();

                        //onaylayanlara bilgi maili...
                        ToAcceptList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAllAcceptLoginList(WfInstanceId);
                        if (ToAcceptList.Count > 0)
                        {
                            for (int j = 0; j < ToAcceptList.Count; j++)
                            {
                                if (ToAcceptList[j].LoginId.ToString() != LoginId.ToString())
                                {
                                    SendEmail(MailTempId, WfInstanceId, LoginId, ToAcceptList[j].LoginId, wfContext, new List<long>());
                                }
                            }
                        }
                    }
                }
            }
        }

        //Tüm onaycılara bilgilendirme maili ---
        public static void SendEmailAllAcceptLoginList(FLogin FLogin, long WfInstanceId, DataTable CommentSender, long MailTempId, Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList)
        {
            //Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextListOwnerMail = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
            IList<FLogin> ToListOwner = new List<FLogin>();
            IList<FLogin> CcListOwner = new List<FLogin>();

            ToListOwner.Add(FLogin);

            if (CommentSender.Rows.Count > 0)
            {
                for (int i = 0; i < CommentSender.Rows.Count; i++)
                {
                    if (FLogin.LoginId == ConvertionHelper.ConvertValue<long>(CommentSender.Rows[i][0].ToString()))
                    {
                        List<FLogin> ToAcceptList = new List<FLogin>();

                        //onaylayanlara bilgi maili...
                        ToAcceptList = Digiturk.Workflow.Digiflow.DataAccessLayer.CheckingWorker.GetAllAcceptLoginList(WfInstanceId);
                        if (ToAcceptList.Count > 0)
                        {
                            for (int j = 0; j < ToAcceptList.Count; j++)
                            {
                                if (ToAcceptList[j].LoginId.ToString() != FLogin.LoginId.ToString())
                                {
                                    IList<FLogin> ToListOwner2 = new List<FLogin>();
                                    ToListOwner2.Add(ToAcceptList[j]);
                                    Digiturk.Workflow.DigiFlow.Framework.Action.CustomMailWorker.SendEmail(MailTempId, ContextList, ToListOwner2, CcListOwner);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// Verilen adresi indirir, string olarak döndürür.
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public static string DownloadHTMLPage(Uri url)
        {
            WebResponse response = null;
            Stream stream = null;
            StreamReader sr = null;

            try
            {
                HttpWebRequest hwr = (HttpWebRequest)WebRequest.Create(url);
                hwr.PreAuthenticate = true;

                hwr.Credentials = new NetworkCredential(ConfigurationManager.AppSettings["Web.Services.UserName"], ConfigurationManager.AppSettings["Web.Services.Password"], ConfigurationManager.AppSettings["Web.Services.Domain"]);
                hwr.UserAgent = "User-Agent: Mozilla/5.0 (Windows; U; Windows NT 6.1; en-US; rv:1.9.2.13) Gecko/20101203 Firefox/3.6.13 IGNORE: MSIE 8.0; Windows NT 6.1; Trident/4.0; AskTbMP3R7/5.9.1.14019)";
                response = hwr.GetResponse();
                stream = response.GetResponseStream();
                if (!response.ContentType.ToLower().StartsWith("text/"))
                {
                    return null;
                }

                string buffer = "", line;
                sr = new StreamReader(stream);
                while ((line = sr.ReadLine()) != null)
                {
                    buffer += line + "\r\n";
                }

                return buffer;
            }
            catch (WebException e)
            {
                throw e;
            }
            catch (IOException e)
            {
                throw e;
            }
            finally
            {
                if (sr != null)
                    sr.Close();
                if (stream != null)
                    stream.Close();
                if (response != null)
                    response.Close();
            }
        }

        /// Akışın sonunda kullanıcıya spesifik bilgiler toplayarak gönderilmek için kullanılır.grup bilgisine göre SetWFView ya da SetViewPermissionToGroup view yetkisi veriliyor

        public static void SendEmailAndView(long TemplateMailId, long wfInstanceId, Digiturk.Workflow.Common.WFContext WfContext, long loginId, IList<string> ToList, IList<long> ccLongList, long LogicalGrupId, long LastActionUserLoginId)
        {
            if (LogicalGrupId == 0) // tek tek
            {
                SendEmail(TemplateMailId, wfInstanceId, LastActionUserLoginId, loginId, WfContext, ccLongList);
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.SetWFView(wfInstanceId, loginId);
            }
            else  // grup
            {
                SendEmail(TemplateMailId, wfInstanceId, WfContext, ToList, LastActionUserLoginId);
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.SetViewPermissionToGroup(LogicalGrupId, wfInstanceId);
            }
        }

        public static List<string> GetLogicalGroupMails(long logicalGroupID)
        {
            List<string> AssignToLoginList = new List<string>();

            DataTable dtb = LogicalGroupHelper.GetPersonelList(logicalGroupID);
            foreach (DataRow item in dtb.Rows)
            {
                string eMail = (item["CONTENT"].ToString());
                if (!AssignToLoginList.Contains(eMail))
                {
                    AssignToLoginList.Add(eMail);
                }
            }
            dtb = null;
            return AssignToLoginList;
        }

        public static WFContext GetContextBuilding(WFContext WfContext, FWfActionInstance actionInstance, ActionType ActionTypeValue, long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId, List<long> AssignToIdList)
        {
            FWfWorkflowInstance WfIns = actionInstance.WfStateInstance.WfWorkflowInstance;
            WfContext.Parameters.AddOrChangeItem("AssigtoLoginId", AssigtoLoginId);
            //WfContext.Parameters.AddOrChangeItem("AssignToIdList", AssignToIdList);
            WfContext.Parameters.AddOrChangeItem("AssignToIdList", 0);
            WfContext.Parameters.AddOrChangeItem("LastActionUserLoginId", LastActionUserLoginId);
            WfContext.Parameters.AddOrChangeItem("ActionTypeValue", (long)((ActionType)ActionTypeValue));
            string CreateUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
            string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
            string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
            string GetFinalLoginId = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
            string GetFinalLoginList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
            string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));
            LastActionUserNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetLastActionUserNameSurName(WfInstanceId);
            if (ActionTypeValue == ActionType.ONWFSTARTOWNER || ActionTypeValue == ActionType.ONWFAPPROVEOWNER)
            {
                if (AssignToIdList.Count > 0)
                {
                    AssignedUserNameSurname = "";
                }
                for (int i = 0; i < AssignToIdList.Count; i++)
                {
                    if (i != AssignToIdList.Count - 1)
                    {
                        AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]) + ",";
                    }
                    else
                    {
                        AssignedUserNameSurname += " " + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(AssignToIdList[i]);
                    }
                }
            }
            AssignedUserNameSurname = "-";
            WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
            WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
            WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
            WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
            string AdminList = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetWorkFlowOfAdminList(WfIns.WfWorkflowDef.WfWorkflowDefId);
            WfContext.Parameters.AddOrChangeItem("WorkFlowAdminList", AdminList);
            string Domain = System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.LinkDomain"];
            string TaskScreenLink = "";
            TaskScreenLink = Domain + "/" + Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.GetScreenName(WfIns.WfWorkflowDef.WfWorkflowDefId) + "?wfInstanceId=" + WfInstanceId.ToString();
            WfContext.Parameters.AddOrChangeItem("TaskScreenLink", TaskScreenLink);
            return WfContext;
        }
    }
}