#!/bin/bash

echo "========================================"
echo "TEST 2: WebView Headers Test (MacBook)"
echo "========================================"
echo ""

# JWT Authentication Configuration
# FILL IN YOUR CREDENTIALS HERE:
USERNAME="DTADPORTALTEST1"  # e.g., "DTUMKORKMAZ"
PASSWORD="xF66X6V43DGQ"  # Your AD password
API_BASE="https://digiflowtest.digiturk.com.tr"

echo "Testing API with webview-specific headers and JWT..."
echo "Username: $USERNAME"
echo ""

# Function to get JWT token
get_jwt_token() {
    echo "[AUTH] Getting JWT token..."

    login_response=$(curl -s -X POST "$API_BASE/api/auth/login" \
        -H "Content-Type: application/json" \
        -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}")

    token=$(echo "$login_response" | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

    if [ -n "$token" ]; then
        echo "✅ JWT token acquired"
        return 0
    else
        echo "❌ Failed to get JWT token"
        echo "Response: $login_response"
        return 1
    fi
}

# Get JWT token
if ! get_jwt_token; then
    echo "❌ Cannot proceed without JWT token"
    exit 1
fi
echo ""

# Test 2.1: Regular API request (public endpoint)
echo "[2.1] Testing regular API request..."
echo "URL: $API_BASE/api/health"
regular_status=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/api/health")
echo "Regular request status: $regular_status"

regular_frame=$(curl -s -I "$API_BASE/api/health" | grep -i "x-frame-options")
echo "Regular X-Frame-Options: $regular_frame"
echo ""

# Test 2.2: WebView API request with mobile headers (public endpoint)
echo "[2.2] Testing webview API request with mobile headers..."
webview_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$API_BASE/api/health")
echo "WebView request status: $webview_status"

webview_frame=$(curl -s -I \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$API_BASE/api/health" | grep -i "x-frame-options")
echo "WebView X-Frame-Options: $webview_frame"
echo ""

# Test 2.3: Authenticated API request (regular)
echo "[2.3] Testing authenticated API request (regular)..."
auth_regular_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $token" \
  "$API_BASE/api/auth/verify-session")
echo "Auth regular status: $auth_regular_status"

auth_regular_frame=$(curl -s -I \
  -H "Authorization: Bearer $token" \
  "$API_BASE/api/auth/verify-session" | grep -i "x-frame-options")
echo "Auth regular X-Frame-Options: $auth_regular_frame"
echo ""

# Test 2.4: Authenticated API request (webview)
echo "[2.4] Testing authenticated API request (webview)..."
auth_webview_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "Authorization: Bearer $token" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$API_BASE/api/auth/verify-session")
echo "Auth webview status: $auth_webview_status"

auth_webview_frame=$(curl -s -I \
  -H "Authorization: Bearer $token" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  "$API_BASE/api/auth/verify-session" | grep -i "x-frame-options")
echo "Auth webview X-Frame-Options: $auth_webview_frame"
echo ""

# Test 2.3: Check CSP headers
echo "[2.3] Testing Content Security Policy..."
regular_csp=$(curl -s -I https://digiflowtest.digiturk.com.tr/api/health | grep -i "content-security-policy")
echo "Regular CSP: $regular_csp"

webview_csp=$(curl -s -I \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  https://digiflowtest.digiturk.com.tr/api/health | grep -i "content-security-policy")
echo "WebView CSP: $webview_csp"
echo ""

echo "========================================"
echo "TEST 2 RESULTS ANALYSIS"
echo "========================================"
echo "Expected Results:"
echo "- Regular requests should have X-Frame-Options: DENY"
echo "- WebView requests should have X-Frame-Options: SAMEORIGIN"
echo "- Both should return status 200"
echo ""
echo "Actual Results:"
echo "- Regular X-Frame-Options: $regular_frame"
echo "- WebView X-Frame-Options: $webview_frame"
echo "- Regular Status: $regular_status"
echo "- WebView Status: $webview_status"
echo ""
echo "Please report back these results!"
echo ""
