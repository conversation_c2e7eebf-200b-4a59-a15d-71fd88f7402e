#!/bin/bash

echo "========================================"
echo "TEST 2: WebView Headers Test (MacBook)"
echo "========================================"
echo ""
echo "Testing API with webview-specific headers..."
echo ""

# Test 2.1: Regular API request
echo "[2.1] Testing regular API request..."
echo "URL: https://digiflowtest.digiturk.com.tr/api/health"
regular_status=$(curl -s -o /dev/null -w "%{http_code}" https://digiflowtest.digiturk.com.tr/api/health)
echo "Regular request status: $regular_status"

regular_frame=$(curl -s -I https://digiflowtest.digiturk.com.tr/api/health | grep -i "x-frame-options")
echo "Regular X-Frame-Options: $regular_frame"
echo ""

# Test 2.2: WebView API request with mobile headers
echo "[2.2] Testing webview API request with mobile headers..."
webview_status=$(curl -s -o /dev/null -w "%{http_code}" \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  https://digiflowtest.digiturk.com.tr/api/health)
echo "WebView request status: $webview_status"

webview_frame=$(curl -s -I \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  -H "X-Request-Source: DigiHRApp" \
  -H "User-Agent: ReactNative-WebView-DigiHR-App" \
  https://digiflowtest.digiturk.com.tr/api/health | grep -i "x-frame-options")
echo "WebView X-Frame-Options: $webview_frame"
echo ""

# Test 2.3: Check CSP headers
echo "[2.3] Testing Content Security Policy..."
regular_csp=$(curl -s -I https://digiflowtest.digiturk.com.tr/api/health | grep -i "content-security-policy")
echo "Regular CSP: $regular_csp"

webview_csp=$(curl -s -I \
  -H "X-Mobile-App: true" \
  -H "X-From-Mobile-WebView: true" \
  https://digiflowtest.digiturk.com.tr/api/health | grep -i "content-security-policy")
echo "WebView CSP: $webview_csp"
echo ""

echo "========================================"
echo "TEST 2 RESULTS ANALYSIS"
echo "========================================"
echo "Expected Results:"
echo "- Regular requests should have X-Frame-Options: DENY"
echo "- WebView requests should have X-Frame-Options: SAMEORIGIN"
echo "- Both should return status 200"
echo ""
echo "Actual Results:"
echo "- Regular X-Frame-Options: $regular_frame"
echo "- WebView X-Frame-Options: $webview_frame"
echo "- Regular Status: $regular_status"
echo "- WebView Status: $webview_status"
echo ""
echo "Please report back these results!"
echo ""
