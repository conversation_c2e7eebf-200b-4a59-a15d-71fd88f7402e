# Mobile WebView Authentication Persistence Solution

## Problem Statement
When the mobile app (DigiHRApp) was closed or refreshed, users had to re-authenticate because:
- The WebView was using transparent Windows authentication without JWT tokens
- No token persistence mechanism existed across app restarts
- The refresh token flow was not implemented for mobile WebView

## Solution Implemented

### 1. New JWT Token Generation Endpoint
Added `/auth/windows` endpoint in `AuthController.cs` that:
- Accepts Windows authentication for mobile WebView requests
- Validates mobile headers (X-From-Mobile-WebView, X-Mobile-App, X-Is-Mobile)
- Generates JWT access token and refresh token
- Returns token information including expiration times and auto-refresh settings

### 2. Refresh Token Endpoint
Added `/auth/refresh` endpoint that:
- Accepts expired access token and valid refresh token
- Validates tokens and extracts user information
- Generates new access and refresh tokens
- Maintains user session without re-authentication

### 3. Token Response Structure
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "base64-refresh-token",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "userId": "12345",
  "username": "DOMAIN\\username",
  "issuedAt": "2025-01-28T10:30:00Z",
  "accessTokenMinutes": 60,
  "refreshTokenDays": 90,
  "autoRefreshEnabled": true,
  "tokenRefreshBufferMinutes": 5
}
```

### 4. Mobile Implementation Requirements
The mobile app needs to:
1. Call `/auth/windows` with Windows credentials on first launch
2. Store tokens securely (iOS Keychain/Android Keystore)
3. Include JWT token in Authorization header for all API calls
4. Check token expiration before API calls
5. Refresh tokens when they expire (or 5 minutes before)
6. Load stored tokens on app restart

## Benefits

1. **Persistent Authentication**: Users stay logged in across app restarts
2. **Long-term Sessions**: Refresh tokens valid for 90 days
3. **Automatic Token Refresh**: Tokens can be refreshed without re-authentication
4. **Secure Token Storage**: Tokens stored in platform-specific secure storage
5. **Seamless Experience**: No repeated login prompts

## Configuration Requirements

The following environment variables must be set:
```
DIGIFLOW_JWT_SECRET=<secure-secret-key>
DIGIFLOW_JWT_ISSUER=DigiflowAPI
DIGIFLOW_JWT_AUDIENCE=DigiflowMobile
DIGIFLOW_JWT_ACCESS_TOKEN_MINUTES=60
DIGIFLOW_JWT_REFRESH_TOKEN_DAYS=90
DIGIFLOW_MAX_REFRESH_TOKENS_PER_USER=5
DIGIFLOW_MOBILE_LONG_TERM_AUTH=true
DIGIFLOW_AUTO_REFRESH_ON_RESUME=true
DIGIFLOW_TOKEN_REFRESH_BUFFER_MINUTES=5
```

## Testing

Use the provided test scripts:
1. `test-mobile-token-persistence.ps1` - Tests the complete token flow
2. `test-mobile-webview-auth.ps1` - Tests WebView authentication

## Implementation Checklist for Mobile Team

- [ ] Implement secure token storage using platform APIs
- [ ] Add Windows auth call to `/auth/windows` on first launch
- [ ] Store all token information securely
- [ ] Load tokens on app startup
- [ ] Add Authorization header to all API requests
- [ ] Implement token expiration checking
- [ ] Implement automatic token refresh
- [ ] Handle token refresh failures gracefully
- [ ] Test across app restarts and network changes
- [ ] Add proper error handling and user feedback

## Files Modified

1. `/src/DigiflowAPI.WebApi/Controllers/AuthController.cs`
   - Added `WindowsAuthForMobile()` method
   - Added `RefreshToken()` method
   - Added token generation helper methods

2. `/src/DigiflowAPI.Application/DTOs/Auth/RefreshTokenRequest.cs`
   - Created DTO for refresh token requests

3. Documentation files created:
   - `MOBILE_TOKEN_PERSISTENCE_GUIDE.md` - Comprehensive implementation guide
   - `test-mobile-token-persistence.ps1` - Test script
   - This summary file

## Next Steps

1. Mobile team implements token storage and management
2. Test token persistence across various scenarios
3. Monitor token refresh patterns in production
4. Consider implementing device-specific tokens for added security
5. Add token revocation mechanism for logout/security