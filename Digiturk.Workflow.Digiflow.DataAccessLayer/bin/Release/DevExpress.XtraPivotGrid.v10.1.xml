<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.XtraPivotGrid.v10.1</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraPivotGrid.Customization.CustomizationFormStyle">

            <summary>
                <para>Contains values that specify the customization form's style.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.Customization.CustomizationFormStyle.Excel2007">
            <summary>
                <para>The Customization Form is represented using the Office 2007 style:
<para>

</para>
An end-user can move fields between areas by dragging their field headers within the Customization Form. When the Defer Layout Update check box is checked, dragging fields doesn't immediately update the layout. To update the layout, click the Update button.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.Customization.CustomizationFormStyle.Simple">
            <summary>
                <para>The Customization Form is represented using the Simple style:
<para>

</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridBestFitMode">

            <summary>
                <para>Contains values that specify which elements are taken into account when calculating the best-fit width for columns.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.Cell">
            <summary>
                <para>The contents of data cells are taken into account when calculating the best-fit size.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.Default">
            <summary>
                <para>This option is the combination of the <see cref="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.FieldValue"/>, <see cref="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.FieldHeader"/> and <see cref="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.Cell"/> options.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.FieldHeader">
            <summary>
                <para>The contents of field headers are taken into account when calculating the best-fit size.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.FieldValue">
            <summary>
                <para>The contents of field values are taken into account when calculating the best-fit size.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridBestFitMode.None">
            <summary>
                <para>The best-fit functionality is disabled.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup">

            <summary>
                <para>Contains options that affect the appearance and behavior of filter dropdown windows. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the PivotGridOptionsFilterPopup class.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that is invoked when options of the created object are changed.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowContextMenu">
            <summary>
                <para>Gets or sets whether right-clicking a filter dropdown window invokes a context menu that allows you to invert the current check states of items.
</para>
            </summary>
            <value><b>true</b> if a context menu is enabled for a filter dropdown; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowFilterTypeChanging">
            <summary>
                <para>Gets or sets whether the filter's type can be changed at runtime, via a combobox displayed at the top of the filter dropdown window.
</para>
            </summary>
            <value><b>true</b> if the filter's type can be changed at runtime; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowIncrementalSearch">
            <summary>
                <para>Gets or sets whether the incremental searching feature is enabled, allowing an end-user to locate an item in the dropdown by typing the item's initial characters.
</para>
            </summary>
            <value><b>true</b> if the incremental searching feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup.AllowMultiSelect">
            <summary>
                <para>Gets or sets whether multiple item selection is enabled.
</para>
            </summary>
            <value><b>true</b> if multiple item selection is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingEditor"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraEditors.Repository.RepositoryItem)">
            <summary>
                <para>Initializes a new instance of the CancelPivotCellEditEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object used to initialize the created CancelPivotCellEditEventArgs object.

            </param>
            <param name="repositoryItem">
		A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the event must be canceled.
</para>
            </summary>
            <value><b>true</b> if the event must be canceled; otherwise, <b>false</b>
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CancelPivotCellEditEventArgs.RepositoryItem">
            <summary>
                <para>Gets the repository item which identifies the settings of the in-place editor for the cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that represents the in-place editor's settings.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx">

            <summary>
                <para>Contains options for a field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.#ctor(DevExpress.XtraPivotGrid.PivotOptionsChangedEventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridFieldOptionsEx class with the specified settings.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will receive change notifications.

            </param>
            <param name="viewBagOwner">
		An IViewBagOwner object that is used to initialize the created object.

            </param>
            <param name="objectPath">
		A string value that is used to initialize the created object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.AllowEdit">
            <summary>
                <para>Gets or sets whether data editing is allowed for the current field.
</para>
            </summary>
            <value><b>true</b> to allow editing cell values that correspond to the current data field; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ReadOnly">
            <summary>
                <para>Gets or sets whether end-users can modify cell values.
</para>
            </summary>
            <value><b>true</b> to prevent a cell value from being changed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowButtonMode">
            <summary>
                <para>Gets or sets which cells corresponding to the current field  display editor buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum"/> value that specifies the current display mode for cell buttons.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx.ShowUnboundExpressionMenu">
            <summary>
                <para>Gets or sets whether an end-user can open an Expression Editor for the current unbound field, using a context menu.
</para>
            </summary>
            <value><b>true</b> if an end-user can open an Expression Editor for the current unbound field, using a context menu; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx">

            <summary>
                <para>Provides customization options for a PivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.#ctor(System.EventHandler,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotGridOptionsCustomizationEx class with the specified settings.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will handle firing of the event, as a result of changing the object's properties.


            </param>
            <param name="viewBagOwner">
		An IViewBagOwner object that is used to initialize the created object.

            </param>
            <param name="objectPath">
		A string value that is used to initialize the created object.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the PivotGridOptionsCustomizationEx class with the specified settings.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will handle firing of the event, as a result of changing the object's properties.


            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.AllowEdit">
            <summary>
                <para>Gets or sets whether data editing is enabled.
</para>
            </summary>
            <value><b>true</b> if data editing is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.AllowResizing">
            <summary>
                <para>Gets or sets whether end-users are allowed to resize the PivotGridControl's elements.
</para>
            </summary>
            <value><b>true</b> if end-users are allowed to resize the PivotGridControl's elements; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.CustomizationFormLayout">
            <summary>
                <para>Gets or sets the customization form's layout when it is painted in Excel2007 style.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Customization.CustomizationFormLayout"/> object that specifies how fields are arranged within the customization form.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.CustomizationFormStyle">
            <summary>
                <para>Gets or sets the customization form's style.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Customization.CustomizationFormStyle"/> value that specifies the customization form's style.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.DeferredUpdates">
            <summary>
                <para>Gets or sets whether dragging fields within the Customization Form (when it's painted using the Excel2007 style) immediately updates the layout of fields in the PivotGrid control. This option controls the state of the 'Defer Layout Update' check box in the Customization Form, when the <see cref="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx.CustomizationFormStyle"/> option is set to <b>Excel2007</b>.
</para>
            </summary>
            <value><b>true</b> if the layout of fields in the PivotGrid control must not be immediately updated; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum">

            <summary>
                <para>Contains values that specify which cells of a data field display editor buttons.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.Default">
            <summary>
                <para>This option is equivalent to the ShowOnlyInEditor option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowAlways">
            <summary>
                <para>Editor buttons are displayed for all cells.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowForFocusedCell">
            <summary>
                <para>Editor buttons are only displayed for the focused cell. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum.ShowOnlyInEditor">
            <summary>
                <para>Editor buttons are only displayed when a cell editor is active. 

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEdit"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraEditors.Repository.RepositoryItem)">
            <summary>
                <para>Initializes a new instance of the PivotCustomCellEditEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object used to initialize the created PivotCustomCellEditEventArgs object.

            </param>
            <param name="repositoryItem">
		A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomCellEditEventArgs.RepositoryItem">
            <summary>
                <para>Gets or sets the in-place editor for the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that specifies the in-place editor for the current cell.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase">

            <summary>
                <para>The base class for classes that provide data for the PivotGrid control's menu item clicking events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraPivotGrid.PivotGridFieldBase,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point,DevExpress.Utils.Menu.DXMenuItem)">
            <summary>
                <para>Initializes a new instance of the PivotGridMenuItemClickEventArgsBase class.
</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase"/> object which holds the pivot grid's view information.

            </param>
            <param name="menuType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu's type. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType"/> property.

            </param>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the pivot grid's context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu"/> property. 

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object which represents the field whose header has been right-clicked by an end-user. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Field"/> property. 

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area"/> property.

            </param>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the point which an end-user right-clicked to invoke the menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point"/> property. 

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object which specifies the menu item that has been clicked. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.Item">
            <summary>
                <para>Gets the menu item that has been clicked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object which specifies the menu item that has been clicked.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase">

            <summary>
                <para>The base class for classes providing data for the PivotGrid control's menu handling events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraPivotGrid.PivotGridFieldBase,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the PivotGridMenuEventArgsBase class.
</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfoBase"/> object which holds the pivot grid's view information.

            </param>
            <param name="menuType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu's type. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType"/> property.

            </param>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the pivot grid's context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu"/> property. 

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object which represents the field whose header has been right-clicked by an end-user. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Field"/> property. 

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area"/> property.

            </param>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the point which an end-user right-clicked to invoke the menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point"/> property. 

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Allow">
            <summary>
                <para>Gets or sets whether the context menu is displayed.
</para>
            </summary>
            <value><b>true</b> to display the context menu; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area">
            <summary>
                <para>Gets the area of the field whose header or value has been right-clicked by a user to invoke the context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Field">
            <summary>
                <para>Gets the field whose header has been right-clicked by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object which represents the field whose header has been right-clicked by an end-user. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu">
            <summary>
                <para>Gets or sets the pivot grid's context menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the pivot grid's context menu.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType">
            <summary>
                <para>Gets the type of the invoked menu.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu's type.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point">
            <summary>
                <para>Gets or sets the location at which the context menu has been invoked.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure which specifies the point at which the end-user right-clicked to invoke the menu. The point's coordinates are set relative to the pivot grid's top-left corner.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the XtraPivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.Utils.AppearanceObject,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
            <summary>
                <para>Initializes a new instance of the PivotCustomAppearanceEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object.

            </param>
            <param name="viewInfo">
		A PivotGridViewInfo object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomAppearanceEventArgs.Appearance">
            <summary>
                <para>Gets or sets the appearance settings of the currently processed cell.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object containing appearance settings for the processed cell.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportFieldValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.#ctor(DevExpress.XtraPrinting.IVisualBrick,DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfoBase,DevExpress.XtraPivotGrid.Printing.ExportAppearanceObject)">
            <summary>
                <para>Initializes a new instance of the CustomExportFieldValueEventArgs class with the specified settings.
</para>
            </summary>
            <param name="brick">
		An IVisualBrick object.

            </param>
            <param name="viewInfo">
		A PivotFieldsAreaCellViewInfoBase object.

            </param>
            <param name="appearance">
		An ExportAppearanceObject object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.Appearance">
            <summary>
                <para>Gets or sets the appearance object used to paint the current field value.
</para>
            </summary>
            <value>An ExportAppearanceObject object that contains the corresponding settings.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.ContainsLevel(System.Int32)">
            <summary>
                <para>Indicates whether the specified row or column level corresponds to the processed field value.
</para>
            </summary>
            <param name="level">
		An integer value that specifies the row or column level.

            </param>
            <returns><b>true</b> if the specified row or column level corresponds to the processed field value; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.CustomTotal">
            <summary>
                <para>Gets a custom total to which the processed field value corresponds.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object that represents the custom total to which the processed value corresponds. <b>null</b> (<b>Nothing</b> in Visual Basic) if the field value doesn't correspond to a custom total.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the processed value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represent the data field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.EndLevel">
            <summary>
                <para>Gets the maximum row level (for row fields) or column level (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column level that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.Field">
            <summary>
                <para>Gets a field whose value is being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field whose value is being processed.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.IsCollapsed">
            <summary>
                <para>Gets whether the processed field value is collapsed.
</para>
            </summary>
            <value><b>true</b> if the processed field value is collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.IsColumn">
            <summary>
                <para>Gets whether the processed value corresponds to a column field.
</para>
            </summary>
            <value><b>true</b> if the processed value corresponds to a column field; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.IsOthersValue">
            <summary>
                <para>Gets whether the processed value corresponds to the "Others" row/column.
</para>
            </summary>
            <value><b>true</b> if the processed value corresponds to the "Others" row/column; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.IsTopMost">
            <summary>
                <para>Gets whether the processed field value resides within the top most row or column.
</para>
            </summary>
            <value><b>true</b> if the processed field value resides within the top most row or column; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.StartLevel">
            <summary>
                <para>Gets the minimum row level (for row fields) or column level (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column level that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.Text">
            <summary>
                <para>Gets the processed field value's display text.
</para>
            </summary>
            <value>A string value that specifies the field value's display text.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.Value">
            <summary>
                <para>Gets the processed field value.
</para>
            </summary>
            <value>An object that represents the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportFieldValueEventArgs.ValueType">
            <summary>
                <para>Gets the type of the currently processed field value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value that identifies the field value's type.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsed"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanded"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotFieldValueEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsed"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanded"/> events.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomPrintEventArgs">

            <summary>
                <para>Represents the base class for classes that provide information to events that fire when exporting cells and field values.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomPrintEventArgs.#ctor(DevExpress.XtraPrinting.IVisualBrick)">
            <summary>
                <para>Initializes a new instance of the CustomPrintEventArgs object.
</para>
            </summary>
            <param name="brick">
		An IVisualBrick object that is used to initialize the <see cref="P:DevExpress.XtraPivotGrid.CustomPrintEventArgs.Brick"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomPrintEventArgs.Brick">
            <summary>
                <para>Provides access to the brick that represents the contents and appearance of a cell, when it is printed or exported.

</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.XtraPrinting.IVisualBrick"/> interface or its descendant (e.g. <see cref="T:DevExpress.XtraPrinting.ITextBrick"/>, <see cref="T:DevExpress.XtraPrinting.IImageBrick"/>, etc.).
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportHeader"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.#ctor(DevExpress.XtraPrinting.IVisualBrick,DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderViewInfoBase,DevExpress.XtraPivotGrid.Printing.ExportAppearanceObject)">
            <summary>
                <para>Initializes a new instance of the CustomExportHeaderEventArgs class with the specified settings.
</para>
            </summary>
            <param name="brick">
		An IVisualBrick object.

            </param>
            <param name="headerViewInfo">
		A PivotHeaderViewInfoBase object.

            </param>
            <param name="appearance">
		An ExportAppearanceObject object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.Appearance">
            <summary>
                <para>Gets or sets the appearance object used to paint the current header.
</para>
            </summary>
            <value>An ExportAppearanceObject object that contains the corresponding settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.Caption">
            <summary>
                <para>Gets the processed field's caption.
</para>
            </summary>
            <value>A string value that specifies the processed field's caption.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportHeaderEventArgs.Field">
            <summary>
                <para>Gets the processed field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldSortBySummaryInfo"/> object that represents the processed field.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomExportCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.#ctor(DevExpress.XtraPrinting.IVisualBrick,DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraPivotGrid.Printing.ExportAppearanceObject,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
            <summary>
                <para>Initializes a new instance of the CustomExportCellEventArgs class with the specified settings.
</para>
            </summary>
            <param name="brick">
		An IVisualBrick object.

            </param>
            <param name="cellViewInfo">
		A PivotCellViewInfo object.

            </param>
            <param name="appearance">
		An ExportAppearanceObject object.

            </param>
            <param name="viewInfo">
		A PivotGridViewInfo object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the cell currently being exported.
</para>
            </summary>
            <value>An ExportAppearanceObject object that contains corresponding appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.ColumnField">
            <summary>
                <para>Gets the column field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the column field. <b>null</b> (<b>Nothing</b> in Visual Basic) if a column grand total cell is being processed.
 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.ColumnFieldIndex">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.ColumnIndex">
            <summary>
                <para>Gets the visual index of the column that contains the current cell. 

</para>
            </summary>
            <value>An integer that is the visual index of the column that contains the current cell. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.ColumnValue">
            <summary>
                <para>Gets the value of the column field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object which represents the value of the corresponding column field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the column where the processed cell resides.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the data field which identifies the column where the processed cell resides.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Focused">
            <summary>
                <para>Gets whether the processed cell is focused.
</para>
            </summary>
            <value><b>true</b> if the processed cell is focused; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.FormatType">
            <summary>
                <para>Gets the type of formatting.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.FormatType"/> enumeration value that specifies the formatting type.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.IsTextFit">
            <summary>
                <para>Gets whether the processed cell's text is entirely displayed.
</para>
            </summary>
            <value><b>true</b> if the cell's text is entirely displayed; <b>false</b> if the cell's text is clipped.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.RowField">
            <summary>
                <para>Gets the row field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the row field. <b>null</b> (<b>Nothing</b> in Visual Basic) if a row grand total cell is being processed.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.RowFieldIndex">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.RowIndex">
            <summary>
                <para>Gets the visual index of the row that contains the current cell. 
</para>
            </summary>
            <value>An integer that specifies the visual index of the row that contains the current cell.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.RowValue">
            <summary>
                <para>Gets the value of the row field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object which represents the value of the corresponding row field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Selected">
            <summary>
                <para>Gets whether the processed cell is selected.
</para>
            </summary>
            <value><b>true</b> if the processed cell is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Text">
            <summary>
                <para>Gets the text displayed within the processed cell.
</para>
            </summary>
            <value>A string value that specifies the cell's display text.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomExportCellEventArgs.Value">
            <summary>
                <para>Gets the processed cell's value.
</para>
            </summary>
            <value>An object that represents the cell's value.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotCustomGroupIntervalEventArgs class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the processed field.

            </param>
            <param name="value">
		An object that specifies the currently processed value. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs.Value"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs.GroupValue">
            <summary>
                <para>Gets or sets the group that will own the currently processed value.
</para>
            </summary>
            <value>An object that specifies the group that will own the currently processed value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomGroupIntervalEventArgs.Value">
            <summary>
                <para>Get the currently processed value.
</para>
            </summary>
            <value>An object that specifies the currently processed value.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender. Identifies the XtraPivotGrid control that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomFieldSortEventArgs class.
</para>
            </summary>
            <param name="data">
		A DevExpress.XtraPivotGrid.Data.PivotGridData object. 

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Data">
            <summary>
                <para>Gets the PivotGridData object that provides additional information on the underlying data.
</para>
            </summary>
            <value>A PivotGridData object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Field">
            <summary>
                <para>Gets the field whose values are being compared.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose values are being compared.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.GetListSourceColumnValue(System.Int32,System.String)">
            <summary>
                <para>Gets a value from the specified row and column.
</para>
            </summary>
            <param name="listSourceRowIndex">
		The index of the row in the data source.

            </param>
            <param name="columnName">
		The name of the column.

            </param>
            <returns>A value from the specified row and column.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.GetSortResult">
            <summary>
                <para>Gets the actual sort result.
</para>
            </summary>
            <returns>An integer value that specifies the actual sort result.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Handled">
            <summary>
                <para>Gets or sets whether a comparison operation is being handled and therefore no default processing is required.

</para>
            </summary>
            <value><b>true</b> if a comparison operation is being handled; otherwise <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.ListSourceRowIndex1">
            <summary>
                <para>Gets the index in the data source of the first of the two rows being compared.

</para>
            </summary>
            <value>An integer value representing the index of the first row in the data source.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.ListSourceRowIndex2">
            <summary>
                <para>Gets the index in the data source of the second of the two rows being compared.

</para>
            </summary>
            <value>An integer value representing the index of the second row in the data source.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Result">
            <summary>
                <para>Gets or sets the result of a custom comparison.
</para>
            </summary>
            <value>An integer value specifying the custom comparison's result.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.SetArgs(System.Int32,System.Int32,System.Object,System.Object,DevExpress.XtraPivotGrid.PivotSortOrder)">
            <summary>
                <para>Assigns the specified values to the properties of the current PivotGridCustomFieldSortEventArgs object.
</para>
            </summary>
            <param name="listSourceRow1">
		An integer that specifies the index of the first data row. 

            </param>
            <param name="listSourceRow2">
		An integer that specifies the index of the second data row. 

            </param>
            <param name="value1">
		An object representing the first value being compared.

            </param>
            <param name="value2">
		An object representing the second value being compared.

            </param>
            <param name="sortOrder">
		The sort order.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.SortOrder">
            <summary>
                <para>Gets the sort order applied to the field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotSortOrder"/> value that represents the field's sort order.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Value1">
            <summary>
                <para>Gets the first value being compared.
</para>
            </summary>
            <value>An object that represents the first value being compared. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomFieldSortEventArgs.Value2">
            <summary>
                <para>Gets the second value being compared.
</para>
            </summary>
            <value>An object that represents the second value being compared. 
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.Prefilter">

            <summary>
                <para>Represents a PivotGridControl's <b>Prefilter</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.#ctor(DevExpress.XtraPivotGrid.IPrefilterOwner)">
            <summary>
                <para>Initializes a new instance of the Prefilter class with the specified owner.
</para>
            </summary>
            <param name="owner">
		An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IPrefilterOwner"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.ChangePrefilterVisible">
            <summary>
                <para>Toggles the visibility state of the <b>Prefilter</b> editor.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.Prefilter.Criteria">
            <summary>
                <para>Gets or sets the <b>Prefilter</b>'s expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the <b>Prefilter</b>'s expression.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.Dispose">
            <summary>
                <para>Disposes of the Prefilter object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.Prefilter.IsPrefilterFormShowing">
            <summary>
                <para>Gets a value indicating whether the <b>Prefilter</b> editor is currently displayed.

</para>
            </summary>
            <value><b>true</b> if the <b>Prefilter</b> is displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.Reset">
            <summary>
                <para>Sets settings provided by the Prefilter class to the default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.ShouldSerialize">
            <summary>
                <para>Tests whether the Prefilter object should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.Prefilter.ToString">
            <summary>
                <para>Returns the textual representation of the current object.
</para>
            </summary>
            <returns>A string representation of the pre-filter's <see cref="P:DevExpress.XtraPivotGrid.Prefilter.Criteria"/>.
</returns>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding"/> events.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="item">
		@nbsp;

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueCancelEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the field value can be expanded/collapsed.
</para>
            </summary>
            <value><b>true</b> to cancel the operation; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotChartItemType,DevExpress.XtraPivotGrid.Data.PivotFieldValueItem,DevExpress.XtraPivotGrid.Data.PivotGridCellItem,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotCustomChartDataSourceDataEventArgs class with the specified settings.
</para>
            </summary>
            <param name="itemType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemType"/> enumeration value specifying the type of a PivotGrid control's item to be represented in a ChartControl. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemType"/> property.

            </param>
            <param name="fieldValueItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> object specifying the field value item. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.FieldValueInfo"/> property.

            </param>
            <param name="cellItem">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridCellItem"/> object specifying the grid cell item. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.CellInfo"/> property.

            </param>
            <param name="value">
		A <see cref="T:System.Object"/> specifying the value to be displayed in a chart. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.Value"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.CellInfo">
            <summary>
                <para>Gets an object which contains information about a PivotGrid control's cell, whose value should be displayed in a ChartControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellValueEventArgs"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.FieldValueInfo">
            <summary>
                <para>Gets an object which contains information about a field value to be displayed in a ChartControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.ItemType">
            <summary>
                <para>Gets a value representing the type of a PivotGrid control's item to be represented in a ChartControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotChartItemType"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomChartDataSourceDataEventArgs.Value">
            <summary>
                <para>Gets or sets a value to be displayed in a ChartControl.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> class descendant representing the value to be displayed.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the XtraPivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal">

            <summary>
                <para>Represents a custom total which can be calculated for an outer column field or row field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotal class with the default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.#ctor(DevExpress.Data.PivotGrid.PivotSummaryType)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotal class with the specified summary function type.
</para>
            </summary>
            <param name="summaryType">
		A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> value that specifies the summary function type for the created custom total object. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase.SummaryType"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotal.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the custom total's cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the custom total's cells.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.CloneTo(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase)">
            <summary>
                <para>Copies settings of the current object to the object passed as the parameter.
</para>
            </summary>
            <param name="clone">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> object to which settings are copied from the current object.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotal.IsEqual(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase)">
            <summary>
                <para>Returns whether the settings of the current and specified objects match.
</para>
            </summary>
            <param name="total">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> object to be compared with the current object.

            </param>
            <returns><b>true</b> if the settings of the objects match; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridHitTest">

            <summary>
                <para>Lists values that identify a pivot grid's elements.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.Cell">
            <summary>
                <para>The test point belongs to a cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.HeadersArea">
            <summary>
                <para>The test point belongs to the header area.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.None">
            <summary>
                <para>The test point does not belong to any visual element or is outside the pivot grid.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHitTest.Value">
            <summary>
                <para>The test point belongs to a field value.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomizationFormShowingEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised an event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.#ctor(System.Windows.Forms.Form,System.Windows.Forms.Control)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs"/> class.
</para>
            </summary>
            <param name="customizationForm">
		A <see cref="T:System.Windows.Forms.Form"/> descendant which represents the customization form. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.CustomizationForm"/> property.

            </param>
            <param name="parentControl">
		A <see cref="T:System.Windows.Forms.Control"/> descendant (pivot grid) which owns the customization form. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.ParentControl"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the standard customization form should be canceled.
</para>
            </summary>
            <value><b>true</b> if the standard customization form should be canceled; <b>false</b> if the form should be displayed after the current event handler completes.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.CustomizationForm">
            <summary>
                <para>Provides access to the customization form.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Form"/> descendant which represents the customization form.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.Handled">
            <summary>
                <para>Gets or sets whether the standard customization form should be canceled. 
</para>
            </summary>
            <value><b>true</b> if the standard customization form should be canceled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomizationFormShowingEventArgs.ParentControl">
            <summary>
                <para>Gets the control which the customization form belongs to.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Control"/> descendant (pivot grid) which owns the customization form.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior">

            <summary>
                <para>Provides behavior options for the control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior"/> class with the specified change notifications delegate.

</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will receive change notifications.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.ApplyBestFitOnFieldDragging">
            <summary>
                <para>Gets or sets whether the Best-Fit feature is automatically applied to a field after it has been dragged and dropped at another location.
</para>
            </summary>
            <value><b>true</b> if the Best-Fit feature is applied to a field after it has been dragged and dropped at another location; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.BestFitMode">
            <summary>
                <para>Gets or sets which elements are taken into account when calculating the best-fit size. Allows you to disable the best-fit functionality.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridBestFitMode"/> value 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.EditorShowMode">
            <summary>
                <para>Gets or sets how in-place editors are activated.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.EditorShowMode"/> value that specifies how in-place editors are activated.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.GetEditorShowMode">
            <summary>
                <para>Returns the actual editor show mode based on the <see cref="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.EditorShowMode"/> property.
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.EditorShowMode"/> value that specifies the actual editor show mode.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.HorizontalScrolling">
            <summary>
                <para>Gets or sets a value which specifies the pivot grid's behavior when it is scrolled horizontally.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridScrolling"/> enumeration member which specifies the pivot grid's behavior when it's scrolled horizontally.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior.RepaintGridOnFocusedCellChanged">
            <summary>
                <para>Gets or sets whether the grid control is repainted when focus is moved from one cell to another.
</para>
            </summary>
            <value><b>true</b> if the grid control is repainted when focus is moved from one cell to another; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender. Identifies the PivotGrid control that raised an event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo">

            <summary>
                <para>Contains information about a specific point within a Header Area.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotGridHeaderHitTest)">
            <summary>
                <para>Initializes a new instance of the PivotGridHeadersAreaHitInfo class.
</para>
            </summary>
            <param name="headersViewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfo"/> object which provides information on the field headers located at the test point.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field which the header at the test point corresponds to. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Field"/> property.

            </param>
            <param name="headerHitTest">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitTest"/> enumeration value which identifies the type of the header's visual element which is located under the test point. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.HeaderHitTest"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Area">
            <summary>
                <para>Gets the header area of the XtraPivotGrid which is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the area located under the test point.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Field">
            <summary>
                <para>Gets the field that the header located at the test point corresponds to.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field header that the test point corresponds to. <b>null</b> (<b>Nothing</b> in Visual Basic) if no field header is located at the test point.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.HeaderHitTest">
            <summary>
                <para>Gets a value which identifies the type of the header's visual element that is located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest"/> enumeration value which identifies the type of the header's visual element that is located under the test point.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.Data.PivotGrid.PivotCustomSummaryInfo)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomSummaryEventArgs class with the specified settings.
</para>
            </summary>
            <param name="data">
		A PivotGridData object which contains the information required to initialize the created PivotGridCustomSummaryEventArgs object.


            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents a data field.

            </param>
            <param name="customSummaryInfo">
		A PivotCustomSummaryInfo object which contains the information required to initialize the created PivotGridCustomSummaryEventArgs object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs.ColumnField">
            <summary>
                <para>Gets the column field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the column field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomSummaryEventArgs.RowField">
            <summary>
                <para>Gets the row field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellClick"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
            <summary>
                <para>Initializes a new instance of the PivotCellEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object.

            </param>
            <param name="viewInfo">
		A PivotGridViewInfo object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Bounds">
            <summary>
                <para>Gets the cell's bounds.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> value that specifies the cell's bounds.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.DisplayText">
            <summary>
                <para>Gets the display text of the cell currently being processed.
</para>
            </summary>
            <value>A string representing the cell's display text.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Focused">
            <summary>
                <para>Gets whether the processed cell is the focused cell.
</para>
            </summary>
            <value><b>true</b> if the processed cell is the focused cell; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellEventArgs.Selected">
            <summary>
                <para>Gets whether the processed cell is selected.
</para>
            </summary>
            <value><b>true</b> if the processed cell is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="cellItem">
		@nbsp;

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs.DisplayText">
            <summary>
                <para>Gets or sets the display text for the cell currently being processed.
</para>
            </summary>
            <value>A string that represents the cell's display text. 
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridHitInfo">

            <summary>
                <para>Contains information about a specific point within a pivot grid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> class with the specified test point.
</para>
            </summary>
            <param name="hitPoint">
		A <see cref="T:System.Drawing.Point"/> structure specifying test point coordinates relative to the pivot grid's top-left corner. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitPoint"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotGridHeaderHitTest,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the PivotGridHitInfo class.
</para>
            </summary>
            <param name="headersViewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfo"/> object which provides information on the field headers located at the test point.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field which the header at the test point corresponds to. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.Field"/> property.

            </param>
            <param name="headerHitTest">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest"/> enumeration value which identifies the type of the header's visual element that is located under the test point. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo.HeaderHitTest"/> property.

            </param>
            <param name="hitPoint">
		A <see cref="T:System.Drawing.Point"/> structure specifying test point coordinates relative to the pivot grid's top-left corner. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitPoint"/>property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,System.Drawing.Point,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
            <summary>
                <para>Initializes a new instance of the PivotGridHitInfo class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object.

            </param>
            <param name="hitPoint">
		A Point structure that specifies the test point for which the hit information is to be retrieved.

            </param>
            <param name="viewInfo">
		A PivotGridViewInfo object.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridHitInfo.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo,DevExpress.XtraPivotGrid.PivotGridValueHitTest,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the PivotGridHitInfo class.
</para>
            </summary>
            <param name="fieldCellViewInfo">
		An object that contains the information on the processed cell.

            </param>
            <param name="valueHitTest">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueHitTest"/> enumeration value which identifies the visual element located under the test point.

            </param>
            <param name="hitPoint">
		A Point structure that specifies the test point for which the hit information is to be retrieved.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.CellInfo">
            <summary>
                <para>Gets information on the cell located at the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which provides information on the cell located at the test point.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HeaderField">
            <summary>
                <para>Gets the field which the header located at the test point corresponds to.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field which the header at the test point corresponds to. <b>null</b> (<b>Nothing</b> in Visual Basic) if there isn't any field header located at the test point.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HeadersAreaInfo">
            <summary>
                <para>Gets information on the field header located at the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHeadersAreaHitInfo"/> object which provides information on the field header located at the specified point.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitPoint">
            <summary>
                <para>Gets the test point.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure specifying test point coordinates relative to the pivot grid's top-left corner.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.HitTest">
            <summary>
                <para>Gets a value identifying the type of the visual element located under the test point.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitTest"/> enumeration value which identifies the type of the visual element that contains the test point.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridHitInfo.ValueInfo">
            <summary>
                <para>Gets the information on the field value located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldValueHitInfo"/> enumeration value which contains information on the field value located under the test point.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs"/> class with the specified settings.

</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo"/> object which holds the pivot grid's view information.

            </param>
            <param name="menuType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu's type. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType"/> property.

            </param>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the pivot grid's context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu"/> property.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose header has been right-clicked by an end-user. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.Field"/> property.

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area"/> property.

            </param>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the point which an end-user right-clicked to invoke the menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.Field">
            <summary>
                <para>Gets the field whose header has been right-clicked by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose header has been right-clicked by an end-user.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.HitInfo">
            <summary>
                <para>Gets details on the grid elements located at the point where an end-user has right-clicked to invoke the context menu.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object which contains information about the grid elements located at the point where an end-user has right-clicked.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCellEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCellEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridScrolling">

            <summary>
                <para>Contains values that specify how the XtraPivotGrid control is scrolled horizontally.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridScrolling.CellsArea">
            <summary>
                <para>Specifies that cells within the Data Area are scrolled when the control is scrolled horizontally. The filter fields and column fields are not moved.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridScrolling.Control">
            <summary>
                <para>Specifies that the filter fields and column fields are scrolled when the control is scrolled horizontally.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest">

            <summary>
                <para>Lists values that identify the visual elements of a field header.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest.Filter">
            <summary>
                <para>The test point belongs to the filter button.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridHeaderHitTest.None">
            <summary>
                <para>The test point doesn't belong to the filter button.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="item">
		@nbsp;

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs.ImageIndex">
            <summary>
                <para>Gets or sets the index of the image to display within the currently processed column/row header.
</para>
            </summary>
            <value>An integer value specifying the zero-based index of the image within the source collection.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCells">

            <summary>
                <para>Stores information on the cells displayed within the XtraPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData)">
            <summary>
                <para>Initializes a new instance of the PivotGridCells class with default settings.
</para>
            </summary>
            <param name="data">
		A PivotGridViewInfoData object which contains the information required to initialize the created PivotGridCells object.


            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.ColumnCount">
            <summary>
                <para>Gets the number of columns in the XtraPivotGrid control.

</para>
            </summary>
            <value>An integer which specifies the number of columns displayed within the XtraPivotGrid control.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.CopySelectionToClipboard">
            <summary>
                <para>Copies the selected cells to the clipboard.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.FocusedCell">
            <summary>
                <para>Gets or sets the coordinates of the focused cell.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the coordinates of the focused cell.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.GetCellInfo(System.Int32,System.Int32)">
            <summary>
                <para>Returns an object which contains information on the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which contains information on the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.GetFocusedCellInfo">
            <summary>
                <para>Returns an object which contains information on the currently focused cell.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object which contains information on the currently focused cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.InvalidateCell(DevExpress.XtraPivotGrid.PivotCellEventArgs)">
            <summary>
                <para>Invalidates the specified cell.
</para>
            </summary>
            <param name="cellInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCellEventArgs"/> object that identifies the cell.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCells.InvalidateCell(System.Int32,System.Int32)">
            <summary>
                <para>Invalidates the cell which is located at the specified position.
</para>
            </summary>
            <param name="x">
		A zero-based integer which identifies the visible index of the row that contains the cell.

            </param>
            <param name="y">
		A zero-based integer which identifies the visible index of the column that contains the cell.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.MultiSelection">
            <summary>
                <para>Gets the selected cells.
</para>
            </summary>
            <value>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.Selection.IMultipleSelection"/> interface and represents the pivot grid's selection.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.RowCount">
            <summary>
                <para>Gets the number of rows in the XtraPivotGrid control.

</para>
            </summary>
            <value>An integer which is the number of rows displayed within the XtraPivotGrid control.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCells.Selection">
            <summary>
                <para>Gets or sets the coordinates of the selected cells.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object which contains the coordinates of the selected cells.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotFieldsAreaCellViewInfo,DevExpress.Utils.Drawing.HeaderObjectInfoArgs,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,DevExpress.Utils.Drawing.HeaderObjectPainter)">
            <summary>
                <para>Initializes a new instance of the PivotCustomDrawFieldValueEventArgs class with the specified settings.
</para>
            </summary>
            <param name="fieldCellViewInfo">
		A PivotFieldsAreaCellViewInfo object.

            </param>
            <param name="info">
		A HeaderObjectInfoArgs object.

            </param>
            <param name="paintArgs">
		A ViewInfoPaintArgs object.

            </param>
            <param name="painter">
		A HeaderObjectPainter object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Area">
            <summary>
                <para>Gets the header area where the field is displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value that specifies the header area in which the field is displayed.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.CustomTotal">
            <summary>
                <para>Gets the custom total which the currently processed column/row header corresponds to.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the custom total which the processed header corresponds to. <b>null</b> (<b>Nothing</b> in Visual Basic) if the processed header doesn't correspond to a custom total.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Data">
            <summary>
                <para>Gets an object that provides methods to work with data. This method supports the internal infrastructure and typically, there is no need to use it directly from your code.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.DisplayText">
            <summary>
                <para>Gets the display text of the header currently being painted.
</para>
            </summary>
            <value>A string value representing the header's display text.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Field">
            <summary>
                <para>Gets the field whose value is to be painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose value is to be painted.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.FieldIndex">
            <summary>
                <para>Gets the field's position among the visible fields within the header area.
</para>
            </summary>
            <value>An integer value that specifies the field's position among the visible fields.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a value displayed in the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column or row field's value for the cell addressed by its zero-based index in the Data Area.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field, whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetHigherLevelFields">
            <summary>
                <para>Returns the parent field(s) for the field value being currently processed.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects that represent parent fields for the field value currently being processed.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.GetHigherLevelFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of a specific parent field corresponding to the field value currently being processed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the parent field whose value is returned.

            </param>
            <returns>An object that represents the value of the specified parent (higher level) field.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Info">
            <summary>
                <para>Gets an object which provides the information required to paint a field value.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Utils.Drawing.HeaderObjectInfoArgs"/> object which provides information about the painted field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.IsOthersValue">
            <summary>
                <para>Gets whether the current header corresponds to the "Others" row/column.
</para>
            </summary>
            <value><b>true</b> if the current header corresponds to the "Others" row/column.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Painter">
            <summary>
                <para>Gets the painter object that provides the default element painting mechanism. 
</para>
            </summary>
            <value>A <b>HeaderObjectPainter</b> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.Value">
            <summary>
                <para>Gets the value of the column field or row field which the currently processed column/row header corresponds to.
</para>
            </summary>
            <value>An object which represents the value of the corresponding column field or row field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs.ValueType">
            <summary>
                <para>Gets the type of the currently processed column/row header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection">

            <summary>
                <para>Represents a collection of custom totals for a field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridFieldBase)">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class with the specified owning <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object which will own the collection being created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor(DevExpress.XtraPivotGrid.PivotGridCustomTotalBase[])">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class and adds copies of the specified custom totals to the collection.
</para>
            </summary>
            <param name="totals">
		An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase"/> objects whose copies will be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridCustomTotalCollection class with default settings,
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Add(DevExpress.Data.PivotGrid.PivotSummaryType)">
            <summary>
                <para>Appends a new item to the collection that represents a custom total of the specified summary function type.

</para>
            </summary>
            <param name="summaryType">
		A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> value that determines the type of the summary function used to calculate the current total. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalBase.SummaryType"/> property.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the created custom total.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridCustomTotal[])">
            <summary>
                <para>Appends an array of custom totals to the current collection. 
</para>
            </summary>
            <param name="customSummaries">
		An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> objects which represent the custom totals to add to the collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Field">
            <summary>
                <para>Gets the field which owns the current collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field which owns the current collection.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to the elements in the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer value which represents the desired custom total's position within the collection. If it's negative or exceeds the last available index, an exception is raised. 


            </param>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object located at the specified index.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection">

            <summary>
                <para>Represents a field collection for the XtraPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridFieldCollection class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object that implements data-aware operations on the data source.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Add(System.String,DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Adds a new field with the specified field name and location to the end of the collection.
</para>
            </summary>
            <param name="fieldName">
		A string that identifies the name of the database field that will be assigned to the new PivotGridField object. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.FieldName"/> property.

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area in which the new PivotGridField object will be positioned. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.Area"/> property.

            </param>
            <returns>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that has been added to the collection.

</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Add">
            <summary>
                <para>Appends a new field to the collection.
</para>
            </summary>
            <returns>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object added to the collection.

</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Add(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Appends the specified field to the collection.
</para>
            </summary>
            <param name="field">
		The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that will be appended to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridField[])">
            <summary>
                <para>Adds an array of fields to the end of the collection.
</para>
            </summary>
            <param name="fields">
		An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.FieldByName(System.String)">
            <summary>
                <para>Returns the field whose name matches the specified string.
</para>
            </summary>
            <param name="name">
		A string that identifies the name of the required field.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object whose name matches the specified string.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Item(System.String)">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object specified by the bound field name.
</para>
            </summary>
            <param name="fieldName">
		A string value specifying the bound field name of the required <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object.

            </param>
            <value>The <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object bound to the specified field.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual fields in the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the desired field's position within the collection. If negative or exceeds the last available index, an exception is raised. 

            </param>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field at the specified position.

</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldCollection.Remove(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Removes the specified <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object from the collection.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the field to remove. 

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu">

            <summary>
                <para>Provides the context menu options for the control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableFieldValueMenu">
            <summary>
                <para>Gets or sets whether end-users can invoke the field value context menu.
</para>
            </summary>
            <value><b>true</b> if end-users can right-click the Field Value to invoke its context menu; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableHeaderAreaMenu">
            <summary>
                <para>Gets or sets whether end-users can invoke the header area context menu.
</para>
            </summary>
            <value><b>true</b> if end-users can right-click the header area to invoke its context menu; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsMenu.EnableHeaderMenu">
            <summary>
                <para>Gets or sets whether end-users can invoke the field header context menu.
</para>
            </summary>
            <value><b>true</b> if end-users can right-click the field header to invoke its context menu; otherwise, false.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances">

            <summary>
                <para>Provides the appearance settings used to paint the elements in a field.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Cell">
            <summary>
                <para>Gets the appearance settings used to paint regular cells corresponding to the current data field.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.CellGrandTotal">
            <summary>
                <para>Gets the appearance settings used to paint grand total cells corresponding to the current data field.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.CellTotal">
            <summary>
                <para>Gets the appearance settings used to paint total cells corresponding to the current data field.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides corresponding settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Header">
            <summary>
                <para>Gets the appearance settings used to paint the field header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides appearance settings used to paint the field header.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.Value">
            <summary>
                <para>Gets the appearance settings used to paint the values of fields.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the field's values.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.ValueGrandTotal">
            <summary>
                <para>Gets the appearance settings used to paint the Grand Total headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the Grand Total headers.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFieldAppearances.ValueTotal">
            <summary>
                <para>Gets the appearance settings used to paint the field's totals.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the field's totals.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridField">

            <summary>
                <para>Represents a field within the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor(System.String,DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Initializes a new instance of the PivotGridField class with the specified field name and location.
</para>
            </summary>
            <param name="fieldName">
		A string identifying the name of the database field. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.FieldName"/> property.

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that defines the location for the PivotGridField object within the control. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.Area"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor">
            <summary>
                <para>Initializes a new instance of the PivotGridField class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData)">
            <summary>
                <para>Initializes a new instance of the PivotGridField class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.AllowEdit">
            <summary>
                <para>Gets or sets whether data editing is allowed.
</para>
            </summary>
            <value><b>true</b> to allow editing cell values that correspond to the current field; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.Appearance">
            <summary>
                <para>Provides access to the appearance settings used to paint the field's header, values and value totals.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldAppearances"/> collection that contains the appearance settings used to paint the field's header, values and value totals.

</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.BestFit">
            <summary>
                <para>Resizes the columns that correspond to the current field to the minimum width required to completely display the column's contents.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanEdit">
            <summary>
                <para>Returns whether cells of the current field can be edited.
</para>
            </summary>
            <value><b>true</b> if cells of the current field can be edited; otherwise, <b>false</b>
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CanShowUnboundExpressionMenu">
            <summary>
                <para>Gets whether a menu used to open an Expression Editor for unbound fields is available.
</para>
            </summary>
            <value><b>true</b> if a menu used to open an Expression Editor for unbound fields is available; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.CustomTotals">
            <summary>
                <para>Gets the collection of custom totals for the current field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotalCollection"/> object which represent the collection of custom totals.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.DataType">
            <summary>
                <para>Gets the field's data type.
</para>
            </summary>
            <value>The <see cref="T:System.Type"/>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.DropDownFilterListSize">
            <summary>
                <para>Gets or sets the width and height of the field's filter dropdown.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the width and height (in pixels) of the field's filter dropdown.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.FieldEdit">
            <summary>
                <para>Gets or sets the editor used to edit cells corresponding to the current data field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant that represents the field's repository item.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.FieldEditName">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.HeaderImages">
            <summary>
                <para>Gets the source of the images that can be displayed within field headers.

</para>
            </summary>
            <value>An object which represents the source of the images that can be displayed within field headers.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ImageIndex">
            <summary>
                <para>Gets or sets the index of the image which is displayed within the field's header.
</para>
            </summary>
            <value>A zero-based integer that represents the index of the image which is displayed within the field's header.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.Options">
            <summary>
                <para>Contains the field's options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldOptionsEx"/> object which contains the field's options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.PivotGrid">
            <summary>
                <para>Gets the XtraPivotGrid control that owns the current field.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> control that owns the current field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ReadOnly">
            <summary>
                <para>Gets or sets whether end-users can modify cell values.
</para>
            </summary>
            <value><b>true</b> to prevent a cell value from being changed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.SetDefaultEditParameters(DevExpress.XtraEditors.Repository.RepositoryItem)">
            <summary>
                <para>Sets specific settings of the specified repository item (in-place editor) to default. This member supports the internal infrastructure and is not intended to be used directly from your code..
</para>
            </summary>
            <param name="edit">
		A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> descendant.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ShowButtonMode">
            <summary>
                <para>Gets or sets which cells corresponding to the current field  display editor buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum"/> value that specifies the current display mode for cell buttons.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ShowFilterPopup">
            <summary>
                <para>Displays the filter dropdown for the current field.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridField.SummaryTypeChanged">
            <summary>
                <para>Occurs after the field's summary type has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridField.ToolTips">
            <summary>
                <para>Gets the field's hint settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldToolTips"/> object which provides the hint settings for the field.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.ToString">
            <summary>
                <para>Returns the field's display text.
</para>
            </summary>
            <returns>A string representing the field's display text.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.XtraClearCustomTotals(DevExpress.Utils.Serializing.XtraItemEventArgs)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="e">
		An XtraItemEventArgs object.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridField.XtraCreateCustomTotalsItem(DevExpress.Utils.Serializing.XtraItemEventArgs)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="e">
		An XtraItemEventArgs object.

            </param>
            <returns>An object.
</returns>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo,DevExpress.XtraPivotGrid.PivotGridMenuType,DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Drawing.Point,DevExpress.Utils.Menu.DXMenuItem)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs"/> class with the specified settings.

</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo"/> object which holds the pivot grid's view information.

            </param>
            <param name="menuType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuType"/> enumeration member which specifies the menu's type. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.MenuType"/> property.

            </param>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the pivot grid's context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Menu"/> property.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose header has been right-clicked by an end-user. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs.Field"/> property.

            </param>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value which specifies the area of the field whose header or value has been right-clicked by an end-user to invoke the context menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Area"/> property.

            </param>
            <param name="point">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the point at which an end-user has right-clicked to invoke the menu. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuEventArgsBase.Point"/> property.

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object or its descendant which represents the clicked menu item. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgsBase.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.Field">
            <summary>
                <para>Gets the field whose header has been right-clicked by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field whose header has been right-clicked.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridMenuItemClickEventArgs.HitInfo">
            <summary>
                <para>Gets details on the grid elements located at the point where an end-user has right-clicked to invoke the context menu. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object which contains information about the grid elements located at the point that has been clicked.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridMenuEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotGridMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender. Identifies the PivotGrid control that raised an event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridMenuEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanged"/>, <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanged"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldWidthChanged"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotFieldEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanged"/>, <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanged"/> and <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldWidthChanged"/> events. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised an event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridMenuType">

            <summary>
                <para>Lists values that specify the pivot grid's context menu types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.FieldValue">
            <summary>
                <para>Corresponds to the field value context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.Header">
            <summary>
                <para>Corresponds to the field header context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.HeaderArea">
            <summary>
                <para>Corresponds to the header area context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridMenuType.HeaderSummaries">
            <summary>
                <para>Corresponds to the menu allowing an end-user to select a summary type for a data field (when the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldOptions.AllowRunTimeSummaryChange"/> option is enabled).
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,System.Object)">
            <summary>
                <para>Initializes a new instance of the PivotFieldDisplayTextEventArgs class with the specified settings.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field currently being processed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="value">
		An object which specifies the current field value. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.Value"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem,System.String)">
            <summary>
                <para>Initializes a new instance of the PivotFieldDisplayTextEventArgs class with the specified item and default text.

</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotFieldValueItem"/> specifying the field value item. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.Item"/> property.


            </param>
            <param name="defaultText">
		A <see cref="T:System.String"/> specifying the default display text. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.DisplayText"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.DisplayText">
            <summary>
                <para>Gets or sets the item's display text.

</para>
            </summary>
            <value>A string value that specifies the item's display text. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.IsPopulatingFilterDropdown">
            <summary>
                <para>Gets whether the current event is called to populate the filter dropdown.
</para>
            </summary>
            <value><b>true</b> if the event is called to populate the filter dropdown; <b>false</b> if the event is called to customize the display text of column and row headers.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs.Value">
            <summary>
                <para>Gets the processed item's value.

</para>
            </summary>
            <value>An object which represents the item's value.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCellDisplayTextEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs">

            <summary>
                <para>Provides data for the events which are invoked for particular data cells.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridCellItem)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="cellItem">
		@nbsp;

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.ColumnCustomTotal">
            <summary>
                <para>Gets the column custom total which displays the current cell.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the column custom total that contains the current cell.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.ColumnField">
            <summary>
                <para>Gets the innermost column field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the column field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.ColumnFieldIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>The integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.ColumnIndex">
            <summary>
                <para>Gets the visual index of the column that contains the current cell.
</para>
            </summary>
            <value>An integer that is the visual index of the column that contains the current cell.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.ColumnValueType">
            <summary>
                <para>Gets the type of the column which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the column in which the processed cell resides.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records used to calculate a summary value for the cell currently being processed. 
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records used to calculate a summary value for the current cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.CreateOLAPDrillDownDataSource(System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns to be returned.
</para>
            </summary>
            <param name="customColumns">
		A list of columns to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.
</para>
            </summary>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.CreateSummaryDataSource">
            <summary>
                <para>Returns a <b>summary data source</b>.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.Data">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the column where the processed cell resides.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the data field which identifies the column where the processed cell resides.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the cell value for the specified data field in the current row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the desired cell's field.

            </param>
            <returns>An object which represents the cell's value. <b>null</b> (<b>Nothing</b> in Visual Basic) if no cell is found.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetCellValue(System.Object[],System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Calculates the summary value for the specified data field from the specified values.
</para>
            </summary>
            <param name="columnValues">
		An array of column values.

            </param>
            <param name="rowValues">
		An array of row values.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the data field.

            </param>
            <returns>An object that represents the summary value which is calculated for the specified data field from the specified values.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a value displayed in the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetColumnFields">
            <summary>
                <para>Returns an array of column fields which correspond to the current cell.
</para>
            </summary>
            <returns>An array of column fields.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetColumnGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the <b>Column Grand Total</b> value for the specified field in the current row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the field whose column grand total in the current row is returned.

            </param>
            <returns>An object which represents the <b>Column Grand Total</b> value for the specified field in the current row.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetColumnGrandTotal(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Calculates the Column Grand Total for the specified data field and from the specified row values.
</para>
            </summary>
            <param name="rowValues">
		An array of row values.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the data field.

            </param>
            <returns>An object that represents the column grand total which is calculated for the specified data field and from the specified row values.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the specified column or row field which identifies the column/row in which the processed cell resides.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose value is returned.

            </param>
            <returns>An object which represents the value of the specified column or row field which identifies the column/row in which the processed cell resides. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified field isn't a column or row field.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column or row field's value for the cell, addressed by its zero-based index in the Data Area.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field, whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Gets the Grand Total value for the specified field.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose Grand Total is returned.

            </param>
            <returns>An object which represents the Grand Total value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetNextColumnCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the same row but in the next column.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the sub column in the next column.

            </param>
            <returns>An object that represents the value in the same row but in the next column.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetNextRowCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the next row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the sub column in the next row.

            </param>
            <returns>An object that represents the value of the cell in the next row.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetPrevColumnCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the same row but in the previous column.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the sub column in the previous column.

            </param>
            <returns>An object that represents the value in the same row but in the previous column.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetPrevRowCellValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of the cell in the previous row.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the sub column in the previous row.

            </param>
            <returns>An object that represents the value of the cell in the previous row.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetRowFields">
            <summary>
                <para>Returns an array of the row fields which correspond to the current cell.
</para>
            </summary>
            <returns>An array of row fields.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetRowGrandTotal(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the <b>Row Grand Total</b> value for the specified field in the current column.
</para>
            </summary>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the field whose row grand total in the current column is returned.

            </param>
            <returns>An object which represents the <b>Row Grand Total</b> value for the specified field in the current column.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetRowGrandTotal(System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Calculates the Row Grand Total for the specified data field and from the specified column values.
</para>
            </summary>
            <param name="columnValues">
		An array of column values.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the data field.

            </param>
            <returns>An object that represents the row grand total which is calculated for the specified data field and from the specified column values.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.IsFieldValueExpanded(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the specified field's value that represents the row or column header of the processed cell is expanded.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field in a pivot grid.

            </param>
            <returns><b>true</b> if the field value is expanded; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.IsFieldValueRetrievable(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Gets whether the value of the specified column or row field can be retrieved for the current cell by the <see cref="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetFieldValue"/> method.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the column or row field.

            </param>
            <returns><b>true</b> if the specified field's value can be retrieved via the <see cref="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.GetFieldValue"/> method; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.IsOthersFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Indicates whether the processed data cell resides within the "Others" row/column when the Top X Value feature is enabled.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the field.

            </param>
            <returns><b>true</b> if the data cell resides within the "Others" row/column; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.Item">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.RowCustomTotal">
            <summary>
                <para>Gets the row custom total which contains the current cell.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the row custom total that contains the current cell.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.RowField">
            <summary>
                <para>Gets the innermost row field which corresponds to the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the row field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.RowFieldIndex">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>The integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.RowIndex">
            <summary>
                <para>Gets the visual index of the row that contains the current cell.
</para>
            </summary>
            <value>An integer that specifies the visual index of the row that contains the current cell.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.RowValueType">
            <summary>
                <para>Gets the type of the row which contains the processed cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which specifies the type of the row in which the processed cell resides.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.SummaryType">
            <summary>
                <para>Gets the type of summary calculated in the current cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryType"/> value that specifies the summary type for the current cell.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.SummaryValue">
            <summary>
                <para>Gets an object which contains the values of the predefined summaries that are calculated for the current cell.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.PivotGrid.PivotSummaryValue"/> object which contains the values of the predefined summaries that are calculated for the current cell.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCellBaseEventArgs.Value">
            <summary>
                <para>Gets the processed cell's value.
</para>
            </summary>
            <value>An object which represents the processed cell's value.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomFieldDataEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomFieldDataEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.CustomFieldDataEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotGridData,DevExpress.XtraPivotGrid.PivotGridField,System.Int32,System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs"/> class.
</para>
            </summary>
            <param name="data">
		A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridData"/> object.

            </param>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the unbound field currently being processed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.Field"/> property.

            </param>
            <param name="listSourceRow">
		An integer value which identifies the current row's index in the data source. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.ListSourceRowIndex"/> property.

            </param>
            <param name="_value">
		An object which represents the processed cell's value. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.Value"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.Field">
            <summary>
                <para>Gets the unbound field currently being processed. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing the unbound field currently being processed.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.GetListSourceColumnValue(System.Int32,System.String)">
            <summary>
                <para>Gets the value of the specified field in the specified row in the control's underlying data source.
</para>
            </summary>
            <param name="listSourceRowIndex">
		An integer value which identifies the required row's index in the data source. 

            </param>
            <param name="columnName">
		A string which represents the required field name.

            </param>
            <returns>An object which represents the field's value in the control's data source.

</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.GetListSourceColumnValue(System.String)">
            <summary>
                <para>Gets the value of the specified cell within the current row in the control's underlying data source.

</para>
            </summary>
            <param name="columnName">
		A string which represents the required field name.

            </param>
            <returns>An object which represents a value from the control's data source.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.ListSourceRowIndex">
            <summary>
                <para>Gets the current row's index in the data source.
</para>
            </summary>
            <value>An integer value identifying the current row's index in the data source. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.CustomFieldDataEventArgs.Value">
            <summary>
                <para>Sets the value of the cell currently being processed.
</para>
            </summary>
            <value>An object representing the value of the cell currently being processed. 
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint">

            <summary>
                <para>Provides hint options for a PivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsHint.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowCellHints">
            <summary>
                <para>Gets or sets whether hints are displayed for cells with truncated content.
</para>
            </summary>
            <value><b>true</b> to display hints for cells with truncated content; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowHeaderHints">
            <summary>
                <para>Gets or sets whether hints are displayed for field headers that have truncated captions.
</para>
            </summary>
            <value><b>true</b> to display hints for field headers that have truncated captions; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsHint.ShowValueHints">
            <summary>
                <para>Gets or sets whether hints are displayed for field values with truncated content.
</para>
            </summary>
            <value><b>true</b> to display hints for field values with truncated content; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridCellType">

            <summary>
                <para>Lists the values that specify the pivot grid cell types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridCellType.Cell">
            <summary>
                <para>Corresponds to the data cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridCellType.CustomTotal">
            <summary>
                <para>Corresponds to the custom total cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridCellType.GrandTotal">
            <summary>
                <para>Corresponds to the grand total cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPivotGrid.PivotGridCellType.Total">
            <summary>
                <para>Corresponds to the automatic total cell.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint">

            <summary>
                <para>Provides the appearance settings used to paint the elements in a pivot grid when it's printed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint.GetAppearanceDefaultInfo">
            <summary>
                <para>Gets an array of the default appearances used to paint the elements of a pivot grid when it's printed.
</para>
            </summary>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs">

            <summary>
                <para>Serves as a base for the classes that provide data for the events which are used to customize column and row headers.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the PivotFieldValueEventArgs class with the specified field.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which specifies the field currently being processed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.#ctor(DevExpress.XtraPivotGrid.Data.PivotFieldValueItem)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="item">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.ChangeExpandedState">
            <summary>
                <para>Changes the expanded state of the field value currently being processed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records associated with the field value currently being processed.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains records associated with the processed field value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.CreateOLAPDrillDownDataSource(System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the current cell. Allows you to specify the columns and limit the number of records to be returned.
</para>
            </summary>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.CustomTotal">
            <summary>
                <para>Gets the custom total which the currently processed column/row header corresponds to.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCustomTotal"/> object which represents the custom total which the processed header corresponds to.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.Data">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Data.PivotGridViewInfoData"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.DataField">
            <summary>
                <para>Gets the data field which identifies the processed value.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represent the data field.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.FieldIndex">
            <summary>
                <para>Gets the field's position among the visible fields within the header area.
</para>
            </summary>
            <value>An integer value that specifies the field's position among the visible fields.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a value displayed in the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column or row field's value for the cell addressed by its zero-based index in the Data Area.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field, whose value is to be obtained.

            </param>
            <param name="cellIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.GetHigherLevelFields">
            <summary>
                <para>Returns the parent field(s) for the field value being currently processed.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects that represent parent fields for the field value currently being processed.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.GetHigherLevelFieldValue(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Returns the value of a specific parent field corresponding to the field value currently being processed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the parent field whose value is returned.

            </param>
            <returns>An object that represents the value of the specified parent (higher level) field.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.IsCollapsed">
            <summary>
                <para>Indicates whether the processed field value is collapsed.
</para>
            </summary>
            <value><b>true</b> if the field value is collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.IsColumn">
            <summary>
                <para>Gets whether the field is displayed within the Column Header Area.
</para>
            </summary>
            <value><b>true</b> if the field is displayed within the Column Header Area; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.IsOthersValue">
            <summary>
                <para>Gets whether the current header corresponds to the "Others" row/column.
</para>
            </summary>
            <value><b>true</b> if the current header corresponds to the "Others" row/column.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.Item">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.MaxIndex">
            <summary>
                <para>Gets the maximum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.

</para>
            </summary>
            <value>An integer value that specifies the maximum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.MinIndex">
            <summary>
                <para>Gets the minimum row index (for row fields) or column index (for column fields) that corresponds to the field value currently being processed.
</para>
            </summary>
            <value>An integer value that specifies the minimum row or column index that corresponds to the processed field value.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.Value">
            <summary>
                <para>Gets the column field or row field value which the currently processed column/row header corresponds to.
</para>
            </summary>
            <value>An object which represents the field value currently being processed.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldValueEventArgs.ValueType">
            <summary>
                <para>Gets the type of the currently processed column/row header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> enumeration value which identifies the type of the currently processed column or row header.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the pivot grid that raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldImageIndexEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotAreaChangingEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField,DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs"/> class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose location is being changed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>
            <param name="newArea">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which indicates the current position of the field being dragged. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewArea"/> property.

            </param>
            <param name="newAreaIndex">
		A zero-based integer which specifies the field's index amongst the other fields displayed within the same area. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.Allow">
            <summary>
                <para>Gets or sets whether the dragged field header can be dropped on the area it's currently located over.
</para>
            </summary>
            <value><b>true</b> to allow the operation; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewArea">
            <summary>
                <para>Gets the current position of the field being dragged. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the current position of the field being dragged.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotAreaChangingEventArgs.NewAreaIndex">
            <summary>
                <para>Gets the index of the field which is being dragged for the area it's currently located over among the other fields displayed within the area.

</para>
            </summary>
            <value>A zero-based integer which specifies the index of the dragged field among the other fields displayed within the area over which it's currently located.

</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition">

            <summary>
                <para>Represents a style format condition which can be applied to a PivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToCell">
            <summary>
                <para>Gets or sets whether the appearance settings can be applied to data cells.
</para>
            </summary>
            <value><b>true</b> to apply the appearance settings to data cells; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToCustomTotalCell">
            <summary>
                <para>Gets or sets whether the appearance settings are applied to custom total cells.
</para>
            </summary>
            <value><b>true</b> to apply the appearance settings to custom total cells; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToGrandTotalCell">
            <summary>
                <para>Gets or sets whether the appearance settings are applied to grand total cells.
</para>
            </summary>
            <value><b>true</b> to apply the appearance settings to grand total cells; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.ApplyToTotalCell">
            <summary>
                <para>Gets or sets whether the appearance settings are applied to automatic total cells.
</para>
            </summary>
            <value><b>true</b> to apply the appearance settings to automatic total cells; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.CanApplyToCell(DevExpress.XtraPivotGrid.PivotGridCellType)">
            <summary>
                <para>Indicates whether the appearance settings specified by the current style format condition can be applied to cells of the specified type.
</para>
            </summary>
            <param name="cellType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCellType"/> enumeration value which specifies the pivot grid cell type.

            </param>
            <returns><b>true</b> if the appearance settings can be applied to cells of the specified type; otherwise, <b>false</b>
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.Field">
            <summary>
                <para>Gets or sets the field which identifies the columns whose values are used in conditional formatting.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldBase"/> object representing the field which identifies the columns whose values are used in conditional formatting.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.FieldName">
            <summary>
                <para>Gets or sets the name of the field which identifies the columns whose values are used in conditional formatting.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the field which identifies the columns whose values are used in conditional formatting.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition.PivotGrid">
            <summary>
                <para>Gets the PivotGrid control which owns the current style format condition.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> object which represents the pivot grid that owns the current style format condition.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection">

            <summary>
                <para>Represents a collection of style format conditions applied to a pivot grid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Add(DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition)">
            <summary>
                <para>Appends a style condition object to the collection.
</para>
            </summary>
            <param name="condition">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.AddRange(DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition[])">
            <summary>
                <para>Adds an array of style condition objects to the end of the collection.
</para>
            </summary>
            <param name="conditions">
		An array of <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> objects to be appended to the collection.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.CompareValues(System.Object,System.Object)">
            <summary>
                <para>Performs a comparison of two objects of the same type and returns a value indicating whether one is less than, equal to or greater than the other.
</para>
            </summary>
            <param name="val1">
		The first object to compare.

            </param>
            <param name="val2">
		The second object to compare.

            </param>
            <returns>An integer value indicating whether one is less than, equal to or greater than the other.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.IsLoading">
            <summary>
                <para>Indicates whether the pivot grid that owns the current collection is currently being initialized.
</para>
            </summary>
            <value><b>true</b> if the pivot grid is being initialized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual items within the collection.
</para>
            </summary>
            <param name="index">
		An integer value specifying the item's zero based index within the collection. If its negative or exceeds the last available index, an exception is raised. 

            </param>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object which resides at the specified position within the collection.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection.Item(System.Object)">
            <summary>
                <para>Gets a style format condition with the specified tag.
</para>
            </summary>
            <param name="tag">
		An object which contains information associated with the style format condition. 

            </param>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridStyleFormatCondition"/> object which represents a style format condition within the collection whose <see cref="P:DevExpress.XtraEditors.StyleFormatConditionBase.Tag"/> property's value matches the <i>tag</i> parameter. <b>null</b> (<b>Nothing</b> in Visual Basic) if no style format condition is found.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfoBase,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,System.Drawing.Rectangle)">
            <summary>
                <para>Initializes a new instance of the PivotCustomDrawHeaderAreaEventArgs class.
</para>
            </summary>
            <param name="headersViewInfo">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.PivotHeadersViewInfo"/> object that provides information about the painted field headers.

            </param>
            <param name="paintArgs">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs"/> object.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the boundaries of the header area currently being painted. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Bounds"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawHeaderAreaEventArgs.Area">
            <summary>
                <para>Gets a value which specifies the header area currently being painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> enumeration value which specifies the header area currently being painted.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldValueEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotHeaderViewInfoBase,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,DevExpress.Utils.Drawing.HeaderObjectPainter)">
            <summary>
                <para>Initializes a new instance of the PivotCustomDrawFieldHeaderEventArgs class with the specified settings.
</para>
            </summary>
            <param name="headerViewInfo">
		A PivotHeaderViewInfoBase object.

            </param>
            <param name="paintArgs">
		A ViewInfoPaintArgs object.

            </param>
            <param name="painter">
		A HeaderObjectPainter object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Field">
            <summary>
                <para>Gets the field whose header is to be painted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field whose header is to be painted.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Info">
            <summary>
                <para>Gets an object which provides the information required to paint a field header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.HeaderObjectInfoArgs"/> object which provides information about the painted field header.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawFieldHeaderEventArgs.Painter">
            <summary>
                <para>Gets the painter object that provides the default element painting mechanism. 
</para>
            </summary>
            <value>A <b>HeaderObjectPainter</b> object.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.#ctor(DevExpress.XtraPivotGrid.IPivotCustomDrawAppearanceOwner,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,System.Drawing.Rectangle)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs"/> class.
</para>
            </summary>
            <param name="appearanceOwner">
		An object that implements the IPivotCustomDrawAppearanceOwner interface, it provides the appearance settings used to paint an element.

            </param>
            <param name="paintArgs">
		A <see cref="T:DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs"/> object.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the element's boundaries. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Bounds"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Appearance">
            <summary>
                <para>Gets the painted element's appearance settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the painted element's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Bounds">
            <summary>
                <para>Gets the bounding rectangle of the painted element.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the element's boundaries.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Graphics">
            <summary>
                <para>Gets an object used to paint an element.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Graphics"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.GraphicsCache">
            <summary>
                <para>Gets an object which specifies the storage for the most commonly used pens, fonts and brushes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.
</para>
            </summary>
            <value><b>true</b> if default painting isn't required; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the pivot grid which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.#ctor(DevExpress.XtraPivotGrid.ViewInfo.PivotCellViewInfo,DevExpress.Utils.AppearanceObject,DevExpress.XtraPivotGrid.ViewInfo.ViewInfoPaintArgs,DevExpress.XtraPivotGrid.ViewInfo.PivotGridViewInfo)">
            <summary>
                <para>Initializes a new instance of the PivotCustomDrawCellEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cellViewInfo">
		A PivotCellViewInfo object.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object.

            </param>
            <param name="paintArgs">
		A ViewInfoPaintArgs object.

            </param>
            <param name="viewInfo">
		A PivotGridViewInfo object.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Appearance">
            <summary>
                <para>Gets the painted cell's appearance settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the painted cell's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Graphics">
            <summary>
                <para>Gets the object used to paint a cell.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Graphics"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.GraphicsCache">
            <summary>
                <para>Gets an object which specifies the storage for the most commonly used pens, fonts and brushes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotCustomDrawCellEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.
</para>
            </summary>
            <value><b>true</b> if default painting isn't required; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldEventArgs">

            <summary>
                <para>Provides data for all field handling events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldEventArgs.#ctor(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotFieldEventArgs"/> class.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field currently being processed. This value is assigned to the <see cref="P:DevExpress.XtraPivotGrid.PivotFieldEventArgs.Field"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotFieldEventArgs.Field">
            <summary>
                <para>Gets the field being processed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which represents the field currently being processed.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventHandler.Invoke(System.Object,DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. Identifies the PivotGrid control that raised the event. 

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotFieldDisplayTextEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridOptionsView">

            <summary>
                <para>Provides view options for the PivotGrid controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridOptionsView.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsView"/> class.
</para>
            </summary>
            <param name="optionsChanged">
		A delegate that will receive change notifications.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.FilterSeparatorBarPadding">
            <summary>
                <para>Gets or sets a value which specifies the distance from the separator line to the adjacent areas.
</para>
            </summary>
            <value>An integer value which specifies the distance from the separator line to the adjacent areas.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.GroupFieldsInCustomizationWindow">
            <summary>
                <para>Gets or sets whether hidden fields are grouped within the customization form (OLAP).
</para>
            </summary>
            <value><b>true</b> to group fields; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.HeaderFilterButtonShowMode">
            <summary>
                <para>Gets or sets how and when filter buttons are displayed within field headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.FilterButtonShowMode"/> enumeration value which specifies how and when filter buttons are displayed within field headers.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridOptionsView.ShowButtonMode">
            <summary>
                <para>Gets or sets a value specifying when buttons of in-place editors are shown in cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotShowButtonModeEnum"/> value that specifies the current display mode for cell buttons.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearancesBase">

            <summary>
                <para>Represents the base class for classes that provide appearance settings for a pivot grid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesBase"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.Cell">
            <summary>
                <para>Gets the appearance settings used to paint data cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint data cells.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.CustomTotalCell">
            <summary>
                <para>Gets the appearance settings used to paint custom total cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint custom total cells.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldHeader">
            <summary>
                <para>Gets the appearance settings used to paint field headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint field headers.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValue">
            <summary>
                <para>Gets the appearance settings used to paint the values of fields and the default appearance settings used to paint grand totals and totals.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint field values.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValueGrandTotal">
            <summary>
                <para>Gets the appearance settings used to paint grand total headers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the grand total headers.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FieldValueTotal">
            <summary>
                <para>Gets the appearance settings used to paint the headers of Totals.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the headers of Totals.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.FilterSeparator">
            <summary>
                <para>Gets the appearance settings used to paint the filter header area separator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter header area separator.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.GrandTotalCell">
            <summary>
                <para>Gets the appearance settings used to paint Grand Total cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint Grand Total cells.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.HeaderGroupLine">
            <summary>
                <para>Gets the appearance settings used to paint connector lines between field headers combined in a field group.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides corresponding appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.Lines">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal and vertical lines.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint grid lines.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearancesBase.TotalCell">
            <summary>
                <para>Gets the appearance settings used to paint automatic total cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint automatic total cells.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridAppearances">

            <summary>
                <para>Provides the appearance settings used to paint a pivot grid.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.ColumnHeaderArea">
            <summary>
                <para>Gets the appearance settings used to paint the column header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the column header area.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.DataHeaderArea">
            <summary>
                <para>Gets the appearance settings used to paint the data header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the data header area.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.Empty">
            <summary>
                <para>Gets the appearance settings used to paint the pivot grid's empty area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the pivot grid's empty space.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.ExpandButton">
            <summary>
                <para>Gets the appearance settings used to paint expand buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint expand buttons.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FilterHeaderArea">
            <summary>
                <para>Gets the appearance settings used to paint the filter header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter header area.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.FocusedCell">
            <summary>
                <para>Gets the appearance settings used to paint the currently focused cell.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the currently focused cell.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetAppearanceDefaultInfo(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Gets the array of default appearances used to paint the pivot grid's elements.
</para>
            </summary>
            <param name="lf">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a pivot grid.

            </param>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetEmptyAppearanceDefaultInfo">
            <summary>
                <para>Gets the empty appearance settings used to paint the elements of a pivot grid.
</para>
            </summary>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetFlatAppearanceDefaultInfo">
            <summary>
                <para>Gets the default appearance settings used to paint the elements of a pivot grid when it's painted in the Flat style.
</para>
            </summary>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetOffice2003AppearanceDefaultInfo">
            <summary>
                <para>Gets the default appearance settings used to paint the elements of a pivot grid when it's painted in the Office2003 style.
</para>
            </summary>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetSkinAppearanceDefaultInfo(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Gets the default appearance settings used to paint the elements of a pivot grid when it's painted in the Skin style.
</para>
            </summary>
            <param name="lf">
		A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object which provides the look and feel settings for a pivot grid.

            </param>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridAppearances.GetWindowsXPAppearanceDefaultInfo">
            <summary>
                <para>Gets the default appearance settings used to paint the elements of a pivot grid when it's painted in the Windows XP style.
</para>
            </summary>
            <returns>An array of DevExpress.Utils.AppearanceDefaultInfo objects.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderArea">
            <summary>
                <para>Gets the appearance settings used to paint the header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the header area.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderFilterButton">
            <summary>
                <para>Gets the appearance settings used to paint filter buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint filter buttons.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.HeaderFilterButtonActive">
            <summary>
                <para>Gets the appearance settings used to paint the filter buttons displayed within the field headers that are involved in filtering.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the filter buttons that are displayed within the field headers involved in filtering.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.PrefilterPanel">
            <summary>
                <para>Gets the appearance settings used to paint the panel displaying the Prefilter's criteria (when the <see cref="P:DevExpress.XtraPivotGrid.PivotGridOptionsCustomization.AllowPrefilter"/> option is enabled).
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the appearance settings used to paint the pivot grid's prefilter panel.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.RowHeaderArea">
            <summary>
                <para>Gets the appearance settings used to paint the row header area.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the row header area.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.SelectedCell">
            <summary>
                <para>Gets the appearance settings used to paint the selected cells.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the selected cells.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridAppearances.SortByColumnIndicatorImage">
            <summary>
                <para>Gets or sets a glyph that is used to indicate that values of column/row fields are sorted by a specific row/column.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object that specifies a corresponding glyph.
</value>


        </member>
        <member name="T:DevExpress.XtraPivotGrid.PivotGridControl">

            <summary>
                <para>Represents the XtraPivotGrid control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraPivotGrid.PivotGridControl"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.About">
            <summary>
                <para>Activates the pivot grid's <b>About</b> dialog box.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ActiveEditor">
            <summary>
                <para>Gets the PivotGrid's active editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.BaseEdit"/> descendant that represents the currently active editor. <b>null</b> (<b>Nothing</b> in Visual Basic) if no cell is currently being edited.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the pivot grid's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> object which provides the appearance settings for the pivot grid's elements.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.AppearancePrint">
            <summary>
                <para>Provides access to the properties that specify the appearances of the pivot grid's elements when the pivot grid is printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> object which provides the appearance settings used to paint the pivot grid's elements when it's printed.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BackgroundImage">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BackgroundImageLayout">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.BeforeLoadLayout">
            <summary>
                <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.BeginRefresh">
            <summary>
                <para>Occurs before the control's data recalculation starts.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BeginUpdate">
            <summary>
                <para>Prevents the pivot grid from being updated until the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate"/> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFit(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Resizes the columns which correspond to the specified field to the minimum width required to completely display the column's contents.

</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object which identifies the columns to which the best width feature should be applied.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.BestFit">
            <summary>
                <para>Resizes the columns which correspond to the data fields and row fields to the minimum width required to completely display the column's contents.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.BorderStyle">
            <summary>
                <para>Gets or sets the border style for the pivot grid.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style of a pivot grid.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CalcHitInfo(System.Drawing.Point)">
            <summary>
                <para>Returns information on the grid elements located at the specified point.

</para>
            </summary>
            <param name="hitPoint">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the pivot grid's top-left corner.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridHitInfo"/> object which contains information about the grid elements located at the test point.

</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CanResizeField(System.Drawing.Point)">
            <summary>
                <para>Indicates whether the field can be resized.
</para>
            </summary>
            <param name="pt">
		A <see cref="T:System.Drawing.Point"/> structure which represents the test point.

            </param>
            <returns><b>true</b> if the field can be resized; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellClick">
            <summary>
                <para>Occurs when a cell is clicked.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellDoubleClick">
            <summary>
                <para>Occurs when a cell is double-clicked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Cells">
            <summary>
                <para>Gets the object which contains information on the cells that are displayed by the control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridCells"/> object which contains information on the data cells.

</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CellSelectionChanged">
            <summary>
                <para>Occurs when the cell selection is modified.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ChartDataVertical">
            <summary>
                <para>Gets or sets whether PivotGrid columns or rows are represented by series in a chart control.
</para>
            </summary>
            <value><b>true</b> if the PivotGrid control's columns are considered as individual series, and column values provide series' values; <b>false</b> if rows are considered as individual series, and row values provide series' values;

</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CloseEditor">
            <summary>
                <para>Hides the active editor, saving changes made.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAll">
            <summary>
                <para>Collapses all the columns and rows in the pivot grid.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllColumns">
            <summary>
                <para>Collapses all columns.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseAllRows">
            <summary>
                <para>Collapses all rows.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CollapseValue(System.Boolean,System.Object[])">
            <summary>
                <para>Collapses a specific column or row that is identified by the specified values.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to collapse a column; <b>false</b> to collapse a row.

            </param>
            <param name="values">
		An array of values that identify the column/row to be collapsed.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32,System.Int32)">
            <summary>
                <para>Returns a list of records used to calculate a summary value for the specified cell. Allows you to limit the number of records to be returned.

</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource(System.Int32,System.Int32)">
            <summary>
                <para>Returns a list of records used to calculate a summary value for the specified cell. 
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateDrillDownDataSource">
            <summary>
                <para>Returns a list of records used to calculate summary values for all cells. 
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns and limit the number of records to be returned.

</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="maxRowCount">
		An integer value that specifies the maximum number of data rows to be returned. <b>-1</b> to retrieve all rows.

            </param>
            <param name="customColumns">
		A list of columns to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateOLAPDrillDownDataSource(System.Int32,System.Int32,System.Collections.Generic.List`1)">
            <summary>
                <para>In OLAP mode, returns a list of records used to calculate a summary value for the specified cell. Allows you to specify the columns to be returned.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <param name="customColumns">
		A list of columns to be returned.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotDrillDownDataSource"/> object that contains the underlying data.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.CreateSummaryDataSource">
            <summary>
                <para>Returns a <b>summary data source</b>.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotSummaryDataSource"/> object that represents the summary data source.
</returns>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomAppearance">
            <summary>
                <para>Allows the appearances of cells to be dynamically customized when the control is displayed onscreen and in the print output.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellDisplayText">
            <summary>
                <para>Enables custom display text to be provided for the cells displayed within the Data Area.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEdit">
            <summary>
                <para>Allows you to assign editors for particular cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellEditForEditing">
            <summary>
                <para>Allows you to assign a custom editor to a cell for in-place editing, and override the default cell editor, which is used in both display and edit modes, by default.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomCellValue">
            <summary>
                <para>Allows you to replace cell values with custom ones.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomChartDataSourceData">
            <summary>
                <para>Occurs when a PivotGridControl prepares data to be displayed in a <see cref="T:DevExpress.XtraCharts.ChartControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomColumnWidth">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawCell">
            <summary>
                <para>Enables data cells to be painted manually.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawEmptyArea">
            <summary>
                <para>Enables a pivot grid's background to be custom painted.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeader">
            <summary>
                <para>Enables field headers to be painted manually.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldHeaderArea">
            <summary>
                <para>Enables header areas to be painted manually.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomDrawFieldValue">
            <summary>
                <para>Enables column and row headers to be painted manually.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomEditValue">
            <summary>
                <para>Enables you to change cell values.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportCell">
            <summary>
                <para>Enables you to render a different content for individual cells.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportFieldValue">
            <summary>
                <para>Enables you to render a different content for individual field values.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomExportHeader">
            <summary>
                <para>Enables you to render a different content for individual field headers.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomFieldSort">
            <summary>
                <para>Provides the ability to sort data using custom rules.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomGroupInterval">
            <summary>
                <para>Allows you to custom group values of column and row fields.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationForm">
            <summary>
                <para>Provides access to the customization form.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Form"/> descendant that represents the customization form.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationFormBounds">
            <summary>
                <para>Gets or sets the boundaries of the customization form.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the customization form's boundaries.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.CustomizationTreeNodeImages">
            <summary>
                <para>Specifies the collection of images identifying nodes in the customization form used when the control is in OLAP mode.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.ImageCollection"/> object that contains the corresponding images.

</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomRowHeight">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomSummary">
            <summary>
                <para>Enables summary values to be calculated manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.CustomUnboundFieldData">
            <summary>
                <para>Enables data to be provided for unbound fields. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DataMember">
            <summary>
                <para>Gets or sets the data source member which supplies data to the control.

</para>
            </summary>
            <value>A string value representing the data source member.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.DataSource">
            <summary>
                <para>Gets or sets the object used as the data source for the current control. 
</para>
            </summary>
            <value>The object used as the data source.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.DataSourceChanged">
            <summary>
                <para>Fires when the pivot grid's data source changes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.DestroyCustomization">
            <summary>
                <para>Closes the customization form. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.EditValue">
            <summary>
                <para>Gets or sets the active editor's value.
</para>
            </summary>
            <value>An object that represents the active editor's value.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.EditValueChanged">
            <summary>
                <para>Fires immediately after a cell's value has been changed and allows you to save the changes.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.EndRefresh">
            <summary>
                <para>Occurs after the control's data recalculation has been completed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.EndUpdate">
            <summary>
                <para><para>Unlocks the pivot grid control after a call to the <see cref="M:DevExpress.XtraPivotGrid.PivotGridControl.BeginUpdate"/> method and causes an immediate update.</para>
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.EnsureViewInfoIsCalculated">
            <summary>
                <para>Ensures that the pivot grid's view information has been calculated. If not, forces the pivot grid to calculate the view information.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAll">
            <summary>
                <para>Expands all the columns and rows in the pivot grid. 

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllColumns">
            <summary>
                <para>Expands all columns.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandAllRows">
            <summary>
                <para>Expands all rows.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExpandValue(System.Boolean,System.Object[])">
            <summary>
                <para>Expands a specific column or row that is identified by the specified values.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to expand a column; <b>false</b> to expand a row.

            </param>
            <param name="values">
		An array of values that identify the column/row to be expanded.

            </param>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ExportFinished">
            <summary>
                <para>Occurs after the PivotGrid's export has been completed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ExportStarted">
            <summary>
                <para>Occurs after the PivotGrid's export has been started.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.String)">
            <summary>
                <para>Exports the control's data to the specified file in CSV format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> containing the full path to the file to which the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.String,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in CSV format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> containing the full path to the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.IO.Stream)">
            <summary>
                <para>Exports the control's data to the specified stream in CSV.
</para>
            </summary>
            <param name="stream">
		A stream to which the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToCsv(System.IO.Stream,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in CSV format using the specified options.
</para>
            </summary>
            <param name="stream">
		A stream to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,System.String)">
            <summary>
                <para>Exports the pivot grid's data to a HTML file with the specified encoding.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> containing the full path (including the file name and extension) specifying where the HTML file will be created.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> representing the encoding name set in the HTML file (e.g. "UTF-8").

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String)">
            <summary>
                <para>Exports the pivot grid's data to the specified file as HTML.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> containing the full path (including the file name and extension) specifying where the HTML file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to an HTML file with the specified encoding and title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.

</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which contains the full path (including the file name and extension) specifying where the HTML file will be created.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> representing the encoding name set in the HTML file (e.g. "UTF-8").

            </param>
            <param name="title">
		A <see cref="T:System.String"/> specifying the name shown as the title of the created HTML document.

            </param>
            <param name="compressed">
		<b>true</b> to compress the HTML code (secondary characters e.g. spaces are removed); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to an HTML document with the specified encoding and title and sends it to the specified stream. The output file can be compressed (secondary characters e.g. spaces are removed) if required.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> which specifies the encoding name set in the HTML document (e.g. "UTF-8").

            </param>
            <param name="title">
		A <see cref="T:System.String"/> which specifies the name shown as the title of the created HTML document.

            </param>
            <param name="compressed">
		<b>true</b> to compress the HTML code (secondary characters e.g. spaces are removed); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream)">
            <summary>
                <para>Exports the pivot grid's data as HTML and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in HTML format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in HTML format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.IO.Stream)">
            <summary>
                <para>Exports the control's data to the specified stream in BMP format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.IO.Stream,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in Image format using the specified Image-specific options. 
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object that contains the export options.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.String)">
            <summary>
                <para>Exports the control's data to the specified file in BMP format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToImage(System.String,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in Image format using the specified Image-specific options. 
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object that contains the export options.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to an MHT file (Web archive, single file) with the specified path and title. The output file can be compressed (secondary characters e.g. spaces are removed) if required.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> which specifies the encoding name set in the MHT file (e.g. "UTF-8").

            </param>
            <param name="title">
		A <see cref="T:System.String"/> which specifies the name shown as the title of the created MHT document.

            </param>
            <param name="compressed">
		<b>true</b> to compress the MHT code (secondary characters e.g. spaces are removed); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.IO.Stream,System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to an MHT document (Web archive, single file) at the specified path and title and sends it to the specified stream. The output file can be compressed (secondary characters e.g. spaces are removed) if required.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created MHT document is exported to.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> which specifies the encoding name set in the MHT document (e.g. "UTF-8").

            </param>
            <param name="title">
		A <see cref="T:System.String"/> which specifies the name shown as the title of the created MHT document.

            </param>
            <param name="compressed">
		<b>true</b> to compress the MHT code (secondary characters e.g. spaces are removed); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String)">
            <summary>
                <para>Exports the pivot grid's data to an MHT file (Web archive, single file) at the specified path. 
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,System.String)">
            <summary>
                <para>Exports the pivot grid's data to an MHT file (Web archive, single file) at the specified path and with the specified encoding.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the MHT file will be created.

            </param>
            <param name="htmlCharSet">
		A <see cref="T:System.String"/> which specifies the encoding name set in the MHT document (e.g. "UTF-8").

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in MHT format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in MHT format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.String)">
            <summary>
                <para>Exports the pivot grid's data to the specified PDF file.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the PDF file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.IO.Stream)">
            <summary>
                <para>Exports the pivot grid's data to a PDF document and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in PDF format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in PDF format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToRtf(System.String)">
            <summary>
                <para>Exports the pivot grid's data to the specified RTF file.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the RTF file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToRtf(System.IO.Stream)">
            <summary>
                <para>Exports the pivot grid's data as Rich Text and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream)">
            <summary>
                <para>Exports the pivot grid's data to a text document and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String,System.Boolean,System.Text.Encoding)">
            <summary>
                <para>Exports the pivot grid's data to a text file with the specified path, separator string, quotation and text encoding settings.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is <b>"\t"</b>.

            </param>
            <param name="quoteStringsWithSeparators">
		<b>true</b> to quote (place quotation marks around) text elements containing symbols which are the same as the specified separator string; otherwise, <b>false</b>. The default value is <b>false</b>.

            </param>
            <param name="encoding">
		A <see cref="T:System.Text.Encoding"/> descendant to encode the created text document with. The default value is <b>System.Text.Encoding.Default</b>.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String)">
            <summary>
                <para>Exports the pivot grid's data to a text document with the specified separator string and sends it to the specified stream.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements. The default value is <b>"\t"</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to a text document with the specified path, separator string, quotation and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements. The default value is <b>"\t"</b>.

            </param>
            <param name="quoteStringsWithSeparators">
		<b>true</b> to quote (place quotation marks around) text elements containing symbols which are the same as the specified separator string; otherwise, <b>false</b>. The default value is <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String,System.Boolean,System.Text.Encoding)">
            <summary>
                <para>Exports the pivot grid's data to a text document with the specified path, separator string, quotation and text encoding settings, and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements. The default value is <b>"\t"</b>.

            </param>
            <param name="quoteStringsWithSeparators">
		<b>true</b> to quote (place quotation marks around) text elements containing symbols which are the same as the specified separator string; otherwise, <b>false</b>. The default value is <b>false</b>.

            </param>
            <param name="encoding">
		A <see cref="T:System.Text.Encoding"/> descendant which encodes the created text document. The default value is <b>System.Text.Encoding.Default</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String)">
            <summary>
                <para>Exports the pivot grid's data to a text file at the specified path.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String)">
            <summary>
                <para>Exports the pivot grid's data to a text file at the specified path and with the specified separator string.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is <b>"\t"</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to a text file at the specified path with the specified separator string and quotation.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the text file will be created.

            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is <b>"\t"</b>.

            </param>
            <param name="quoteStringsWithSeparators">
		<b>true</b> to quote (place quotation marks around) text elements containing symbols which are the same as the specified separator string; otherwise, <b>false</b>. The default value is <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,System.String,System.Text.Encoding)">
            <summary>
                <para>Exports the pivot grid's data to a text document with the specified separator string, text encoding settings and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created document is exported.


            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is <b>"\t"</b>.

            </param>
            <param name="encoding">
		A <see cref="T:System.Text.Encoding"/> descendant that specifies the encoding of the created text document.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,System.String,System.Text.Encoding)">
            <summary>
                <para>Exports the pivot grid's data to a text document with the specified separator string and text encoding settings, and sends it to the specified file.

</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) where the text file will be created.


            </param>
            <param name="separator">
		A <see cref="T:System.String"/> which contains the symbols that will be used to separate the text elements of the document in the created text file. The default value is <b>"\t"</b>.

            </param>
            <param name="encoding">
		A <see cref="T:System.Text.Encoding"/> descendant that specifies the encoding of the created text document.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in Text format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in Text format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String)">
            <summary>
                <para>Exports the pivot grid's data to the specified file as XLS.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the XLS file will be created.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data to the specified file as XLS.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the full path (including the file name and extension) to where the XLS file will be created.

            </param>
            <param name="useNativeFormat">
		<b>true</b> to export numeric field values as numbers; <b>false</b> to export numeric field values as text. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream)">
            <summary>
                <para>Exports the pivot grid's data as XLS and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created document is exported.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream,System.Boolean)">
            <summary>
                <para>Exports the pivot grid's data as XLS and sends it to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object which the created document is exported to.

            </param>
            <param name="useNativeFormat">
		<b>true</b> to use the data format of the exported data fields for the cells in the XLS document; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in XLS format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports the control's data to the specified stream in XLS format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object, to which the created document is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when the control is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.TextExportMode)">
            <summary>
                <para>Exports the control's data to the specified stream in XLS format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which data is exported.

            </param>
            <param name="textExportMode">
		A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXls(System.String,DevExpress.XtraPrinting.TextExportMode)">
            <summary>
                <para>Exports the control's data to the specified file in XLS format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.


            </param>
            <param name="textExportMode">
		A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String)">
            <summary>
                <para>Exports the control's data to the specified file in XLSX (MS Excel 2007) format.
</para>
            </summary>
            <param name="filePath">
		A string that specifies the full path to the file to which the data is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream)">
            <summary>
                <para>Exports the control's data to the specified stream in XLSX (MS Excel 2007) format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which data is exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A string that specifies the full path to the file to which the data is exported.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object that contains export options.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports the control's data to the specified file in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which data is exported.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when the control is exported.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.String,DevExpress.XtraPrinting.TextExportMode)">
            <summary>
                <para>Exports the control's data to the specified file in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the name (including the full path) of the file to which the control is exported.

            </param>
            <param name="textExportMode">
		A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.TextExportMode)">
            <summary>
                <para>Exports the control's data to the specified file in XLSX (MS Excel 2007) format using the specified options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which data is exported.

            </param>
            <param name="textExportMode">
		A <see cref="T:DevExpress.XtraPrinting.TextExportMode"/> value that specifies how values are exported.

            </param>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanged">
            <summary>
                <para>Occurs after the field's location or visibility has been changed. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldAreaChanging">
            <summary>
                <para>Enables you to control whether the dragged field header can be dropped at the area it's currently located over. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldExpandedInFieldGroupChanged">
            <summary>
                <para>Fires when the expansion status of fields combined into a field group is changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldFilterChanged">
            <summary>
                <para>Occurs after the filter criteria have been changed for a specific field.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Fields">
            <summary>
                <para>Provides access to a pivot grid's field collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFieldCollection"/> object which represents a collection of all the fields within a pivot grid.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.FieldsCustomization(System.Drawing.Point)">
            <summary>
                <para>Invokes the customization form at the specified point.

</para>
            </summary>
            <param name="showPoint">
		A structure specifying the location of the customization form's top-left corner. The point is set in screen coordinates.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.FieldsCustomization">
            <summary>
                <para>Invokes the customization form.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.FieldsCustomization(System.Windows.Forms.Control)">
            <summary>
                <para>Invokes the customization form.
</para>
            </summary>
            <param name="parentControl">
		A <see cref="T:System.Windows.Forms.Control"/> descendant (pivot grid) which owns the customization form.

            </param>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldUnboundExpressionChanged">
            <summary>
                <para>Fires after the <see cref="P:DevExpress.XtraPivotGrid.PivotGridFieldBase.UnboundExpression"/> property's value has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsed">
            <summary>
                <para>Fires immediately after a field value has been collapsed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueCollapsing">
            <summary>
                <para>Enables you to control whether field values can be collapsed. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueDisplayText">
            <summary>
                <para>Enables the display text of individual column and row headers and filter dropdown items to be customized.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanded">
            <summary>
                <para>Fires immediately after a field value has been expanded.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueExpanding">
            <summary>
                <para>Enables you to control whether field values can be expanded. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueImageIndex">
            <summary>
                <para>Enables images to be shown within column and row headers.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldValueNotExpanded">
            <summary>
                <para>Occurs when a user clicks the expand button, and the field value cannot be expanded.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FieldWidthChanged">
            <summary>
                <para>Provides a notification that the field's width has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.FocusedCellChanged">
            <summary>
                <para>Fires in response to cell focus changing. 

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Font">
            <summary>
                <para>Overrides the base class <b>Font</b> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object to apply to the text displayed by the control.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ForceInitialize">
            <summary>
                <para>Forces the pivot grid control to finish its initialization.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.FormatConditions">
            <summary>
                <para>Gets the collection of <b>style format conditions</b> for a pivot grid.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridFormatConditionCollection"/> object which represents the collection of style format conditions.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Int32,System.Int32)">
            <summary>
                <para>Returns a value displayed in the specified cell.
</para>
            </summary>
            <param name="columnIndex">
		A zero-based integer which identifies the visible index of the column.

            </param>
            <param name="rowIndex">
		A zero-based integer which identifies the visible index of the row.

            </param>
            <returns>A value displayed in the specified cell.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Object[],System.Object[],DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Retrieves a cell value, identified by the specified data field, column and row field values.
</para>
            </summary>
            <param name="columnValues">
		An array of column field values that identify the column where the cell resides.

            </param>
            <param name="rowValues">
		An array of row field values that identify the column where the cell resides.

            </param>
            <param name="dataField">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> that represents the data field, whose value is retrieved.

            </param>
            <returns>A cell value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetCellValue(System.Object[],System.Object[])">
            <summary>
                <para>Retrieves a cell value, identified by the specified column and row field values.
</para>
            </summary>
            <param name="columnValues">
		An array of column field values that identify the column where the cell resides.

            </param>
            <param name="rowValues">
		An array of row field values that identify the column where the cell resides.

            </param>
            <returns>A cell value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetColumnIndex(System.Object[])">
            <summary>
                <para>Gets the specified column's index.
</para>
            </summary>
            <param name="values">
		An array of column field values that identify the column.

            </param>
            <returns>An integer value that specifies the column's index. <b>-1</b> if the specified column was not found.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldAt(System.Drawing.Point)">
            <summary>
                <para>Returns the field whose header is displayed at the specified point.
</para>
            </summary>
            <param name="pt">
		A <see cref="T:System.Drawing.Point"/> structure which represents the test point. The point is relative to the top left corner of the control.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> field whose header is displayed at the specified point; <b>null</b> if there is no field header located at the specified point.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldByArea(DevExpress.XtraPivotGrid.PivotArea,System.Int32)">
            <summary>
                <para>Returns a field at the specified visual position in the specified area.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the area containing the required field.

            </param>
            <param name="areaIndex">
		An integer value that specifies the visible index of the field within the specified area.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object at the specified visual position within the specified area.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldList">
            <summary>
                <para>Returns the list of fields that are available in the bound data source.
</para>
            </summary>
            <returns>A list of fields in the bound data source.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldsByArea(DevExpress.XtraPivotGrid.PivotArea)">
            <summary>
                <para>Returns a list of fields displayed in the specified area.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that identifies the required area.

            </param>
            <returns>A list of visible fields displayed in the specified area.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValue(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns the specified column or row field's value for the cell addressed by its zero-based index in the Data Area.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object representing a column or row field whose value is to be obtained.

            </param>
            <param name="lastLevelIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns>An object representing the field's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueOLAPMember(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns an OLAP member for the specified field value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that represents the pivot grid field. 

            </param>
            <param name="lastLevelIndex">
		An integer value that specifies the cell's index.

            </param>
            <returns>An object that implements the <see cref="T:DevExpress.XtraPivotGrid.IOLAPMember"/> interface. <b>null</b> (<b>Nothing</b>) if the specified summary cell doesn't correspond to the specified field's value. 
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueType(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Gets the type of a specific column/row field's value.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that refers to the required field.

            </param>
            <param name="lastLevelIndex">
		A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.


            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that idenfifies the type of the required column/row field value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetFieldValueType(System.Boolean,System.Int32)">
            <summary>
                <para>Gets the type of a field value in the column or row area.
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> to retrieve information on a field value in the column area; <b>false</b> to retrieve information on a field value in the row area.

            </param>
            <param name="lastLevelIndex">
		A zero-based index of a column/row that identifies the required field value. Indexes are numbered starting from the left, for column fields, and from the top, for row fields.


            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridValueType"/> value that idenfifies the type of the required column/row field value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetKPIBitmap(DevExpress.XtraPivotGrid.PivotKPIGraphic,System.Int32)">
            <summary>
                <para>Returns an image that corresponds to a specific KPI value.
</para>
            </summary>
            <param name="graphic">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set.

            </param>
            <param name="state">
		An integer value that specifies the KPI value (-1, 0 or 1). 

            </param>
            <returns>A Bitmap object that corresponds to the specified KPI value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIList">
            <summary>
                <para>Gets the list of key performance indicators (KPI) in a cube.
</para>
            </summary>
            <returns>The list of KPI names.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIMeasures(System.String)">
            <summary>
                <para>Returns the Measures dimension's members used to calculate the value, goal, status and weight of the specified Key Performance Indicator (KPI). 
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name. 

            </param>
            <returns>A PivotOLAPKPIMeasures object that contains measures used to determine the KPI's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIServerGraphic(System.String,DevExpress.XtraPivotGrid.PivotKPIType)">
            <summary>
                <para>Returns a graphic set defined on the server for the specified KPI's trend and status.
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name.

            </param>
            <param name="kpiType">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIType"/> enumeration value that identifies the image set. Images can be obtained only for trend and status KPI types.

            </param>
            <returns>A <see cref="T:DevExpress.XtraPivotGrid.PivotKPIGraphic"/> enumeration value that specifies the KPI graphic set.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetOLAPKPIValue(System.String)">
            <summary>
                <para>Returns the specified KPI's value.
</para>
            </summary>
            <param name="kpiName">
		A string value that specifies the KPI's name.


            </param>
            <returns>A PivotOLAPKPIValue object that represents the specified KPI's value.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.GetRowIndex(System.Object[])">
            <summary>
                <para>Gets the specified row's index.
</para>
            </summary>
            <param name="values">
		An array of row field values that identify the row.

            </param>
            <returns>An integer value that specifies the row's index. <b>-1</b> if the specified row was not found.
</returns>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.GridLayout">
            <summary>
                <para>Fires after the control's layout has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Groups">
            <summary>
                <para>Gets the collection of field groups.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridGroupCollection"/> object which represents a collection of field groups.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.HeaderImages">
            <summary>
                <para>Gets or sets the source of the images available for display within field headers.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.ImageList"/> object which represents the source of images.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.HiddenEditor">
            <summary>
                <para>Fires immediately after an inplace editor has been closed. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.HideCustomizationForm">
            <summary>
                <para>Fires before the customization form is closed. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.HideEditor">
            <summary>
                <para>Hides the active editor discarding any changes made.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.InvalidValueException">
            <summary>
                <para>Enables you to provide a proper response to entering an invalid cell value.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsDragging">
            <summary>
                <para>Indicates whether a field header is being dragged by a user.
</para>
            </summary>
            <value><b>true</b> if a user drags a field header; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.IsObjectCollapsed(DevExpress.XtraPivotGrid.PivotGridField,System.Int32)">
            <summary>
                <para>Returns whether the specified column field value or row field value is collapsed.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> object that identifies a column or row field.

            </param>
            <param name="lastLevelIndex">
		A zero-based index of a cell in the Data Area that identifies the required field value. Indexes are numbered starting from the left edge for column fields, and from the top edge for row fields.

            </param>
            <returns><b>true</b> if the specified column field value or row field value is collapsed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.IsObjectCollapsed(System.Boolean,System.Int32,System.Int32)">
            <summary>
                <para>Returns whether the specified column field value or row field value is collapsed. 
</para>
            </summary>
            <param name="isColumn">
		<b>true</b> if the field value corresponds to a column field; <b>false</b> if the field value corresponds to a row field.

            </param>
            <param name="lastLevelIndex">
		A zero-based index of a cell that identifies the required field value. Indices are numbered starting from the left for column fields, and from the top for row fields.

            </param>
            <param name="level">
		An integer value that specifies the nesting level of the field value.

            </param>
            <returns><b>true</b> if the specified column field value or row field value is collapsed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsOLAPDataSource">
            <summary>
                <para>Returns whether a connection string is specified via the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString"/> property, and so the control is bound to a cube in an MS Analysis Services database.
</para>
            </summary>
            <value><b>true</b> if a connection string is specified via  the <see cref="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString"/> property; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.IsPrintingAvailable">
            <summary>
                <para>Indicates whether the pivot grid can be printed.
</para>
            </summary>
            <value><b>true</b> if the pivot grid can be printed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LayoutChanged">
            <summary>
                <para>Updates a pivot grid.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.LayoutUpgrade">
            <summary>
                <para>Occurs when a layout is restored from a data store (a stream, xml file or the system registry) and its version is different than that of the control's current layout version.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.LeftTopCoord">
            <summary>
                <para>Gets or sets the column and row indexes of the data cell displayed in the top left corner.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that identifies the column and row indexes of the data cell displayed in the top left corner.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ListSource">
            <summary>
                <para>Gets the actual data source whose data is displayed within the control.
</para>
            </summary>
            <value>An <see cref="T:System.Collections.IList"/> object that represents the actual data source that provides data for the control.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LoadCollapsedStateFromFile(System.String)">
            <summary>
                <para>Restores the collapsed state of field values from the specified file.
</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/> value which specifies the path to the file from which the collapsed state of field values is read. If the specified file doesn't exist, an exception is raised.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.LoadCollapsedStateFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the collapsed state of field values from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which the collapsed state of field values is read. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings that control the pivot grid's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the pivot grid's look and feel.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.MenuItemClick">
            <summary>
                <para>Allows custom responses to be provided for clicks on context menu items.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.MenuManager">
            <summary>
                <para>Gets or sets the menu manager which controls the look and feel of the context menus.
</para>
            </summary>
            <value>An object which implements the <see cref="T:DevExpress.Utils.Menu.IDXMenuManager"/> interface.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OLAPConnectionString">
            <summary>
                <para>Specifies a connection string to a cube in an MS Analysis Services database.
</para>
            </summary>
            <value>A connection string.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.OLAPQueryTimeout">
            <summary>
                <para>Fires after a specific interval of time has elapsed when sending a query to an OLAP server.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsBehavior">
            <summary>
                <para>Provides access to the control's behavior options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsBehavior"/> object containing the control's behavior options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsChartDataSource">
            <summary>
                <para>Provides access to the options controlling the display of the PivotGrid control's data in a chart control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsChartDataSource"/> object that contains corresponding options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsCustomization">
            <summary>
                <para>Provides access to the pivot grid's customization options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsCustomizationEx"/> object which contains the pivot grid's customization options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsData">
            <summary>
                <para>Provides access to the pivot grid's data specific options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsData"/> class that contains the pivot grid's data specific options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsDataField">
            <summary>
                <para>Provides access to the options which control the presentation of the data fields in the PivotGrid. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsDataField"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsFilterPopup">
            <summary>
                <para>Contains settings that specify the appearance and behavior of filter dropdown windows..
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsFilterPopup"/> object that specifies the appearance and behavior of filter dropdown windows.

</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsHint">
            <summary>
                <para>Provides access to the pivot grid's hint options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsHint"/> object which contains the pivot grid's hint options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsLayout">
            <summary>
                <para>Provides options which control how the pivot grid's layout is stored to/restored from storage (a stream, xml file or system registry).
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraPivotGrid.OptionsLayoutPivotGrid"/> object which provides options for controlling how the layout is stored and restored. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsMenu">
            <summary>
                <para>Provides access to the pivot grid's menu options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsMenu"/> object which contains the pivot grid's menu options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsOLAP">
            <summary>
                <para>Provides access to the pivot grid's OLAP mode specific options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsOLAP"/> object which contains the pivot grid's OLAP mode specific options. 
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsPrint">
            <summary>
                <para>Provides access to the pivot grid's print options. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsPrint"/> object which contains the pivot grid's print options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsSelection">
            <summary>
                <para>Provides access to the pivot grid's selection options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsSelection"/> object which contains the pivot grid's selection options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.OptionsView">
            <summary>
                <para>Provides access to the pivot grid's display options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridOptionsView"/> object which contains the pivot grid's display options.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.PaintAppearance">
            <summary>
                <para>Provides access to the appearance settings currently used to paint the pivot grid's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearances"/> object which provides the appearance settings currently used to paint the pivot grid's elements.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.PaintAppearancePrint">
            <summary>
                <para>Provides access to the appearance settings used to paint the pivot grid's elements when it's printed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.PivotGridAppearancesPrint"/> object which provides the appearance settings used to paint the pivot grid's elements when it's printed.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.PostEditor">
            <summary>
                <para>Posts the value being edited to the associated data source without closing the editor.
</para>
            </summary>
            <returns><b>true</b> if the value being edited has been successfully validated and saved to the associated data source; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Prefilter">
            <summary>
                <para>Gets the Prefilter's settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPivotGrid.Prefilter"/> object that contains the Prefilter's settings.
</value>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.PrefilterCriteriaChanged">
            <summary>
                <para>Fires when the Prefilter's criteria are changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.Print">
            <summary>
                <para>Prints the pivot grid.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RefreshData">
            <summary>
                <para>Reloads data from the control's data source and recalculates summaries.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.RepositoryItems">
            <summary>
                <para>Returns the collection of repository items (inplace editors).
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemCollection"/> object that stores the repository items.
</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromRegistry(System.String)">
            <summary>
                <para>Restores the layout stored at the specified system registry path.
</para>
            </summary>
            <param name="path">
		A string value which specifies the system registry path. If the specified path doesn't exist, this method does nothing.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Restores the pivot grid's layout stored at the specified system registry path, using the specified settings. 
</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path. If the specified path doesn't exist, calling this method does nothing.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant which specifies which options should be restored.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Restores a pivot grid's layout from the specified stream, using the specified settings.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant which contains the layout settings. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant which specifies which options should be restored.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Restores a pivot grid's layout from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which the pivot grid's settings are read. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromXml(System.String)">
            <summary>
                <para>Restores a pivot grid's layout from the specified XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the XML file from which pivot grid's settings are read. If the specified file doesn't exist, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RestoreLayoutFromXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Restores a pivot grid's layout using the specified settings from the specified XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the XML file from which the pivot grid's settings are read. If the specified file doesn't exist, an exception is raised.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be restored.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFields">
            <summary>
                <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all the fields in the bound data source. 

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.RetrieveFields(DevExpress.XtraPivotGrid.PivotArea,System.Boolean)">
            <summary>
                <para>Creates <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> objects for all the fields in the bound data source, and moves the fields to the specified area, making them visible or hidden.
</para>
            </summary>
            <param name="area">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotArea"/> value that specifies the area to which the created fields are moved.

            </param>
            <param name="visible">
		<b>true</b> if the created fields are made visible; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveCollapsedStateToFile(System.String)">
            <summary>
                <para>Saves the collapsed state of field values to the specified file.
</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/> value which specifies the path to the file to which the collapsed state of field values is saved. If the specified file doesn't exist, it is created.


            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveCollapsedStateToStream(System.IO.Stream)">
            <summary>
                <para>Saves the collapsed state of field values to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the collapsed state of field values is saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToRegistry(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves a pivot grid's layout to the specified system registry path, using the specified settings.
</para>
            </summary>
            <param name="path">
		A string value which specifies the system registry path to which the layout is saved.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToRegistry(System.String)">
            <summary>
                <para>Saves a pivot grid's layout to a system registry path.
</para>
            </summary>
            <param name="path">
		A string value which specifies the system registry path to which the layout is saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves a pivot grid's layout to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which a pivot grid's layout is written.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToStream(System.IO.Stream,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves a pivot grid's layout to the specified stream, using the specified settings.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the pivot grid's layout is written.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToXml(System.String,DevExpress.Utils.OptionsLayoutBase)">
            <summary>
                <para>Saves a pivot grid's layout to the specified XML file, using the specified settings.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the file where the pivot grid's layout settings should be stored. If an empty string is specified, an exception is raised.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> descendant that specifies which options should be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SaveLayoutToXml(System.String)">
            <summary>
                <para>Saves a pivot grid's layout to an XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the file where a pivot grid's layout should be stored. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToFile(System.String)">
            <summary>
                <para>Saves the PivotGridControl's data and full layout to the specified file.
</para>
            </summary>
            <param name="path">
		A string that specifies the path to the file in which the control's data and layout will be saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToFile(System.String,System.Boolean)">
            <summary>
                <para>Saves the PivotGridControl's data and full layout to the specified file, and allows the data to be compressed.
</para>
            </summary>
            <param name="path">
		A string that specifies the path to the file in which the control's data and layout will be saved.

            </param>
            <param name="compress">
		<b>true</b> to compress the output file; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToStream(System.IO.Stream)">
            <summary>
                <para>Saves the PivotGridControl's data and full layout to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which data is saved.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SavePivotGridToStream(System.IO.Stream,System.Boolean)">
            <summary>
                <para>Saves the PivotGridControl's data and full layout to the specified stream, and allows the data to be compressed.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which data is saved.

            </param>
            <param name="compress">
		<b>true</b> to compress the output stream; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.SetCommandsVisibility(DevExpress.XtraPrinting.IPrintingSystem)">
            <summary>
                <para>This member supports the internal infrastructure and cannot be used directly from your code.
</para>
            </summary>
            <param name="ps">
		@nbsp;

            </param>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowCustomizationForm">
            <summary>
                <para>Fires immediately after the customization form has been invoked. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowEditor">
            <summary>
                <para>Invokes the focused cell's editor.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowEditor(System.Drawing.Point)">
            <summary>
                <para>Invokes the in-place editor for the cell at the specified position.
</para>
            </summary>
            <param name="location">
		The point where the target cell is located.

            </param>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingCustomizationForm">
            <summary>
                <para>Occurs before the customization form is displayed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowingEditor">
            <summary>
                <para>Allows you to cancel editor activation.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShowMenu">
            <summary>
                <para>Enables you to customize the context menu or prevent it from being shown.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ShownEditor">
            <summary>
                <para>Fires immediately after a cell editor has been invoked. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowPrintPreview">
            <summary>
                <para>Opens the Print Preview window for the pivot grid.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ShowUnboundExpressionEditor(DevExpress.XtraPivotGrid.PivotGridField)">
            <summary>
                <para>Invokes an <b>Expression Editor</b> that enables editing  an expression for the specified unbound field.
</para>
            </summary>
            <param name="field">
		A <see cref="T:DevExpress.XtraPivotGrid.PivotGridField"/> that represents an unbound field whose expression will be edited in the Expression Editor.

            </param>
            <returns><b>true</b> if the OK button has been pressed when closing the editor; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.Text">
            <summary>
                <para>Gets or sets the string associated with the control.

</para>
            </summary>
            <value>A string associated with the control.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ToolTipController">
            <summary>
                <para>Gets or sets the tooltip controller component that controls the appearance, position and the content of the hints displayed by the XtraPivotGrid control.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the XtraPivotGrid control.
</value>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.UseDisabledStatePainter">
            <summary>
                <para>Gets or sets whether the control is painted grayed out, when in the disabled state.

</para>
            </summary>
            <value><b>true</b> if the control is painted grayed out, when in the disabled state; otherwise, <b>false</b>

</value>


        </member>
        <member name="M:DevExpress.XtraPivotGrid.PivotGridControl.ValidateEditor">
            <summary>
                <para>Validates the active editor.
</para>
            </summary>
            <returns><b>true</b> if the active editor's value is valid; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.XtraPivotGrid.PivotGridControl.ValidatingEditor">
            <summary>
                <para>Enables you to manually validate cell values.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraPivotGrid.PivotGridControl.ValueImages">
            <summary>
                <para>Gets or sets the source of the images that are available for display within field values.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.ImageList"/> object which represents the source of images.
</value>


        </member>
    </members>
</doc>
