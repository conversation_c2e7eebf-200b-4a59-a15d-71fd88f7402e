﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PurchaseForPaymentRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PURCHASE_FOR_PAYMENT" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="PURCHASE_PAYMENT_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RequestTypeId" column="REQUEST_TYPE_ID" />
    <property name="RequestOwnerId" column="REQUEST_OWNER_ID" />
    <property name="PurchaseId" column="PURCHASE_ID" />
    <property name="IsPurchaseOld" column="IS_PURCHASE_OLD" />
    <property name="PersonnelId" column="PERSONNEL_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>