using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class NotAllowInterventionRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long RegionId { get; set; }
        public virtual long CityId { get; set; }
        public virtual long ServicesId { get; set; }
        public virtual DateTime OpenDate { get; set; }
        public virtual string SiteName { get; set; }
        public virtual string SiteContactName { get; set; }
        public virtual string SiteContactPhone { get; set; }
        public virtual string OperationalCompanyName { get; set; }
        public virtual string OperationalCompanyPersonelName { get; set; }
        public virtual string OperationalCompanyPersonelPhone { get; set; }
        public virtual string Description { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}