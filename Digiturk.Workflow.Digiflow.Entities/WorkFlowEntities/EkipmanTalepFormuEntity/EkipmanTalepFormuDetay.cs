﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EkipmanTalepFormuDetay : EntityBase, IDetailEntity
    {
        //EKİPMAN TALEP FORM (SEVKİYAT DEĞİL!!!)

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long Ekipman_Adet { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual int Yts_Id { get; set; }
        public virtual int Malzeme_Id { get; set; }

        
    }
}
