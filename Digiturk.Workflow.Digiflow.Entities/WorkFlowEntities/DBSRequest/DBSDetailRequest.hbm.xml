﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="DBSDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_DBS_DETAIL_REQUEST" schema="DT_WORKFLOW">
    
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="USERNAME" column="USERNAME" />
    <property name="PERMISSION_TYPE" column="PERMISSION_TYPE" />
    <property name="GIRIS_TARIHI" column="GIRIS_TARIHI" />
    <property name="YETKI_ID" column="YETKI_ID" />
    <property name="YETKI_ADI" column="YETKI_ADI" />
  </class>
</hibernate-mapping>