﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class DBSDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties


        //public virtual long ID { get; set; }
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string USERNAME { get; set; }
        public virtual string PERMISSION_TYPE { get; set; }
        public virtual DateTime GIRIS_TARIHI { get; set; }
        public virtual long YETKI_ID { get; set; }
        public virtual string YETKI_ADI { get; set; }

        #endregion Entity Properties
    }
}