﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="MalzemeCikisEntityDetay,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_MALZEME_CIKIS_REQUEST_DETAIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>    
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="FirmaId" column="FIRMA_ID" />
    <property name="TeslimAlanSirketId" column="TESLIM_ALAN_SIRKET_ID" />
    <property name="TeslimAlanSirketAd" column="TESLIM_ALAN_SIRKET_AD" />
    <property name="Sube" column="SUBE" />
    <property name="Tip" column="TIP" />
    <property name="Marka" column="MARKA" />
    <property name="Model" column="MODEL" />
    <property name="AssetNo" column="ASSET_NO" />
    <property name="SeriNo" column="SERI_NO" />
    <property name="CikisNedeni" column="CIKIS_NEDENI" />
    <property name="Adet" column="ADET" />
    <property name="Tarih" column="TARIH" />
    <property name="VarlikYonetimiId" column="VARLIK_YONETIMI_ID" />
    <property name="VarlikYonetimiKaydiVarMi" column="VARLIK_YONETIMI_KAYDI_VAR_MI" />
    <property name="GarantiKapsamindaMi" column="GARANTI_KAPSAMINDA_MI" />
  </class>
</hibernate-mapping>