﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="MacroDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_MACRO_DETAIL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="RaporVeri" column="RAPOR_VERI" />
    <property name="RaporVeriFirma" column="RAPOR_VERI_FIRMA" />
    <property name="YetkiSure" column="YETKI_SURE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Yetki" column="YETKI" />
  </class>
</hibernate-mapping>