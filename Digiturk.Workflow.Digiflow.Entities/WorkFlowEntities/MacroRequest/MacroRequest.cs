using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class MacroRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long PersonelID { get; set; }
        public virtual string PersonelUserName { get; set; }
        public virtual string Description { get; set; }
        public virtual string MacroID { get; set; }
        public virtual string Purpose { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long IsApprovalRequest { get; set; }

        #endregion Entity Properties
    }
}