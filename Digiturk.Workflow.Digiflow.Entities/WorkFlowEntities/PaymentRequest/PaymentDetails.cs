using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PaymentDetails : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string PurchaseType { get; set; }
        public virtual long PurchaseInstanceId { get; set; }
        public virtual long RelatedRequestID { get; set; } //PaymentInstanceId ye denk geliyor.
        public virtual decimal PurchaseAmount { get; set; }
        public virtual decimal PurchaseAmountWithOption { get; set; }
        public virtual decimal PaymentAmount { get; set; }
        public virtual decimal CurrentAmount { get; set; }
        public virtual long Status { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }

        #endregion Entity Properties
    }
}