﻿using System;
using Digiturk.Workflow.Entities;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class IdeaEvaluation : EntityBase, IEntity
    {
        public virtual long RequestId { get; set; }
        public virtual string IdeaName { get; set; }
        public virtual string Category { get; set; }
        public virtual string ProblemOpportunity { get; set; }
        public virtual string DetailedDescription { get; set; }
        public virtual string OtherContributors { get; set; }
        public virtual string CEOApprovalRequired { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}
