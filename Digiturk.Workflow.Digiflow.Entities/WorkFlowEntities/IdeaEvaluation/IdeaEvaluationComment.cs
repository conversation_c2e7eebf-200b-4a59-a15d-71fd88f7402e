﻿using System;
using Digiturk.Workflow.Entities;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class IdeaEvaluationComment : EntityBase, IEntity
    {
        public virtual long CommentId { get; set; }
        public virtual long RequestId { get; set; }
        public virtual long FLoginId { get; set; }
        public virtual string EvaluationStatus { get; set; }
        public virtual string CommentText { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}
