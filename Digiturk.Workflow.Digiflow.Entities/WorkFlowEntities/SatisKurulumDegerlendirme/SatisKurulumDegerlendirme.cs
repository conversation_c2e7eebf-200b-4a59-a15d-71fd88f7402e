using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SatisKurulumDegerlendirme : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long ServisId { get; set; }
        public virtual long BolgeId { get; set; }
        public virtual long IlId { get; set; }

        public virtual string IlAd { get; set; }
        public virtual string BayiId { get; set; }
        public virtual string BayiAd { get; set; }

        public virtual string UyeNo { get; set; }
        public virtual string PotansiyelNo { get; set; }
        public virtual long TeknikBolgeYoneticisi { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual long AnaIslemTipi { get; set; }
        public virtual long AltIslemTipi { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties

    }
}