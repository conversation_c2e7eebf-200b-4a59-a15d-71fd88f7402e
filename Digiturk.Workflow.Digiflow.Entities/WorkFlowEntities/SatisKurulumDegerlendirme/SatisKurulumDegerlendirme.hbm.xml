<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="SatisKurulumDegerlendirme,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SATIS_DEGERLENDIRME" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
	  <property name="ServisId" column="SERVIS_ID" />
    <property name="BolgeId" column="BOLGE_ID" />
    <property name="IlId" column="IL_ID" />
    <property name="IlAd" column="IL_AD" />
    <property name="BayiId" column="BAYI_ID" />
	  <property name="BayiAd" column="BAYI_AD" />
    <property name="UyeNo" column="UYE_NO" />
    <property name="PotansiyelNo" column="POTANSIYEL_NO" />
    <property name="TeknikBolgeYoneticisi" column="TEKNIK_BOLGE_YONETICISI" />
    <property name="Aciklama" column="ACIKLAMA" />    
    <property name="AnaIslemTipi" column="ANA_ISLEM_TIPI" />
	  <property name="AltIslemTipi" column="ALT_ISLEM_TIPI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>

