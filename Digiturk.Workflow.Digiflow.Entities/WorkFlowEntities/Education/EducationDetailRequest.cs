using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EducationDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long ParticipantLoginId { get; set; }
        public virtual long IsJoined { get; set; }
        public virtual string Note { get; set; }
        public virtual string CertificationFiles { get; set; }
        public virtual string OtherFiles { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Egitmen { get; set; }
        public virtual string EgitimYeri { get; set; }
        public virtual long SoruID { get; set; }
        public virtual long Cevap { get; set; }
        public virtual string EgitimOzet { get; set; }
        public virtual string EgitimTavsiye { get; set; }
        public virtual string EgitimKisiTavsiye { get; set; }
        public virtual string DigerGorusler { get; set; }

        #endregion Entity Properties
    }
}