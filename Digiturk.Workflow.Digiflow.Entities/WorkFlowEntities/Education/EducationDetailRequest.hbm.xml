<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="EducationDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EDUCATION_DETAILREQUEST" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="EDUCATION_DETAIL_REQUEST_ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
        <property name="ParticipantLoginId" column="PARTICIPANT_LOGIN_ID" />
        <property name="IsJoined" column="IS_JOINED" />
        <property name="Note" column="NOTE" />
        <property name="CertificationFiles" column="CERTIFICATION_FILES" />
        <property name="OtherFiles" column="OTHER_FILES" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="VersionID" column="VERSION_ID" />
        <property name="Egitmen" column="EGITMEN" />
        <property name="EgitimYeri" column="EGITIM_YERI" />
        <property name="SoruID" column="SORU_ID" />
        <property name="Cevap" column="CEVAP" />
        <property name="EgitimOzet" column="EGITIM_OZET" />
        <property name="EgitimTavsiye" column="EGITIM_TAVSIYE" />
        <property name="EgitimKisiTavsiye" column="EGITIM_KISI_TAVSIYE" />
        <property name="DigerGorusler" column="DIGER_GORUSLER" />
      </class>
    </hibernate-mapping>