﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EducationRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EDUCATION_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EDUCATION_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="RequestTime" column="REQUEST_TIME" />
    <property name="StartTime" column="START_TIME" />
    <property name="EndTime" column="END_TIME" />
    <property name="EducationDay" column="EDUCATION_DAY" />
    <property name="Subject" column="SUBJECT" />
    <property name="tb_Subject" column="OTHER_SUBJECT" />
    <property name="CompanyName" column="COMPANY_NAME" />
    <property name="OtherCompanyName" column="OTHER_COMPANY_NAME" />
    <property name="CompanyWebAddreess" column="COMPANY_WEB_ADDRESS" />
    <property name="CompanyContact" column="COMPANY_CONTACT" />
    <property name="CompanyContactPhone" column="COMPANY_CONTACT_PHONE" />
    <property name="OtherAttendees" column="OTHER_ATTENDEES" />
    <property name="IsAbroad" column="IS_ABROAD" />
    <property name="IsFree" column="IS_FREE" />
    <property name="IsInhouse" column="IS_INHOUSE" />
    <property name="EducationCost" column="EDUCATION_COST" />
    <property name="EducationCostType" column="EDUCATION_COST_TYPE" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="EducationBenefits" column="EDUCATION_BENEFITS" />
    <property name="TravelCost" column="TRAVEL_COST" />
    <property name="TravelCostType" column="TRAVEL_COST_TYPE" />
    <property name="IsFreeTravel" column="IS_FREE_TRAVEL" />
    <property name="IsBudgeted" column="IS_BUDGETED" />
  </class>
</hibernate-mapping>