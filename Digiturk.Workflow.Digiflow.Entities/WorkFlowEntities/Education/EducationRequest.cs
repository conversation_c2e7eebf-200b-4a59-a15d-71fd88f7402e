﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EducationRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual DateTime RequestTime { get; set; }
        public virtual DateTime StartTime { get; set; }
        public virtual DateTime EndTime { get; set; }
        public virtual long EducationDay { get; set; }
        public virtual string Subject { get; set; }
        public virtual string tb_Subject { get; set; }              // Deniz Ekledi
        public virtual string CompanyName { get; set; }
        public virtual string OtherCompanyName { get; set; }          // Deniz Ekledi
        public virtual string CompanyWebAddreess { get; set; }
        public virtual string CompanyContact { get; set; }
        public virtual string CompanyContactPhone { get; set; }
        public virtual string OtherAttendees { get; set; }
        public virtual long IsAbroad { get; set; }
        public virtual long IsFree { get; set; }
        public virtual long IsInhouse { get; set; }
        public virtual decimal EducationCost { get; set; }
        public virtual string EducationCostType { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string EducationBenefits { get; set; }// Fatih ekledi
        public virtual decimal TravelCost { get; set; }// Fatih ekledi
        public virtual string TravelCostType { get; set; }// Fatih ekledi
        public virtual long IsFreeTravel { get; set; }// Fatih ekledi
        public virtual long IsBudgeted { get; set; }// Fatih ekledi
    }
}