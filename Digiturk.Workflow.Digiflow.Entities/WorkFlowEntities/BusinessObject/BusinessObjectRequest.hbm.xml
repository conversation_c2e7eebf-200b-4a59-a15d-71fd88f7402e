<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BusinessObjectRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BO_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="BO_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="IsNewBOSelection" column="IS_NEW_BO_SELECTION" />
    <property name="RequestLoginId" column="REQUEST_LOGIN_ID" />
    <property name="CopyToLoginId" column="COPY_TO_LOGIN_ID" />
    <property name="IsUniversSelection" column="IS_UNIVERS_SELECTION" />
    <property name="UniversName" column="UNIVERSNAME" />
    <property name="IsDataFeederSelection" column="IS_DATA_FEEDER_SELECTION" />
    <property name="DataFeederDescription" column="DATA_FEEDER_DESCRIPTION" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="TargetUserName" column="TARGETUSERNAME" />
    <property name="CopyUserName" column="COPYUSERNAME" />
  </class>
</hibernate-mapping>