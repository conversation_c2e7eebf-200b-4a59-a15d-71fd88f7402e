﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BillReturnApproveRequestRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BILL_RETURN_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestID" type="long" column="BILL_RETURN_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="LoginId" column="LOGIN_ID" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="CompaignCode" column="COMPAIGN_CODE" />
    <property name="BudgetCode" column="BUDGET_CODE" />
    <property name="BillSubject" column="BILL_SUBJECT" />
    <property name="PersonOrCompanyName" column="PERSON_COMPANY_NAME" />
    <property name="BillTypeId" column="BILL_TYPE_ID" />
    <property name="BillDescription" column="BILL_DESCRIPTION" />
    <property name="BillAmount" column="BILL_AMOUNT" />
    <property name="BillCurrency" column="BILL_CURRENCY" />
    <property name="BillAmountText" column="BILL_AMOUNT_TEXT" />
    <property name="ExchangeRateBuy" column="EXCHANGE_RATE_BUY" />
    <property name="ExchangeRateSell" column="EXCHANGE_RATE_SELL" />
    <property name="IsPurchaseRequest" column="IS_PURCHASE_REQUEST" />
    <property name="IsContract" column="IS_CONTRACT" />
    <property name="IsDeliveryForm" column="IS_DELIVERY_FORM" />
    <property name="IsDispatch" column="IS_DISPATCH" />
    <property name="DispatchDate" column="DISPATCH_DATE" />
    <property name="DispatchNo" column="DISPATCH_NO" />
    <property name="IsBill" column="IS_BILL" />
    <property name="BillDate" column="BILL_DATE" />
    <property name="BillNo" column="BILL_NO" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>