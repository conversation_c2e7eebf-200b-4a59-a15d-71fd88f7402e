﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class HavaMuhalefetiFormDetail : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }

        public virtual string <PERSON>i<PERSON>odu { get; set; }
        public virtual string Bayi<PERSON>di { get; set; }
        public virtual string Mahalle { get; set; }
        public virtual string Ilce { get; set; }
        public virtual string Il { get; set; }
        public virtual string Ulke { get; set; }
        public virtual DateTime SorunBasTarih { get; set; }
        public virtual DateTime SorunBitTarih { get; set; }
        public virtual long SorunGunSayi { get; set; }

        public virtual string UyeAktarimDonem { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}
