<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="HavaMuhalefetiForm,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_HAVA_MUHALEFETI_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="KontrolTip" column="KONTROL_TIP" />
	  <property name="Aciklama" column="ACIKLAMA" />
    <property name="Dokuman" column="DOKUMAN" />  
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
	<property name="FormTip" column="FORM_TIPI" />
  </class>
</hibernate-mapping>
