﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="HavaMuhalefetiFormDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_HAVA_MUHALEFETI_DETAIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="BayiKodu" column="BAYI_KODU" />
    <property name="BayiAdi" column="BAYI_ADI" />
	  <property name="Mahalle" column="MAHALLE" />
    <property name="Ilce" column="ILCE" />
    <property name="Il" column="IL" />
    <property name="Ulke" column="ULKE" />
    <property name="SorunBasTarih" column="SORUN_BASLANGIC_TARIHI" />
    <property name="SorunBitTarih" column="SORUN_BITIS_TARIHI" />
    <property name="SorunGunSayi" column="SORUN_GUN_SAYI" />
    <property name="UyeAktarimDonem" column="UYE_AKTARIM_DONEM" />  

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>
