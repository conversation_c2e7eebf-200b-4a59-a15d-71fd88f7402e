﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="RotaRaporDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ROTA_RAPOR_DETAIL_RQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ProgramNo" column="GROUP_NO" />
    <property name="Program" column="GROUP_NO_DESCRIPTION" />
    <property name="FunctionNo" column="REPORT_NO" />
    <property name="Function" column="REPORT_NO_DESCRIPTION" />
    <property name="Category" column="CATEGORY" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>