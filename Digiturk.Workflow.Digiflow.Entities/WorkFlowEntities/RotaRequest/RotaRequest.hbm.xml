<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="RotaRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ROTA_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ROTA_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="GroupNoDescription" column="GROUP_NO_DESCRIPTION" />
    <property name="PersonelID" column="PERSONEL_ID" />
    <property name="PersonelUserName" column="PERSONEL_USER_NAME" />
    <property name="GroupNo" column="GROUP_NO" />
    <property name="ReportNo" column="REPORT_NO" />
    <property name="ReportNoList" column="REPORT_NO_LIST" />
    <property name="SourcePersonelId" column="SOURCE_PERSONEL_ID" />
    <property name="TargetPersonelId" column="TARGET_PERSONEL_ID" />
    <property name="ProcessType" column="PROCESS_TYPE" />
    <property name="Description" column="DESCRIPTION" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="TargetUserName" column="TARGETUSERNAME" />
    <property name="CopyUserName" column="COPYUSERNAME" />
  </class>
</hibernate-mapping>