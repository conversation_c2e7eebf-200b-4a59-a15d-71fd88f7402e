﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiCezaDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }

        public virtual string CezaTipi { get; set; }
        public virtual string UyeNo { get; set; }
        public virtual string UyeAdSoyad { get; set; }

        public virtual decimal TahsilEdilenUcret { get; set; }
        public virtual decimal TahsilEdilmesiGerekenUcret { get; set; }
        public virtual decimal KesilecekCezaTutar { get; set; }

        public virtual string TahsilEdilenParaBirim { get; set; }
        public virtual string TahsilEdilmesiGerekenParaBirim { get; set; }
        public virtual string CezaParaBirim { get; set; }

        public virtual string Aciklama { get; set; }
        public virtual string DokumanLink { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long CezaTipiId { get; set; }

        #endregion Entity Properties
    }
}