﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiCezaRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_CEZA_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Il" column="IL" />
    <property name="Ilce" column="ILCE" />
    <property name="Mahalle" column="MAHALLE" />
    <property name="BayiKodu" column="BAYI_KODU" />
    <property name="BayiAdi" column="BAYI_ADI" />
    <property name="VergiNo" column="VERGI_NO" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="FATURA_NO" column="FATURA_NO" />
    <property name="FATURA_ACIKLAMA" column="FATURA_ACIKLAMA" />
    
  </class>
</hibernate-mapping>