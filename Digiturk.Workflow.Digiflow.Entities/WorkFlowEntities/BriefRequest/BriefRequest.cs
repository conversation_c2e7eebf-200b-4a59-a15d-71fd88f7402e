﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BriefRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long TalepEden { get; set; }
        public virtual string TalepAdi { get; set; }
        public virtual DateTime TalepTarihi { get; set; }
        public virtual DateTime TalepTeslimTarihi { get; set; }
        public virtual string BriefOnaylayanlar { get; set; }
        public virtual DateTime BriefOnayTarihi { get; set; }
        public virtual DateTime PlanTeslimTarihi { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}