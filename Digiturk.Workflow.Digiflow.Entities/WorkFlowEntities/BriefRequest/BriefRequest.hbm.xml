﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BriefRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BRIEF_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="BRIEF_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="TalepEden" column="TALEP_EDEN" />
    <property name="TalepAdi" column="TALEP_ADI" />
    <property name="TalepTarihi" column="TALEP_TARIHI" />
    <property name="TalepTeslimTarihi" column="TALEP_TESLIM_TARIHI" />
    <property name="BriefOnaylayanlar" column="BRIEF_ONAYLAYANLAR" />
    <property name="BriefOnayTarihi" column="BRIEF_ONAY_TARIHI" />
    <property name="PlanTeslimTarihi" column="PLAN_TESLIM_TARIHI" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>