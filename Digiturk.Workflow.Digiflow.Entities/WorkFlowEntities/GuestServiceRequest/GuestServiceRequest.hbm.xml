﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="GuestServiceRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_GUEST_SERVICE_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="GUEST_SERVICE_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="LoginId" column="LOGIN_ID" />
    <property name="AcanLoginId" column="ACAN_LOGIN_ID" />
    <property name="ServisAdi" column="SERVIS_ADI" />
    <property name="ServisId" column="SERVIS_ID" />
    <property name="Tarih" column="TARIH" />
    <property name="TalepSira" column="TALEP_SIRA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Talep_Tip" column="TALEP_TIP" />
    <property name="Bitis_Tarih" column="BITIS_TARIH" />
  </class>
</hibernate-mapping>