using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BoScheduleRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string RAPOR_AMACI { get; set; }
        public virtual string RAPOR_ADI_KLASORU { get; set; }
        public virtual string RAPOR_SORGU_SECIM { get; set; }
        public virtual string ORNEK_ISIM { get; set; }
        public virtual string FILTRE_ALAN { get; set; }
        public virtual string FILTRE_DEGER { get; set; }
        public virtual string RAPOR_BICIM { get; set; }
        public virtual string RAPOR_EVENT { get; set; }
        public virtual string RAPOR_YOLLANMA_HEDEF { get; set; }
        public virtual string RAPOR_MAIL_FROM { get; set; }
        public virtual string RAPOR_MAIL_TO { get; set; }
        public virtual string RAPOR_MAIL_CC { get; set; }
        public virtual string RAPOR_MAIL_BCC { get; set; }
        public virtual string RAPOR_MAIL_SUBJECT { get; set; }
        public virtual string RAPOR_MESSAGE { get; set; }
        public virtual string RAPOR_MAIL_ISMINI_KULLAN { get; set; }
        public virtual string RAPOR_MAIL_YENIDEN_ISIMLENDIR { get; set; }
        public virtual string RAPOR_BI_KIME { get; set; }
        public virtual string RAPOR_BI_ISMINI_KULLAN { get; set; }
        public virtual string RAPOR_BI_YENIDEN_ISIMLENDIR { get; set; }
        public virtual string RAPOR_BI_FARKLI_GONDER { get; set; }
        public virtual string RAPOR_SIK_GUN { get; set; }
        public virtual string RAPOR_SIK_GUN_DEGER { get; set; }
        public virtual string RAPOR_SIK_AY { get; set; }
        public virtual string RAPOR_SIK_AY_DEGER { get; set; }
        public virtual string RAPOR_SIK_AY_N { get; set; }
        public virtual string RAPOR_SIK_AY_N_DEGER { get; set; }
        public virtual string RAPOR_SIK_AY_SON { get; set; }
        public virtual string RAPOR_SIK_SIMDI { get; set; }
        public virtual string RAPOR_SIK_AY_N_GUN { get; set; }
        public virtual string RAPOR_SIK_AY_N_HAFTA { get; set; }
        public virtual string RAPOR_SIK_AY_N_HAFTA_GUN { get; set; }
        public virtual string RAPOR_SIK_SAAT { get; set; }
        public virtual string RAPOR_SIK_SAAT_N { get; set; }
        public virtual string RAPOR_SIK_SAAT_DAKIKA_N { get; set; }
        public virtual string RAPOR_SIK_ILK_PAZARTESI { get; set; }
        public virtual string RAPOR_SIK_HAFTALIK { get; set; }
        public virtual string RAPOR_SIK_HAFTALIK_N { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }


        #endregion Entity Properties
    }
}