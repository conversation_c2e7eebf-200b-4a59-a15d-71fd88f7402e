﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class AracTakipRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual string Bolge { get; set; }
        public virtual DateTime Cikis_Tarihi { get; set; }
        public virtual string <PERSON><PERSON><PERSON>_Saati { get; set; }
        public virtual DateTime Donus_Tarihi { get; set; }
        public virtual string Donus_<PERSON>ati { get; set; }
        public virtual string Talep_<PERSON>eni { get; set; }
        public virtual long Verilen_Arac { get; set; }
        public virtual string Verilen_Arac_Plaka { get; set; }
        public virtual long Request_User_Id { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        #endregion
    }
}
