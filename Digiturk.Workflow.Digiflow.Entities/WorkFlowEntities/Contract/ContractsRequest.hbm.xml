<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ContractsRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CONTRACTSREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CONTRACTS_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="Parties" column="PARTIES" />
    <property name="Firms" column="FIRMS" />
    <property name="TaxNo" column="TAXNO" />
    <property name="TaxRegion" column="TAXREGION" />
    <property name="StartDate" column="STARTDATE" />
    <property name="EndDate" column="ENDDATE" />
    <property name="Other" column="OTHER" />
    <property name="ContractCategory" column="CONTRACT_CATEGORY" />
    <property name="Subject" column="SUBJECT" />
    <property name="PaymentAmount" column="PAYMENT_AMOUNT" />
    <property name="PaymentCurrencyType" column="PAYMENT_CURRENT_TYPE" />
    <property name="PaymentType" column="PAYMENT_TYPE" />
    <property name="Description" column="DESCRIPTION" />
    <property name="CancelByDigiTurk" column="CANCEL_DIGITURK" />
    <property name="CancelTwoSide" column="CANCEL_TWO_SIDE" />
    <property name="CancelCurePeriod" column="CANCEL_CURE_PERIOD" />
    <property name="CancelOther" column="CANCEL_OTHER" />
    <property name="CurePeriodDay" column="CURE_PERIOD_DAY" />
    <property name="confidence" column="CONFIDENCE" />
    <property name="PunishmentClauses" column="PUNISMENT_CLAUSES" />
    <property name="ContractControlDate" column="CONTRACT_CONTROL_DATE" />
    <property name="ContractType" column="CONTRACT_TYPE" />
    <property name="ContractSoftFile" column="SOFT_FILE_NAME" />
    <property name="LastComment" column="LASTCOMMENT" />
    <property name="UrunServis" column="URUNSERVIS" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="ContractSignedSoftFile" column="SIGNED_SOFT_FILE_NAME" />
  </class>
</hibernate-mapping>