using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ContractsRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual string Parties { get; set; }
        public virtual string Firms { get; set; }
        public virtual string TaxNo { get; set; }
        public virtual string TaxRegion { get; set; }
        public virtual DateTime StartDate { get; set; }
        public virtual DateTime EndDate { get; set; }
        public virtual string Other { get; set; }
        public virtual long ContractCategory { get; set; }
        public virtual string Subject { get; set; }
        public virtual decimal PaymentAmount { get; set; }
        public virtual string PaymentCurrencyType { get; set; }
        public virtual long PaymentType { get; set; }
        public virtual string Description { get; set; }
        public virtual long CancelByDigiTurk { get; set; }
        public virtual long CancelTwoSide { get; set; }
        public virtual long CancelCurePeriod { get; set; }
        public virtual long CancelOther { get; set; }
        public virtual long CurePeriodDay { get; set; }
        public virtual string confidence { get; set; }
        public virtual string PunishmentClauses { get; set; }
        public virtual DateTime ContractControlDate { get; set; }
        public virtual string ContractType { get; set; }
        public virtual string ContractSoftFile { get; set; }
        public virtual string LastComment { get; set; }
        public virtual string UrunServis { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        public virtual string ContractSignedSoftFile { get; set; }

        #endregion Entity Properties
    }
}