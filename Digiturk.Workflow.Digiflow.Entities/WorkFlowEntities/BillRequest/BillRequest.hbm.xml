﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BillRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BILL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="BillInfo" column="BILL_INFO" />
    <property name="BillRequester" column="BILL_REQUESTER" />
    <property name="RequesterAddress" column="REQUESTER_ADDRESS" />
    <property name="TaxOffice" column="TAX_OFFICE" />
    <property name="TaxNumber" column="TAX_NUMBER" />
    <property name="BillAmount" column="BILL_AMOUNT" />
    <property name="BillAmountCurrency" column="BILL_AMOUNT_CURRENCY" />
    <property name="ExchangeRateBuy" column="EXCHANGE_RATE_BUY" />
    <property name="ExchangeRateSell" column="EXCHANGE_RATE_SELL" />
    <property name="CollectingType" column="COLLECTING_TYPE" />
    <property name="PaymentDay" column="PAYMENT_DAY" />
    <property name="BillInfoNameSurname" column="BILL_INFO_NAME_SURNAME" />
    <property name="BillInfoPhone" column="BILL_INFO_PHONE" />
    <property name="IsContract" column="IS_CONTRACT" />
    <property name="IsOther" column="IS_OTHER" />
    <property name="CompanyName" column="COMPANY_NAME" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="InLettering" column="IN_LETTERING" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="ContractDate" column="CONTRACT_DATE" />

    <property name="PaymentDate" column="PAYMENT_DATE" />
    <property name="ServiceSDate" column="SERVICE_START_DATE" />
    <property name="ServiceEDate" column="SERVICE_END_DATE" />
    <property name="BillType" column="BILL_TYPE" />
    <property name="BillInfoEmail" column="BILL_INFO_EMAIL" />
	<property name="AccountCode" column="ACCOUNT_CODE" />
  </class>
</hibernate-mapping>