﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiKesinHesapDetay,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_KESIN_HESAP_DETAY" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="BAYI_KESIN_HESAP_ID" />
    <property name="InaktifTablosuFormId" column="INAKTIF_TABLOSU_FORM_ID" />
    <property name="BayiTemsilcisi" column="BAYI_TEMSILCISI" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping> 
