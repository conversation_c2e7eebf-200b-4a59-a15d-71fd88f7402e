﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BayiKesinHesapDetay : EntityBase, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long InaktifTablosuFormId { get; set; }
        public virtual string BayiTemsilcisi { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }



    }
}
