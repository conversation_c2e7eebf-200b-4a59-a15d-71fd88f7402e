﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BayiKesinHesapDetay2,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BAYI_KESIN_HESAP_DETAY_2" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="BAYI_KESIN_HESAP_ID" />
    <property name="RelatedDetailRequestID" column="BAYI_KESIN_HESAP_DETAY_ID" />    
    <property name="Borc" column="BORC" />
    <property name="Alacak" column="ALACAK" />
    <property name="Tutar" column="TUTAR" />
    <property name="Durum" column="DURUM" />
    <property name="SatisYetkilisiYorum" column="SATIS_YETKILISI_YORUM" />
    <property name="FinansYetkilisiYorum" column="FINANS_YETKILISI_YORUM" />     
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />

  </class>
</hibernate-mapping>

