﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CorporateCommunication : EntityBase, IEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long UserId { get; set; }
        public virtual string RequestName { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual DateTime RequestDeliveryDate { get; set; }
        public virtual string BriefApproverName { get; set; }
        public virtual string BriefApproverId { get; set; }
        public virtual DateTime DateOfApproval { get; set; }
        public virtual DateTime PlannedDeliveryDate { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
    }
}