﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CorporateCommunication, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CORP_COMMUNICATION_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CORPORATE_COMMUNICATION_REQ_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="UserId" column="USER_ID" />
    <property name="RequestName" column="REQUEST_NAME" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="RequestDeliveryDate" column="REQUEST_DELIVERY_DATE" />
    <property name="BriefApproverName" column="BRIEF_APPROVER_NAME" />
    <property name="BriefApproverId" column="BRIEF_APPROVER_ID" />
    <property name="DateOfApproval" column="DATE_OF_APPROVAL" />
    <property name="PlannedDeliveryDate" column="PLANNED_DELIVERY_DATE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>