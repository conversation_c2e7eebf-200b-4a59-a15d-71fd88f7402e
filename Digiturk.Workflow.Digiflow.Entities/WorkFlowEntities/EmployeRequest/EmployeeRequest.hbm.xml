﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EmployeeRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EMPLOYEE_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EMPLOYEE_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="EmployeeType" column="EMPLOYEE_TYPE" />
    <property name="Divison" column="DIVISION" />
    <property name="Department" column="DEPARTMENT" />
    <property name="Unit" column="UNIT" />
    <property name="Team" column="TEAM" />
    <property name="Title" column="TITLE" />
    <property name="StartDate" column="START_DATE" />
    <property name="EndDate" column="END_DATE" />
    <property name="IsReplacement" column="IS_REPLACEMENT" />
    <property name="Budgeted" column="BUDGETED" />
    <property name="DeptBudgetAccount" column="DEPT_BUDGET_ACCOUNT" />
    <property name="OutsourceDescription" column="OUTSOURCE_DESCRIPTION" />
    <property name="OutsourceCompany" column="OUTSOURCE_COMPANY" />
    <property name="SerialNumber" column="SERIAL_NUMBER" />
    <property name="LeavePersonelName" column="LEAVE_PERSONEL_NAME" />
    <property name="MainResponsibilities" column="MAIN_RESPONSIBILITIES" />
    <property name="Salary" column="SALARY" />
    <property name="WorkDescription" column="WORK_DESCRIPTION" />
    <property name="Extras" column="EXTRAS" />
    <property name="Requirements" column="REQUIREMENTS" />
    <property name="ReasonForHiring" column="REASON_FOR_HIRING" />
    <property name="DepartmentManager" column="DEPARTMENT_MANAGER" />
    <property name="ApprovingDepartmentManager" column="APPROVING_DEPARTMENT_MANAGER" />
    <property name="DivisionManager" column="DIVISION_MANAGER" />
    <property name="UnitManager" column="UNIT_MANAGER" />
    <property name="ApprovingDivisionManager" column="APPROVING_DIVISION_MANAGER" />
    <property name="ApprovingUnitManager" column="APPROVING_UNIT_MANAGER" />
    <property name="InnerServicesManager" column="INNER_SERVICES_MANAGER" />
    <property name="ApprovingInnerServicesManager" column="APPROVING_INNER_SERVICES_MAN" />
    <property name="GeneralManager" column="GENERAL_MANAGER" />
    <property name="ApprovingGeneralManager" column="APPROVING_GENERAL_MANAGER" />
    <property name="DepartmentManagerApproval" column="DEPARTMENT_MAN_APPROVAL" />
    <property name="DivisionManagerApproval" column="DIVISION_MAN_APPROVAL" />
    <property name="UnitManagerApproval" column="UNIT_MANAGER_APPROVAL" />
    <property name="InnerServicesManagerApproval" column="INNER_SERVICES_MAN_APPROVAL" />
    <property name="GeneralManagerApproval" column="GENERAL_MAN_APPROVAL" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastModified" column="LAST_MODIFIED" />
    <property name="LastModifiedBy" column="LAST_MODIFIED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="RequestType" column="REQUEST_TYPE" />
    <property name="SeriNo" column="SERI_NO" />
    <property name="BuddyLoginId" column="BUDDY_LOGINID" />
	<property name="Lokasyon" column="LOKASYON" />
  </class>
</hibernate-mapping>