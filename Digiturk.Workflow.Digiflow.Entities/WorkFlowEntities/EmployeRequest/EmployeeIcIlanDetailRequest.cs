﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeeIcIlanDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long DeptId { get; set; }
        public virtual string DeptName { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}