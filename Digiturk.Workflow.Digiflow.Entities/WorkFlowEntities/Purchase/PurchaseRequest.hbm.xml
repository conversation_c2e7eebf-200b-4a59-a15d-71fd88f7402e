<?xml version="1.0" encoding="utf-8" ?>
      <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
        <class name="PurchaseRequest , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PURCHASEREQUEST" schema="DT_WORKFLOW">
          <id name="RequestId" type="long" column="REQUEST_ID">
            <generator class="trigger-identity"></generator>
          </id>
          <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
          <property name="FirmId" column="FIRM_ID" />
          <property name="RequestSubject" column="REQUEST_SUBJECT" />
          <property name="ProjectId" column="PROJECT_ID" />
          <property name="CampaignId" column="CAMPAIGN_ID" />
          <property name="Description" column="DESCRIPTION" />
          <property name="BudgetId" column="BUDGET_ID" />
          <property name="PurchaseType" column="PURCHASE_TYPE" />
          <property name="PurchaseBudget" column="PURCHASE_BUDGET" />
          <property name="FirmChoice" column="FIRM_CHOICE" />
          <property name="PreferFirm" column="PREFER_FIRM" />
          <property name="PreferFirmReason" column="PREFER_FIRM_REASON" />
          <property name="StandartProduct" column="STANDART_PRODUCT" />
          <property name="EstimateTotalAmount" column="ESTIMATE_TOTAL_AMOUNT" />
          <property name="CurrencyCode" column="CURRENCY_CODE" />
          <property name="InsertFileName" column="INSERT_FILE_NAME" />
          <property name="DeliveryPlace" column="DELIVERY_PLACE" />
          <property name="DeliveryDate" column="DELIVERY_DATE" />
          <property name="Notes" column="NOTES" />
          <property name="ProductOrService" column="PRODUCT_OR_SERVICE" />
          <property name="PurchasingDate" column="PURCHASING_DATE" />
          <property name="HasContractExist" column="HAS_CONTRACT_EXIST" />

          <property name="ReqFirmContact" column="REQ_FIRM_CONTACT" />
          <property name="ReqFirmChoice" column="REQ_FRIM_CHOICE" />
          <property name="QualityPrivateInst" column="QUALITY_PRIVATE_INST" />
          <property name="QualityStandartPrd" column="QUALITY_STANDART_PRD" />
          <property name="QualityCertificate" column="QUALITY_CERTIFICATE" />
          <property name="QualityGeneralCntrl" column="QUALITY_GENERAL_CNTRL" />
          <property name="QualityTestResult" column="QUALITY_TEST_RESULT" />
          <property name="QualityControlPlan" column="QUALITY_CONTROL_PLAN" />
          <property name="ServiceRequest" column="SERVICE_REQUEST" />
          <property name="ServiceSeller" column="SERVICE_SELLER" />
          <property name="ServiceOtherFirm" column="SERVICE_OTHER_FIRM" />
          <property name="ServiceOprtnInst" column="SERVICE_OPRTN_INST" />
          <property name="ServiceUsageInst" column="SERVICE_USAGE_INST" />
          <property name="ServiceEduReq" column="SERVICE_EDU_REQ" />
          <property name="ServiceOther" column="SERVICE_OTHER" />

          <property name="PurchasePaymentType" column="PURCHASE_PAYMENT_TYPE" />

          <property name="Created" column="CREATED" />
          <property name="LastUpdated" column="LAST_UPDATED" />
          <property name="CreatedBy" column="CREATED_BY" />
          <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
          <property name="VersionID" column="VERSION_ID" />  
          <property name="PurchaseCheckNeeded" column="PURCHASE_CHK_NEED" />
          <property name="HasOtherCompetitors" column="HAS_OTHER_COMPETITORS" />
          <property name="OtherCompetitors" column="OTHER_COMPETITORS" />
          <property name="FinancialRisk" column="FINANCIAL_RISK" />
          <property name="IsOriginalManufacturer" column="IS_ORIGINAL_MANUFACTURER" />
          <property name="DepartmentApproval" column="DEPARTMENT_APPROVAL" />
          <property name="RegionalManagerApproval" column="REGIONAL_MNG_APPROVAL" />
          
          
          
    </class>
    </hibernate-mapping>