using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PurchaseDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long OrderNo { get; set; }
        public virtual string CategoryId { get; set; }
        public virtual string Category { get; set; }
        public virtual string ProductId { get; set; }
        public virtual string Product { get; set; }
        public virtual string ProductDefination { get; set; }
        public virtual long Unit { get; set; }
        public virtual string UnitOfQuantity { get; set; }
        public virtual decimal EstimateTotalAmount { get; set; }
        public virtual string CurrencyCode { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}