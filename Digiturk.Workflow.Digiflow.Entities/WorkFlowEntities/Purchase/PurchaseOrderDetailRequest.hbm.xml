﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="PurchaseOrderDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_PURCHASE_ORDER_DET_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="SorumluPersonel" column="SORUMLU_PERSONEL" />
    <property name="Tarih" column="TARIH" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Tutar" column="TUTAR" />
  </class>
</hibernate-mapping>