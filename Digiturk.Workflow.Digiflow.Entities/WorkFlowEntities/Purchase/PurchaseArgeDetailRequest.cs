﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PurchaseArgeDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string SubProjectName { get; set; }
        public virtual string SubProjectDescription { get; set; }
        public virtual long Ratio { get; set; }
        public virtual DateTime PaymentDate { get; set; }
        public virtual string ServiceType { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}