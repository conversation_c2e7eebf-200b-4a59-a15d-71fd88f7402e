using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class PurchaseRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual string FirmId { get; set; }
        public virtual string RequestSubject { get; set; }
        public virtual string ProjectId { get; set; }
        public virtual string CampaignId { get; set; }
        public virtual string Description { get; set; }
        public virtual string BudgetId { get; set; }
        public virtual long PurchaseType { get; set; }
        public virtual long PurchaseBudget { get; set; }
        public virtual long FirmChoice { get; set; }
        public virtual string PreferFirm { get; set; }
        public virtual string PreferFirmReason { get; set; }
        public virtual long StandartProduct { get; set; }
        public virtual decimal EstimateTotalAmount { get; set; }
        public virtual string CurrencyCode { get; set; }
        public virtual string InsertFileName { get; set; }
        public virtual string DeliveryPlace { get; set; }
        public virtual string DeliveryDate { get; set; }
        public virtual string Notes { get; set; }

        public virtual long ProductOrService { get; set; }
        public virtual DateTime PurchasingDate { get; set; }
        public virtual long HasContractExist { get; set; }

        public virtual long ReqFirmContact { get; set; }
        public virtual long ReqFirmChoice { get; set; }
        public virtual long QualityPrivateInst { get; set; }
        public virtual long QualityStandartPrd { get; set; }
        public virtual long QualityCertificate { get; set; }
        public virtual long QualityGeneralCntrl { get; set; }
        public virtual long QualityTestResult { get; set; }
        public virtual long QualityControlPlan { get; set; }
        public virtual long ServiceRequest { get; set; }
        public virtual long ServiceSeller { get; set; }
        public virtual long ServiceOtherFirm { get; set; }
        public virtual long ServiceOprtnInst { get; set; }
        public virtual long ServiceUsageInst { get; set; }
        public virtual long ServiceEduReq { get; set; }
        public virtual long ServiceOther { get; set; }

        public virtual string PurchasePaymentType { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual Int32 PurchaseCheckNeeded { get; set; }
        public virtual Int32 HasOtherCompetitors { get; set; }
        public virtual string OtherCompetitors { get; set; }
        public virtual string FinancialRisk { get; set; }
        public virtual Int32 IsOriginalManufacturer { get; set; }
        public virtual Int32 DepartmentApproval { get; set; }
        public virtual Int32 RegionalManagerApproval { get; set; }

        #endregion Entity Properties
    }
}