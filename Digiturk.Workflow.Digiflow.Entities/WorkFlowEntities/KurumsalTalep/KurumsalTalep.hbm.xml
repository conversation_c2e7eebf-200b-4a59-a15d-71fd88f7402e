﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="<PERSON>rumsalTalep,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_Kurumsal_TALEP" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="<PERSON>rumsal_TALEP_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="ILGILI_UYGULAMA" column="ILGILI_UYGULAMA" />
    <property name="ONCELIK" column="ONCELIK" />
    <property name="ACIKLAMA" column="ACIKLAMA" />
    <property name="TESLIM_TARIHI" column="TESLIM_TARIHI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>