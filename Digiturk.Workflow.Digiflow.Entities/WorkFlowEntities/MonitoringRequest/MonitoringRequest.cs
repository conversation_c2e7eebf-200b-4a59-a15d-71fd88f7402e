using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class MonitoringRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long FlowDefId { get; set; }
        public virtual long FlowInstanceID { get; set; }
        public virtual long FlowTypeID { get; set; }
        public virtual string FlowTypeName { get; set; }
        public virtual long PersonelID { get; set; }
        public virtual long IsActive { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual string FlowDefIdList { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual long OwnerLoginID { get; set; }

        #endregion Entity Properties
    }
}