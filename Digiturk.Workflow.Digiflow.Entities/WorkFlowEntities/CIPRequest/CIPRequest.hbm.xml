﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CIPRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CIP_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CIP_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="RequestType" column="REQUEST_TYPE" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="SubscrbNo" column="SUBSCRB_NO" />
    <property name="SubscrbName" column="SUBSCRB_NAME_SURNAME" />
    <property name="Tckn" column="TCKN" />
    <property name="CellPhone" column="CELL_PHONE" />
    <property name="Job" column="JOB" />
    <property name="Position" column="POSITION" />
    <property name="Description" column="DESCRIPTION" />
    <property name="ContactPerson" column="CONTACT_PERSON" />
    <property name="CipFeature" column="CIP_FEATURE" />
    <property name="RequestReason" column="REQUEST_REASON" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    </class>
</hibernate-mapping>