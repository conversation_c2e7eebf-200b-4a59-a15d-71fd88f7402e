﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ExpenseReprDetail : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual DateTime ExpenseRepDate { get; set; }
        public virtual string VisiterFullName { get; set; }
        public virtual string VisitorCompany { get; set; }
        public virtual string VisitorJob { get; set; }
        public virtual string VisitSubject { get; set; }
        public virtual decimal ReprAmount { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}