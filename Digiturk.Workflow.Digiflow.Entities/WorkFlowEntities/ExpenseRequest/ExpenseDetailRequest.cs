﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ExpenseDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string ExpenseTypeId { get; set; }
        public virtual decimal ExpenseAmount { get; set; }
        public virtual string ExpenseCurrency { get; set; }
        public virtual DateTime ExpenseDate { get; set; }
        public virtual string ExpenseDescription { get; set; }
        public virtual string DistanceDescription { get; set; }
        public virtual string BillID { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual string CreatedBy { get; set; }
        public virtual string LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}