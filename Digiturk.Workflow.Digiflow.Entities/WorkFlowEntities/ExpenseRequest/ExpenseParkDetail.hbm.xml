﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ExpenseParkDetail, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EXPENSE_PARK_DETAIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EXPENSE_PARK_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ParkStartDate" column="PARK_START_DATE" />
    <property name="ParkStartTime" column="PARK_START_TIME" />
    <property name="ParkEndDate" column="PARK_END_DATE" />
    <property name="ParkEndTime" column="PARK_END_TIME" />
    <property name="ParkTime" column="PARK_TIME" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>