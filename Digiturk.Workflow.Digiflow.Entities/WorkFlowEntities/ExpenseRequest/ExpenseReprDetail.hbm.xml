﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ExpenseReprDetail, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EXPENSE_REPR_DETAIL" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EXPENSE_REPR_DETAIL_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ExpenseRepDate" column="EXPENSE_REPR_DATE" />
    <property name="VisiterFullName" column="VISITOR_FULLNAME" />
    <property name="VisitorCompany" column="VISITOR_COMPANY" />
    <property name="VisitorJob" column="VISITOR_JOB" />
    <property name="VisitSubject" column="VISIT_SUBJECT" />
    <property name="ReprAmount" column="REPR_AMOUNT" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>