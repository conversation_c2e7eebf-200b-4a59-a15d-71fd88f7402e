﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeesDetailAcil : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string AcilAdSoyad { get; set; }
        public virtual string AcilYakinlik { get; set; }
        public virtual string AcilTelis { get; set; }
        public virtual string AcilTelev { get; set; }
        public virtual string AcilGsm { get; set; }
        public virtual string AcilAdres { get; set; }
        public virtual string AcilEmail { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
    }
}