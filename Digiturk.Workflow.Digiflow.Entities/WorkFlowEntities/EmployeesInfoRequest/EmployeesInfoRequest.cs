﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeesInfoRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long UserId { get; set; }
        public virtual string Firma { get; set; }
        public virtual string Sirket { get; set; }
        public virtual long SicilNo { get; set; }
        public virtual string KullaniciAdi { get; set; }
        public virtual string AdiSoyadi { get; set; }
        public virtual long DepartmanId { get; set; }
        public virtual string ButceGrubu { get; set; }
        public virtual string Email { get; set; }
        public virtual string IsManager { get; set; }
        public virtual string ButceOnayYetki { get; set; }
        public virtual string MasaTel { get; set; }
        public virtual DateTime DogumTarihi { get; set; }
        public virtual DateTime IseGirisTarihi { get; set; }
        public virtual string TcNo { get; set; }
        public virtual string VergiNo { get; set; }
        public virtual string SgkNo { get; set; }

        //public virtual string MezunOkul { get; set; }
        //public virtual string MezunYil { get; set; }
        public virtual string Adres { get; set; }

        public virtual string CepTel { get; set; }
        public virtual string MedeniDurum { get; set; }
        public virtual string Cinsiyet { get; set; }
        public virtual string EsAdSoyad { get; set; }
        public virtual string EsTcNo { get; set; }
        public virtual DateTime EsDogumTarihi { get; set; }
        public virtual string EsCalisiyormu { get; set; }
        public virtual string KanGrup { get; set; }
        public virtual string KullaniciGrubu { get; set; }
        public virtual string BedenOlcusu { get; set; }
        public virtual string Vardiyalimi { get; set; }
        public virtual string ArgePersonelimi { get; set; }
        public virtual string Title { get; set; }
        public virtual string GsmProfil { get; set; }
        public virtual string Unvan { get; set; }
        public virtual string Il { get; set; }
        public virtual string Lokasyon { get; set; }
        public virtual string BulunduguKat { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }

        public virtual string SODEXHO_KART_NO { get; set; }
        public virtual string Aciklama { get; set; }
        public virtual string Name { get; set; }

        public virtual string Surname { get; set; }
        public virtual string Pozisyon { get; set; }
        public virtual string SozlesmeTuru { get; set; }
        public virtual string CalismaTipi { get; set; }
        public virtual string CalisanTahsil { get; set; }
    }
}