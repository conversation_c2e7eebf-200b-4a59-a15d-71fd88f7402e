﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EmployeesDetailChild, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EMPLOYEES_DETAIL_CHILD" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="EMPLOYEES_DETAIL_CHILD_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="CocukAdSoyad" column="COCUK_ADSOYAD" />
    <property name="CocukTcNo" column="COCUK_TCNO" />
    <property name="CocukDogumTarihi" column="COCUK_DOGUM_TARIHI" />
    <property name="CocukCinsiyet" column="COCUK_CINSIYET" />
    <property name="CocukTahsil" column="COCUK_TAHSIL" />
    <property name="CocukId" column="COCUK_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionId" column="VERSION_ID" />
  </class>
</hibernate-mapping>