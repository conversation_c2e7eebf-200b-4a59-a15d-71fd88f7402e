﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeesDetailTahsil : EntityBase, IEntity, IDetailEntity
    {
        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual long TahsilId { get; set; }
        public virtual string Tahsil { get; set; }
        public virtual string TahsilKod { get; set; }
        public virtual string Okul { get; set; }
        public virtual string OkulKod { get; set; }
        public virtual string Fakulte { get; set; }
        public virtual string FakulteKod { get; set; }
        public virtual string Bolum { get; set; }
        public virtual string BolumKod { get; set; }
        public virtual short GirisYili { get; set; }
        public virtual short MezuniyetYili { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionId { get; set; }
    }
}