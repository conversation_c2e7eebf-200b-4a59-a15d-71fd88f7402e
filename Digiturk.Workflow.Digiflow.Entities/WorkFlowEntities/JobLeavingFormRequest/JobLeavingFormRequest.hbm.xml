﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="JobLeavingFormRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_LEAVING_FORM_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="JOB_LEAVING_FORM_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FullName" column="FULLNAME" />
    <property name="LeavingDate" column="LEAVING_DATE" />
    <property name="Division" column="DIVISION" />
    <property name="Department" column="DEPARTMENT" />
    <property name="Unit" column="UNIT" />
    <property name="ManagerId" column="MANAGER_ID" />
    <property name="Location" column="LOCATION" />
    <property name="PersonalTypeId" column="PERSONAL_TYPE_ID" />
    <property name="Floor" column="FLOOR" />
    <property name="DonePoldy" column="HAS_DONE_POLDY" />
    <property name="DoneInsuranceQuit" column="DONE_INSURANCE_QUIT" />
    <property name="DoneIntranetQuit" column="DONE_INTRANET_QUIT" />
    <property name="DoneSodexoQuit" column="DONE_SODEXO_QUIT" />
    <property name="DoneLifeInsuranceQuit" column="DONE_LIFE_INSURANCE_QUIT" />
    <property name="TakenIdCard" column="TAKEN_ID_CARD" />
    <property name="TakenCompanyCar" column="TAKEN_COMPANY_CAR" />
    <property name="TakenCarryMatic" column="TAKEN_CARRY_MATIC" />
    <property name="TakenGSMCard" column="TAKEN_GSM_CARd" />
    <property name="TakenMobilePhone" column="TAKEN_MOBILE_PHONE" />
    <property name="IsEveryThingOk" column="IS_EVERYTHING_OK" />
    
    <property name="SalaryAdvanceExist" column="SALARYADVANCE_EXIST" />
    <property name="SalaryAdvanceAmount" column="SALARYADVANCE_AMOUNT" />
    <property name="JobAdvanceExist" column="JOBADVANCE_EXIST" />
    <property name="JobAdvanceAmount" column="JOBADVANCE_AMOUNT" />
    <property name="LoanRePaymentExist" column="LOANREPAYMENT_EXIST" />
    <property name="LoadRePaymentAmount" column="LOADREPAYMENT_AMOUNT" />
    <property name="CostPaymentExist" column="COSTPAYMENT_EXIST" />
    <property name="CostPaymentAmount" column="COSTPAYMENT_AMOUNT" />
    <property name="EmployeeDiscountRemoved" column="EMPLOYEEDISCOUNT_REMOVED" />
    <property name="TestDevicesTracked" column="TEST_DEVICES_TRACKED" />
    <property name="HayatSigortasiCikisi" column="HAYAT_SIGORTASI_CIKISI" />
    <property name="BireyselEmeklilikCikisi" column="BIREYSEL_EMEKLILIK_CIKISI" />
    <property name="YakaKartiGeriAlindi" column="YAKA_KARTI_GERI_ALINDI" />
    <!--
    SalaryAdvanceExist 		SALARYADVANCE_EXIST 
    SalaryAdvanceAmount		SALARYADVANCE_AMOUNT
    JobAdvanceExist		JOBADVANCE_EXIST
    JobAdvanceAmount		JOBADVANCE_AMOUNT
    LoanRePaymentExist		LOANREPAYMENT_EXIST
    LoadRePaymentAmount		LOADREPAYMENT_AMOUNT
    CostPaymentExist		COSTPAYMENT_EXIST
    CostPaymentAmount		COSTPAYMENT_AMOUNT
    EmployeeDiscountRemoved		EMPLOYEEDİSCOUNT_REMOVED
    TestDevicesTracked		TEST_DEVICES_TRACKED

      -->
    <property name="TakenTechComputer" column="TAKEN_TECH_COMPUTER" />
    <property name="TakenTechDeskPhone" column="TAKEN_TECH_DESK_PHONE" />
    <property name="TakenTechMailAccount" column="TAKEN_TECH_MAIL_ACCOUNT" />
    <property name="TakenTechUsername" column="TAKEN_TECH_USERNAME" />
    <property name="TakenTechOutlook" column="TAKEN_TECH_OUTLOOK" />
    <property name="TakenTechOtherStaff" column="TAKEN_TECH_OTHER_STAFF" />
    <property name="TakenTechOtherStaffDesc" column="TAKEN_TECH_OTHER_STAFF_DESC" />
    <property name="TakenTechBrokenProduct" column="TAKEN_TECH_BROKEN_PRD" />
    <property name="TakenTechBrokenProductDesc" column="TAKEN_TECH_BROKEN_PRD_DESC" />
    <property name="TakenMaterial" column="TAKEN_MATERIAL" />
    
    <!--
      TAKEN_TECH_COMPUTER          NUMBER,
  TAKEN_TECH_DESK_PHONE        NUMBER,
  TAKEN_TECH_MAIL_ACCOUNT      NUMBER,
  TAKEN_TECH_USERNAME          NUMBER,
  TAKEN_TECH_OUTLOOK           NUMBER,
  TAKEN_TECH_OTHER_STAFF       NUMBER,
  TAKEN_TECH_OTHER_STAFF_DESC  VARCHAR(1000),
  TAKEN_TECH_BROKEN_PRD        NUMBER,
  TAKEN_TECH_BROKEN_PRD_DESC   VARCHAR(1000)
    -->
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>