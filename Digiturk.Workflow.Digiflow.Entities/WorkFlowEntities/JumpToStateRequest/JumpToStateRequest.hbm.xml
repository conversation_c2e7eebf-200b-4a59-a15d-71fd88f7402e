<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="JumpToStateRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JUMP_TO_STATE_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="JUMP_TO_STATE_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FlowName" column="FLOW_NAME" />
    <property name="StateName" column="STATE_NAME" />
    <property name="FlowInstanceID" column="FLOW_INSTANCE_ID" />
    <property name="StateInstanceID" column="STATE_INSTANCE_ID" />
    <property name="Link" column="LINK" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="Description" column="DESCRIPTION" />
  </class>
</hibernate-mapping>