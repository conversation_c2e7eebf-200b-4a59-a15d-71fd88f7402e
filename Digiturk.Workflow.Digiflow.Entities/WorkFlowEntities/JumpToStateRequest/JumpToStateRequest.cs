using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JumpToStateRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string FlowName { get; set; }
        public virtual string StateName { get; set; }
        public virtual long FlowInstanceID { get; set; }
        public virtual long StateInstanceID { get; set; }
        public virtual string Link { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Description { get; set; }

        #endregion Entity Properties
    }
}