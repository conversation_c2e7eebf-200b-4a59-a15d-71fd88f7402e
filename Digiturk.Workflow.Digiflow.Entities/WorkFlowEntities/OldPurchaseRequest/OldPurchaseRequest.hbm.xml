﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="OldPurchaseRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_OLD_PURCHASE_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="OLD_PURCHASE_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FormId" column="FORM_ID" />
    <property name="UserName" column="USER_NAME" />
    <property name="Subject" column="SUBJECT" />
    <property name="PurchaseCreated" column="PURCHASE_CREATED" />
    <property name="Status" column="STATUS" />
    <property name="BudgetCodes" column="BUDGET_CODES" />
    <property name="Amount" column="AMOUNT" />
    <property name="Currency" column="CURRENCY" />
    <property name="ConsumedAmount" column="CONSUMED_AMOUNT" />
    <property name="AmountOpt" column="AMOUNT_OPT" />
    <property name="CurrentAmount" column="CURRENT_AMOUNT" />
    <property name="Type" column="TYPE" />
    <property name="PurchaseCreatedBy" column="PURCHASE_CREATED_BY" />
    <property name="PurchaseInnerOrOuter" column="PURCHASE_INNER_OR_OUTER" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>