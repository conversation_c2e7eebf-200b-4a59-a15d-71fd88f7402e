﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="RotaMacroDelDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ROTA_MACRO_DEL_DETAIL_RQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="PersonelID" column="PERSONEL_ID" />
    <property name="PersonelUserName" column="PERSONEL_USER_NAME" />
    <property name="RotaGroupNo" column="ROTA_GROUP_NO" />
    <property name="RotaReportNo" column="ROTA_REPORT_NO" />
    <property name="MacroReportName" column="MACRO_REPORT_NAME" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="RotaGroup" column="ROTA_GROUP" />
    <property name="RotaReport" column="ROTA_REPORT" />
    <property name="RaporTip" column="RAPOR_TIP" />
    <property name="NameSurname" column="NAME_SURNAME" />
    <property name="WorkflowId" column="WORKFLOW_ID" />

  </class>
</hibernate-mapping>