<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="JobEntranceFormAcilDurum, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE_ACILDURUM" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_ID" />

        <property name="PoldySira" column="POLDYSIRA" />
        <property name="AdiSoyadi" column="ADISOYADI" />
        <property name="IsTel" column="ISTEL" />
        <property name="EvTel" column="EVTEL" />
        <property name="GSM" column="GSM" />
        <property name="YakinlikDerecesiId" column="YAKINLIKDERECESI" />
        <property name="Adres" column="ADRES" />
        <property name="EPosta" column="EPOSTA" />

        <property name="Status" column="STATUS" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="Version_ID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>