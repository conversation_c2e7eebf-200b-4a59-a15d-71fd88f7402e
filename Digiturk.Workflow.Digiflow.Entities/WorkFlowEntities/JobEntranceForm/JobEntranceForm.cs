﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobEntranceForm : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }

        public virtual string AdiSoyadi { get; set; }
        public virtual string SicilNo { get; set; }
        public virtual int Hr_Users_ID { get; set; }
        public virtual DateTime IseBaslamaTarihi { get; set; }
        public virtual long UnvanId { get; set; }
        public virtual string BagliOlduguFirmaPoldyId { get; set; }
        public virtual long BagliOlduguFirmaHRId { get; set; }
        public virtual bool OnayYetkisiVarmi { get; set; }
        public virtual string BagliOlacagiKisi { get; set; }
        public virtual bool Vadiyalimi { get; set; }
        public virtual bool ArgePersonelimi { get; set; }
        public virtual string TitleId { get; set; }
        public virtual string IlId { get; set; }
        public virtual long LokasyonId { get; set; }
        public virtual string LokasyonAdi { get; set; }
        public virtual long BulunduguKatId { get; set; }
        public virtual string BulunduguKatAdi { get; set; }

        public virtual bool BilgisayarVerilecekmi { get; set; }
        public virtual long BilgisayarTipiId { get; set; }
        public virtual bool MasaTelefonuVerilecekmi { get; set; }
        public virtual bool YakaKartiVerilecekmi { get; set; }
        public virtual bool SirketAraciVerilecekmi { get; set; }
        public virtual bool TasitmatikVerilecekmi { get; set; }
        public virtual bool OtoparkVerilecekmi { get; set; }
        public virtual bool KartvizitVerilecekmi { get; set; }
        public virtual bool CicekVerilecekmi { get; set; }
        public virtual bool IseBaslamaKitiVerilecekmi { get; set; }
        public virtual bool CepTelefonuVerilecekmi { get; set; }
        public virtual bool CepTelefonuSureklimi { get; set; }
        public virtual long CepTelefonuSuresi { get; set; }
        public virtual string CepTelefonuTalepNedeni { get; set; }
        public virtual bool GSMHatVerilecekmi { get; set; }
        public virtual bool GSMHatSureklimi { get; set; }
        public virtual long GSMHatSuresi { get; set; }
        public virtual string GSMHatTalepNedeni { get; set; }
        public virtual long GSMHatTarifeId { get; set; }
        public virtual bool ModemVerilecekmi { get; set; }
        public virtual bool ModemSureklimi { get; set; }
        public virtual long ModemSuresi { get; set; }
        public virtual string ModemTalepNedeni { get; set; }
        public virtual long ModemKota { get; set; }

        public virtual bool KullaniciAdiAcildi { get; set; }
        public virtual string KullaniciAdi { get; set; }
        public virtual bool MasaTelefonuAtandi { get; set; }
        public virtual string MasaTelefonu { get; set; }
        public virtual bool EMailAccountAtandi { get; set; }
        public virtual string EMailAccount { get; set; }
        public virtual bool EMailGroupAtandi { get; set; }
        public virtual bool ST_HD1Tamamlandi { get; set; }

        public virtual bool BilgisayarAtandi { get; set; }
        public virtual bool ZimmetImzalandi { get; set; }
        public virtual bool ST_HD2Tamamlandi { get; set; }

        public virtual bool SGKIseGirisYapildi { get; set; }
        public virtual bool BankaKartiBasvuru { get; set; }
        public virtual bool SaglikSigortasiKayit { get; set; }
        public virtual bool SodexoKayit { get; set; }
        public virtual string TCKN { get; set; }
        public virtual DateTime DogumTarihi { get; set; }
        public virtual string VKN { get; set; }
        public virtual string OzelCepNo { get; set; }
        public virtual string SGKN { get; set; }
        public virtual string MedeniDurum { get; set; }
        public virtual string MezunOlduguOkul { get; set; }
        public virtual string Cinsiyet { get; set; }
        public virtual string MezunOlduguYil { get; set; }
        public virtual string KanGrubuId { get; set; }
        public virtual string KanGrubuAdi { get; set; }
        public virtual string KullaniciGrubu { get; set; }
        public virtual string BedenOlcusu { get; set; }
        public virtual string ButceGrubu { get; set; }
        public virtual string Adres { get; set; }

        public virtual string EsAdi { get; set; }
        public virtual string EsTCKN { get; set; }
        public virtual DateTime EsDogum { get; set; }
        public virtual bool EsCalisiyor { get; set; }

        public virtual bool YakaKartiBasvuru { get; set; }
        public virtual string Resim { get; set; }
        public virtual bool ST_IKTamamlandi { get; set; }

        public virtual bool GSMHatAtandi { get; set; }
        public virtual string GSMNo { get; set; }
        public virtual bool ModemAtandi { get; set; }
        public virtual bool ST_GSMTamamlandi { get; set; }

        public virtual bool KartvizitBasvuru { get; set; }
        public virtual bool ST_KARTVIZITTamamlandi { get; set; }

        public virtual bool CicekGonderilecek { get; set; }
        public virtual bool ST_CICEKTamamlandi { get; set; }

        public virtual bool CepTelefonuTahsisi { get; set; }
        public virtual bool ST_CEPTELEFONUTamamlandi { get; set; }

        public virtual bool SirketAracTahsisi { get; set; }
        public virtual bool TasitmatikTahsisi { get; set; }
        public virtual bool OtoparkYerAyarlama { get; set; }
        public virtual bool ST_ARACTamamlandi { get; set; }

        public virtual bool YakaKartiCikartildi { get; set; }
        public virtual bool GuvenlikBilgilendirildi { get; set; }
        public virtual bool ST_YAKAKARTITamamlandi { get; set; }

        public virtual bool IseBaslamaKitiGonderildi { get; set; }
        public virtual bool ST_ISEBASLAMAKITITamamlandi { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime Last_Updated { get; set; }
        public virtual long Created_By { get; set; }
        public virtual long Last_Updated_By { get; set; }
        public virtual long Version_ID { get; set; }

        #endregion Entity Properties
    }
}