<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="JobEntranceFormCocuk, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE_COCUK" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_ID" />

        <property name="PoldyId" column="POLDYID" />
        <property name="AdiSoyadi" column="ADISOYADI" />
        <property name="TCKN" column="TCKN" />
        <property name="DogumTarihi" column="DOGUMTARIHI" />
        <property name="Cinsiyet" column="CINSIYET" />
        <property name="TahsilDurumuID" column="TAHSILDURUMU" />

        <property name="Status" column="STATUS" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="Version_ID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>