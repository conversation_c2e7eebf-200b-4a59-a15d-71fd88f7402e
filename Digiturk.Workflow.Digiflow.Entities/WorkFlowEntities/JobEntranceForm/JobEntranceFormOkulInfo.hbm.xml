<?xml version="1.0" encoding="utf-8" ?>
    <hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
      <class name="JobEntranceFormOkulInfo, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_ENTRANCE_OKUL" schema="DT_WORKFLOW">
        <id name="RequestId" type="long" column="ID">
          <generator class="trigger-identity"></generator>
        </id>
        <property name="RelatedRequestID" column="RELATED_ID" />

        <property name="TahsilId" column="TAHSIL_ID" />
        <property name="Tahsil" column="TAHSIL" />
        <property name="TahsilKod" column="TAHSIL_KOD" />
        <property name="Okul" column="OKUL" />
        <property name="OkulKod" column="OKUL_KOD" />
        <property name="Fakulte" column="FAKULTE" />
        <property name="FakulteKod" column="FAKULTE_KOD" />
        <property name="Bolum" column="BOLUM" />
        <property name="BolumKod" column="BOLUM_KOD" />
        <property name="GirisYili" column="GIRIS_YILI" />
        <property name="MezuniyetYili" column="MEZUNIYET_YILI" />

        <property name="Status" column="STATUS" />
        <property name="Created" column="CREATED" />
        <property name="LastUpdated" column="LAST_UPDATED" />
        <property name="CreatedBy" column="CREATED_BY" />
        <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
        <property name="Version_ID" column="VERSION_ID" />
      </class>
    </hibernate-mapping>