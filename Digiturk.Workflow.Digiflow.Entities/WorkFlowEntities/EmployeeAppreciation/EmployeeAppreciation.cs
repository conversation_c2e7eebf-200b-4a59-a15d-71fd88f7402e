﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class EmployeeAppreciation : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; } 
        public virtual long? CandidateId { get; set; }
        public virtual string CandidateName { get; set; }
        public virtual string CandidateDepartment { get; set; }
        public virtual string CandidatePosition { get; set; }
        public virtual string AwardCategories { get; set; }
        public virtual string ExampleCase { get; set; }
        public virtual int? BehaviorValueAlignmentScore { get; set; }
        public virtual int? BehaviorConcretenessScore { get; set; }
        public virtual int? BehaviorImpactScore { get; set; }
        public virtual int? RepeatabilityScore { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion
    }

}
