﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ChannelRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CHANNEL_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CHANNEL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="ChannelType01" column="CHANNEL_TYPE_01" />
    <property name="ChannelName" column="CHANNEL_NAME" />
    <property name="ChannelNo" column="CHANNEL_NO" />
    <property name="PlanningPublishDate" column="PLANNING_PUBLISH_DATE" />
    <property name="PresentationCaptureEntryDate" column="PRSNT_CAPTURE_ENTRY_DATE" />
    <property name="PublishLanguage" column="PUBLISH_LANGUAGE" />
    <property name="Language01" column="LANGUAGE_01" />
    <property name="Language02" column="LANGUAGE_02" />
    <property name="Password" column="PASSWORD" />
    <property name="ChannelProperty" column="CHANNEL_PROPERTY" />
    <property name="ChannelType02" column="CHANNEL_TYPE_02" />
    <property name="ChannelTypeOther02" column="CHANNEL_TYPE_OTHER_02" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>