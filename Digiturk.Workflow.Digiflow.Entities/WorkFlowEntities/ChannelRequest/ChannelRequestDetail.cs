﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ChannelRequestDetail : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        /// <summary>
        /// RequestId
        /// </summary>
        public virtual long RequestId { get; set; }

        /// <summary>
        /// Parent Entity Request Id
        /// </summary>
        public virtual long RelatedRequestID { get; set; }

        /// <summary>
        /// Form Name
        /// </summary>
        public virtual string FormName { get; set; }

        /// <summary>
        /// Object Name
        /// </summary>
        public virtual string Name { get; set; }

        /// <summary>
        /// Object Value
        /// </summary>
        public virtual string Value { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}