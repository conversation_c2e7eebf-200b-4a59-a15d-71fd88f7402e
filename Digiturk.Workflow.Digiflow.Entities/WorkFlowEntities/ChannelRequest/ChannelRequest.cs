﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ChannelRequest : EntityBase, IEntity
    {
        #region Entity Properties

        /// <summary>
        /// RequestId
        /// </summary>
        public virtual long RequestId { get; set; }

        /// <summary>
        /// Kanal Türü
        /// </summary>
        public virtual String ChannelType01 { get; set; }

        /// <summary>
        /// Kanal Adi
        /// </summary>
        public virtual String ChannelName { get; set; }

        /// <summary>
        /// Kanal No
        /// </summary>
        public virtual String ChannelNo { get; set; }

        /// <summary>
        /// Planlanan Yayin <PERSON> ve <PERSON>ati
        /// </summary>
        public virtual DateTime PlanningPublishDate { get; set; }

        /// <summary>
        /// Tanıtım Capt. Giris Tarihi ve Saati
        /// </summary>
        public virtual DateTime PresentationCaptureEntryDate { get; set; }

        /// <summary>
        /// Yayin <PERSON>
        /// </summary>
        public virtual String PublishLanguage { get; set; }

        /// <summary>
        /// Dil-1
        /// </summary>
        public virtual String Language01 { get; set; }

        /// <summary>
        /// Dil-2
        /// </summary>
        public virtual String Language02 { get; set; }

        /// <summary>
        /// Sifre
        /// </summary>
        public virtual String Password { get; set; }

        /// <summary>
        /// RTUK Record
        /// </summary>
        public virtual String RtukRecord { get; set; }

        /// <summary>
        /// Ozellik
        /// </summary>
        public virtual String ChannelProperty { get; set; }

        /// <summary>
        /// Tur
        /// </summary>
        public virtual String ChannelType02 { get; set; }

        /// <summary>
        /// Tur-Other
        /// </summary>
        public virtual String ChannelTypeOther02 { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}