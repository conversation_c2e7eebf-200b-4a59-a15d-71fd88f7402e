﻿using Digiturk.Workflow.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SatinAlmaSurecleriOnayRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual string Talep_Tipi { get; set; }
        public virtual string Pr_Number { get; set; }
        public virtual string Sirket_Adi { get; set; }

        public virtual decimal Pr_Tutari_Usd { get; set; }

        public virtual string Aciklama { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }

    }
}
