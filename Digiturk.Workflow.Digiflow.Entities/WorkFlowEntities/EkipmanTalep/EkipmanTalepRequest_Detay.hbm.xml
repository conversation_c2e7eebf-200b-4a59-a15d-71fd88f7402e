﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="EkipmanTalepRequest_Detay , Digiturk.Workflow.Digiflow.Entities" table="WF_DF_EKIPMAN_TALEP_FORMU_DETAY" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="EKIPMAN_TALEP_FORM_ID" />
    <property name="Name_Surname" column="NAME_SURNAME" />
    <property name="Bolge" column="BOLGE" />
    <property name="Il" column="IL" />
    <property name="Yts_Adi" column="YTS_ADI" />
    <property name="Depo_Kodu" column="DEPO_KODU" />
    <property name="Ekipman_Secimi" column="EKIPMAN_SECIMI" />
    <property name="Ekipman_Turu" column="EKIPMAN_TURU" />
    <property name="Ekipman_Adet" column="EKIPMAN_ADET" />
    <property name="Aciklama" column="ACIKLAMA" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>
