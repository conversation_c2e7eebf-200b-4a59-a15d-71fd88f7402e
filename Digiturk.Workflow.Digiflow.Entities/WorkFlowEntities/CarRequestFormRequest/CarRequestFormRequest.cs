﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CarRequestFormRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual string FullName { get; set; }
        public virtual DateTime RequestDate { get; set; }
        public virtual DateTime LeavingDate { get; set; }
        public virtual DateTime ReturnDate { get; set; }
        public virtual string RequestReason { get; set; }
        public virtual long RegionTypeId { get; set; }
        public virtual long CarPlateId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}