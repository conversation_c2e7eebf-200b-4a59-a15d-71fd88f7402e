﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CarRequestFormRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CAR_FORM_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CAR_FORM_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FullName" column="FULLNAME" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="LeavingDate" column="LEAVING_DATE" />
    <property name="ReturnDate" column="RETURN_DATE" />
    <property name="RequestReason" column="REQUEST_REASON" />
    <property name="RegionTypeId" column="REGION_TYPE_ID" />
    <property name="CarPlateId" column="CAR_PLATE_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>