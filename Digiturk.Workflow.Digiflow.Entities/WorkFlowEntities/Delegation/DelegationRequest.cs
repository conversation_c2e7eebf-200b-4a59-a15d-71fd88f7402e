﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class DelegationRequest : EntityBase, IEntity
    {
        public virtual long RequestId { get; private set; }
        public virtual string DelegationComment { get; set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long DelegatedLoginId { get; set; }
        public virtual long WorkflowDefId { get; set; }
        public virtual DateTime StartTime { get; set; }
        public virtual DateTime EndTime { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string WorkFlowIds { get; set; }
    }
}