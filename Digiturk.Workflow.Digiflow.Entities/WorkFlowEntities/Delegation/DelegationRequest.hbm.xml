﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="DelegationRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_DELEGATION_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="DELEGATION_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="WorkflowDefId" column="WORKFLOW_DEF_ID" />
    <property name="StartTime" column="START_TIME" />
    <property name="EndTime" column="END_TIME" />
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="DelegatedLoginId" column="DELEGATED_LOGIN_ID" />
    <property name="DelegationComment" column="DELEGATION_COMMENT" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="WorkFlowIds" column="WORKFLOW_IDS" />
  </class>
</hibernate-mapping>