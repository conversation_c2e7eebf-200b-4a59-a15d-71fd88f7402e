﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ConsultantJobEntranceFormRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual string FullName { get; set; }
        public virtual DateTime StartDate { get; set; }
        public virtual string LeavedPersonnel { get; set; }
        public virtual string CompanyId { get; set; }
        public virtual string OtherCompany { get; set; }
        public virtual long ManagerId { get; set; }
        public virtual long BudgetId { get; set; }
        public virtual string Responsibility { get; set; }
        public virtual string JobDescription { get; set; }
        public virtual string NeedingQualification { get; set; }
        public virtual string FringeBenefits { get; set; }
        public virtual string WhyHired { get; set; }
        public virtual string MailGroupName { get; set; }
        public virtual long WillHaveComputer { get; set; }
        public virtual long ComputerTypeId { get; set; }
        public virtual long WillHaveDeskPhone { get; set; }
        public virtual long WillHaveIdCard { get; set; }
        public virtual long WillHaveCompanyCar { get; set; }
        public virtual long WillHaveBenzine { get; set; }
        public virtual long WillHaveCarPark { get; set; }
        public virtual long WillHaveGSMCard { get; set; }
        public virtual long WillHaveMobilePhone { get; set; }
        public virtual long HasDocumentList { get; set; }
        public virtual string DosyaEkleri { get; set; }

        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}