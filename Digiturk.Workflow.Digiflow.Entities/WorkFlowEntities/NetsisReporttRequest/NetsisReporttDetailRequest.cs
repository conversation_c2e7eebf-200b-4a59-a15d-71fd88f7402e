﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class NetsisReporttDetailRequest : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual string CariKod { get; set; }
        public virtual string CariIsim { get; set; }
        public virtual string GrupKod { get; set; }
        public virtual string GrupIsim { get; set; }
        public virtual decimal Onceki { get; set; }
        public virtual decimal P1 { get; set; }
        public virtual decimal P2 { get; set; }
        public virtual decimal P3 { get; set; }
        public virtual decimal P4 { get; set; }
        public virtual decimal P5 { get; set; }
        public virtual decimal P6 { get; set; }
        public virtual decimal P7 { get; set; }
        public virtual decimal P8 { get; set; }
        public virtual decimal Toplam { get; set; }
        public virtual decimal VadesiGelenBorc { get; set; }
        public virtual decimal DepStopaj { get; set; }
        public virtual decimal DepTutar { get; set; }
        public virtual string DepAciklama { get; set; }
        public virtual string DepUser { get; set; }
        public virtual decimal HazTutar { get; set; }
        public virtual string <PERSON>z<PERSON><PERSON>klama { get; set; }
        public virtual string HazUser { get; set; }
        public virtual string CariIban { get; set; }
        public virtual string FirmaHesapKod { get; set; }
        public virtual string DovizTip { get; set; }
        public virtual string DokumanLink { get; set; }
        public virtual string IslemTip { get; set; }
        public virtual string DpOnayDurum { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string Sirket { get; set; }
        public virtual string Yurtdisimi { get; set; }
        public virtual string DepKontrol { get; set; }
        public virtual string HazCari { get; set; }
        public virtual DateTime HazOdemeTarih { get; set; }
        public virtual long IncKeyno { get; set; }
        public virtual decimal TlVadeBorc { get; set; }
        public virtual decimal TlToplam { get; set; }

        #endregion Entity Properties
    }
}