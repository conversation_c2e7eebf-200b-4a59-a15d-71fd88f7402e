﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="BiFikrimVar,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_BI_FIKRIM_VAR" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="BI_FIKRIM_VAR_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Subject" column="SUBJECT" />
    <property name="SuggestionDetail" column="SUGGUESTION_DETAIL" />
    <property name="Benefit" column="BENEFIT" />
    <property name="Location" column="LOCATION" />
    <property name="Suggester" column="SUGGESTER" />
    <property name="SuggesterFullname" column="SUGGESTER_FULLNAME" />
    <property name="CreatorFullname" column="CREATOR_FULLNAME" />
    <property name="SuggestersTeam" column="SUGGESTERS_TEAM" />
    <property name="PlanDate" column="PLAN_DATE" />
    <property name="ExecutionDate" column="EXECUTION_DATE" />
    <property name="DelegationNote" column="DELEGATION_NOTE" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>