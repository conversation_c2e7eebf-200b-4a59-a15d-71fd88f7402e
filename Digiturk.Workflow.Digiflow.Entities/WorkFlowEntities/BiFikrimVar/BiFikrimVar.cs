﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class BiFikrimVar : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual string Subject { get; set; }
        public virtual string SuggestionDetail { get; set; }
        public virtual string Benefit { get; set; }
        public virtual string Location { get; set; }
        public virtual string Suggester { get; set; }
        public virtual string SuggesterFullname { get; set; }
        public virtual string CreatorFullname { get; set; }
        public virtual string SuggestersTeam { get; set; }
        public virtual DateTime? PlanDate { get; set; }
        public virtual DateTime? ExecutionDate { get; set; }
        public virtual string DelegationNote { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        #endregion
    }
}