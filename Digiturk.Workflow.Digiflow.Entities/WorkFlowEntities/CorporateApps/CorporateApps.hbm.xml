﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CorporateApps,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CORPORATE_APPS" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CORPORATE_APPS_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>

    <property name="KULLANIM_AMACI" column="KULLANIM_AMACI" />
    <property name="EKLI_DOSYA" column="EKLI_DOSYA" />
    <property name="DETAYLI_ACIKLAMA" column="DETAYLI_ACIKLAMA" />
    <property name="TESLIM_TALEP_TARIHI" column="TESLIM_TALEP_TARIHI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>