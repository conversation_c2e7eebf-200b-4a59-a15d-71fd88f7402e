﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class JobQuitFormRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long RequestOwnerId { get; set; }
        public virtual long PersonnelId { get; set; }
        public virtual long PersonnelTypeId { get; set; }
        public virtual string Location { get; set; }
        public virtual string Floor { get; set; }
        public virtual DateTime QuitDate { get; set; }
        public virtual string VolutaryQuit { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        #endregion
    }
}
