﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="JobQuitFormRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_QUIT_REQUEST" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="JOB_QUIT_REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="RequestOwnerId" column="REQUEST_OWNER_ID" />
		<property name="PersonnelId" column="PERSONNEL_ID" />
		<property name="PersonnelTypeId" column="PERSONNEL_TYPE_ID" />
		<property name="Location" column="LOCATION" />
		<property name="Floor" column="FLOOR" />
		<property name="QuitDate" column="QUIT_DATE" />
		<property name="VolutaryQuit" column="VOLUNTARY_QUIT" />
		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>


