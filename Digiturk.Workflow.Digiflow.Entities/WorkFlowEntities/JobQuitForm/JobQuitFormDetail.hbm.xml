﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities"
                   namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="JobQuitFormDetail,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_JOB_QUIT_DETAIL" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="JOB_QUIT_DETAIL_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="RelatedRequestID" column="RELATED_REQUEST_ID" />

		<property name="QUIT_POLDY" column="QUIT_POLDY" />
		<property name="QUIT_SOCIAL_INSURANCE" column="QUIT_SOCIAL_INSURANCE" />
		<property name="QUIT_INTRANET" column="QUIT_INTRANET" />
		<property name="QUIT_SODEXO" column="QUIT_SODEXO" />
		<property name="QUIT_HEALTH_INSURANCE" column="QUIT_HEALTH_INSURANCE" />
		<property name="QUIT_LIFE_INSURENCE" column="QUIT_LIFE_INSURENCE" />
		<property name="QUIT_PRIVATE_PENSION" column="QUIT_PRIVATE_PENSION" />
		<property name="RETRIEVAL_PERSONNEL_CARD" column="RETRIEVAL_PERSONNEL_CARD" />
		<property name="RETRIEVAL_COMPANY_VEHICLE" column="RETRIEVAL_COMPANY_VEHICLE" />
		<property name="RETRIEVAL_TRANSPORTMATIC" column="RETRIEVAL_TRANSPORTMATIC" />
		<property name="RETRIEVAL_GSM_LINE" column="RETRIEVAL_GSM_LINE" />
		<property name="RETRIEVAL_MOBILE_PHONE" column="RETRIEVAL_MOBILE_PHONE" />
		<property name="RETRIEVAL_NOT_EXISTING" column="RETRIEVAL_NOT_EXISTING" />
		<property name="RETRIEVAL_PERSONEL_COMPUTER" column="RETRIEVAL_PERSONEL_COMPUTER" />
		<property name="CLOSURE_TELEPHONE_LINE" column="CLOSURE_TELEPHONE_LINE" />
		<property name="CLOSURE_MAIL_ACCOUNT" column="CLOSURE_MAIL_ACCOUNT" />
		<property name="CLOSURE_USER_ACCOUNT" column="CLOSURE_USER_ACCOUNT" />
		<property name="CLOSURE_OUTLOOK_USER" column="CLOSURE_OUTLOOK_USER" />
		<property name="EXISTS_OTHER_DEBITTED_HARDWARE" column="EXISTS_OTHER_DEBITTED_HARDWARE" />
		<property name="OTHER_DEBITTED_HARDWARES" column="OTHER_DEBITTED_HARDWARES" />
		<property name="EXIST_BROKEN_HARDWARES" column="EXIST_BROKEN_HARDWARES" />
		<property name="BROKEN_HARDWARES" column="BROKEN_HARDWARES" />
		<property name="SECURITY_INFORMED" column="SECURITY_INFORMED" />
		<property name="RETRIEVAL_DESKTOP_EQUIPMENTS" column="RETRIEVAL_DESKTOP_EQUIPMENTS" />
		<property name="EXISTS_DESKTOP_EQUIPMENTS" column="EXISTS_DESKTOP_EQUIPMENTS" />
		<property name="EXISTS_JOB_ADVANCE" column="EXISTS_JOB_ADVANCE" />
		<property name="AMOUNT_JOB_ADVANCE" column="AMOUNT_JOB_ADVANCE" />
		<property name="EXISTS_SALARY_ADVANCE" column="EXISTS_SALARY_ADVANCE" />
		<property name="AMOUNT_SALARY_ADVANCE" column="AMOUNT_SALARY_ADVANCE" />
		<property name="EXISTS_LOAN_PAYBACK" column="EXISTS_LOAN_PAYBACK" />
		<property name="AMOUNT_LOAN_PAYBACK" column="AMOUNT_LOAN_PAYBACK" />
		<property name="EXISTS_COST_PAYMENT" column="EXISTS_COST_PAYMENT" />
		<property name="AMOUNT_COST_PAYMENT" column="AMOUNT_COST_PAYMENT" />
		<property name="BLOCKED_EMPLOYEE_DISCOUNT" column="BLOCKED_EMPLOYEE_DISCOUNT" />
		<property name="TEST_DEVICES_TRACKED" column="TEST_DEVICES_TRACKED" />
		<property name="INSAN_KAYNAKLARI_DOLDURAN" column="INSAN_KAYNAKLARI_DOLDURAN" />
		<property name="INSAN_KAYNAKLARI_DOLUM_TARIH" column="INSAN_KAYNAKLARI_DOLUM_TARIH" />
		<property name="IDARI_ISLER_DOLDURAN" column="IDARI_ISLER_DOLDURAN" />
		<property name="IDARI_ISLER_DOLUM_TARIH" column="IDARI_ISLER_DOLUM_TARIH" />
		<property name="YARDIM_MASASI_DOLDURAN" column="YARDIM_MASASI_DOLDURAN" />
		<property name="YARDIM_MASASI_DOLUM_TARIH" column="YARDIM_MASASI_DOLUM_TARIH" />
		<property name="GUVENLIK_DOLDURAN" column="GUVENLIK_DOLDURAN" />
		<property name="GUVENLIK_DOLUM_TARIHI" column="GUVENLIK_DOLUM_TARIHI" />
		<property name="KIRTASIYE_DOLDURAN" column="KIRTASIYE_DOLDURAN" />
		<property name="KIRTASIYE_DOLUM_TARIH" column="KIRTASIYE_DOLUM_TARIH" />
		<property name="MUHASEBE_DOLDURAN" column="MUHASEBE_DOLDURAN" />
		<property name="MUHASEBE_DOLUM_TARIH" column="MUHASEBE_DOLUM_TARIH" />
		<property name="PAZARLAMA_DOLDURAN" column="PAZARLAMA_DOLDURAN" />
		<property name="PAZARLAMA_DOLUM_TARIH" column="PAZARLAMA_DOLUM_TARIH" />
		<property name="SET_TOP_BOX_DOLDURAN" column="SET_TOP_BOX_DOLDURAN" />
		<property name="SET_TOP_BOX_DOLUM_TARIH" column="SET_TOP_BOX_DOLUM_TARIH" />
		<property name="Created" column="CREATED" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>