﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class AlternativeManagerRequest : EntityBase, IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long RequestOwnerId { get; set; }
        public virtual long PersonnelId { get; set; }
        public virtual long PersonnelRealManagerId { get; set; }
        public virtual long PersonnelDelegatedNewManagerId { get; set; }
        public virtual DateTime StartDate { get; set; }
        public virtual DateTime EndDate { get; set; }
        public virtual string RequestComment { get; set; }
        public virtual string WorkFlowDefIds { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        #endregion
    }
}
