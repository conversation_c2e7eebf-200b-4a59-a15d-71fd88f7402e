<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="CampaignRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CAMPAIGN_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="CAMPAIGN_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
     <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="OfferName" column="OFFER_NAME" />
    <property name="OfferOwner" column="OFFER_OWNER" />
    <property name="OfferCode" column="OFFER_CODE" />
    <property name="OfferType" column="OFFER_TYPE" />
    <property name="OfferBeginDate" column="OFFER_BEGIN_DATE" />
    <property name="OfferEndDate" column="OFFER_END_DATE" />
    <property name="OfferReviseDate" column="OFFER_REVISE_DATE" />
    <property name="OfferReviseReason" column="OFFER_REVISE_REASON" />
    <property name="OfferExplanation" column="OFFER_EXPLANATION" />
    <property name="IsCommitment" column="IS_COMMITMENT" />
    <property name="EquipmentSales" column="EQUIPMENT_SALES" />
    <property name="ModuleSales" column="MODULE_SALES" />
    <property name="CreditCardSales" column="CREDIT_CARD_SALES" />
    <property name="ServicesBanderol" column="SERVICES_BANDEROL" />
    <property name="TryFree" column="TRY_FREE" />
    <property name="BulkSale" column="BULKSALE" />
    <property name="Renew" column="RENEW" />
    <property name="IsEncrypted" column="IS_ENCRYPTED" />
    <property name="OutsideSetupFee" column="OUTSIDE_SETUP_FEE" />
    <property name="InsideSetupFee" column="INSIDE_SETUP_FEE" />
    <property name="DealerEarn1" column="DEALER_EARN1" />
    <property name="DealerEarn2" column="DEALER_EARN2" />
    <property name="DealerSalesBonus" column="DEALER_SALES_BONUS" />
    <property name="DealerSalesBonusNotStandard" column="DEALER_SALES_BONUS_NOTSTANDARD" />
    <property name="ActivationFee1" column="ACTIVATION_FEE1" />
    <property name="ActivationFee2" column="ACTIVATION_FEE2" />
    <property name="DealerSalesTarget" column="DEALER_SALES_TARGET" />
    <property name="SmsContent" column="SMS_CONTENT" />
    <property name="MemberComplaint" column="MEMBER_COMPLAINT" />
    <property name="ItDevelopmentNeeds" column="IT_DEVELOPMENT_NEEDS" />
    <property name="OtherRisks" column="OTHER_RISKS" />
    <property name="OfferSubscriberEffect" column="OFFER_SUBSCRIBER_EFFECT" />
    <property name="OfferRevenueEffect" column="OFFER_REVENUE_EFFECT" />
    <property name="LastAnalysis" column="LAST_ANALYSIS" />
    <property name="RequireErrorReport" column="REQUIRE_ERROR_REPORT" />
    <property name="CriteriaForSuccess" column="CRITERIA_FOR_SUCCESS" />
    <property name="PurposeOfOffer" column="PURPOSE_OF_OFFER" />
    <property name="TargetGroupDefinition" column="TARGET_GROUP_DEFINITION" />
    <property name="InstanceID" column="INSTANCE_ID" />
    <property name="RevisedInstanceID" column="REVISED_INSTANCE_ID" />
    <property name="ReviseVersion" column="REVISE_VERSION" />

    <property name="DiscountAmount" column="DISCOUNT_AMOUNT" />
    <property name="HasPleasureObligation" column="HAS_PLEASURE_OBLIGATION" />
    <property name="HasNoSetupFee" column="HAS_NO_SETUP_FEE" />
    <property name="HasNoDealerSalesBonus" column="HAS_NO_DEALER_SALES_BONUS" />
    <property name="HasNoActivationFee" column="HAS_NO_ACTIVATION_FEE" />
    <property name="ActivationInstallment" column="ACTIVATION_INSTALLMENT" />
  </class>
</hibernate-mapping>