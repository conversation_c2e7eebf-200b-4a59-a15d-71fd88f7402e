using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class CampaignRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long? CreatedBy { get; set; }
        public virtual long? LastUpdatedBy { get; set; }
        public virtual long? VersionID { get; set; }
        public virtual string OfferName { get; set; }
        public virtual string OfferOwner { get; set; }
        public virtual string OfferCode { get; set; }
        public virtual long OfferType { get; set; }
        public virtual DateTime OfferBeginDate { get; set; }
        public virtual DateTime OfferEndDate { get; set; }
        public virtual DateTime OfferReviseDate { get; set; }
        public virtual string OfferReviseReason { get; set; }
        public virtual string OfferExplanation { get; set; }
        public virtual long? IsCommitment { get; set; }
        public virtual long? EquipmentSales { get; set; }
        public virtual long? ModuleSales { get; set; }
        public virtual long? CreditCardSales { get; set; }
        public virtual long? ServicesBanderol { get; set; }
        public virtual long? TryFree { get; set; }
        public virtual long? BulkSale { get; set; }
        public virtual long? Renew { get; set; }
        public virtual long? IsEncrypted { get; set; }
        public virtual string OutsideSetupFee { get; set; }
        public virtual string InsideSetupFee { get; set; }
        public virtual string DealerEarn1 { get; set; }
        public virtual string DealerEarn2 { get; set; }
        public virtual long DealerSalesBonus { get; set; }
        public virtual string DealerSalesBonusNotStandard { get; set; }
        public virtual string ActivationFee1 { get; set; }
        public virtual string ActivationFee2 { get; set; }
        public virtual long? DealerSalesTarget { get; set; }
        public virtual string SmsContent { get; set; }
        public virtual string MemberComplaint { get; set; }
        public virtual string ItDevelopmentNeeds { get; set; }
        public virtual string OtherRisks { get; set; }
        public virtual long? OfferSubscriberEffect { get; set; }
        public virtual long? OfferRevenueEffect { get; set; }
        public virtual string LastAnalysis { get; set; }
        public virtual string RequireErrorReport { get; set; }
        public virtual string CriteriaForSuccess { get; set; }
        public virtual string PurposeOfOffer { get; set; }
        public virtual string TargetGroupDefinition { get; set; }
        public virtual string InstanceID { get; set; }
        public virtual long RevisedInstanceID { get; set; }
        public virtual long ReviseVersion { get; set; }

        public virtual string DiscountAmount { get; set; }
        public virtual long? HasPleasureObligation { get; set; }
        public virtual long? HasNoSetupFee { get; set; }
        public virtual long? HasNoDealerSalesBonus { get; set; }
        public virtual long? HasNoActivationFee { get; set; }
        public virtual string ActivationInstallment { get; set; }

        #endregion Entity Properties
    }
}