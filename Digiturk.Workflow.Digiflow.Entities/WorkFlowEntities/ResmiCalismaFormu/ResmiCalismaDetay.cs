using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ResmiCalismaDetay : EntityBase, IEntity, IDetailEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long RelatedRequestID { get; set; }
        public virtual DateTime CalismaTarih { get; set; }
        public virtual string BaslangicSaat { get; set; }
        public virtual string BitisSaat { get; set; }
        public virtual decimal CalismaSure { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }
        public virtual string GorevYeri { get; set; }

        public virtual string Aciklama { get; set; }
        #endregion Entity Properties
    }
}
