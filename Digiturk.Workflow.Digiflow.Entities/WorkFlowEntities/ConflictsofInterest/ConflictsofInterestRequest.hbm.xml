﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="ConflictsofInterestRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CONFLICTSOFINTEREST" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="REQUEST_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="PersonelId" column="PERSONEL_ID" />
		<property name="DocumentId" column="DOCUMENT_ID" />
		<property name="Name" column="NAME" />
		<property name="Position" column="POSITION" />
		<property name="StaffId" column="STAFF_ID" />
		<property name="Dept" column="DEPT" />
		<property name="IsEmployeeCompany" column="IS_EMPLOYEE_COMPANY" />		
		<property name="Created" column="CREATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>
