﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class ConflictsofInterestRequest : EntityBase, IEntity 
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long PersonelId { get; set; }
        public virtual long DocumentId { get; set; }
        public virtual string Name { get; set; }
        public virtual string Position { get; set; }
        public virtual long StaffId { get; set; }
        public virtual string Dept { get; set; }
        public virtual string IsEmployeeCompany { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}