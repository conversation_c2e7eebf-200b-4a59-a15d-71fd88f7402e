﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
	<class name="ConflictsofInterestDetailRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_CONFLICTSOFINTEREST_DET" schema="DT_WORKFLOW">
		<id name="RequestId" type="long" column="REQUEST_DETAIL_ID">
			<generator class="trigger-identity"></generator>
		</id>
		<property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
		<property name="CompanyName" column="COMPANY_NAME" />
		<property name="CrNo" column="CR_NO" />
		<property name="TypeOfActivities" column="TYPE_OF_ACTIVITIES" />	
		<property name="Created" column="CREATED" />
		<property name="CreatedBy" column="CREATED_BY" />
		<property name="LastUpdated" column="LAST_UPDATED" />
		<property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
		<property name="VersionID" column="VERSION_ID" />
	</class>
</hibernate-mapping>
