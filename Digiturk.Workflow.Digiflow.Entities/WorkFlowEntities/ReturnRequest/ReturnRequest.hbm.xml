<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ReturnRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_RETURN_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="RETURN_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="CompanyName" column="COMPANY_NAME" />
    <property name="RequestDate" column="REQUEST_DATE" />
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" />
    <property name="PurchaseRequestNo" column="PURCHASE_REQUEST_NO" />
    <property name="PurchaseOwnerNameSurname" column="PURCHASE_OWNER_NAME_SURNAME" />
    <property name="IsSupplementaryBudget" column="IS_SUPPLEMENTARY_BUDGET" />
    <property name="SupplementaryBudgets" column="SUPPLEMENTARY_BUDGETS" />
    <property name="PurchaseBudgets" column="PURCHASE_BUDGETS" />
    <property name="NotInRD" column="NOT_IN_RD" />
    <property name="Purchaser" column="PURCHASER" />
    <property name="BillSubject" column="BILL_SUBJECT" />
    <property name="ReturnBillDescription" column="RETURN_BILL_DESCRIPTION" />
    <property name="CampaignCode" column="CAMPAIGN_CODE" />
    <property name="PaymentAmount" column="PAYMENT_AMOUNT" />
    <property name="PaymentCurrency" column="PAYMENT_CURRENCY" />
    <property name="InLettering" column="IN_LETTERING" />
    <property name="ExchangeRateBuy" column="EXCHANGE_RATE_BUY" />
    <property name="ExchangeRateSell" column="EXCHANGE_RATE_SELL" />
    <property name="IsContract" column="IS_CONTRACT" />
    <property name="IsPurchase" column="IS_PURCHASE" />
    <property name="IsDeliveryForm" column="IS_DELIVERY_FORM" />
    <property name="IsDispatch" column="IS_DISPATCH" />
    <property name="DispatchDate" column="DISPACH_DATE" />
    <property name="DispatchId" column="DISPATCH_ID" />
    <property name="IsBill" column="IS_BILL" />
    <property name="BillDate" column="BILL_DATE" />
    <property name="BillId" column="BILL_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>