﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ServPersJobEntranceRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SP_JOB_ENT_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="SERV_PERS_JOB_ENTRANCE_REQUEST">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="FullName" column="FULLNAME" />
    <property name="StartDate" column="STARTDATE" />
    <property name="LeavedPersonnel" column="LEAVEDPERSONNEL" />
    <property name="CompanyId" column="COMPANY_ID" />
    <property name="OtherCompany" column="OTHER_COMPANY" />
    <property name="ManagerId" column="MANAGER_ID" />

    <property name="BudgetId" column="BUDGET_ID" />
    <property name="Responsibility" column="RESPONSIBILITY" />
    <property name="JobDescription" column="JOB_DESCRIPTION" />
    <property name="NeedingQualification" column="NEEDING_QUALIFICATION" />
    <property name="FringeBenefits" column="FRINGE_BENEFITS" />
    <property name="WhyHired" column="WHY_HIRED" />

    <property name="MailGroupName" column="MAIL_GROUP_NAME" />
    <property name="WillHaveComputer" column="WILL_HAVE_COMPUTER" />
    <property name="ComputerTypeId" column="COMPUTER_TYPE_ID" />
    <property name="WillHaveDeskPhone" column="WILL_HAVE_DESKPHONE" />
    <property name="WillHaveIdCard" column="WILL_HAVE_ID_CARD" />
    <property name="WillHaveCompanyCar" column="WILL_HAVE_COMPANY_CAR" />
    <property name="WillHaveBenzine" column="WILL_HAVE_BENZINE" />
    <property name="WillHaveCarPark" column="WILL_HAVE_CAR_PARK" />
    <property name="WillHaveGSMCard" column="WILL_HAVE_GSM_CARD" />
    <property name="WillHaveMobilePhone" column="WILL_HAVE_MOBILE_PHONE" />
    <property name="HasDocumentList" column="HAS_DOCUMENT_LIST" />
    <property name="DosyaEkleri" column="DOSYA_EKLERI" />

    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>