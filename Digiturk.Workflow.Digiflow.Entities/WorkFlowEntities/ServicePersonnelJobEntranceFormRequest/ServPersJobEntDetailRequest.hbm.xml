﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="ServPersJobEntDetailRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_SP_JOB_ENT_DETAIL_REQ" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="SP_JOB_ENT_DETAIL_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="RelatedRequestID" column="RELATED_REQUEST_ID" />
    <property name="ParticipantLoginId" column="PARTICIPANT_LOGIN_ID" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
    <property name="CompletedEvent" column="COMPLETED_EVENT" />
    <property name="CompletedEventValue" column="COMPLETED_EVENT_VALUE" />
  </class>
</hibernate-mapping>