﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="OperationalWorkRequest,Digiturk.Workflow.Digiflow.Entities" table="WF_DF_OPERATIONAL_WORK_REQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="OPER_WORK_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Documents" column="DOCUMENTS" />
    <property name="Explanation" column="EXPLANATION" />
    <property name="RequirementId" column="REQUIREMENT_ID" />
    <property name="Location" column="LOCATION" />
    <property name="Created" column="CREATED" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>