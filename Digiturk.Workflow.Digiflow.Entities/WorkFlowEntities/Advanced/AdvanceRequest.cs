using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class AdvanceRequest : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; private set; }
        public virtual long OwnerLoginId { get; set; }
        public virtual long IsWorkAdvance { get; set; }
        public virtual string CompanyType { get; set; }
        public virtual decimal AdvancePrice { get; set; }
        public virtual string AdvancePriceCurrently { get; set; }
        public virtual string AdvancePriceWritingWith { get; set; }
        public virtual long PaymentType { get; set; }
        public virtual string BankBranch { get; set; }
        public virtual string AccountNumber { get; set; }
        public virtual DateTime PaymentDate { get; set; }
        public virtual string PaymentDescription { get; set; }
        public virtual string Notes { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}