<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="AdvanceRequest, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_ADVANCEREQUEST" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ADVANCE_REQUEST_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="OwnerLoginId" column="OWNER_LOGIN_ID" type="long" />
    <property name="IsWorkAdvance" column="IS_WORK_ADVANCE" type="long" />
    <property name="CompanyType" column="COMPANY_TYPE2" type="string" />
    <property name="AdvancePrice" column="ADVANCE_PRICE" type="decimal" />
    <property name="AdvancePriceCurrently" column="ADVANCE_PRICE_CURRENTLY" type="string" />
    <property name="AdvancePriceWritingWith" column="ADVANCE_PRICE_WRITTING_WITH" type="string" />
    <property name="PaymentType" column="PAYMENT_TYPE" type="long" />
    <property name="BankBranch" column="BANK_BRANCH" type="string" />
    <property name="AccountNumber" column="ACCOUNT_NUMBER" type="string" />
    <property name="PaymentDate" column="PAYMENT_DATE" type="DateTime" />
    <property name="PaymentDescription" column="PAYMENT_DESCRIPTION" type="string" />
    <property name="Notes" column="NOTE" type="string" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionID" column="VERSION_ID" type="long" />
  </class>
</hibernate-mapping>