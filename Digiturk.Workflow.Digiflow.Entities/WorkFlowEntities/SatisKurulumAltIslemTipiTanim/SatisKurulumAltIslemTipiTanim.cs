using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class SatisKurulumAltIslemTipiTanim : EntityBase, IEntity
    {
        #region Entity Properties

        public virtual long RequestId { get; set; }
        public virtual long AnaIslemTipId { get; set; }
        public virtual string <PERSON>ciklama { get; set; }
        public virtual string EkrandaGoster { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long VersionID { get; set; }

        #endregion Entity Properties
    }
}

