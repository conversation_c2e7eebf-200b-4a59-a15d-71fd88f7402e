﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class InsteadOfUser : EntityBase, Digiturk.Workflow.Digiflow.Entities.IEntity
    {
        #region
        public virtual long RequestId { get; set; }
        public virtual long LoginId { get; set; }
        public virtual long InsteadOfLoginId { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual long LastUpdatedBy { get; set; }

        #endregion
    }
}