﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="WorkflowPermissionType, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_WORKFLOW_PERMISSION_TYPE" schema="DT_WORKFLOW">
    <id name="PermissionTypeID" type="long" column="PERMISSION_TYPE_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="Name" column="NAME" />
    <property name="Description" column="DESCRIPTION" />
    <property name="IsActive" column="IS_ACTIVE" />
  </class>
</hibernate-mapping>