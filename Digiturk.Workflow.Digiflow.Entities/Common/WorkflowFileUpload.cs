﻿using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class WorkflowFileUpload
    {
        public virtual long Id { get; set; }
        public virtual long WorkflowInstanceId { get; set; }
        public virtual long StateInstanceId { get; set; }
        public virtual long UploadLoginId { get; set; }
        public virtual DateTime UploadDate { get; set; }
        public virtual string UploadFilePath { get; set; }
        public virtual string UploadComment { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
    }
}