﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="WorkflowPermission, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_WORKFLOW_PERMISSION" schema="DT_WORKFLOW">
    <id name="PermissionID" type="long" column="WORKFLOW_PERMISSION_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="WorkflowID" column="WORKFLOW_ID" />
    <property name="LoginID" column="LOGIN_ID" />
    <property name="IsLoginGroup" column="IS_LOGIN_GROUP" />
    <property name="PermissionTypeID" column="PERMISSION_TYPE_ID" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="LastUpdated" column="LAST_UPDATED" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" />
  </class>
</hibernate-mapping>