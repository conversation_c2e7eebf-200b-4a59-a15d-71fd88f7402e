﻿using Digiturk.Workflow.Entities;
using System;

namespace Digiturk.Workflow.Digiflow.Entities
{
    public class WorkflowPermission : EntityBase
    {
        public virtual long PermissionID { get; private set; }
        public virtual long WorkflowID { get; set; }
        public virtual long LoginID { get; set; }
        public virtual int IsLoginGroup { get; set; }
        public virtual long PermissionTypeID { get; set; }
        public virtual DateTime Created { get; set; }
        public virtual long CreatedBy { get; set; }
        public virtual DateTime LastUpdated { get; set; }
        public virtual long LastUpdatedBy { get; set; }
    }
}