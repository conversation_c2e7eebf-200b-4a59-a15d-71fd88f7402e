﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="UserLangSettings, Digiturk.Workflow.Digiflow.Entities" table="WF_DF_USER_LANG" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="USER_LANG_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="UserId" column="USER_ID" />
    <property name="LanguageId" column="LANG_ID" />
    <property name="Created" column="CREATED" />
    <property name="CreatedBy" column="CREATED_BY" />
    <property name="Updated" column="LAST_UPDATED" />
    <property name="UpdatedBy" column="LAST_UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>