﻿<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="RaporTalepAdmin, Digiturk.Workflow.Digiflow.Entities" table="TALEP_TAKIP_UYGULAMA" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="STRTIPI" column="STRTIPI" type="string" />
    <property name="STRTURU" column="STRTURU" type="string" />
    <property name="STRUYGULAMA" column="STRUYGULAMA" type="string" />
    <property name="STRGOSTER" column="STRGOSTER" type="string" />
    <property name="Created" column="CREATED" type="DateTime" />
    <property name="CreatedBy" column="CREATED_BY" type="long" />
    <property name="LastUpdated" column="LAST_UPDATED" type="DateTime" />
    <property name="LastUpdatedBy" column="LAST_UPDATED_BY" type="long" />
    <property name="VersionId" column="VERSION_ID" type="long" />
  </class>
</hibernate-mapping>