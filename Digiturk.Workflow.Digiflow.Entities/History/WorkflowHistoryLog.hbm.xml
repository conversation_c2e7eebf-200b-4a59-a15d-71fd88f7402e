<?xml version="1.0" encoding="utf-8" ?>
<hibernate-mapping xmlns="urn:nhibernate-mapping-2.2" assembly="Digiturk.Workflow.Digiflow.Entities" namespace="Digiturk.Workflow.Digiflow.Entities">
  <class name="WorkflowHistoryLog, Digiturk.Workflow.Digiflow.Entities" table="F_WF_WORKFLOW_HISTORY_LOG" schema="DT_WORKFLOW">
    <id name="RequestId" type="long" column="LOG_ID">
      <generator class="trigger-identity"></generator>
    </id>
    <property name="WfHistoryId" column="WF_HISTORY_ID" />
    <property name="Taskcomment" column="TASKCOMMENT" />
    <property name="Updated" column="UPDATED" />
    <property name="UpdatedBy" column="UPDATED_BY" />
    <property name="VersionID" column="VERSION_ID" />
  </class>
</hibernate-mapping>