@echo off
echo ========================================
echo TEST 5: IIS Configuration Test (Windows)
echo ========================================
echo.
echo Testing IIS and application configuration...
echo.

echo [5.1] Checking IIS status...
iisreset /status
echo.

echo [5.2] Checking application pools...
%windir%\system32\inetsrv\appcmd list apppool /name:"DigiflowAPI*"
echo.

echo [5.3] Checking websites...
%windir%\system32\inetsrv\appcmd list site /name:"*Digiflow*"
echo.

echo [5.4] Testing local API health endpoint...
echo Testing: http://localhost/DigiflowAPI/api/health
curl -s -o nul -w "Local API Status: %%{http_code}" http://localhost/DigiflowAPI/api/health
echo.
echo.

echo [5.5] Testing local API with webview headers...
curl -s -I -H "X-Mobile-App: true" -H "X-From-Mobile-WebView: true" http://localhost/DigiflowAPI/api/health | findstr "X-Frame-Options"
echo.

echo [5.6] Checking for web.config overrides...
if exist "C:\inetpub\wwwroot\DigiflowAPI\web.config" (
    echo ✅ web.config found
    findstr /i "frame-options\|security" "C:\inetpub\wwwroot\DigiflowAPI\web.config"
) else (
    echo ⚠️ web.config not found at expected location
)
echo.

echo [5.7] Checking application logs...
echo Recent application errors:
powershell "Get-EventLog -LogName Application -Source '*Digiflow*' -Newest 5 -ErrorAction SilentlyContinue | Select-Object TimeGenerated, EntryType, Message"
echo.

echo ========================================
echo TEST 5 RESULTS
echo ========================================
echo Please report back:
echo 1. IIS status and application pool status
echo 2. Local API health endpoint status
echo 3. X-Frame-Options header from local test
echo 4. Any web.config security settings found
echo 5. Any recent application errors
echo.
echo If issues found, try:
echo 1. iisreset
echo 2. Restart application pools
echo 3. Check application logs
echo.
pause
