﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{DF01CF52-A7B1-41B0-B982-42BC2EDA601A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.WebCore</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.WebCore</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AjaxControlToolkit">
      <HintPath>\\dtl1iis3\Deployment\AjaxControlToolkit.dll</HintPath>
    </Reference>
    <Reference Include="DigiportMenuDisplayHelpers">
      <HintPath>\\dtl1iis3\Deployment\DigiportMenuDisplayHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Common">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Common.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authentication">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authentication.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Authorization, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Authorization.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.CoreHelpers">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.CoreHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.ExceptionEntites">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.ExceptionEntites.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.DigiFlow.Framework">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.DigiFlow.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.GenericMailHelper">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.GenericMailHelper.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Digiflow.WorkFlowHelpers, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.WorkFlowHelpers.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Engine">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Engine.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Entities">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Entities.dll</HintPath>
    </Reference>
    <Reference Include="Digiturk.Workflow.Repository">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Repository.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.DataAccess, Version=4.122.19.1, Culture=neutral, PublicKeyToken=89b483f429c47342, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>\\dtl1iis3\Deployment\ODPNET\x86\Net2\Oracle.DataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Data" />
    <Reference Include="System.Web.Extensions" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\Common.cs" />
    <Compile Include="Digiport\CommonBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Digiport\ComponentBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Digiport\SliderBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\AdminPanelMaster.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\BaseMasterPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\SecureReportMasterPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\ReportBaseMasterPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="UICultureClass.cs" />
    <Compile Include="WebPage\NotificationPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebPage\ReportBasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebPage\YYSSecurePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\SecureMasterPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\UnSecurePage.cs" />
    <Compile Include="WebPage\BasePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="WebPage\SecurePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebPage\UnSecurePage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WebPage\WorkFlowPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="MasterPage\YYSSecureMasterPage.cs">
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="WorkFlowEntites\DelegationObject.cs" />
    <Compile Include="WorkFlowEntites\FlowAdminOprObject.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="WebCore.cd" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>COPY /Y "$(TargetPath)" \\dtl1iis3\Deployment</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\Digiturk.Workflow.Digiflow.WebCore\*.* /s</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>