﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Entities;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Web;

/// <summary>
/// Summary description for UICultureClass
/// </summary>

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public static class UICultureClass
    {
        public static string ChangeCulture(long ChangeLoginId, string langstr)
        {
            Digiturk.Workflow.Digiflow.Entities.Languages lang = Languages.Turkish;
            if (ChangeLoginId > 0)
            {
                lang = UICultureClass.GetLanguage(ChangeLoginId);
            }
            if (!(ChangeLoginId == 0 && !string.IsNullOrEmpty(langstr)))
            {
                if (lang == Digiturk.Workflow.Digiflow.Entities.Languages.Turkish)
                {
                    langstr = "tr-TR";
                }
                else
                {
                    langstr = "en-US";
                }
            }
            CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo(langstr);
            CultureInfo.DefaultThreadCurrentCulture = new CultureInfo(langstr);
            CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo(langstr);
            CultureInfo.DefaultThreadCurrentCulture = new CultureInfo(langstr);
            return langstr;

        }

        #region Culture Helper



        public static long GetSettingsId(long UserId)
        {
            string SQL = "Select * from DT_WORKFLOW.WF_DF_USER_LANG where USER_ID=" + UserId;
            DataTable dtb = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL, new List<Digiturk.Workflow.Digiflow.DataAccessLayer.CustomParameterList>());
            if (dtb.Rows.Count > 0)
            {
                return long.Parse(dtb.Rows[0]["USER_LANG_ID"].ToString());
            }
            else
            {
                return 0;
            }
        }

        public static Languages GetLangFromString(string Lang)
        {
            if (Lang == "en-US")
            {
                return Languages.English;
            }
            else
            {
                return Languages.Turkish;
            }
        }

        public static void SaveLanguageSettings(Languages settings, long UserId)
        {
            long SettingsId = GetSettingsId(UserId);
            UserLangSettings SettingsEntity = new UserLangSettings();
            if (SettingsId > 0)
            {
                SettingsEntity = WFRepository<UserLangSettings>.GetEntity(SettingsId);
                SettingsEntity.Updated = DateTime.Now;
                SettingsEntity.UpdatedBy = UserId;
            }
            else
            {
                SettingsEntity.Created = DateTime.Now;
                SettingsEntity.CreatedBy = UserId;
            }
            SettingsEntity.UserId = UserId;
            SettingsEntity.LanguageId = (long)settings;
            ActionHelpers.EntitySave(SettingsEntity);
        }

        public static Languages GetLanguage(long UserId)
        {
            string SQL = "Select * from DT_WORKFLOW.WF_DF_USER_LANG where USER_ID=" + UserId;
            DataTable dtb = Digiturk.Workflow.Digiflow.DataAccessLayer.ModelWorking.GetDataTable("DefaultConnection", SQL, new List<Digiturk.Workflow.Digiflow.DataAccessLayer.CustomParameterList>());
            if (dtb.Rows.Count > 0)
            {
                return (Languages)long.Parse(dtb.Rows[0]["LANG_ID"].ToString());
            }
            else
            {
                return Languages.Turkish;
            }
        }

        public static string GetCultureInfo(Languages settings)
        {
            if (settings == Languages.Turkish)
            {
                return "tr-TR";
            }
            else if (settings == Languages.English)
            {
                return "en-US";
            }
            else
            {
                return "tr-TR";
            }
        }

        #endregion
    }
}