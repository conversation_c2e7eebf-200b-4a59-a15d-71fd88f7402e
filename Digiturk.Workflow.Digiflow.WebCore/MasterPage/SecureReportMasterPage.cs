﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore.MasterPage
{
    public abstract class SecureReportMasterPage : BaseMasterPage
    {
        private string UserName
        {
            get
            {
                string User = Page.User.Identity.Name.ToUpper(System.Globalization.CultureInfo.CurrentCulture);
                //string User = @"DIGITURKCC\CCCTOKER";
                User = User.Replace("İ", "I");
                User = User.Replace("Ö", "O");
                User = User.Replace("Ü", "U");
                return User;
            }
        }

        public string PageName
        {
            get
            {
                return Request.Url.Segments[Request.Url.Segments.Length - 1];
            }
        }

        public AuthenticationResult UserInformation
        {
            get
            {
                if (Session[SecurePage.SessionUserVariable] == null)
                {
                    if (IsInDebugMode)
                    {
                        if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                        }
                        else
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, 0, UserName);
                        }
                    }
                    else
                    {
                        Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                    }
                }
                else
                {
                    if (!((AuthenticationResult)Session[SecurePage.SessionUserVariable]).IsLogin)
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                            {
                                /// Debug modu dikkate alarak bir kurgu düşünmemiz gerekiyor.
                                Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                            }
                        }
                        else
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                        }
                    }
                    else
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                            {
                                if (((AuthenticationResult)Session[SecurePage.SessionUserVariable]).LoginObject.LoginId != ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]))
                                {
                                    Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                                }
                            }
                        }
                    }
                }
                return (AuthenticationResult)Session[SecurePage.SessionUserVariable];
            }
        }

        protected override void OnInit(EventArgs e)
        {
            if (UserInformation == null || !UserInformation.IsLogin)
            {
                SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.10", null);
                Response.Redirect("Error.aspx", false);
            }
            base.OnInit(e);
        }
    }
}