﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore.MasterPage
{
    public abstract class SecureMasterPage : BaseMasterPage
    {
        private string UserName
        {
            get
            {
                string User = Page.User.Identity.Name.ToUpper(System.Globalization.CultureInfo.CurrentCulture);
                //string User = @"DIGITURKCC\CCCTOKER";
                User = User.Replace("İ", "I");
                User = User.Replace("Ö", "O");
                User = User.Replace("Ü", "U");
                return User;
            }
        }

        public bool IsCheckAuthorizeControl { get; set; }

        public abstract void AuthorizeControl(bool CanCreate, bool CanApproval, bool CanReject, bool CanForward, bool CanSendtoCommend, bool CanSendRequestToComment, bool CanSuspend, bool CanResume, bool CanCancel, bool CanFinalize, bool CanAddToComment, bool CanFileUpload, bool CanSendTask, bool CanConditionalAccept, bool CanCorrection, long SendTaskLogicalGroupId, long ForwardLogicalGroupId, long SendCommentLogicalGroupId);

        public string PageName
        {
            get
            {
                //if (Request.QueryString["RequestType"] != null)
                //{
                //    if (Request.QueryString["RequestType"] == "0")
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "1")
                //    {
                //        return "OutsourceRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "2")
                //    {
                //        return "StajyerRequest.aspx".ToLower();
                //    }
                //    else
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //}
                if (Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower() == "nemployeerequest.aspx")
                {
                    return "EmployeeRequest.aspx".ToLower();

                    //if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1222)
                    //{
                    //    return "EmployeeRequest.aspx".ToLower();
                    //}
                    //else if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1243)
                    //{
                    //    return "StajyerRequest.aspx".ToLower();
                    //}
                    //else if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1244)
                    //{
                    //    return "OutsourceRequest.aspx".ToLower();
                    //}
                    //else
                    //{
                    //    return "EmployeeRequest.aspx".ToLower();
                    //}
                }
                else
                {
                    string pageName = Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower().Replace("_mobil", "").Replace("_Mobil", "").Replace("_onay2", "");
                    return pageName;
                    //return Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower();
                }
            }
        }
     
        public AuthenticationResult UserInformation
        {
            get
            {
                if ((AuthenticationResult)Session[BasePage.SessionUserVariable] == null)
                {
                    return ((SecurePage)this.Page).UserInformation;
                }
                else
                {
                    return (AuthenticationResult)Session[BasePage.SessionUserVariable];
                }
            }
        }
     

        protected override void OnInit(EventArgs e)
        {
            Session["_Culture"] = UICultureClass.ChangeCulture(UserInformation.LoginObject.LoginId, Session["_Culture"] == null ? string.Empty : Session["_Culture"].ToString());

            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init", "Secure Master Page ");
            Session["FormCreateTime"] = DateTime.Now;
            //////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecureMasterPage", "OnInitBeforeIsPostBack");
            if (!Page.IsPostBack && !Page.IsCallback)
            {
                try
                {
                    //  ////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecureMasterPage", "UserChecking");
                    if (UserInformation == null || !UserInformation.IsLogin)
                    {
                        SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.3", null);
                        Response.Redirect("Error.aspx", true);
                    }
                }
                catch (Digiflow.ExceptionEntites.NotAuthenticationException ex)
                {
                    SetSessionError("Kimlik Doğrulama Bilgisi", "<h3>Lütfen aşağıdaki yönergeleri uygulayınız !</h3><br><br> Kullanıcı bilgileriniz doğrulanamadı.<br>Kurumsal uygulamalar ve iş akışlarını kullanabilmeniz için, kullanıcınızın <b>(" + UserInformation.LoginObject.DomainUserName + ")</b> IK sisteminde tanımlı olması gerekmektedir.<br><br>Kullanıcı adınızı tanımlatmak üzere lütfen Insan Kaynakları departmanı ile iletişime geçiniz. <br><br> İlgili mail : <EMAIL>", ex);
                    Response.Redirect("Error.aspx", true);
                }
                catch (Exception ex)
                {
                    SetSessionError("Uygulama Hatası", ex.Message, ex);
                    Response.Redirect("Error.aspx", true);
                    throw ex;
                }

                if (this.Page is Workflow.Digiflow.WebCore.WorkFlowPage)
                {                   
                        if (System.Configuration.ConfigurationSettings.AppSettings["IsYYSActive"] == "true")
                        {
                            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init F1", "Secure Master Page ");
                            if (((WorkFlowPage)this.Page).InstanceId > 0 && ((WorkFlowPage)this.Page).CurrentWfIns.WfCurrentState != null)
                            {
                                UserInformation.AuthoList = null;
                                UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, ((WorkFlowPage)this.Page).CurrentStateDef.WfStateDefId);
                            }
                            else if (((WorkFlowPage)this.Page).InstanceId == 0)
                            {
                                if (UserInformation.AuthoList != null)
                                {
                                    if (UserInformation.AuthoList.WorkFlowDefinitionId != 0 && UserInformation.AuthoList.StateDefinitionId != 0)
                                    {
                                        ////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecureMasterPage", "NewAuthorizeZero");
                                        UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, 0, 0);
                                    }
                                }
                                else if (((WorkFlowPage)this.Page).CopyInstanceId > 0)
                                {
                                    using (UnitOfWork.Start())
                                    {
                                        long WfStateDefId = ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId;
                                        FWfWorkflowDef WorkFlowDef = Digiturk.Workflow.Common.WFRepository<FWfWorkflowDef>.GetEntity(WfStateDefId);
                                        long StateDefId = WorkFlowDef.InitialState.WfStateDefId;
                                        UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, StateDefId);
                                    }
                                }
                                else
                                {
                                    UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, 0, 0);
                                }
                            }
                            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init F1 Sonu", "Secure Master Page ");
                            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init F2", "Secure Master Page ");
                            if (((WorkFlowPage)this.Page).CurrentActionTaskInstance != null)
                            {
                                ////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecureMasterPage", "CheckOtorize");

                                if (UserInformation.AuthoList.ContainsKey(SetPageName(PageName).ToLower()))
                                {
                                    string sayfa = SetPageName(PageName).ToLower();
                                if (this.Page is WorkFlowPage)
                                {
                                    ((WorkFlowPage)this.Page).DelegeHasConditionAcceptCorrection();
                                }
                                AuthoInfo yetki = UserInformation.AuthoList[sayfa];
                                    //TodoYetki Kodunu Ekleyelim.
                                    
                                    this.AuthorizeControl(
                                        yetki.CanCreate,
                                        yetki.CanApproval,
                                        yetki.CanReject,
                                        yetki.CanForward,
                                        yetki.CanSendtoCommend,
                                        yetki.CanSendRequestToComment,
                                        yetki.CanSuspend,
                                        yetki.CanResume,
                                        yetki.CanCancel,
                                        yetki.CanFinalize,
                                        yetki.CanAddToComment,
                                        yetki.CanFileUpload,
                                        yetki.CanSendTask,
                                    yetki.CanConditionalAccept,
                                    yetki.CanCorrection,
                                    yetki.SendTaskLogicalGroupId,
                                    yetki.ForwardLogicalGroupId,
                                    yetki.SendCommentLogicalGroupId
                                        );
                                    yetki = null;
                                }
                            }
                            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init F2 Sonu", "Secure Master Page ");
                        }                    
                }
            }
            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "On Init Sonu", "Secure Master Page ");
            base.OnInit(e);
        }

        public string SetPageName(string pageName)
        {
            pageName = pageName.Contains("_mobil") ? pageName.Replace("_mobil", "") : pageName;
            pageName = pageName.Contains("_Mobil") ? pageName.Replace("_Mobil", "") : pageName;
            pageName = pageName.Contains("_onay2") ? pageName.Replace("_onay2", "") : pageName;

            return pageName;
        }

        /// <summary>
        /// Sayfa Üzerinde Oluşan bir hatayı kontrol etmek için kullanılır.
        /// </summary>
        /// <param name="title"> Hata Penceresinin Title Bilgisi</param>
        /// <param name="message"> Mesaj </param>
        /// <param name="instance"> Hata Yığını </param>
        public void SetSessionError(string title, string message, Exception instance)
        {
            Session[BasePage.SessionErrorTitleVariable] = title;
            Session[BasePage.SessionErrorMessageVariable] = message;
            Session[BasePage.SessionExceptionInstanceVariable] = instance;
        }
    }
}