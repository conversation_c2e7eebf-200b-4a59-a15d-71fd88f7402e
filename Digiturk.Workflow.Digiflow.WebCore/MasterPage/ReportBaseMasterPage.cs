﻿namespace Digiturk.Workflow.Digiflow.WebCore.MasterPage
{
    public abstract class ReportBaseMasterPage : SecureReportMasterPage
    {
        private ReportAdminType _ReportAdmin;

        public ReportAdminType ReportAdmin
        {
            get
            {
                return _ReportAdmin;
            }
        }

        public void SetReportAdmin(ReportAdminType AdminType)
        {
            _ReportAdmin = AdminType;
        }
    }

    public enum ReportAdminType
    {
        User, Manager, Admin
    }
}