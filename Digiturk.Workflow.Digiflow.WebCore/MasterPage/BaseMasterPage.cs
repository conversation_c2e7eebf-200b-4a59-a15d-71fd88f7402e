﻿using System;
using System.Configuration;

namespace Digiturk.Workflow.Digiflow.WebCore.MasterPage
{
    public class BaseMasterPage : System.Web.UI.MasterPage
    {
       
        public bool IsInDebugMode
        {
            get { return Convert.ToBoolean(ConfigurationManager.AppSettings["debugMode"].ToString()); }
        }

        /// <summary>
        /// Sayfa Üzerinde Oluşan bir hatayı kontrol etmek için kullanılır.
        /// </summary>
        /// <param name="title"> Hata Penceresinin Title Bilgisi</param>
        /// <param name="message"> Mesaj </param>
        /// <param name="instance"> Hata Yığını </param>
        public void SetSessionError(string title, string message, Exception instance)
        {
            Session[BasePage.SessionErrorTitleVariable] = title;
            Session[BasePage.SessionErrorMessageVariable] = message;
            Session[BasePage.SessionExceptionInstanceVariable] = instance;
        }
    }
}