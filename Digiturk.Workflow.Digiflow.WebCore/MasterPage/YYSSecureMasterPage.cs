﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore.MasterPage
{
    public abstract class YYSSecureMasterPage : BaseMasterPage
    {
        public abstract void BuildMenu(bool FlowAdmin, bool RuleOperation, bool Logicalgroup);

        public void KillSession()
        {
            Session[BasePage.AdminUser] = null;
            //Session[BasePage.SessionUserVariable] = null;
            Session[BasePage.SessionExceptionInstanceVariable] = null;
        }

        public YYSAdminType AdminType
        {
            get
            {
                if (Session[BasePage.AdminUser] == null)
                {
                    Session[BasePage.AdminUser] = Digiflow.WorkFlowHelpers.WorkFlowInformationHelper.GetFlowAdminType(UserInformation.LoginObject.LoginId);
                }
                return (YYSAdminType)Session[BasePage.AdminUser];
                //return YYSAdminType.SystemAndFlowAdmin;
            }
        }

        protected override void OnInit(EventArgs e)
        {
            KillSession();
            if (UserInformation == null || !UserInformation.IsLogin)
            {
                SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.5", null);
                Response.Redirect("Error.aspx", false);
            }

            if (AdminType == null)
            {
                SetSessionError("YYS Admin tanımınız bulunmamaktadır", "Kullanıcı bilgileriniz doğrulanamadı.6", null);
                Response.Redirect("Error.aspx", false);
            }
            if (AdminType == YYSAdminType.None)
            {
                SetSessionError("YYS Admin tanımınız bulunmamaktadır", "Kullanıcı bilgileriniz doğrulanamadı.7", null);
                Response.Redirect("Error.aspx", false);
            }
            else
            {
                if (AdminType == YYSAdminType.SystemAndFlowAdmin)
                {
                    BuildMenu(true, true, true);
                }
                else if (AdminType == YYSAdminType.FlowAdmin)
                {
                    BuildMenu(false, true, true);
                }
                else if (AdminType == YYSAdminType.SystemAdmin)
                {
                    BuildMenu(true, true, true);
                }
            }

            if (Session.Keys.Count == 0)
            {
                SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.8", null);
                Response.Redirect("Error.aspx", false);
            }
            base.OnInit(e);
        }

        private string UserName
        {
            get
            {
                string User = Page.User.Identity.Name.ToUpper(System.Globalization.CultureInfo.CurrentCulture);
                //string User = @"DIGITURKCC\CCCTOKER";
                User = User.Replace("İ", "I");
                User = User.Replace("Ö", "O");
                User = User.Replace("Ü", "U");
                return User;
            }
        }

        public string PageName
        {
            get
            {
                return Request.Url.Segments[Request.Url.Segments.Length - 1];
            }
        }

        public AuthenticationResult UserInformation
        {
            get
            {
                if (Session[SecurePage.SessionUserVariable] == null)
                {
                    if (IsInDebugMode)
                    {
                        if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                        }
                        else
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, 0, UserName);
                        }
                    }
                    else
                    {
                        Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                    }
                }
                else
                {
                    if (!((AuthenticationResult)Session[SecurePage.SessionUserVariable]).IsLogin)
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                            {
                                /// Debug modu dikkate alarak bir kurgu düşünmemiz gerekiyor.
                                Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                            }
                        }
                        else
                        {
                            Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                        }
                    }
                    else
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[SecurePage.QueryStringLoginIdVariable] != null)
                            {
                                if (((AuthenticationResult)Session[SecurePage.SessionUserVariable]).LoginObject.LoginId != ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]))
                                {
                                    Session[SecurePage.SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[SecurePage.QueryStringLoginIdVariable]), UserName);
                                }
                            }
                        }
                    }
                }
                return (AuthenticationResult)Session[SecurePage.SessionUserVariable];
            }
        }
    }
}