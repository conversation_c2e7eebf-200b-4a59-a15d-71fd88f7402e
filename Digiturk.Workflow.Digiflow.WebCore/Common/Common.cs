﻿using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System.Collections.Generic;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public class YYSCommon
    {
        /// <summary>
        /// Returns html formatted success message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Success message text</param>
        /// <returns>Html formatted success message</returns>
        public static string GetSuccessMessage(string title, string message)
        {
            message += "<p>&nbsp;</p><a href='default.aspx'><- Anasay<PERSON>ya dön</a>";
            return ShowInformation(title, message);
        }

        /// <summary>
        /// Returns html formatted error message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Error message text</param>
        /// <returns>Html formatted error message</returns>
        public static string ShowError(string title, string message)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p></div></div> ", title, message);
        }

        /// <summary>
        /// Returns html formatted error message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Error message text</param>
        /// <param name="details">Details of exception</param>
        /// <returns>Html formatted error message</returns>
        public static string ShowError(string title, string message, string details)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p><p>&nbsp;</p><a href=\"#\" OnClick=\"ShowHideDiv('ErrorBoxDiv')\">Hata Detayları</a></div><div id=\"ErrorBoxDiv\" class=\"errorBox\" style=\"text-align: left\">{2}</div> </div>", title, message, details);
        }

        /// <summary>
        /// Returns html formatted information message
        /// </summary>
        /// <param name="title">Title of message</param>
        /// <param name="message">Information message text</param>
        /// <returns>Html formatted information message</returns>
        public static string ShowInformation(string title, string message)
        {
            return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-highlight ui-corner-all\" style=\"margin-top: 20px; padding: 0 .7em;\"> <p><span class=\"ui-icon ui-icon-info\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br />{1}</p></div></div>", title, message);
        }

        /// <summary>
        ///
        /// </summary>
        /// <param name="loginId"></param>
        /// <returns></returns>
        //public static int GetActiveTransactionsCount(long loginId)
        //{
        //    try
        //    {
        //        using (UnitOfWork.Start())
        //        {
        //            IList<FWfActionTaskInstance> result = ActionTaskWorker.GetInboxList(loginId);
        //            IList<FWfActionTaskInstance> result1 = ActionTaskWorker.GetDelegatedInboxList(loginId);
        //            IList<FWfActionTaskInstance> result2 = ActionTaskWorker.GetCommentInboxList(loginId, true);
        //            return (result.Count + result1.Count + result2.Count);
        //        }
        //    }
        //    catch
        //    {
        //        return 0;
        //    }
        //}
        ///// <summary>
        ///// Kullanıcının inbox, delege inbox ve comment inbox listelerindeki toplam kayıt sayısını döner
        ///// </summary>
        ///// <param name="loginId">Kullanıcının Id değeri</param>
        ///// <returns>Inbox, delege inbox ve comment inbox listelerindeki toplam kayıt sayısı</returns>
        //public static int GetSuspendedTransactionsCount(long loginId)
        //{
        //    try
        //    {
        //        using (UnitOfWork.Start())
        //        {
        //            IList<FWfActionTaskInstance> waitingList = ActionTaskWorker.GetTakedOnInboxList(loginId);
        //            IList<FWfActionTaskInstance> result = ActionTaskWorker.GetSuspendedInboxList(loginId);
        //            return (result.Count + waitingList.Count);
        //        }
        //    }
        //    catch
        //    {
        //        return 0;
        //    }
        //}

        /// <summary>
        /// Kullanıcının akış yöneticisi olup olmadığını döner
        /// </summary>
        /// <returns></returns>
        public static bool IsWorkflowManager(FLogin login)
        {
            IList<FWfWorkflowInstance> mList = new List<FWfWorkflowInstance>();
            using (UnitOfWork.Start())
            {
                if (login != null)
                {
                    mList = ActionTaskWorker.GetModifyList(login.LoginId.ToString());
                    if (mList.Count > 0)
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
        }

        //#region Constants
        ////public const string SessionUserVariable = "__SessionUser";
        //public const string SessionErrorTitleVariable = "__ErrorTitle";
        //public const string SessionErrorMessageVariable = "__ErrorMessage";
        //public const string SessionExceptionInstanceVariable = "__Exception";
        ////public const string QueryStringLoginIdVariable = "LoginId";
        ////public const string QueryStringInstanceId = "wfInstanceId";
        ////public const string QueryStringStart = "start";
        //#endregion

        ///// <summary>
        ///// Session'da yer alan hata değişkenlerini yönetir
        ///// </summary>
        ///// <param name="title"> Hatanın Title'ı </param>
        ///// <param name="message"> Hatanın Mesajı</param>
        ///// <param name="instance"> InstanceId'si</param>
        //public static void SetSessionError(string title, string message, Exception instance)
        //{
        //    HttpContext.Current.Session[SessionErrorTitleVariable] = title;
        //    HttpContext.Current.Session[SessionErrorMessageVariable] = message;
        //    HttpContext.Current.Session[SessionExceptionInstanceVariable] = instance;
        //}
        ///// <summary>
        ///// Belirtilen kullanıcının sistem yöneticisi olup olmadığını kontrol eder
        ///// </summary>
        ///// <param name="user">Kullanıcı</param>
        ///// <returns>Boolean</returns>
        //public static bool IsSystemManager(object user)
        //{
        //    return true;
        //}
        ///// <summary>
        ///// Uygulamanın Debug modda olup olmadığını belirten bir boolean değer döner
        ///// </summary>
        ////public static bool IsInDebugMode
        ////{
        ////    get { return Convert.ToBoolean(ConfigurationManager.AppSettings["debugMode"].ToString()); }
        ////}
        ///// <summary>
        ///// Kullanıcının inbox, delege inbox ve comment inbox listelerindeki toplam kayıt sayısını döner
        ///// </summary>
        ///// <param name="loginId">Kullanıcının Id değeri</param>
        ///// <returns>Inbox, delege inbox ve comment inbox listelerindeki toplam kayıt sayısı</returns>
        ////public static int GetActiveTransactionsCount(long loginId)
        ////{
        ////    try
        ////    {
        ////        using (UnitOfWork.Start())
        ////        {
        ////            IList<FWfActionTaskInstance> result = ActionTaskWorker.GetInboxList(loginId);
        ////            IList<FWfActionTaskInstance> result1 = ActionTaskWorker.GetDelegatedInboxList(loginId);
        ////            IList<FWfActionTaskInstance> result2 = ActionTaskWorker.GetCommentInboxList(loginId, true);
        ////            return (result.Count + result1.Count + result2.Count);
        ////        }
        ////    }
        ////    catch
        ////    {
        ////        return 0;
        ////    }
        ////}
        ///// <summary>
        ///// Sayfalarda görüntülenecek sayfa başlığı metnini konfigurasyon dosyasından okuyarak döner
        ///// </summary>
        ////public static string PageTitle
        ////{
        ////    get
        ////    {
        ////        return ConfigurationManager.AppSettings["PageTitle"].ToString();
        ////    }
        ////}
        ///// <summary>
        ///// Kullanıcının akış yöneticisi olup olmadığını döner
        ///// </summary>
        ///// <returns></returns>
        ////public static bool IsWorkflowManager(FLogin login)
        ////{
        ////    IList<FWfWorkflowInstance> mList = new List<FWfWorkflowInstance>();
        ////    using (UnitOfWork.Start())
        ////    {
        ////        if (login != null)
        ////        {
        ////            mList = ActionTaskWorker.GetModifyList(login.LoginId.ToString());
        ////            if (mList.Count > 0)
        ////            {
        ////                return true;
        ////            }
        ////        }
        ////    }

        ////    return false;
        ////}
        ///// <summary>
        ///// İşlem yapan aktif kullanıcı döner
        /////
        ///// Önce session'a bakılır
        ///// Session boşsa sid ile kontrol yapılır
        ///// Sid kontrolü boş dönerse ve debug modda ise querystring kontrolü yapılır
        ///// hepsi boş dönerse null döner
        ///// </summary>
        ///// <returns></returns>
        //public static FLogin GetCurrentLogin()
        //{
        //    // SID ile login denetimi
        //    FLogin login = null;
        //    if (Common.IsInDebugMode && HttpContext.Current.Request.QueryString[Common.QueryStringLoginIdVariable] != null)
        //    {
        //        login = Common.GetLogin(Convert.ToInt64(HttpContext.Current.Request.QueryString[Common.QueryStringLoginIdVariable].ToString()));
        //    }
        //    else if (login == null)
        //    {
        //        login = Common.GetLoginBySID();
        //    }
        //    if (login != null)
        //    {
        //        HttpContext.Current.Session[SessionUserVariable] = login;
        //        return login;
        //    }
        //    //else
        //    //{
        //    //    // debug modda ise query string ile kontrol
        //    //    if (Common.IsInDebugMode)
        //    //    {
        //    //        if (HttpContext.Current.Request.QueryString[Common.QueryStringLoginIdVariable] != null)
        //    //        {
        //    //            login = Common.GetLogin(Convert.ToInt64(HttpContext.Current.Request.QueryString[Common.QueryStringLoginIdVariable].ToString()));
        //    //        }

        //    //        if (login != null)
        //    //        {
        //    //            HttpContext.Current.Session[SessionUserVariable] = login;
        //    //            return login;
        //    //        }
        //    //    }
        //    //}

        //    return null;
        //}

        ///// <summary>
        ///// Returns html formatted success message
        ///// </summary>
        ///// <param name="title">Title of message</param>
        ///// <param name="message">Success message text</param>
        ///// <returns>Html formatted success message</returns>
        //public static string GetSuccessMessage(string title, string message)
        //{
        //    message += "<p>&nbsp;</p><a href='default.aspx'><- Anasayfaya dön</a>";
        //    return ShowInformation(title, message);
        //}

        ///// <summary>
        ///// Returns html formatted error message
        ///// </summary>
        ///// <param name="title">Title of message</param>
        ///// <param name="message">Error message text</param>
        ///// <returns>Html formatted error message</returns>
        //public static string ShowError(string title, string message)
        //{
        //    return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p></div></div> ", title, message);
        //}

        ///// <summary>
        ///// Returns html formatted error message
        ///// </summary>
        ///// <param name="title">Title of message</param>
        ///// <param name="message">Error message text</param>
        ///// <param name="details">Details of exception</param>
        ///// <returns>Html formatted error message</returns>
        //public static string ShowError(string title, string message, string details)
        //{
        //    return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-error ui-corner-all\" style=\"padding: 0 .7em;\"><p><span class=\"ui-icon ui-icon-alert\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br /><br />{1}</p><p>&nbsp;</p><a href=\"#\" OnClick=\"ShowHideDiv('ErrorBoxDiv')\">Hata Detayları</a></div><div id=\"ErrorBoxDiv\" class=\"errorBox\" style=\"text-align: left\">{2}</div> </div>", title, message, details);
        //}

        ///// <summary>
        ///// Returns html formatted information message
        ///// </summary>
        ///// <param name="title">Title of message</param>
        ///// <param name="message">Information message text</param>
        ///// <returns>Html formatted information message</returns>
        //public static string ShowInformation(string title, string message)
        //{
        //    return string.Format("<div class=\"ui-widget\"><div class=\"ui-state-highlight ui-corner-all\" style=\"margin-top: 20px; padding: 0 .7em;\"> <p><span class=\"ui-icon ui-icon-info\" style=\"float: left; margin-right: .3em;\"></span><strong>{0}</strong><br />{1}</p></div></div>", title, message);
        //}

        //public static string GetCurrentStateDescriptionOfWorkflow(FWfWorkflowInstance ins)
        //{
        //    string ret = string.Empty;
        //    try
        //    {
        //        if (ins != null)
        //        {
        //            ret = "İş akışı şu anda <b>" + GetCurrentTaskOfWorkflow(ins) + "</b> aşamasındadır.";
        //        }
        //        if (ins != null)
        //        {
        //            string users = GetCurrentAssşignedUserkOfWorkflow(ins.WfWorkflowInstanceId);
        //            if (!string.IsNullOrEmpty(users))
        //            {
        //                if (users.IndexOf(",") > -1)
        //                {
        //                    ret += string.Format(" Akış şu anda <b>{0}</b> kullanıcılarına atanmıştır.", users);
        //                }
        //                else
        //                {
        //                    ret += string.Format(" Akış şu anda <b>{0}</b> kullanıcısına atanmıştır.", users);
        //                }
        //            }
        //        }
        //    }
        //    catch
        //    {
        //        ret = string.Empty;
        //    }
        //    return ret;
        //}

        ///// <summary>
        ///// Yeni iş akışı oluşturulurken, akışı oluşturan kullanıcıya WFVIEW
        ///// yetkisi vermek için kullanılır
        ///// </summary>
        ///// <param name="workflowInstanceId">Workflow instance id</param>
        ///// <param name="loginId">Login Id</param>
        //public static void GrantViewPermissionToOwner(long workflowInstanceId, long loginId)
        //{
        //    using (UnitOfWork.Start())
        //    {
        //        var viewList = Digiturk.Workflow.Digiflow.DataAccessLayer.InboxWorker.GetViewList(loginId.ToString());
        //        FWfWorkflowInstance wf = WFRepository<FWfWorkflowInstance>.GetEntity(workflowInstanceId);
        //        if (wf != null)
        //        {
        //            if (viewList.Count == 0 || viewList.IndexOf(wf) < 0)
        //            {
        //                Digiturk.Workflow.Digiflow.Actions.AssignmentBase.Assign(loginId, "LOGIN", wf.WfWorkflowInstanceId, "WFVIEW");
        //            }
        //        }
        //    }
        //}

        ///// <summary>
        ///// Workflow'un şu anki durumunu döner
        ///// </summary>
        ///// <param name="workflowInstanceId"></param>
        ///// <returns></returns>
        //public static string GetCurrentTaskOfWorkflow(FWfWorkflowInstance ins)
        //{
        //    string ret = string.Empty;

        //    try
        //    {
        //        if (ins != null)
        //        {
        //            var lastState = ins.WfWorkflowInstanceFWfStateInstanceList.OrderBy(t => t.WfStateInstanceId).ToList()[ins.WfWorkflowInstanceFWfStateInstanceList.Count - 1];
        //            var IsFinished = lastState.WfStateDef.WfStateType.WfStateTypeCd == FWfStateTypeValues.End;

        //            if (IsFinished)
        //            {
        //                ret = lastState.WfStateDef.Name;
        //            }
        //            else
        //            {
        //                ret = ins.WfCurrentState.WfStateDef.Name;
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ret = string.Empty;
        //    }

        //    return ret;
        //}

        ///// <summary>
        ///// Workflow'un şu an atanmış olduğu kullanıcı / kullanıcıları döner
        ///// </summary>
        ///// <param name="workflowInstanceId"></param>
        ///// <returns></returns>
        //public static string GetCurrentAssşignedUserkOfWorkflow(long workflowInstanceId)
        //{
        //    string ret = string.Empty;
        //    try
        //    {
        //        FWfWorkflowInstance workflowInstance = WFRepository<FWfWorkflowInstance>.GetEntity(workflowInstanceId);
        //        FWfActionTaskInstance actionTaskInstance = WFRepository<FWfActionTaskInstance>.GetEntity(workflowInstance.WfCurrentState.WfCurrentActionInstanceId.Value);
        //        var loginList = AssignmentHelper.GetAssignedList("", FWfAssignmentTypeValues.TaskInbox.ToString(), actionTaskInstance.WfActionDef.WfActionDefId.ToString(), actionTaskInstance.WfActionInstanceId.ToString());
        //        if (loginList.Count == 1)
        //        {
        //            ret = WorkFlowDataHelper.GetLoginNameSurname(loginList[0].LoginId);
        //        }
        //        else if (loginList.Count > 1)
        //        {
        //            foreach (var item in loginList)
        //            {
        //                ret += WorkFlowDataHelper.GetLoginNameSurname(item.LoginId) + ", ";
        //            }
        //            ret = ret.Substring(0, ret.LastIndexOf(","));
        //        }
        //    }
        //    catch
        //    {
        //        ret = string.Empty;
        //    }
        //    return ret;
        //}

        ///// <summary>
        ///// Converts history decision table to html formatted table
        ///// </summary>
        ///// <param name="dt">History decision table</param>
        ///// <param name="includeComments">Include comment values</param>
        ///// <returns>Html formatted table</returns>
        //public static string ConvertHistoryDataTableToHtml(DataTable dt, bool includeComments)
        //{
        //    Dictionary<string, List<DataRow>> tmp = new Dictionary<string, List<DataRow>>();

        //    foreach (DataRow dr in dt.Rows)
        //    {
        //        if (tmp.ContainsKey(dr[0].ToString()))
        //        {
        //            tmp[dr[0].ToString()].Add(dr);
        //        }
        //        else
        //        {
        //            tmp.Add(dr[0].ToString(), new List<DataRow>());
        //            tmp[dr[0].ToString()].Add(dr);
        //        }
        //    }

        //    IDictionaryEnumerator en = tmp.GetEnumerator();
        //    StringBuilder sb = new StringBuilder();

        //    //create table header
        //    sb.AppendLine("<table border=\"0\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"><tr><td style=\"border-bottom: 1px solid #EBEBEB; padding: 3px\" bgcolor=\"#F0F0F0\"><b>İşlem Geçmişi</b></td></tr><tr><td style=\"padding: 3px\">");

        //    while (en.MoveNext())
        //    {
        //        var rows = en.Value as List<DataRow>;
        //        sb.AppendLine("<table border=\"0\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"><tr><td style=\"padding: 3px\"><img src=\"images/plus_icon_trans.gif\">&nbsp;<b>" + rows[0]["State"].ToString() + "</b></td></tr><tr><td style=\"padding: 3px\"><table border=\"0\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\"><tr><td style=\"padding: 2px\" align=\"center\" width=\"25%\" valign=\"top\"><b>Kullanıcı</b></td><td style=\"padding: 2px\" align=\"center\" width=\"15%\" valign=\"top\"><b>İşlem</b></td><td style=\"padding: 2px\" align=\"center\" width=\"15%\" valign=\"top\"><b>Tarih</b></td><td style=\"padding: 2px\" align=\"left\" width=\"45%\" valign=\"top\"><b>Yorum</b></td></tr>");

        //        if (rows != null)
        //        {
        //            foreach (DataRow item in rows)
        //            {
        //                sb.AppendLine("<tr><td style=\"padding: 2px\" align=\"center\" width=\"25%\" valign=\"top\">" + item["User"].ToString() + "</td> <td style=\"padding: 2px;color:" + item["Color"].ToString() + " \" align=\"center\" width=\"15%\" valign=\"top\">" + item["Action"].ToString() + "</td> <td style=\"padding: 2px\" align=\"center\" width=\"15%\" valign=\"top\">" + item["Date"].ToString() + "</td> <td style=\"padding: 2px\" align=\"left\" width=\"45%\" valign=\"top\">" + GetCommentForItem(includeComments, item["Comment"].ToString()) + "</td> </tr>");
        //            }
        //        }
        //        sb.AppendLine("</table></td></tr></table>");
        //    }

        //    sb.AppendLine("</td></tr></table>");
        //    return sb.ToString();
        //}

        ///// <summary>
        ///// Returns comment text if comments are allowed
        ///// </summary>
        ///// <param name="p">Control variable</param>
        ///// <param name="p_2">Comment text</param>
        ///// <returns>Comment value to be displayed at html table</returns>
        //private static string GetCommentForItem(bool p, string p_2)
        //{
        //    if (p)
        //    {
        //        return p_2;
        //    }
        //    return string.Empty;
        //}

        //public static bool IsInGsmRequestFlowManagerList(string val)
        //{
        //    bool ret = false;

        //    try
        //    {
        //        string cf = ConfigurationManager.AppSettings["GSMTalepOnayYetkiliLoginIdList"].ToString();

        //        if (cf.Contains("|"))
        //        {
        //            string[] arr = cf.Split('|');
        //            ret = arr.Contains(val);
        //        }
        //        else
        //        {
        //            ret = cf == val;
        //        }
        //    }
        //    catch
        //    {
        //        ret = false;
        //    }

        //    return ret;
        //}

        //public static bool GetGSMTalepOnayYetkili1LoginId(string val)
        //{
        //    try
        //    {
        //        if (ConfigurationManager.AppSettings["GSMTalepOnayYetkili1LoginId"].ToString().Contains('|'))
        //        {
        //            string[] arr = ConfigurationManager.AppSettings["GSMTalepOnayYetkili1LoginId"].ToString().Split('|');
        //            if (arr.Contains(val))
        //            {
        //                return true;
        //            }
        //            return false;
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        //public static bool GetGSMTalepOnayYetkili2LoginId(string val)
        //{
        //    try
        //    {
        //        if (ConfigurationManager.AppSettings["GSMTalepOnayYetkili2LoginId"].ToString().Contains('|'))
        //        {
        //            string[] arr = ConfigurationManager.AppSettings["GSMTalepOnayYetkili2LoginId"].ToString().Split('|');
        //            if (arr.Contains(val))
        //            {
        //                return true;
        //            }
        //            return false;
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        //public static bool GetGSMTalepOnayYetkili3LoginId(string val)
        //{
        //    try
        //    {
        //        if (ConfigurationManager.AppSettings["GSMTalepOnayYetkili3LoginId"].ToString().Contains('|'))
        //        {
        //            string[] arr = ConfigurationManager.AppSettings["GSMTalepOnayYetkili3LoginId"].ToString().Split('|');
        //            if (arr.Contains(val))
        //            {
        //                return true;
        //            }
        //            return false;
        //        }
        //        else
        //        {
        //            return true;
        //        }
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        ///// <summary>
        ///// Belirtilen kullanıcının IK tablosundaki sicil alanının değerini döner
        ///// </summary>
        ///// <param name="userId"></param>
        ///// <returns></returns>
        //public static string GetSicilName(long userId)
        //{
        //    string loginName = string.Empty;
        //    using (UnitOfWork.Start())
        //    {
        //        Digiturk.Workflow.Digiflow.Entities.HRUsers user;
        //        var lg = WFRepository<FLogin>.GetEntityList(NHibernate.Criterion.DetachedCriteria.For<FLogin>().Add(NHibernate.Criterion.Expression.Eq("LoginId", userId)));
        //        if (lg.Count > 0)
        //        {
        //            loginName = WFRepository<FLogin>.GetEntity(userId).LoginName;
        //            user = Digiturk.Workflow.Digiflow.Actions.DBSLiveHelper.GetHrUser(loginName);
        //            loginName = user.SICIL.ToString();
        //        }
        //    }
        //    return loginName;
        //}

        ///// <summary>
        ///// Employee Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetEmployeeRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["EmployeeRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1222";
        //    }
        //}

        ///// <summary>
        ///// Outsource Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetOutsourceRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["OutsourceRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1244";
        //    }
        //}

        ///// <summary>
        ///// Trainee Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetTraineeRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["TraineeRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1243";
        //    }
        //}
        ///// <summary>
        ///// Delegation Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetDelegationRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["DelegationRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1247";
        //    }
        //}
        ///// <summary>
        ///// JumpToState Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetJumpToStateRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["JumpToStateRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1249";
        //    }
        //}
        ///// <summary>
        ///// Education Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetEducationRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["EducationRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1267";
        //    }
        //}

        ///// <summary>
        ///// GSM Request Workflow Definition Id
        ///// </summary>
        ///// <returns></returns>
        //public static string GetGSMRequestDefinitionId()
        //{
        //    try
        //    {
        //        return ConfigurationManager.AppSettings["GSMRequestDefinitionId"].ToString();
        //    }
        //    catch
        //    {
        //        return "1289";
        //    }
        //}

        ///// <summary>
        ///// Açılmış olan sayfaya bakarak hangi iş akışı için işlem yapıldığını belirler
        ///// </summary>
        ///// <returns></returns>
        //public static WorkflowType DetectWorkflowType()
        //{
        //    WorkflowType wf = WorkflowType.None;
        //    string url = HttpContext.Current.Request.ServerVariables["URL"].ToString();

        //    if (url.IndexOf("gsmrequest.aspx", StringComparison.OrdinalIgnoreCase) > -1)
        //    {
        //        return WorkflowType.GSMRequest;
        //    }
        //    else if (url.IndexOf("employeerequest.aspx", StringComparison.OrdinalIgnoreCase) > -1)
        //    {
        //        return WorkflowType.EmployeeRequest;
        //    } // Bu Form Tek bir parametre ile çalışıyor bu kısmı ayrıştırmamız gerekli
        //    else if (url.IndexOf("outsourcerequest.aspx", StringComparison.OrdinalIgnoreCase) > -1)
        //    {
        //        return WorkflowType.OutsourceRequest;
        //    }// Bu Form Tek bir parametre ile çalışıyor bu kısmı ayrıştırmamız gerekli
        //    else if (url.IndexOf("traineerequest.aspx", StringComparison.OrdinalIgnoreCase) > -1)
        //    {
        //        return WorkflowType.TraineeRequest;
        //    }
        //    else if (url.IndexOf("Delegation.aspx", StringComparison.OrdinalIgnoreCase) > -1)
        //    {
        //        return WorkflowType.DelegationRequest;
        //    }

        //    return wf;
        //}

        ///// <summary>
        ///// Belirtilen workflowun sayfasını döner
        ///// </summary>
        ///// <param name="wfName"></param>
        ///// <returns></returns>
        //public static string GetWorkflowPage(string wfName)
        //{
        //    string ret = string.Empty;

        //    switch (wfName)
        //    {
        //        case "GSM Talebi":
        //            ret = "GsmRequest.aspx";
        //            break;
        //        case "Personel Talebi":
        //            ret = "EmployeeRequest.aspx";
        //            break;
        //        default:
        //            break;
        //    }

        //    return ret;
        //}
    }
}