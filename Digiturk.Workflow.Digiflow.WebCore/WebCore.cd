﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Digiturk.Workflow.Digiflow.WebCore.BasePage" Collapsed="true">
    <Position X="2" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>ACQAAAAAECCQAgBAAAAAAAAAAIoAACCAAoABAAABACA=</HashCode>
      <FileName>WebPage\BasePage.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Digiturk.Workflow.Digiflow.WebCore.SecurePage">
    <Position X="3.75" Y="2" Width="1.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAEAAAAAAAAAAAAAAAAAAAQIAQAAAAAAQAA=</HashCode>
      <FileName>WebPage\SecurePage.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Digiturk.Workflow.Digiflow.WebCore.UnSecurePage">
    <Position X="0.5" Y="2" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=</HashCode>
      <FileName>WebPage\UnSecurePage.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Class Name="Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage">
    <Position X="2.75" Y="4.5" Width="3" />
    <Compartments>
      <Compartment Name="Properties" Collapsed="true" />
      <Compartment Name="Nested Types" Collapsed="false" />
    </Compartments>
    <NestedTypes>
      <Delegate Name="Digiturk.Workflow.Digiflow.WebCore.WorkFlowPage.ActionOperation">
        <TypeIdentifier>
          <NewMemberFileName>WebPage\WorkFlowPage.cs</NewMemberFileName>
        </TypeIdentifier>
      </Delegate>
    </NestedTypes>
    <TypeIdentifier>
      <HashCode>AIUgJjUowEwBIACFgAAQkAhyEAoHgVQjBCgxACTzwEA=</HashCode>
      <FileName>WebPage\WorkFlowPage.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Font Name="Tahoma" Size="8.25" />
</ClassDiagram>