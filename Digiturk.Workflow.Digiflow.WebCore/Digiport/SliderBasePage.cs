﻿using DigiportMenuDisplayHelpers.Base;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Digiturk.Workflow.Digiflow.WebCore.Digiport
{
    public class SliderBasePage : CommonBasePage
    {
        public int slideType = 0;
        public string slideWidth = "470px";
        public string slideHeight = "310px";
        public string thumbWidth = "150px";
        public string thumbHeight = "70px";
        public int autoSlideInterval = 5000;
        public float transitionDuration = (float)0.8;
        public int effectType = 1;
        public int transitionEasing = 1;
        public int transitionEasing2 = 1;
        public int titleMode = 0;
        public bool showProgressBar = false;
        public bool showThumbnails = false;
        public string componentTitle { get; set; }

        protected override void OnInit(EventArgs e)
        {
            componentTitle = string.Empty;
            if (!string.IsNullOrEmpty(Request.QueryString["component-title"]))
                componentTitle = Request.QueryString["component-title"];
            if (string.IsNullOrEmpty(Request.QueryString["slidetype"]))
                throw new Exception();
            if (!Int32.TryParse(Request.QueryString["slidetype"], out slideType))
                throw new Exception();
            DigiportMenuDisplayHelpers.SlideOptions options = new DigiportMenuDisplayHelpers.SlideHtmlHelper().GetSlidesOptions(slideType);
            if (options != null)
            {
                slideWidth = options.slideWidth;
                slideHeight = options.slideHeight;
                autoSlideInterval = options.autoSlideInterval;
                transitionDuration = options.transitionDuration;
                effectType = (int)options.effectType;
                transitionEasing = (int)options.transitionEasing;
                transitionEasing2 = (int)options.transitionEasing2;
                titleMode = (int)options.titleMode;
                showProgressBar = options.showProgressBar;
                showThumbnails = options.showThumbnails;
                thumbWidth = options.thumbWidth;
                thumbHeight = options.thumbHeight;
            }
            base.OnInit(e);
        }
    }
}
