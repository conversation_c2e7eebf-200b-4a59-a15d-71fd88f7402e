﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Digiturk.Workflow.Digiflow.WebCore.Digiport
{
    public class ComponentBasePage: CommonBasePage
    {
        public int componentType = 0;
        public string componentTitle { get; set; }
        protected override void OnInit(EventArgs e)
        {
            componentTitle = string.Empty;
            if (!string.IsNullOrEmpty(Request.QueryString["component-title"]))
                componentTitle = Request.QueryString["component-title"];
            if (string.IsNullOrEmpty(Request.QueryString["component-type"]))
                throw new Exception();
            if (!Int32.TryParse(Request.QueryString["component-type"], out componentType))
                throw new Exception();
                base.OnInit(e);
        }
    }
}
