﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites
{
    public class FlowAdminOprObject
    {
        public DateTime OperationStartTime { get; set; }
        public long RealUserId { get; set; }
        public long DelegateUserId { get; set; }

        public FlowAdminOprObject(long InstanceId, long DefId)
        {
            OperationStartTime = DateTime.Now;
            RealUserId = Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
            //Todo Delegasyon Change
            DelegateUserId = WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(RealUserId, DefId, DateTime.Now);
            //DelegateUserId = WorkflowDelegationHelper.GetActiveDelegate(RealUserId, DefId, DateTime.Now);
        }
    }
}