﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using System;
using System.Data;

namespace Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites
{
    public class DelegationObject
    {
        public DelegationObject(long DelegationUserId)
        {
            DataTable Result = WorkflowDelegationHelper.GetAllDelegations(DelegationUserId, DateTime.Now);
            if (Result.Rows.Count > 0)
            {
                RequestID = ConvertionHelper.ConvertValue<long>(Result.Rows[0]["WF_DELEGATION_ID"]);
                OwnerLoginId = ConvertionHelper.ConvertValue<long>(Result.Rows[0]["DELEGATION_OWNER_REF_ID"]);
                DelegatedLoginId = ConvertionHelper.ConvertValue<long>(Result.Rows[0]["DELEGATE_REF_ID"]);
                StartTime = ConvertionHelper.ConvertValue<DateTime>(Result.Rows[0]["DELEGATION_START_DATE"]);
                EndTime = ConvertionHelper.ConvertValue<DateTime>(Result.Rows[0]["DELEGATION_END_DATE"]);
                WorkflowDefId = ConvertionHelper.ConvertValue<long>(Result.Rows[0]["WORKFLOW_DEF_ID"]);
                DelegationComment = ConvertionHelper.ConvertValue<string>(Result.Rows[0]["DELEGATION_COMMENT"]);
            }
        }

        public long RequestID { get; private set; }
        public string DelegationComment { get; set; }
        public long OwnerLoginId { get; set; }
        public long DelegatedLoginId { get; set; }
        public long WorkflowDefId { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public DateTime Created { get; set; }
        public long CreatedBy { get; set; }
        public DateTime LastUpdated { get; set; }
        public long LastUpdatedBy { get; set; }
        public long VersionID { get; set; }
    }
}