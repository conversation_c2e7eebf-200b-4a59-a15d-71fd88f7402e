﻿using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using System;
using System.Globalization;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public class SecurePage : BasePage
    {
     
        private string UserName
        {
            get
            {
                string User = Page.User.Identity.Name.ToUpper(System.Globalization.CultureInfo.CurrentCulture);

                User = User.Replace("İ", "I");
                User = User.Replace("Ö", "O");
                User = User.Replace("Ü", "U");
                return User;
            }
        }

        public void TriggerInitializeCulture()
        {
            this.InitializeCulture();
        }

        protected override void InitializeCulture()
        {
            Session["UI_Dil"] = UICultureClass.ChangeCulture(UserInformation.LoginObject.LoginId, Session["UI_Dil"] == null ? string.Empty : Session["UI_Dil"].ToString());
            //CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo(Session["UI_Dil"].ToString());
            //CultureInfo.DefaultThreadCurrentCulture = new CultureInfo(Session["UI_Dil"].ToString());
            //CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo(Session["UI_Dil"].ToString());
            //CultureInfo.DefaultThreadCurrentCulture = new CultureInfo(Session["UI_Dil"].ToString());
            System.Threading.Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.GetCultureInfo(Session["UI_Dil"].ToString());
            System.Threading.Thread.CurrentThread.CurrentCulture = new System.Globalization.CultureInfo("tr-TR");
            //System.Threading.Thread.CurrentThread.CurrentUICulture = new System.Globalization.CultureInfo("tr-TR");
            //System.Threading.Thread.CurrentThread.CurrentCulture = System.Globalization.CultureInfo.GetCultureInfo(Session["UI_Dil"].ToString());
            //System.Threading.Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.GetCultureInfo(Session["UI_Dil"].ToString());
            //System.Threading.Thread.CurrentThread.CurrentCulture = System.Globalization.CultureInfo.GetCultureInfo(Session["UI_Dil"].ToString());
            //System.Threading.Thread.CurrentThread.CurrentUICulture = System.Globalization.CultureInfo.GetCultureInfo(Session["UI_Dil"].ToString());

            base.InitializeCulture();
        }





        public AuthenticationResult UserInformation
        {
            get
            {
                // WorkFlowTraceWorker.OracleLog("123456789", "UserInformation", "Secure Page ");
                if (Session[SessionUserVariable] == null)
                {
                    if (IsInDebugMode)
                    {
                        if (Request.QueryString[QueryStringLoginIdVariable] != null)
                        {
                            Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]), UserName);
                        }
                        else
                        {
                            Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, 0, UserName);
                        }
                    }
                    else
                    {
                        Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]), UserName);
                    }
                }
                else
                {
                    if (!((AuthenticationResult)Session[SessionUserVariable]).IsLogin)
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[QueryStringLoginIdVariable] != null)
                            {
                                /// Debug modu dikkate alarak bir kurgu düşünmemiz gerekiyor.
                                Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]), UserName);
                            }
                        }
                        else
                        {
                            Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]), UserName);
                        }
                    }
                    else
                    {
                        if (IsInDebugMode)
                        {
                            if (Request.QueryString[QueryStringLoginIdVariable] != null)
                            {
                                if (((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId != ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]))
                                {
                                    Session[SessionUserVariable] = AuthenticationManager.Execute(IsInDebugMode, ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringLoginIdVariable]), UserName);
                                }
                            }
                        }
                    }
                }
                // WorkFlowTraceWorker.OracleLog("123456789", "UserInformation Sonu", "Secure Page ");
                return (AuthenticationResult)Session[SessionUserVariable];
            }
        }

        private static void DisablePageCaching()
        {
            //Used for disabling page caching
        }

        protected override void OnInit(EventArgs e)
        {
            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "OnInit", "Secure Page ");
            //////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecurePage", "PageOnInitBeforeIsPostBack");
            if (!Page.IsPostBack && !Page.IsCallback)
            {
                try
                {
                    // ////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "SecurePage", "CheckUser");
                    if (UserInformation == null || !UserInformation.IsLogin)
                    {
                        SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.1", null);
                        Response.Redirect("Error.aspx", false);
                    }
                    if (Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower() == "businessobjectrequest.aspx")
                    {
                        if (Digiturk.Workflow.Digiflow.Authentication.DomainAuthentication.CheckDalActiveDirectory(UserInformation.LoginObject.DomainUserName, "Stajyer Users"))
                        {
                            SetSessionError("Yetkisiz Erişim", "Digiturk veri güvenliği politikaları nedeniyle stajer kullanıcılara Business Objects erişimi verilmemektedir. Anlayışınız için teşekkür ederiz.", null);
                            Response.Redirect("Error.aspx", false);
                        }
                    }
                }
                catch (Digiflow.ExceptionEntites.NotAuthenticationException ex)
                {
                    SetSessionError("Kimlik Doğrulama Hatası", "Kullanıcı bilgileriniz doğrulanamadı.2", ex);
                    Response.Redirect("Error.aspx", false);
                }
                catch (Exception ex)
                {
                    SetSessionError("Uygulama Hatası", ex.Message, ex);
                    Response.Redirect("Error.aspx", false);
                    throw ex;
                }
                System.Web.HttpContext.Current.Response.AddHeader("Pragma", "no-cache");
            }
            // WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "OnInit Sonu" , "Secure Page ");
            base.OnInit(e);
        }

        public void HandleSessionError(string title, string message, Exception instance)
        {
            Session[SessionErrorTitleVariable] = title;
            Session[SessionErrorMessageVariable] = message;
            Session[SessionExceptionInstanceVariable] = instance;
            Response.Redirect("/Error.aspx", false);
        }
    }
}