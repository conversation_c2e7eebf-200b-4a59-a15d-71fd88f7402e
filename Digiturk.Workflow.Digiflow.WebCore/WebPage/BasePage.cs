﻿using System;
using System.Configuration;
using System.Web;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public abstract class BasePage : System.Web.UI.Page
    {
        #region Form Üzerindeki Sabit Değişkenler

        public const string SessionUserVariable = "__SessionUserInfo";
        public const string SessionErrorTitleVariable = "__ErrorTitle";
        public const string SessionErrorMessageVariable = "__ErrorMessage";
        public const string SessionExceptionInstanceVariable = "__Exception";
        public const string QueryStringLoginIdVariable = "LoginId";
        public const string QueryStringInstanceId = "wfInstanceId";
        public const string QueryStringStart = "start";
        public const string QueryStringCopyInstanceId = "CopyInstanceId";
        public const string SessionFWfWorkflowInstance = "__SessionFWfWorkflowInstance";
        public const string SessionFWfActionTaskInstance = "__SessionFWfActionTaskInstance";
        public const string SessionwfContext = "__SessionwfContext";
        public const string SessionAssignedUser = "__SessionAssignedUser";
        public const string SessionFlowAdmin = "__SessionFlowAdmin";
        public const string SessionIsFlowAdmin = "__SessionIsFlowAdmin";
        public const string SessionDelegation = "__SessionDelegation";
        public const string AdminUser = "___AdminUser";

        #endregion Form Üzerindeki Sabit Değişkenler

        //protected override void OnError(EventArgs e)
        //{
        //    System.Exception Ex = Server.GetLastError();
        //    //Ex.Data;
        //    base.OnError(e);
        //    // Tarık
        //    //selim
        //}

        /// <summary>
        /// Uygulamanın Debug Modda olup olmadığını sorar.
        /// </summary>
        /// 
      
        public bool IsInDebugMode
        {
            get { return Convert.ToBoolean(ConfigurationManager.AppSettings["debugMode"].ToString()); }
        }

        /// <summary>
        /// Sayfa Üzerinde Oluşan bir hatayı kontrol etmek için kullanılır.
        /// </summary>
        /// <param name="title"> Hata Penceresinin Title Bilgisi</param>
        /// <param name="message"> Mesaj </param>
        /// <param name="instance"> Hata Yığını </param>
        public void SetSessionError(string title, string message, Exception instance)
        {
            Session[SessionErrorTitleVariable] = title;
            Session[SessionErrorMessageVariable] = message;
            Session[SessionExceptionInstanceVariable] = instance;
        }

        /// <summary>
        /// Uygulama Ram'i büyürse app.poolu restart etmek için
        /// </summary>
        /// <returns></returns>
        public static bool RecycleApplication()
        {
            bool Success = true;

            try
            {
                HttpRuntime.UnloadAppDomain();
            }
            catch (Exception ex)
            {
                Success = false;
            }
            if (!Success)
            {
                try
                {
                    string WebConfigPath = HttpContext.Current.Request.PhysicalApplicationPath + "Web.config";
                    System.IO.File.SetLastWriteTimeUtc(WebConfigPath, DateTime.UtcNow);
                }
                catch (Exception ex)
                {
                    Success = false;
                }
            }
            return Success;
        }
    }
}