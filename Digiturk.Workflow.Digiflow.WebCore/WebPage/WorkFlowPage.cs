﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Web.UI.WebControls;
using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.Authentication;
using Digiturk.Workflow.Digiflow.Authorization;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.DataAccessLayer;
using Digiturk.Workflow.Digiflow.Entities.Enums;
using Digiturk.Workflow.Digiflow.Framework;
using Digiturk.Workflow.Digiflow.WebCore.WorkFlowEntites;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public abstract class WorkFlowPage : SecurePage
    {
        public string dosyaUploadYasakliKarakterler = @"ğ@/\:*[?""']<>|{#}%~&";
        private long logDefGroupIdAction = 659; //live

        #region WorkFlowPage Standart Fonksiyonları

        /// <summary>
        ///  Bu Method ile Yeni bir Form oluşturulur.
        /// </summary>
        public abstract void CreateWorkFlow();

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda onaylanır.
        /// </summary>
        public abstract void ApprovalWorkFlow();

        /// <summary>
        ///  Bu Method ile Form bulunduğu adımda reddedilir.
        /// </summary>
        public abstract void RejectWorkFlow();

        /// <summary>
        /// Ekran üzerindeki kontrolleri disable etmek için kullanılır.
        /// </summary>
        public abstract void DisabledControl();

        /// <summary>
        /// Yeni bir Akış Başlatılıyor
        /// </summary>
        public abstract void NewWorkFlowLoading();

        /// <summary>
        /// Entitydeki değerler Kontrollere yükletir.
        /// </summary>
        public abstract void LoadEntityToControls();

        /// <summary>
        ///  Kontrollerin veri kaynakları bind edilir.
        /// </summary>
        public abstract void LoadDataBinding();

        /// <summary>
        /// Kontrolleri Enable etmek için kullanılır
        /// </summary>
        public virtual void EnabledToControl()
        {
        }

        /// <summary>
        /// Akış Tamamlandıktan Sonra İptal Edilme işlemi Söz konusu ise bu method akış bazlı ovveride edilerek şekillendirilir ve CancelWorkFlow() methodu içinden çağrılır.
        /// </summary>
        public virtual void CancelEndFlow()
        {
        }

        /// <summary>
        /// Akış birinin üzerine görev atama şekliyle atandığında Çalıştırılır.
        /// </summary>
        /// <param name="SendTaskUserId"></param>
        public virtual void SendTaskWorkFlow(long SendTaskUserId)
        {
        }

        /// <summary>
        /// Sayfanın Altındaki ActionTab Durumu değiştirilir.
        /// </summary>
        /// <param name="newRequestTabPanelVisible"></param>
        /// <param name="approveRejectTabPanelVisible"></param>
        /// <param name="forwardTabPanelVisible"></param>
        /// <param name="suspendResumeTabPanelVisible"></param>
        /// <param name="abortTabPanelVisible"></param>
        /// <param name="addCommentTabPanelVisible"></param>
        /// <param name="rollbackTabPanelVisible"></param>
        /// <param name="fileUploadTabPanelVisible"></param>
        public abstract void ChangeVisibilityOfTabs(bool newRequestTabPanelVisible, bool approveRejectTabPanelVisible, bool forwardTabPanelVisible, bool suspendResumeTabPanelVisible, bool abortTabPanelVisible, bool addCommentTabPanelVisible, bool rollbackTabPanelVisible, bool fileUploadTabPanelVisible, bool sendToCommentTabPanelVisible);

        /// <summary>
        /// Sayfafa Bilgi Mesajı yayınlanır.
        /// </summary>
        /// <param name="Title"></param>
        /// <param name="Message"></param>
        public abstract void ShowInformation(string Title, string Message);

        #endregion WorkFlowPage Standart Fonksiyonları

        #region WorkFlowForm Properties

        /// <summary>
        /// Kabul State inde kullanılacak ActionTipi
        /// </summary>
        public WorkflowHistoryActionType AcceptActionType
        {
            get
            {
                if (Session["_AcceptActionType"] == null)
                {
                    Session["_AcceptActionType"] = WorkflowHistoryActionType.STARTED;
                }
                return ((WorkflowHistoryActionType)Session["_AcceptActionType"]);
            }
            set
            {
                Session["_AcceptActionType"] = value;
            }
        }

        /// <summary>
        /// Akışın Durdurulup Durdurulmadığını bilir.
        /// </summary>

        public bool IsSystemAdmin
        {
            get
            {
                return Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkflowAdminHelpers.IsSystemAdmin(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
            }
        }

        public bool FlowIsSuspend
        {
            get
            {
                if (InstanceId > 0)
                {
                    if (CurrentActionTaskInstance != null)
                    {
                        return CurrentActionTaskInstance.StartTime > DateTime.Now;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }
        }

        /// <summary>
        /// Akış içerisinde Girilen açıklama
        /// </summary>
        public string Commend
        {
            get { return Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.ParaBirimiDonustur(_Comment.TrimStart().TrimEnd()); }
            set { _Comment = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.ParaBirimiDonustur(value); }
        }

        /// <summary>
        /// Comment Açıklaması lokal değişkeni
        /// </summary>
        private string _Comment;

        /// <summary>
        /// Mail Gönderme işleminde WFContex i doldurmak için kullanılır.
        /// </summary>
        public ContextObject SenderObj { get; set; }

        /// <summary>
        /// Maili Göndericek kullanıcıların listesini tutar
        /// </summary>
        public List<FLogin> ToList { get; set; }

        /// <summary>
        /// Sayfayla İlgili Mesajları Base de tutar
        /// </summary>
        public string PageShowInformation { get; set; }

        /// <summary>
        ///
        /// </summary>
        public long CopyInstanceId
        {
            get
            {
                if (Request.QueryString[QueryStringCopyInstanceId] != null)
                {
                    return ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringCopyInstanceId]);
                }
                else
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// Form Üzerine Parametre ile gönderilen InstaceId değerini tutar. Devamlı Readonlydir.
        /// </summary>
        public long InstanceId
        {
            get
            {
                if (Request.QueryString[QueryStringInstanceId] != null)
                {
                    return ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringInstanceId]);
                }
                else
                {
                    return 0;
                }
            }
        }

        /// <summary>
        ///  Bu Forma Ait Başka bir Kullanıcıya bir delegasyon olup olmadığını tespit eder.
        /// </summary>
        public bool FormDelegation
        {
            get
            {
                if (InstanceId == 0) return false;
                foreach (var item in AssignToIdListLong)
                {
                    if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, DateTime.Now))
                    {
                        return true;
                    }
                }
                return false;
            }
        }



        /// <summary>
        /// Formu Açan kullanıcı Akışın içerisinde mi?
        /// </summary>
        public bool IsActionFlow
        {
            get
            {
                return WorkFlowInformationHelper.IsActionFlow(InstanceId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
            }
        }

        /// <summary>
        /// Delege edilen kişi
        /// </summary>
        public bool OwnDelegation
        {
            get
            {
                if (InstanceId == 0) return false;
                return WorkFlowInformationHelper.DelegateCheck(InstanceId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, DateTime.Now);
            }
        }

        public long CurrentWFDefId
        {
            get
            {
                long WfDefID = 0;
                using (UnitOfWork.Start())
                {
                    try
                    {
                        WfDefID = ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId;
                    }
                    catch (Exception)
                    {
                    }
                }
                return WfDefID;
            }
        }
        public string CurrentWFDefName
        {
            get
            {
                string WfDefName = null;
                using (UnitOfWork.Start())
                {
                    try
                    {
                        WfDefName = ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.Name;
                    }
                    catch (Exception)
                    {
                    }
                }
                return WfDefName;
            }
        }

        /// <summary>
        ///  Bu Properties Forma Ait Akış Örneği nesnesini tutar
        /// </summary>
        public FWfWorkflowInstance CurrentWfIns
        {
            get
            {
                if (InstanceId > 0)
                {
                    try
                    {
                        if (CopyInstanceId == 0)
                        {
                            if (Session[SessionFWfWorkflowInstance] != null)
                            {
                                if (((FWfWorkflowInstance)Session[SessionFWfWorkflowInstance]).WfWorkflowInstanceId != InstanceId)
                                {
                                    Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                                }
                            }
                            else
                            {
                                Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                            }
                        }
                    }
                    catch (Exception)
                    {
                        Response.Redirect("~/inbox.aspx", true);
                        Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                    }
                }
                else if (CopyInstanceId > 0)
                {
                    Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(CopyInstanceId);
                }
                else
                {
                    return null;
                }
                return ((FWfWorkflowInstance)Session[SessionFWfWorkflowInstance]);
            }
            //27.09.2012 Kerem
            set
            {
                Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
            }
        }

        /// <summary>
        ///  Bu Properties Forma Ait Action task instance örneğini tutar.
        /// </summary>
        public FWfActionTaskInstance CurrentActionTaskInstance
        {
            get
            {
                using (UnitOfWork.Start())
                {
                    try
                    {
                        if (Session[SessionFWfActionTaskInstance] != null)
                        {
                            if (((FWfActionTaskInstance)Session[SessionFWfActionTaskInstance]).WfActionInstanceId != CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value || CurrentWfIns.WfCurrentState != null)
                            {
                                Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                            }
                        }
                        else
                        {
                            if (CurrentWfIns != null && CurrentWfIns.WfCurrentState != null)
                            {
                                Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                            }
                            else
                            {
                                return null;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Session[SessionFWfWorkflowInstance] = null;
                        if (CurrentWfIns == null)
                        {
                            Session[SessionFWfActionTaskInstance] = null;
                        }
                        else if (CurrentWfIns.WfCurrentState != null && CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId != null && CurrentStateDef.WfStateType.WfStateTypeCd == "MIDDLE")
                        {
                            Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                        }
                        else
                        {
                            Session[SessionFWfActionTaskInstance] = null;
                        }
                        System.Exception exs = ex;
                    }
                }
                return ((FWfActionTaskInstance)Session[SessionFWfActionTaskInstance]);
            }
        }

        /// <summary>
        /// Bu Properties WFContext Nesnesini Tutar
        /// </summary>
        public WFContext CurrentWFContext
        {
            get
            {
                if (Session[SessionwfContext] == null)
                {
                    using (UnitOfWork.Start())
                    {
                        FWfWorkflowInstance LocalIns = CurrentWfIns;
                        FWfActionTaskInstance LocalActionTaskIns = CurrentActionTaskInstance;
                        checked
                        {
                            Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                            Session[SessionwfContext] = new WFContext(CurrentWfIns);
                        }
                    }
                }
                return ((WFContext)Session[SessionwfContext]);
            }

            set
            {
                Session[SessionwfContext] = value;
            }
        }

        /// <summary>
        ///  Şuanda üzerinde bulunan state instance
        /// </summary>
        public FWfStateInstance CurrentStateIns
        {
            get
            {
                if (CurrentWfIns.WfCurrentState != null)
                {
                    Digiturk.Workflow.Entities.FWfStateInstance StateIns = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    return StateIns;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        ///  Şuanda üzerinde bulunan state Definition
        /// </summary>
        public FWfStateDef CurrentStateDef
        {
            get
            {
                Digiturk.Workflow.Entities.FWfStateDef StateDef = WFRepository<FWfStateDef>.GetEntity(CurrentStateIns.WfStateDef.WfStateDefId);
                return StateDef;
            }
        }

        public long WfDefID
        {
            get
            {
                string sayfa = SetPageName(PageName).ToLower();
                string wfDefId = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WorkFlowDefinitionHelper.GetNodeAttributeName(sayfa, "VALUE");
                if (wfDefId == "")
                {
                    wfDefId = "0";
                }
                return ConvertionHelper.ConvertValue<long>(wfDefId);
            }
        }

        /// <summary>
        /// Bu Properties Bu Akışa Assign edilmiş kullanıcıyı getirir. FLogin Tipinden Döndürür
        /// </summary>
        public FLogin AssignedUser
        {
            get
            {
                if (FormDelegation)
                {
                    /// Form Delege edilmişse
                    if (AssignToIdListLong.Count > 1)
                    {
                        // Birden Fazla Kişiye Delege edilmişse bu kişi kimin delegesi ise burdan delege edilenin Id si dönmeli.
                        //WorkflowDelegationHelper.(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                        foreach (var item in AssignToIdListLong)
                        {
                            if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, DateTime.Now))
                            {
                                FLogin AssignedUsr = WFRepository<FLogin>.GetEntity(item);
                                return AssignedUsr;
                            }
                        }
                        if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                        {
                            return WFRepository<FLogin>.GetEntity(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                        }
                        else
                        {
                            return FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                        }
                    }
                    else
                    {
                        return FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                    }
                }
                else
                {
                    if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                    {
                        return WFRepository<FLogin>.GetEntity(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                    }
                    else
                    {
                        return FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                    }
                    //return FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
                }
            }
        }

        /// <summary>
        /// Login Olan Kişi Akış Admini ise Flogini döner
        /// </summary>
        public FLogin FlowAdmin
        {
            get
            {
                FLogin sonuc = null;
                if (IsFlowAdmin)
                {
                    sonuc = (FLogin)WFRepository<FLogin>.GetEntity(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                }

                return sonuc;
            }
        }

        /// <summary>
        ///  Login olan Kişinin Akış Admini olup olmadığını Tespit eder.
        /// </summary>
        public bool IsFlowAdmin
        {
            get
            {
                bool sonuc = false;
                if (CurrentWfIns != null)
                {
                    sonuc = WorkflowAdminHelpers.IsWfAdmin(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                }

                return sonuc;
            }
        }
        /// <summary>
        /// AssignEdilen Kullanıcıların Listesini bulunduran Fonksiyon
        /// </summary>
        public List<long> AssignToIdListLong
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAssignLoginList(InstanceId);
            }
        }

        /// <summary>
        /// AssignEdilen Kullanıcıların Listesini bulunduran Fonksiyon
        /// </summary>
        public List<FLogin> AssignToIdList
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(InstanceId);
            }
        }

        /// <summary>
        /// Atanan kullanıcının Login Id si
        /// </summary>
        public long AssignToId
        {
            get
            {
                if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                {
                    return ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
                }
                else
                {
                    return Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
                }
            }
        }

        /// <summary>
        /// Atanan kullanıcının Login Id si
        /// </summary>
        public long AuthorizedAssignToId
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAuthorizedAssignToLoginId(WfDefID, CurrentStateDef.WfStateDefId);
            }
        }

        /// <summary>
        /// Yoruma gönderilen kullanıcı
        /// </summary>
        public long CommendToId
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetCommentToLoginId(InstanceId);
            }
        }

        /// <summary>
        /// Yoruma gönderilen Kullanıcıların Listesini bulunduran Fonksiyon
        /// </summary>
        public List<long> CommendToIdLongList
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetCommendToLoginIDList(InstanceId);
            }
        }

        /// <summary>
        /// Yoruma gönderilen Kullanıcıların Listesini bulunduran Fonksiyon
        /// </summary>
        public List<long> CommendToDelegeIdLongList
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetCommendToLoginDelegeIDList(InstanceId);
            }
        }



        /// <summary>
        /// Yoruma gönderilen Kullanıcıların Listesini bulunduran Fonksiyon
        /// </summary>
        public List<FLogin> CommendToIdList
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetCommendToLoginList(InstanceId);
            }
        }

        /// <summary>
        /// Delege edilmiş kullanıcı
        /// </summary>
        public long DelegationUserId
        {
            get
            {
                //Todo Delegasyon Change
                return WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                //return WorkflowDelegationHelper.GetActiveDelegate(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
            }
        }

        /// <summary>
        /// Son işlem yapmış kullanıcı
        /// </summary>
        public long LastSendTaskLoginId
        {
            get
            {
                return DataAccessLayer.CheckingWorker.GetLastActionToLoginId(InstanceId);
            }
        }

        /// <summary>
        /// Son işlem yapmış kullanıcılar
        /// </summary>
        public string[] LastSendTaskLoginIdList
        {
            get
            {
                return DataAccessLayer.CheckingWorker.GetLastActionToLoginIdList(InstanceId);
            }
        }

        /// <summary>
        /// Son İşlem yapmış kullanıcı mı?
        /// </summary>
        public bool IsLastSendTaskLoginUser
        {
            get
            {
                return LastSendTaskLoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
            }
        }

        /// <summary>
        /// Akış Historyden mi Açılıyor?
        /// </summary>
        public bool IsOpenHistory
        {
            get
            {
                if (Request.QueryString["history"] != null) { if (Request.QueryString["history"] == "hq") { return true; } }
                return false;
            }
        }

        /// <summary>
        /// Bu Akışın bulunduğu Noktadan Geri alınabilir mi?
        /// </summary>
        public bool IsRolledBack
        {
            get
            {
                using (UnitOfWork.Start())
                {
                    FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                    FWfStateInstance prevStateIns = WfIns.WfWorkflowInstanceFWfStateInstanceList.OrderByDescending(t => t.WfStateInstanceId).ToList()[1];
                    return prevStateIns.WfStateDef.WfStateType.WfStateTypeCd == "MIDDLE";
                }
            }
        }

        /// <summary>
        /// Bu kullanıcının görüntüleme yetkisi var mı?
        /// </summary>
        public bool IsViewUser
        {
            get
            {
                return Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.IsViewUser(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            }
        }

        public bool IsReportAdmin
        {
            get
            {
                return Digiflow.WorkFlowHelpers.ReportInformationHelper.IsReportAdmin(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
            }
        }

        #endregion WorkFlowForm Properties

        #region WorkFlowPage Mevcut Fonksiyonları

        /// <summary>
        /// Geri Gönderme Fonksiyon
        /// </summary>
        public void SendBack()
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            using (UnitOfWork.Start(true))
            {
                ActionTaskWorker.TakeOn(CurrentActionTaskInstance.WfActionInstanceId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                long WorkflowInstanceId = CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId;
                long AssignToLoginId = Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(WorkflowInstanceId);
                if (AssignToLoginId == 0)
                {
                    AssignToLoginId = CurrentWfIns.OwnerLogin.LoginId;
                }
                //long NewAssignLoginId = Digiflow.DataAccessLayer.CheckingWorker.GetManager(AssignToLoginId); Murat Kasapoğlu değiştirdi.06.01.2025 alternatif yönetici
                long NewAssignLoginId = Digiflow.DataAccessLayer.CheckingWorker.GetManager(AssignToLoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
                CurrentWFContext.Parameters.AddOrChangeItem("NewAssignLoginId", NewAssignLoginId);
                if (!CurrentWFContext.Parameters.ContainsKey("WfInstanceId")) CurrentWFContext.Parameters.AddOrChangeItem("WfInstanceId", InstanceId);
                CurrentWFContext.Parameters.AddOrChangeItem("Onay", "SendBack");
                //if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignedUser.LoginId));
                if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
                if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
                CurrentWFContext.Save();
                ActionTaskWorker.Send(CurrentActionTaskInstance.WfActionInstanceId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                WfHistoryExec(WorkflowHistoryActionType.SENDBACK, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                UnitOfWork.Commit();
            }
        }

        /// <summary>
        /// İş Akışını Yönlendirir
        /// </summary>
        public void ForwardWorkFlow(long FwUserId)
        {
            if (FwUserId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendirilecek Kullanıcıyı Seçiniz");
            }
            if (string.IsNullOrEmpty(Commend))
            {
                //throw new Exception("Yorum alanı boş bırakılamaz.");
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            long AssignUserId = 0;
            long ActionUserId = ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
            if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
            {
                AssignUserId = ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
            }
            else
            {
                AssignUserId = AssignToId;
            }
            if (FwUserId == null || FwUserId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yönlendireceğiniz kullanıcıyı seçiniz");
            }
            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Forward İşlemi Yapılıyor

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ForwardWorkFlow(InstanceId, CurrentActionTaskInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject, FwUserId);

                #endregion Forward İşlemi Yapılıyor

                #region Yönlendirilen Kişiye Mail Atılıyor

                var FwLogin = WFRepository<FLogin>.GetEntity(FwUserId);
                ToList.Add(FwLogin);
                SenderObj.Add("Action", "yönlendirildi");
                SenderObj.Add("ActionEng", "Forwarded");
                SenderObj.Add("ActionDescription", Commend);
                //SenderObj.Add("ActionOwner",WfDataHelpers.GetCommentsLoginString(WfDataHelpers.GetLastComment(CurrentWfIns)));
                SendMail(FwUserId, 9, "yönlendirildi", "Forwarded", false);

                #endregion Yönlendirilen Kişiye Mail Atılıyor

                #region Formu Oluşturan Kişiye Mail Atılıyor

                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("ActionDescription", Commend);
                //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                //CurrentWFContext.Parameters["ActionTo"] = CurrentWFContext.Parameters["ActionTo"].ToString().Replace("(ilgili kişi:", "").Replace(")", "");
                CurrentWFContext.Parameters["ActionTo"] = CurrentWFContext.Parameters["ActionTo"].ToString().Replace("(", "").Replace(")", "");
                CurrentWFContext.Parameters.AddOrChangeItem("ActionToPersonel", WfDataHelpers.GetLoginNameSurname(FwLogin));
                SendMail(0, 997, "yönlendirildi", "Forwarded", false);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WfDataHelpers.GetLoginNameSurname(FwLogin));
                CurrentWFContext.Save();

                //if (CurrentPageMode == PageMode.Modify)
                //{
                //    FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                //    ToList.Add(ToListItem);
                //    SenderObj.Add("ActionDescription", Commend);
                //    SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                //    SendMail(CurrentWfIns.OwnerLogin.LoginId, 8, "yönlendirildi", false);

                //}
                //else
                //{
                //}

                #endregion Formu Oluşturan Kişiye Mail Atılıyor

                #region History Kaydı Oluşturuluyor

                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, ActionUserId, AssignUserId, WorkflowHistoryActionType.FORWARD, Commend);
                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, AssignToId, AssignToId, WorkflowHistoryActionType.ASSIGN, "");

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
            DisabledControl();
        }

        /// <summary>
        /// Yoruma Gönderme İşlemi Yapılır.
        /// </summary>
        public void SendtoCommendWorkFlow(long SendtoCommendUserId)
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            if (SendtoCommendUserId == null || SendtoCommendUserId == 0)
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Lütfen Yoruma Gönderilecek Kullanıcıyı Seçiniz");
            }
            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Yoruma Gönderme İşlemi Yapılır

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.SendtoCommendWorkFlow(InstanceId, CurrentActionTaskInstance, SendtoCommendUserId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);

                #endregion Yoruma Gönderme İşlemi Yapılır

                #region Yoruma Gönderilen Kişiye Mail Atılıyor

                var FwLogin = WFRepository<FLogin>.GetEntity(SendtoCommendUserId);
                ToList.Add(FwLogin);
                SenderObj.Add("Action", "yoruma gönderildi");
                SenderObj.Add("ActionEng", "Forwarded for remarks");
                SenderObj.Add("ActionDescription", Commend);

                //Delegesine de mail atılır.
                long WfDelegationId = WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(SendtoCommendUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                string DelegationNameSurName = "";
                string DelegationByNameSurName = "";
                if (WfDelegationId > 0)
                {

                    DelegationNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfDelegationId);
                    DelegationByNameSurName = DelegationNameSurName + "(" + WflowDataHelpers.LoginNameSurnameGet(SendtoCommendUserId) + " Delegesi)";

                    //var FwLogin2 = WFRepository<FLogin>.GetEntity(WfDelegationId);
                    //ToList.Add(FwLogin2);

                    SenderObj.Add("ActionTo", String.Format(DelegationByNameSurName));
                }

                SendMail(SendtoCommendUserId, 9, "yoruma gönderildi", "Forwarded for remarks", false);


                #endregion Yoruma Gönderilen Kişiye Mail Atılıyor

                #region Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
                ToList.Add(CurrentWfIns.OwnerLogin);
                SenderObj.Add("ActionDescription", Commend);
                //SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));


                if (WfDelegationId > 0)
                {

                    DelegationNameSurName = Digiturk.Workflow.Digiflow.Authorization.WflowDataHelpers.LoginNameSurnameGet(WfDelegationId);
                    DelegationByNameSurName = DelegationNameSurName + "(" + WflowDataHelpers.LoginNameSurnameGet(SendtoCommendUserId) + " Delegesi)";

                    //var FwLogin2 = WFRepository<FLogin>.GetEntity(WfDelegationId);
                    //ToList.Add(FwLogin2);

                    SenderObj.Add("ActionTo", String.Format(DelegationByNameSurName));
                }
                else
                {
                    SenderObj.Add("ActionTo", String.Format("({0})", WfDataHelpers.GetLoginNameSurname(FwLogin)));
                }



                SendMail(0, 997, "yoruma gönderildi", "Forwarded for remarks", false);

                //if (CurrentPageMode == PageMode.Modify)
                //{
                //}
                //else
                //{
                //    ToList.Add(CurrentWfIns.OwnerLogin);
                //    SenderObj.Add("ActionDescription", Commend);
                //    SenderObj.Add("ActionTo", String.Format("(ilgili kişi: {0})", WfDataHelpers.GetLoginNameSurname(SendtoCommendUserId)));
                //    SendMail(0, 997, "yoruma gönderildi", FwLogin, false);
                //}

                #endregion Formu Oluşturan Kişiye Bilgilendirme Maili Atılıyor

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(WorkflowHistoryActionType.SENDTOCOMMENT);

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
        }

        /// <summary>
        ///  Yoruma gönderilen Formu Cevaplama (Yorum Ekleme) işlemi yapılır.
        /// </summary>
        public void SendRequestToCommentWorkFlow()
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw new Exception("Yorum alanı boş bırakılamaz.");
            }

            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                #region Son ActionTaskInstanceÜretilir

                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfActionTaskInstance taskIns = WfDataHelpers.GetLastActionTaskInstance(WfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList.ToList().OrderBy(t => t.WfActionInstanceId));
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, WfIns.WfWorkflowDef.WfWorkflowDefId);

                #endregion Son ActionTaskInstanceÜretilir

                #region Yorumu Cevaplama İşlemi Yapılır

                //ActionHelpers.SendRequestToCommentWorkFlow(WfIns, taskIns, ( (AuthenticationResult)Session[SessionUserVariable]).LoginObject, CurrentPageMode, Commend);

                #endregion Yorumu Cevaplama İşlemi Yapılır

                #region History Kaydı Oluşturuluyor

                //if (ActionTaskWorker.GetCommentInboxList(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId).Contains(CurrentActionTaskInstance))
                if (Digiflow.WorkFlowHelpers.FormInformationHelper.IsWFCommentInbox(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, InstanceId) || AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                {
                    WfHistoryExec(WorkflowHistoryActionType.COMMENTED);
                    if (Digiflow.WorkFlowHelpers.FormInformationHelper.IsWFCommentInbox(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, InstanceId))
                    {
                        #region Yoruma Gönderen Kişiye Yorumunu Giriyor.

                        long SendToCommentLoginId = FormInformationHelper.SendToCommentLogin(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, InstanceId);
                        var FwLogin = WFRepository<FLogin>.GetEntity(SendToCommentLoginId);
                        ToList.Add(FwLogin);
                        SenderObj.Add("Action", "Yorum Talebi Cevaplandı");
                        SenderObj.Add("ActionEng", "Comment Request Answered");
                        SenderObj.Add("ActionDescription", Commend);
                        SendMail(SendToCommentLoginId, 9, "Yorum Talebi Cevaplandı", "Comment Request Answered", false);

                        #endregion Yoruma Gönderen Kişiye Yorumunu Giriyor.
                    }
                }
                else
                {
                    WfHistoryExec(WorkflowHistoryActionType.ADDTOCOMMEND);

                    //yorumu ekle adımında gönder 
                    long YorumGrup = ConvertionHelper.ConvertValue<int>(LogicalGroupHelper.LogicalGroupIDBul("Satinalma_YorumGrup"));
                    DataTable DtYorum = LogicalGroupHelper.GetLoginPersonelList(YorumGrup);
                    FLogin FLogin = WFRepository<FLogin>.GetEntity(UserInformation.LoginObject.LoginId);

                    Digiturk.Workflow.DigiFlow.Framework.Action.ContextList ContextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                    ContextList.AddOrChangeItems("Yorum", Commend);
                    ContextList.AddOrChangeItems("WorkFlowInsId", ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowInstanceId.ToString());
                    ContextList.AddOrChangeItems("OnayDurum", "");
                    ContextList.AddOrChangeItems("IslemYapan", WfDataHelpers.GetLoginNameSurname(UserInformation.LoginObject.LoginId));


                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAllAcceptLoginList(FLogin, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowInstanceId, DtYorum, 1007, ContextList);
                    DtYorum = null;
                }

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
            DisabledControl();
        }

        /// <summary>
        /// Handles file upload workflow operations including history tracking and notifications
        /// </summary>
        /// <param name="uploadedFiles">List of uploaded file URLs</param>
        public void FileUploadWorkFlow(List<string> uploadedFiles)
        {
            try
            {
                if (string.IsNullOrEmpty(Commend))
                {
                    throw new Exception("Yorum alanı boş bırakılamaz.");
                }

                FlowAdminOprObject flowAdminOprs;
                using (UnitOfWork.Start())
                {
                    // Get current workflow instance and task instance
                    FWfWorkflowInstance wfIns = WFRepository<FWfWorkflowInstance>.GetEntity(this.InstanceId);
                    FWfActionTaskInstance taskIns = WfDataHelpers.GetLastActionTaskInstance(
                        wfIns.WfCurrentState.WfStateInstanceFWfActionInstanceList
                        .ToList()
                        .OrderBy(t => t.WfActionInstanceId)
                    );

                    // Initialize flow admin operations
                    flowAdminOprs = new FlowAdminOprObject(this.InstanceId, wfIns.WfWorkflowDef.WfWorkflowDefId);

                    // Create HTML for file links
                    string filesHtml = string.Join("", uploadedFiles.Select(file =>
                    {
                        string fileName = file.Split('/').Last();
                        return $@" - <a target='_blank' href='{file}'>{fileName}</a><br/>";
                    }));

                    // Combine comment and files for history
                    string commentWithFiles = $@"{Commend}<br/>{filesHtml}";

                    // Add history record
                    WfHistoryExec(WorkflowHistoryActionType.FILEUPLOADED, commentWithFiles);

                    // Send email notification
                    FLogin fLogin = WFRepository<FLogin>.GetEntity(UserInformation.LoginObject.LoginId);

                    var contextList = new Digiturk.Workflow.DigiFlow.Framework.Action.ContextList();
                    contextList.AddOrChangeItems("Yorum", Commend);
                    contextList.AddOrChangeItems("Files",
                        string.Join(Environment.NewLine,
                        uploadedFiles.Select(file => $@"<a href='{file}'>{file}</a>"))
                    );
                    contextList.AddOrChangeItems("WorkFlowInsId", wfIns.WfWorkflowInstanceId.ToString());
                    contextList.AddOrChangeItems("OnayDurum", "");
                    contextList.AddOrChangeItems("IslemYapan",
                        WfDataHelpers.GetLoginNameSurname(UserInformation.LoginObject.LoginId));

                    // Send notification email
                    DataTable dtYorum = new DataTable(); // Initialize with required data if needed
                    //Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper
                    //    .SendEmailAllAcceptLoginList(fLogin, wfIns.WfWorkflowInstanceId, dtYorum, 1008, contextList);
                }

                // Check flow admin operations
                FlowAdminOperationChecking(flowAdminOprs);

                // Clear the comment after successful upload
                Commend = string.Empty;
            }
            catch (Exception ex)
            {
                throw new Exception("Dosya yükleme işlemi sırasında hata oluştu: " + ex.Message);
            }
        }

        /// <summary>
        ///  Akışı Askıya almak için çalıştırılır
        /// </summary>
        /// <param name="SuspendDate"></param>
        public void SuspendWorkFlow(DateTime SuspendDate)
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Form Askıya Alınıyor.

                DateTime delegationStartTime = DateTime.MinValue;
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.SuspendRequest(CurrentWfIns, SuspendDate, CurrentActionTaskInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject, Commend);

                #endregion Form Askıya Alınıyor.

                #region Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("ActionDescription", Commend);
                SenderObj.Add("ActionTo", "");
                SenderObj.Add("SuspendDate", CoreHelpers.DateTimeHelper.TarihFormatla(SuspendDate) + " 00:00:00");
                SendMail(0, 996, "askıya alındı", "Suspended", false);

                #endregion Form Askıya Alındıktan Sonra Bilgilendirme Yapılıyor.

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(WorkflowHistoryActionType.SUSPEND);

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
        }

        /// <summary>
        /// Akışı Devam ettirmek için çalıştırılır
        /// </summary>
        public void ResumeWorkFlow()
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            FlowAdminOprObject FlowAdminOprs;

            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Resume İşlemi Yapılıyor

                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.ResumeRequest(CurrentWfIns, CurrentActionTaskInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject, Commend);

                #endregion Resume İşlemi Yapılıyor

                #region Resume İşleminden sonra bilgilendirme yapılıyor.

                if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                {
                    return;
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("ActionDescription", Commend);
                SenderObj.Add("ActionTo", "");
                SendMail(0, 8, "devam ettirildi", "Resumed", false);
                DisabledControl();

                #endregion Resume İşleminden sonra bilgilendirme yapılıyor.

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(WorkflowHistoryActionType.RESUME);

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
        }

        /// <summary>
        /// Akışı iptal ettirme işlemi yapılması için çalışılır.
        /// </summary>
        public void CancelWorkFlow()
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

                for (int i = 0; i < AssignToIdListLong.Count; i++)
                {
                    Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                    asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(AssignToIdListLong[i].ToString(), InstanceId.ToString(), "WFVIEW", false));
                    if (asgnmt.WfAssignmentId != 0)
                    {
                        Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                    }
                }

                #endregion İptal Öncesi Atanan kişilere yapılan görüntüleme yetkilerini iptal et

                if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd == "COMPLETED")
                {
                    CancelEndFlow();
                }
                Digiturk.Workflow.Digiflow.WorkFlowHelpers.ActionHelpers.CancelWorkflow(CurrentWfIns, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject, Commend);
                CurrentWfIns = ((FWfWorkflowInstance)Session[SessionFWfWorkflowInstance]);

                #region Form İptal edildikten sonra gerekli mailing yapılıyor

                #region eski kontrol

                //if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1313 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1336 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 2112 && CurrentWfIns.WfWorkflowDef.WfWorkflowDefId != 1429) // iade de eklendi

                #endregion eski kontrol

                bool isLogical = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsDefExistLogicalGroup(logDefGroupIdAction, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, "0");

                if (!isLogical)
                {
                    if (CurrentWfIns.WfCurrentState == null || CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId == null)
                    {
                        return;
                    }
                }
                FLogin ToListItem = WFRepository<FLogin>.GetEntity(CurrentWfIns.OwnerLogin.LoginId);
                ToList.Add(ToListItem);
                SenderObj.Add("DelegateTo", "");
                SenderObj.Add("Delegated", "");
                SenderObj.Add("ActionDescription", Commend);
                SenderObj.Add("ActionTo", "");
                SendMail(0, 8, "iptal edildi", "Cancelled", false);

                #endregion Form İptal edildikten sonra gerekli mailing yapılıyor

                #region Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                List<long> AcceptUserList = Digiflow.DataAccessLayer.CheckingWorker.GetLastAcceptLoginList(InstanceId);
                for (int i = 0; i < AcceptUserList.Count; i++)
                {
                    FLogin ToListItemRed = WFRepository<FLogin>.GetEntity(AcceptUserList[i]);
                    ToList.Add(ToListItemRed);
                    SenderObj.Add("DelegateTo", "");
                    SenderObj.Add("Delegated", "");
                    SenderObj.Add("ActionDescription", Commend);
                    SenderObj.Add("ActionTo", "");
                    SenderObj.Add("WfAssigner", Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(AcceptUserList[i]));
                    SendMail(0, 995, "iptal edildi", "Cancelled", ((AuthenticationResult)Session[SessionUserVariable]).LoginObject, false);
                }

                #endregion Akış Reddedildiğinde Haberdar Edilecek Kullanıcıların listesi

                #region History Kaydı Oluşturuluyor

                WfHistoryExec(WorkflowHistoryActionType.CANCEL);

                #endregion History Kaydı Oluşturuluyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
            DisabledControl();
        }

        /// <summary>
        /// Finalize İşlemi yapılması için çalıştırılır.
        /// </summary>
        public void FinalizeWorkFlow()
        {
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            FlowAdminOprObject FlowAdminOprs;
            using (UnitOfWork.Start())
            {
                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #region Form Sonlandırılıyor

                //FinalizeWorkflow(wfIns);

                #endregion Form Sonlandırılıyor
            }
            FlowAdminOperationChecking(FlowAdminOprs);
            DisabledControl();
        }

        /// <summary>
        /// Form Rollback edileceğinde çağrılır.
        /// </summary>
        public void RollBackWorkFlow()
        {
            #region Yorum Girilmiş mi? ve Geri Alma İşlemini Yapacak kişi Gerçekten Son State de mi kontrolü Yapılıyor

            long RollBackMailTemplate = 999;
            List<FLogin> LastAssignList = AssignToIdList;
            long LastStateId = CurrentStateIns.WfStateInstanceId;
            if (string.IsNullOrEmpty(Commend))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.EmptyCommentValidationException();
            }
            if (!IsLastSendTaskLoginUser && !IsFlowAdmin && !IsSystemAdmin && !LastSendTaskLoginIdList.Contains(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId.ToString()))
            {
                throw Digiflow.CoreHelpers.ExceptionHelper.ValidationError("Geri alma işlemi son işlem yapan kişi yada akış yöneticisi tarafından yapılır.");
            }
            FlowAdminOprObject FlowAdminOprs;

            #endregion Yorum Girilmiş mi? ve Geri Alma İşlemini Yapacak kişi Gerçekten Son State de mi kontrolü Yapılıyor

            using (UnitOfWork.Start())
            {
                #region Geri Almadan Önceki Action Task Instance Kaydediliyor

                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);

                FWfActionTaskInstance PreviusActionTaskIns = CurrentActionTaskInstance;

                FlowAdminOprs = new FlowAdminOprObject(InstanceId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);

                #endregion Geri Almadan Önceki Action Task Instance Kaydediliyor

                #region Helper Üzerindeki RollBack Fonksiyonu Çağrılır

                ActionHelpers.RollbackWorkflow(WfIns, CurrentActionTaskInstance.WfActionInstanceId);

                #endregion Helper Üzerindeki RollBack Fonksiyonu Çağrılır

                #region Geri Alma Öncesi Atanmış olan kişilere

                for (int i = 0; i < LastAssignList.Count; i++)
                {
                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(LastAssignList[i].LoginId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, "Geri Alındı", "Received Back", Commend, RollBackMailTemplate, InstanceId, CurrentWFContext);
                    Digiturk.Workflow.Entities.FWfAssignment asgnmt = new Digiturk.Workflow.Entities.FWfAssignment();
                    asgnmt.WfAssignmentId = ConvertionHelper.ConvertValue<long>(Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.GetAssignmentId(LastAssignList[i].LoginId.ToString(), InstanceId.ToString(), "WFVIEW", false));
                    if (asgnmt.WfAssignmentId != 0)
                    {
                        Digiflow.WorkFlowHelpers.FormInformations.MonitoringHelper.DeleteAssignment(asgnmt.WfAssignmentId);
                    }

                    #region History Kaydı Oluşturuluyor

                    if (i == 0)
                    {
                        WfHistoryExec(PreviusActionTaskIns, WorkflowHistoryActionType.ROLLBACK);
                    }
                    else
                    {
                        WorkflowHistoryWorker.DeleteHistoryAssignLog(InstanceId, LastStateId, LastAssignList[i].LoginId);
                    }

                    #endregion History Kaydı Oluşturuluyor
                }

                #endregion Geri Alma Öncesi Atanmış olan kişilere

                #region Akışı Oluşturan Kişiye Mail Atılır

                Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(CurrentWfIns.OwnerLogin.LoginId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, "Geri Alındı", "Received Back", Commend, RollBackMailTemplate, InstanceId, CurrentWFContext);

                #endregion Akışı Oluşturan Kişiye Mail Atılır

                #region Assign İşlemine Ait History Kaydı Oluşturuluyor ve Yeniden Atanan Kullanıcılara Mail Atılır

                foreach (var AssigmentItem in AssignToIdList)
                {
                    RollBackMailTemplate = 994;
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, AssigmentItem.LoginId, AssigmentItem.LoginId, WorkflowHistoryActionType.ASSIGN, "");
                    Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailAction(AssigmentItem.LoginId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, "Geri Alındı", "Received Back", Commend, RollBackMailTemplate, InstanceId, CurrentWFContext);
                }

                #endregion Assign İşlemine Ait History Kaydı Oluşturuluyor ve Yeniden Atanan Kullanıcılara Mail Atılır

                #region Assign İşleminde İşlemi yapan kişinin görüntüleme yetkisi yoksa yetki verilir

                ActionHelpers.SetWFView(InstanceId, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);

                #endregion Assign İşleminde İşlemi yapan kişinin görüntüleme yetkisi yoksa yetki verilir
            }
            FlowAdminOperationChecking(FlowAdminOprs);
            DisabledControl();
        }

        /// <summary>
        /// Standart Form Üzerinden mail gönderme fonksiyonudur
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        public void SendMail(long ActionToLoginId, long MailTemplateId, string Action, bool IsSave)
        {
            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
                //toList.Add(flogin); // talep sahibi
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId));
            //if(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)!=null)CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetCommentsLoginString(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)));
            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
            /// Burda Sadece Yes mi set edicek Ya Reddedilirse nolucak????
            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(((AuthenticationResult)Session[SessionUserVariable]).LoginObject, AssignedUser));
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            //FWfActionTaskInstance ActionTaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentActionTaskInstance.WfActionInstanceId);
            //Dictionary<string, string> MyDic = new Dictionary<string, string>();
            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            //MailHelper.SendEmailDirect(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext,MyDic,new List<string>(),new List<string>());
            //MailHelper.SendEmailDirect(
            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
        }

        /// <summary>
        /// Standart Form Üzerinden mail gönderme fonksiyonudur
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        public void SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, bool IsSave)
        {
            if (ActionToLoginId > 0)
            {
                FLogin ActionTo = WFRepository<FLogin>.GetEntity(ActionToLoginId);
                CurrentWFContext.Parameters.AddOrChangeItem("ActionTo", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(ActionTo.LoginId));
                //toList.Add(flogin); // talep sahibi
            }
            foreach (var item in SenderObj)
            {
                CurrentWFContext.Parameters.AddOrChangeItem(item.Key, item.Value);
            }
            CurrentWFContext.Parameters.AddOrChangeItem("Action", Action);
            CurrentWFContext.Parameters.AddOrChangeItem("ActionEng", ActionEng);

            CurrentWFContext.Parameters.AddOrChangeItem("ActionDate", DateTime.Now.ToString());
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetLoginNameSurname(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId));
            //if(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)!=null)CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", WorkFlowHelpers.WfDataHelpers.GetCommentsLoginString(WorkFlowHelpers.WfDataHelpers.GetLastComment(CurrentWfIns)));
            FWfWorkflowDef WfDef = WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            CurrentWFContext.Parameters.AddOrChangeItem("WorkflowName", WfDef.Name);
            CurrentWFContext.Parameters.AddOrChangeItem("WfOwner", WfDataHelpers.GetLoginNameSurname(CurrentWfIns.OwnerLogin.LoginId));
            /// Burda Sadece Yes mi set edicek Ya Reddedilirse nolucak????
            if (!CurrentWFContext.Parameters.ContainsKey("Onay")) CurrentWFContext.Parameters.AddOrChangeItem("Onay", "Yes");
            if (!CurrentWFContext.Parameters.ContainsKey("LastUpdatedBy")) CurrentWFContext.Parameters.AddOrChangeItem("LastUpdatedBy", FormInformationHelper.LastUpdatedBy(((AuthenticationResult)Session[SessionUserVariable]).LoginObject, AssignedUser));
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentLoginType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentLoginType", "LOGIN");
            if (!CurrentWFContext.Parameters.ContainsKey("AssignmentType")) CurrentWFContext.Parameters.AddOrChangeItem("AssignmentType", "TASKINBOX");
            //FWfActionTaskInstance ActionTaskIns = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentActionTaskInstance.WfActionInstanceId);
            //Dictionary<string, string> MyDic = new Dictionary<string, string>();
            if (CurrentActionTaskInstance == null)
            {
                //FWfActionTaskInstance
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);

                MailHelper.SendMail(MailTemplateId, ActionInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(WfIns.WfWorkflowInstanceId);
            }
            else
            {
                MailHelper.SendMail(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext, ToList, new List<FLogin>());
                Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentActionTaskInstance.WfStateInstance.WfWorkflowInstance.WfWorkflowInstanceId);
            }

            //MailHelper.SendEmailDirect(MailTemplateId, CurrentActionTaskInstance, CurrentWFContext,MyDic,new List<string>(),new List<string>());
            //MailHelper.SendEmailDirect(
            if (IsSave) CurrentWFContext.Save();
            SenderObj.Clear();
            ToList.Clear();
        }

        /// <summary>
        /// Standart Mail Gönderme Fonksiyonudur. ActionOwner Parametresini alır
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        public void SendMail(long ActionToLoginId, long MailTemplateId, string Action, FLogin ActionOwner, bool IsSave)
        {
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwner);
            SendMail(ActionToLoginId, MailTemplateId, Action, IsSave);
        }

        /// <summary>
        /// Standart Mail Gönderme Fonksiyonudur. ActionOwner Parametresini alır
        /// </summary>
        /// <param name="ActionToLoginId"></param>
        /// <param name="MailTemplateId"></param>
        /// <param name="Action"></param>
        /// <param name="IsSave"></param>
        public void SendMail(long ActionToLoginId, long MailTemplateId, string Action, string ActionEng, FLogin ActionOwner, bool IsSave)
        {
            CurrentWFContext.Parameters.AddOrChangeItem("ActionOwner", ActionOwner);
            SendMail(ActionToLoginId, MailTemplateId, Action, ActionEng, IsSave);
        }

        /// Stateji Akışta Kime Mail gönderilir.
        /// Eğer Form Onaylandı İse
        /// 1-Formun Sahibine Mail Atılır
        /// 2-Formun Assign edildiği kişiye mail atılır.
        /// 3-Sabit bir kişiye mail atılır.
        /// <summary>
        /// İşlemi yapan kullanıcıyı döner
        /// </summary>
        /// <param name="currentUser"></param>
        /// <param name="assignedUser"></param>
        /// <returns></returns>
        public string LastUpdatedBy(FLogin currentUser, FLogin assignedUser)
        {
            if (assignedUser == null)
            {
                return WfDataHelpers.GetLoginNameSurname(currentUser.LoginId);
            }

            if (currentUser.LoginId == assignedUser.LoginId)
            {
                return WfDataHelpers.GetLoginNameSurname(currentUser.LoginId);
            }

            if (currentUser.LoginId != assignedUser.LoginId)
            {
                //return string.Format("{0} yerine {1}", WfDataHelpers.GetLoginNameSurname(assignedUser.LoginId), WfDataHelpers.GetLoginNameSurname(currentUser.LoginId));
                return string.Format("{1} on behalf of {0}", WfDataHelpers.GetLoginNameSurname(assignedUser.LoginId), WfDataHelpers.GetLoginNameSurname(currentUser.LoginId));
            }

            return WfDataHelpers.GetLoginNameSurname(currentUser.LoginId);
        }

        /// <summary>
        /// History Logunu yazdığımız fonksiyondur.
        /// </summary>
        /// <param name="wfHistory"></param>
        public void WfHistoryExec(FWfActionTaskInstance PreviusActionTaskIns, WorkflowHistoryActionType wfHistory)
        {
            #region History Kaydı Atılıyor

            WorkflowHistoryWorker.Execute(PreviusActionTaskIns, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignToId, wfHistory, Commend);

            #endregion History Kaydı Atılıyor
        }

        /// <summary>
        /// History Logunu yazdığımız fonksiyondur.
        /// </summary>
        /// <param name="wfHistory"></param>
        public void WfHistoryExec(WorkflowHistoryActionType wfHistory)
        {
            #region History Kaydı Atılıyor

            if (CurrentActionTaskInstance == null)
            {
                FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                FWfActionInstance ActionInstance = WFRepository<FWfActionInstance>.GetEntity(StateInstance.WfStateInstanceFWfActionInstanceList[StateInstance.WfStateInstanceFWfActionInstanceList.Count - 1].WfActionInstanceId);
                WorkflowHistoryWorker.Execute(ActionInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignToId, wfHistory, Commend);
            }
            else
            {
                WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignToId, wfHistory, Commend);
            }

            #endregion History Kaydı Atılıyor
        }

        /// <summary>
        /// History Logunu ActionLoginId belirterek Yazdığımız Fonksiyon
        /// </summary>
        /// <param name="wfHistory"></param>
        /// <param name="ActionLoginId"></param>
        public void WfHistoryExec(WorkflowHistoryActionType wfHistory, long ActionLoginId)
        {
            WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, ActionLoginId, ActionLoginId, wfHistory, Commend);
        }

        /// <summary>
        /// History Logunu Comment belirterek Yazdığımız Fonksiyon
        /// </summary>
        /// <param name="wfHistory"></param>
        /// <param name="ActionLoginId"></param>
        public void WfHistoryExec(WorkflowHistoryActionType wfHistory, string Comment)
        {
            WorkflowHistoryWorker.Execute(CurrentActionTaskInstance, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, AssignToId, wfHistory, Comment);
        }

        #endregion WorkFlowPage Mevcut Fonksiyonları

        #region Formun Loadını Yöneten Fonksiyon

        /// <summary>
        /// Master Sessinlardaki değerleri temizlemek için kullanılır
        /// </summary>
        public void KillSession()
        {
            Session[SessionFWfActionTaskInstance] = null;
            Session[SessionFWfWorkflowInstance] = null;
            Session[SessionwfContext] = null;
            Session[SessionFlowAdmin] = null;
            Session[SessionAssignedUser] = null;
            Session[SessionDelegation] = null;
            Session[SessionFlowAdmin] = null;
        }

        /// <summary>
        /// Belirtilen UserId ye Assigment var mı kontrolü yapar.
        /// </summary>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public bool AssignToLoginIdCheck(long LoginId)
        {
            //for (int i = 0; i < AssignToIdList.Count; i++)
            //{
            //    if (AssignToIdList[i].LoginId == LoginId)
            //        return true;
            //}
            //return false;
            return AssignToIdListLong.Contains(LoginId);


        }

        /// <summary>
        /// Belirtilen UserId ye Comment task var mı kontrolü yapar.
        /// </summary>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public bool CommentToLoginIdCheck(long LoginId)
        {
            //for (int i = 0; i < CommendToIdList.Count; i++)
            //{
            //    if (CommendToIdList[i].LoginId == LoginId)
            //        return true;
            //}
            //return false;
            return CommendToIdLongList.Contains(LoginId);
        }

        /// <summary>
        /// Belirtilen UserId'nin  Comment task var mı ve delegesi var mı kontrolü yapar.
        /// </summary>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public bool CommentToLoginDelegeIdCheck(long LoginId)
        {
            return CommendToDelegeIdLongList.Contains(LoginId);
        }



        /// <summary>
        /// Üst Yöneticisinin Tespit edilip Context e yazılmasını sağlayan kod parçası
        /// </summary>
        public void BuildAssignmentParameter()
        {
            //if (CurrentActionTaskInstance != null)
            //{
            //    #region Üst Yöneticisi Tespit edilmesi

            //    using (UnitOfWork.Start())
            //    {
            //        WFContext wfContext = new WFContext(WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId));
            //        if (!wfContext.Parameters.ContainsKey("ManagerType"))
            //        {
            //            Digiturk.Workflow.Common.WorkflowLoginHelper wfHelper = new WorkflowLoginHelper();
            //            long Owner = CurrentWfIns.OwnerLogin.LoginId;
            //            long PrntManager = 0;
            //            string ManageType = "";
            //            foreach (var item in AssignToIdList)
            //            {
            //                PrntManager = Digiflow.DataAccessLayer.CheckingWorker.GetManager(item.LoginId);
            //                ManageType = wfHelper.GetManagerType(PrntManager);
            //            }
            //            string OwnerManagerType = wfHelper.GetManagerType(Owner);
            //            //wfContext.Parameters.AddOrChangeItem("ManagerType", ManageType);
            //            //                        wfContext.Parameters.AddOrChangeItem("OwnerManagerType", OwnerManagerType);
            //            wfContext.Save();
            //        }
            //    }

            //    #endregion Üst Yöneticisi Tespit edilmesi
            //}
        }

        /// <summary>
        /// Kopyalanmış Instance Build Ediliyor
        /// </summary>
        public void CopyInstanceBuilding()
        {
            Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(CopyInstanceId);
        }

        /// <summary>
        /// Kopyalanmış Instance Temizleniyor.
        /// </summary>
        public void ResetCopyInstance()
        {
            Session[SessionFWfWorkflowInstance] = null;
        }

        public void ActionPanelRefresh(bool reLoad)
        {
            #region Authorization List ekleniyor
            bool canFileUpload = false;
            if (CurrentActionTaskInstance != null)
            {
                ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new Authorization.AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentStateDef.WfStateDefId);
                canFileUpload = ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFileUpload;
            }
            Label lbl = (Label)Master.FindControl("lblWfInsId");
            lbl.Text = "Akış Numarası :" + InstanceId.ToString();

            // Ekran Üzerindeki kontrolleri yüklenen akışın entity değerlerine göre doldurur.
            try
            {
                if (reLoad)
                {
                    LoadEntityToControls();
                    BuildAssignmentParameter();
                    DisabledControl();
                }
            }
            catch (Exception) { }

            #endregion Authorization List ekleniyor

            #region ActionPanelRefresh Rules

            if (CurrentActionTaskInstance == null)
            {
                // Akış tamamlanmış demektir.
                ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
            }
            else
            {
                if (CurrentActionTaskInstance.StartTime > DateTime.Now)
                {
                    //Bu Akış Askıya alınmış demektir.
                    if (IsFlowAdmin || FormDelegation || AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                    {
                        ChangeVisibilityOfTabs(false, false, false, true, false, true, false, false, false);
                    }
                }
                else if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd != "STARTED")
                {
                    // Bu akış bitmiş ama End State exec olmamış demektir demektir.
                    ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                }
                else if (IsFlowAdmin)
                {
                    ///Form Oluşturma Hariç Tüm Tablar açılır.
                    ChangeVisibilityOfTabs(false, true, true, true, true, true, IsRolledBack, canFileUpload, true);
                }
                else
                {
                    if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId) && CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                    {
                        // Form bana delege edildiyse yada Bana atandıysa yada atanan kişiler listesindeysem. ve Akışı da Oluşturansam
                        ///Onay,Red,Yoruma Gönder,Yönlendir,Durdur,Devam Ettir,Yorum Gir
                        ChangeVisibilityOfTabs(false, true, true, true, true, true, false, canFileUpload, true);
                    }
                    else if (FormDelegation || AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                    {
                        // Form bana delege edildiyse yada Bana atandıysa yada atanan kişiler listesindeysem.
                        ///Onay,Red,Yoruma Gönder,Yönlendir,Durdur,Devam Ettir,Yorum Gir
                        ChangeVisibilityOfTabs(false, true, true, true, false, true, false, canFileUpload, true);
                    }
                    else if (CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId && LastSendTaskLoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                    {
                        // Akışı Açansam ve Son İşlem Yapan bensem.
                        ///Yorum Gir, Geri Al, İptal Et
                        ChangeVisibilityOfTabs(false, false, false, false, true, true, true, canFileUpload, false);
                    }
                    else if (CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                    {
                        // Akışı Açan bensem
                        ///Yorum Gir, İptal Et
                        ChangeVisibilityOfTabs(false, false, false, false, true, true, false, canFileUpload, false);
                    }
                    else if (LastSendTaskLoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || LastSendTaskLoginIdList.Contains(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId.ToString()))
                    {
                        // Son işlemi yapan bensem.
                        /// Yorum Gir, Geri Al
                        ChangeVisibilityOfTabs(false, false, false, false, false, true, true, canFileUpload, false);
                    }
                    else if (CommentToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                    {
                        /// bana yoruma gönderildiyse
                        ChangeVisibilityOfTabs(false, false, false, false, false, true, false, canFileUpload, false);
                    }
                    else if (CommentToLoginDelegeIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                    {
                        ///  yoruma gönderildiyse ve delegesi var ise
                        ChangeVisibilityOfTabs(false, false, false, false, false, true, false, canFileUpload, false);
                    }

                    else if (IsActionFlow)
                    {
                        // akışa Herhangi bir yerde dokunduysam
                        ChangeVisibilityOfTabs(false, false, false, false, false, true, false, canFileUpload, false);
                    }
                    else
                    {
                        /// Herşey kapalı.
                        ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                    }
                }

                #region Bu Akışı Delege Etti İsem Üzerinde İşlem Yapamam

                if (OwnDelegation)
                {
                    //ShowInformation("Delegasyon", "Yetkilerinizi delege ettiğiniz için bu akış üzerinde işlem yapamazsınız");
                    ShowInformation("Delegasyon", GetGlobalResourceObject("Resource", "main_delege_uyari").ToString());

                    ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                }

                #endregion Bu Akışı Delege Etti İsem Üzerinde İşlem Yapamam
            }

            #endregion ActionPanelRefresh Rules

            #region MasterPage İşlemleri Tekrarlanır

            if (this.Master is MasterPage.SecureMasterPage)
            {
                if (System.Configuration.ConfigurationSettings.AppSettings["IsYYSActive"] == "true")
                {
                    if (((WorkFlowPage)this.Page).InstanceId > 0 && ((WorkFlowPage)this.Page).CurrentWfIns.WfCurrentState != null)
                    {
                        if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList != null)
                        {
                            if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList.WorkFlowDefinitionId != CurrentWfIns.WfWorkflowDef.WfWorkflowDefId && ((AuthenticationResult)Session[SessionUserVariable]).AuthoList.StateDefinitionId != CurrentStateDef.WfStateDefId)
                            {
                                ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, ((WorkFlowPage)this.Page).CurrentStateDef.WfStateDefId);
                            }
                        }
                        else
                        {
                            ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, ((WorkFlowPage)this.Page).CurrentStateDef.WfStateDefId);
                        }
                    }
                    else if (((WorkFlowPage)this.Page).InstanceId == 0)
                    {
                        if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList != null)
                        {
                            if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList.WorkFlowDefinitionId != 0 && ((AuthenticationResult)Session[SessionUserVariable]).AuthoList.StateDefinitionId != 0)
                            {
                                ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, 0, 0);
                            }
                        }
                        else
                        {
                            ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, 0, 0);
                        }
                    }
                    if (((WorkFlowPage)this.Page).CurrentActionTaskInstance != null)
                    {
                        string sayfa = ((MasterPage.SecureMasterPage)this.Master).PageName.ToLower();

                        if (sayfa.Contains("_mobil.aspx"))
                        {
                            sayfa = sayfa.Replace("_mobil.aspx", ".aspx");
                        }

                        if (sayfa.Contains("_Mobil.aspx"))
                        {
                            sayfa = sayfa.Replace("_Mobil.aspx", ".aspx");
                        }

                        if (sayfa.Contains("advance_mobil_onay2.aspx"))
                        {
                            sayfa = sayfa.Replace("advance_mobil_onay2.aspx", "advance.aspx");
                        }
                        sayfa = SetPageName(sayfa);
                        AuthoInfo yetki = ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[sayfa];
                        DelegeHasConditionAcceptCorrection();
                        ((MasterPage.SecureMasterPage)this.Master).AuthorizeControl(
                            yetki.CanCreate,
                            yetki.CanApproval,
                            yetki.CanReject,
                            yetki.CanForward,
                            yetki.CanSendtoCommend,
                            yetki.CanSendRequestToComment,
                            yetki.CanSuspend,
                            yetki.CanResume,
                            yetki.CanCancel,
                            yetki.CanFinalize,
                            yetki.CanAddToComment,
                            yetki.CanFileUpload,
                            yetki.CanSendTask,
                            yetki.CanConditionalAccept,
                            yetki.CanCorrection,
                            yetki.SendTaskLogicalGroupId,
                            yetki.ForwardLogicalGroupId,
                            yetki.SendCommentLogicalGroupId);
                    }
                }
            }

            #endregion MasterPage İşlemleri Tekrarlanır
        }

        /// <summary>
        /// İşlem Yapan Yetkilendirmelerinde Atananın Yetkisi işlem yapanın Yetkisinden doğru ise 
        /// </summary>
        /// <param name="ActionAuthority"></param>
        /// <param name="AssignedAuthority"></param>
        /// <returns></returns>
        public bool ConditionalResult(bool ActionAuthority, bool AssignedAuthority)
        {
            bool result = false;
            if (ActionAuthority == true)
            {
                result = true;
            }
            else
            {
                result = AssignedAuthority;
            }
            return result;
        }

        public void DelegeHasConditionAcceptCorrection()
        {
            if (CurrentWfIns.WfCurrentState != null)
            {
                if (FormDelegation)
                {
                    long asignedUserId = DelegeAssignUser(UserInformation.LoginObject.LoginId);
                    Digiturk.Workflow.Digiflow.Authorization.AuthorizationList list = new AuthorizationList(asignedUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentStateDef.WfStateDefId);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCreate = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCreate, list[PageName].CanCreate);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanApproval = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanApproval, list[PageName].CanApproval);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanReject = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanReject, list[PageName].CanReject);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward, list[PageName].CanForward);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend, list[PageName].CanSendtoCommend);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendRequestToComment = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendRequestToComment, list[PageName].CanSendRequestToComment);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSuspend = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSuspend, list[PageName].CanSuspend);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanResume = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanResume, list[PageName].CanResume);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCancel = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCancel, list[PageName].CanCancel);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFinalize = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFinalize, list[PageName].CanFinalize);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask, list[PageName].CanSendTask);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept, list[PageName].CanConditionalAccept);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection, list[PageName].CanCorrection);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanAddToComment = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanAddToComment, list[PageName].CanAddToComment);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanRollBack = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanRollBack, list[PageName].CanRollBack);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendBack = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendBack, list[PageName].CanSendBack);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFileUpload = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFileUpload, list[PageName].CanFileUpload);
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].SendTaskLogicalGroupId = list[PageName].SendTaskLogicalGroupId;
                    }
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].ForwardLogicalGroupId = list[PageName].ForwardLogicalGroupId;
                    }
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].SendCommentLogicalGroupId = list[PageName].SendCommentLogicalGroupId;
                    }

                    //SendCommentLogicalGroupId
                    //SendTaskLogicalGroupId = 
                    //((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept = list[PageName].CanConditionalAccept;
                    //((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection = list[PageName].CanCorrection;
                }

                if (IsFlowAdmin || IsSystemAdmin)
                {
                    long asignedUserId = AssignToId;

                    long authorizedLoginId = CheckingWorker.GetAuthorizedAssignToLoginId(WfDefID, CurrentStateDef.WfStateDefId);
                    if (authorizedLoginId > 0) asignedUserId = authorizedLoginId;
                    Digiturk.Workflow.Digiflow.Authorization.AuthorizationList list = new AuthorizationList(asignedUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentStateDef.WfStateDefId);

                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCreate = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCreate, list[PageName].CanCreate);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanApproval = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanApproval, list[PageName].CanApproval);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanReject = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanReject, list[PageName].CanReject);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward, list[PageName].CanForward);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend, list[PageName].CanSendtoCommend);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendRequestToComment = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendRequestToComment, list[PageName].CanSendRequestToComment);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSuspend = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSuspend, list[PageName].CanSuspend);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanResume = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanResume, list[PageName].CanResume);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCancel = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCancel, list[PageName].CanCancel);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFinalize = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFinalize, list[PageName].CanFinalize);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask, list[PageName].CanSendTask);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept, list[PageName].CanConditionalAccept);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection, list[PageName].CanCorrection);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanAddToComment = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanAddToComment, list[PageName].CanAddToComment);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanRollBack = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanRollBack, list[PageName].CanRollBack);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendBack = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendBack, list[PageName].CanSendBack);
                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFileUpload = ConditionalResult(((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanFileUpload, list[PageName].CanFileUpload);
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendTask)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].SendTaskLogicalGroupId = list[PageName].SendTaskLogicalGroupId;
                    }
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanForward)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].ForwardLogicalGroupId = list[PageName].ForwardLogicalGroupId;
                    }
                    if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanSendtoCommend)
                    {
                        ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].SendCommentLogicalGroupId = list[PageName].SendCommentLogicalGroupId;
                    }
                    //((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanConditionalAccept = list[PageName].CanConditionalAccept;
                    //((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName].CanCorrection = list[PageName].CanCorrection;
                }
            }
        }

        /// <summary>
        /// Form Bana delege edildiyse Atananlardan Kimin Delegesiyim.
        /// </summary>
        /// <returns></returns>
        private long DelegeAssignUser(long delegeloginId)
        {
            foreach (var item in AssignToIdListLong)
            {
                if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, DateTime.Now))
                {
                    return item;
                }
            }
            return 0;
        }

        /// <summary>
        ///  Base Page OnInit Events
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            yetkiKontrolleri();
            base.OnInit(e);
        }

        public void yetkiKontrolleri()
        {
            if ((AuthenticationResult)Session[SessionUserVariable] == null)
            {
                Session[SessionUserVariable] = UserInformation.AuthoList;
            }
            string adres = Request.Url.Segments[Request.Url.Segments.Length - 1].ToString().Replace(".aspx", "");
            ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage - " + adres, "OnInit Start");
            KillSession();
            ToList = new List<FLogin>();
            SenderObj = new ContextObject();

            if (!Page.IsPostBack && !Page.IsCallback)
            {
                if (!Page.IsPostBack)
                {
                    if (IsFlowAdmin)
                    {
                        ShowInformation(GetGlobalResourceObject("Resource", "main_akis_yoneticisi").ToString(), GetGlobalResourceObject("Resource", "main_akis_yoneticisi_bildirim").ToString());
                    }
                }
                if (CurrentWfIns != null)
                {
                    Digiturk.Workflow.Digiflow.Framework.WorkflowHistoryWorker.UpdateHistoryTable(CurrentWfIns.WfWorkflowInstanceId);
                }

                if (!Page.IsPostBack)
                {
                    // Tüm Akışlar İçin Çalıştırılır
                    ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "BeforeLoadDataBinding");
                    LoadDataBinding();
                    ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "AfterLoadDataBinding");
                    if (CopyInstanceId > 0)
                    {
                        #region Kopyalama Instance

                        CopyInstanceBuilding();
                        if (CurrentWfIns != null)
                        {
                            ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "BeforeLoadEntityToControls");
                            LoadEntityToControls();
                            ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "AfterLoadEntityToControls");
                        }
                        EnabledToControl();
                        ResetCopyInstance();
                        string sayfa = SetPageName(PageName).ToLower();
                        if (UserInformation.AuthoList.ContainsKey(SetPageName(PageName).ToLower()))
                        {

                        }
                        else
                        {
                            FWfWorkflowDef WorkFlowDef = Digiturk.Workflow.Common.WFRepository<FWfWorkflowDef>.GetEntity(WfDefID);
                            long StateDefId = WorkFlowDef.InitialState.WfStateDefId;
                            WorkFlowDef = null;
                            UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, WfDefID, StateDefId);
                        }
                        AuthoInfo yetki = ((AuthenticationResult)Session[BasePage.SessionUserVariable]).AuthoList[sayfa];

                        ChangeVisibilityOfTabs(yetki.CanCreate, false, false, false, false, false, false, false, false);

                        #endregion Kopyalama Instance
                    }
                    else if (InstanceId == 0)
                    {
                        ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "BeforeNewWorkFlowLoading");

                        #region Yeni Akış Başlatırken Yapılacak İşlemler

                        // Yeni Bir Akış Üretilmek için sayfa açıldığında çalıştırılır

                        string sayfa = SetPageName(PageName).ToLower();
                        if (UserInformation.AuthoList.ContainsKey(SetPageName(PageName).ToLower()))
                        {

                        }
                        else
                        {
                            FWfWorkflowDef WorkFlowDef = Digiturk.Workflow.Common.WFRepository<FWfWorkflowDef>.GetEntity(WfDefID);
                            long StateDefId = WorkFlowDef.InitialState.WfStateDefId;
                            WorkFlowDef = null;
                            UserInformation.AuthoList = new AuthorizationList(UserInformation.LoginObject.LoginId, WfDefID, StateDefId);
                        }
                        AuthoInfo yetki = ((AuthenticationResult)Session[BasePage.SessionUserVariable]).AuthoList[sayfa];

                        ChangeVisibilityOfTabs(yetki.CanCreate, false, false, false, false, false, false, false, false);

                        NewWorkFlowLoading();

                        #endregion Yeni Akış Başlatırken Yapılacak İşlemler

                        ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "AfterNewWorkFlowLoading");
                    }
                    else if (InstanceId > 0)
                    {
                        DelegeHasConditionAcceptCorrection();
                        #region Ekran Oluşturulmuş bir akış için Sayfa Yükleniyor

                        #region Authorization List ekleniyor

                        ////WorkFlowTraceWorker.OracleLog(( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName, "WorkFlowPage- " + adres, "BeforeLoadEntityToControls");
                        if (CurrentActionTaskInstance != null)
                        {

                            if (((AuthenticationResult)Session[SessionUserVariable]).AuthoList != null)
                            {
                                if (!((AuthenticationResult)Session[SessionUserVariable]).AuthoList.ContainsKey(SetPageName(PageNameLocal).ToLower()))
                                {
                                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new Authorization.AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentStateDef.WfStateDefId);
                                }
                            }
                            else
                            {
                                ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new Authorization.AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, CurrentStateDef.WfStateDefId);
                            }

                        }
                        Label lbl = (Label)Master.FindControl("lblWfInsId");
                        lbl.Text = "Akış Numarası :" + InstanceId.ToString();
                        // Ekran Üzerindeki kontrolleri yüklenen akışın entity değerlerine göre doldurur.
                        LoadEntityToControls();
                        BuildAssignmentParameter();
                        //
                        //Form Update edilebilir state'te mi bulunur
                        if (CurrentWfIns.WfCurrentState != null)
                        {
                            if (!FormInformationHelper.IsFormUpdateState(CurrentWfIns.WfCurrentState.WfStateDef.WfStateDefId))
                            {
                                DisabledControl();
                            }
                        }
                        else
                        {
                            DisabledControl();
                        }

                        #endregion Authorization List ekleniyor

                        if (CurrentActionTaskInstance == null)
                        {
                            // Akış tamamlanmış demektir.
                            //Akış tamamlandıktan sonra iptal edilecek akışlar

                            //Belirli akışların akış kapandıktan sonra iptal et tabının görünür olmasını sağlayan kontrol

                            #region eski kontrol

                            //if ((CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1336 || CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1429 || CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1992 || CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 2112) && (IsFlowAdmin || CurrentWfIns.OwnerLogin.LoginId == ( (AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || OwnDelegation))

                            #endregion eski kontrol

                            bool isLogical = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsDefExistLogicalGroup(logDefGroupIdAction, ((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, "0");

                            if (isLogical && (IsFlowAdmin || CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || OwnDelegation || IsSystemAdmin))
                            {
                                using (UnitOfWork.Start())
                                {
                                    FWfWorkflowInstance WfIns = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                                    FWfStateInstance StateInstance = WFRepository<FWfStateInstance>.GetEntity(WfIns.WfWorkflowInstanceFWfStateInstanceList[WfIns.WfWorkflowInstanceFWfStateInstanceList.Count - 1].WfStateInstanceId);
                                    ((AuthenticationResult)Session[SessionUserVariable]).AuthoList = new AuthorizationList(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, StateInstance.WfStateDef.WfStateDefId);
                                }
                                ChangeVisibilityOfTabs(false, false, false, false, true, true, false, false, false);
                            }
                            else if (IsActionFlow || IsViewUser || IsReportAdmin || IsFlowAdmin || IsSystemAdmin) //
                            {
                                // akışa Herhangi bir yerde dokunduysam yada Görüntüleme yetkim varsa.
                                ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                            }
                            else
                            {
                                /// Herşey kapalı.
                                Session[SessionUserVariable] = null;

                                SetSessionError("Yetkisiz Erişim", GetGlobalResourceObject("Resource", "main_yetki_uyari").ToString(), null);
                                Response.Redirect("Error.aspx", false);
                                Response.End();
                            }
                        }
                        else
                        {
                            AuthoInfo yetki = ((AuthenticationResult)Session[SessionUserVariable]).AuthoList[PageName];

                            long logicalN = 418;// bilgilerde günceleme yapacaklar
                            long logicalND = 416;     //Live Hazine onayına gönderecekler

                            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
                            {
                                logicalN = 384;      //Test Ortamında bilgilerde günceleme yapacaklar
                                logicalND = 333;     //Test Hazine onayına gönderecekler
                            }
                            else
                            {
                                logicalN = 418;      //Live Ortamda bilgilerde günceleme yapacaklar
                                logicalND = 416;     //Live Hazine onayına gönderecekler
                            }

                            bool isLogicalN = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsExistLogicalGroup(logicalN, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);
                            bool isLogicalND = Digiturk.Workflow.Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsExistLogicalGroup(logicalND, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId);

                            if (CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1551 && isLogicalN && !isLogicalND && CurrentWfIns.WfCurrentState.WfStateDef.WfStateDefId == 1959)
                            {
                                ChangeVisibilityOfTabs(false, false, false, false, true, true, false, false, false);
                            }
                            else if (CurrentActionTaskInstance.StartTime > DateTime.Now)
                            {
                                //Bu Akış Askıya alınmış demektir.
                                if (IsFlowAdmin || IsSystemAdmin || FormDelegation || AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                                {
                                    ChangeVisibilityOfTabs(false, false, false, true, false, true, false, false, false);
                                }
                            }
                            else if (CurrentWfIns.WfWorkflowStatusType.WfWorkflowStatusTypeCd != "STARTED")
                            {
                                // Bu akış bitmiş ama End State exec olmamış demektir demektir.
                                ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                            }
                            else if (IsFlowAdmin || IsSystemAdmin)
                            {
                                ///Form Oluşturma Hariç Tüm Tablar açılır.
                                ChangeVisibilityOfTabs(false, true, true, true, true, true, IsRolledBack, yetki.CanFileUpload, true);
                            }
                            else
                            {
                                if (AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId) && CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                                {
                                    // Form bana delege edildiyse yada Bana atandıysa yada atanan kişiler listesindeysem.
                                    ///Onay,Red,Yoruma Gönder,Yönlendir,Durdur,Devam Ettir,Yorum Gir
                                    ChangeVisibilityOfTabs(false, true, true, true, true, true, false, yetki.CanFileUpload, true);
                                }
                                else if (FormDelegation || AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || AssignToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                                {
                                    // Form bana delege edildiyse yada Bana atandıysa yada atanan kişiler listesindeysem.
                                    ///Onay,Red,Yoruma Gönder,Yönlendir,Durdur,Devam Ettir,Yorum Gir
                                    ChangeVisibilityOfTabs(false, true, true, true, false, true, false, yetki.CanFileUpload, true);
                                }
                                else if (CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId && (LastSendTaskLoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || LastSendTaskLoginIdList.Contains(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId.ToString())))
                                {
                                    // Akışı Açansam ve Son İşlem Yapan bensem.
                                    ///Yorum Gir, Geri Al, İptal Et
                                    ChangeVisibilityOfTabs(false, false, false, false, true, true, IsRolledBack, yetki.CanFileUpload, false);
                                }
                                else if (CurrentWfIns.OwnerLogin.LoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                                {
                                    // Akışı Açan bensem
                                    ///Yorum Gir, İptal Et
                                    ChangeVisibilityOfTabs(false, false, false, false, true, true, false, yetki.CanFileUpload, false);
                                }
                                else if (LastSendTaskLoginId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId || LastSendTaskLoginIdList.Contains(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId.ToString()))
                                {
                                    // Son işlemi yapan bensem.
                                    /// Yorum Gir, Geri Al
                                    if(AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                                        ChangeVisibilityOfTabs(false, yetki.CanApproval || yetki.CanReject, yetki.CanForward, yetki.CanSuspend || yetki.CanResume, yetki.CanCancel, yetki.CanAddToComment, true, yetki.CanFileUpload, yetki.CanSendtoCommend);
                                    else ChangeVisibilityOfTabs(false, false, false, false, false, false, true, yetki.CanFileUpload, false);

                                }
                                else if (CommentToLoginIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId) || CommentToLoginDelegeIdCheck(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId))
                                {
                                    /// bana yoruma gönderildiyse
                                    if (AssignToId == ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId)
                                        ChangeVisibilityOfTabs(false, yetki.CanApproval || yetki.CanReject, yetki.CanForward, yetki.CanSuspend || yetki.CanResume, yetki.CanCancel, true, false, yetki.CanFileUpload, yetki.CanSendtoCommend);
                                    else ChangeVisibilityOfTabs(false, false, false, false, false, true, false, yetki.CanFileUpload, false);
                                }
                                else if (IsActionFlow || IsViewUser || IsReportAdmin)
                                {
                                    // akışa Herhangi bir yerde dokunduysam yada Görüntüleme yetkim varsa.
                                    ChangeVisibilityOfTabs(false, false, false, false, false, true, false, yetki.CanFileUpload, false);
                                }
                                else
                                {
                                    /// Herşey kapalı.
                                    Session[SessionUserVariable] = null;

                                    SetSessionError("Yetkisiz Erişim", GetGlobalResourceObject("Resource", "main_yetki_uyari").ToString(), null);
                                    Response.Redirect("Error.aspx", false);
                                    Response.End();
                                }

                                // İptali Açmak için Uygun Case i debug edip bulacaksın
                                //ChangeVisibilityOfTabs(false, false, false, false, false, true, false);
                            }
                        }

                        #endregion Ekran Oluşturulmuş bir akış için Sayfa Yükleniyor

                        //Change Todo Kerem
                        //06.09.2013
                        //ActionPanelRefresh(false);
                    }

                    #region Bu Akışı Delege Etti İsem Üzerinde İşlem Yapamam

                    if (OwnDelegation)
                    {
                        // ShowInformation("Delegasyon", "Yetkilerinizi delege ettiğiniz için bu akış üzerinde işlem yapamazsınız");
                        ShowInformation("Delegasyon", GetGlobalResourceObject("Resource", "main_delege_uyari").ToString());
                        ChangeVisibilityOfTabs(false, false, false, false, false, false, false, false, false);
                    }

                    #endregion Bu Akışı Delege Etti İsem Üzerinde İşlem Yapamam
                }
            }
        }

        #endregion Formun Loadını Yöneten Fonksiyon

        #region FlowAdminConvertion

        // Form üzerine atanmış bir delegasyon varsa ve form flow admini sisteme girip işlem yapmışsa
        // Delelasyonu ikiye bölüp araya yeni delegasyon yapmak için kullanılır.
        /// <summary>
        /// FlowAdmin işlemini Handle Etmek için kullanılan bir Delegate yapısı
        /// </summary>
        /// <param name="ObjectList"></param>
        protected delegate void ActionOperation(object[] ObjectList);

        /// <summary>
        /// FlowAdmin işlemini Handle etmek için kullanılan operasyon methodu
        /// </summary>
        /// <param name="Operation"></param>
        /// <param name="ObjectList"></param>
        protected void FlowAdminOperationChecking(ActionOperation Operation, Object[] ObjectList)
        {
            if (IsFlowAdmin)
            {
                long RealUserId = Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
                //Todo Delegasyon Change
                long DelegateUserId = WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(RealUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                //long DelegateUserId = WorkflowDelegationHelper.GetActiveDelegate(RealUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                long FlowAdminUserId = ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
                DateTime StartDate = DateTime.Now;
                Operation(ObjectList); // İşlemi Yapıyoruz.
                DateTime EndDate = DateTime.Now;
                if (DelegateUserId > 0)
                {
                    /// Delegasyonu Parçalıyoruz
                    WorkFlowEntites.DelegationObject dlgReq = new WorkFlowEntites.DelegationObject(DelegateUserId);
                    WorkflowDelegationHelper.EndDelegation(RealUserId, DelegateUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, StartDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu",
                        StartDate,
                        EndDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        DelegateUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        dlgReq.DelegationComment + "(Akış Admini işlemi Sonrası devam ettirildi.)",
                        EndDate,
                        dlgReq.EndTime);
                }
                else
                {
                    /// Akış Admini için Delegasyon üretiyoruz
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu oluşturuldu",
                        StartDate,
                        EndDate);
                }
            }

            //WorkflowDelegationHelper.GetActiveDelegate(
            //DelegationRequest dlgReq = WFRepository<DelegationRequest>.GetEntity(actionInstance.WfStateInstance.WfWorkflowInstance.EntityRefId);
            //WorkflowDelegationHelper.StartDelegation(dlgReq.OwnerLoginId, dlgReq.DelegatedLoginId, dlgReq.WorkflowDefId, dlgReq.DelegationComment, dlgReq.StartTime, dlgReq.EndTime);
            //Digiturk.Workflow.Digiflow.Actions.AssignmentBase.Assign(dlgReq.DelegatedLoginId, "LOGIN", dlgReq.OwnerLoginId, "WFVIEW");

            // Bu kullanıcı için bu DefId li forma delegasyon varmı?
            //Varsa:
            // Var olan delegasyonun başlangıç ve bitiş Tarihlerini tespit et İşlemi yapmadan önceki
            // Yoksa : İşlem yapılmadan önce bir Datetime.Now al bittikten sonra da bir datetimeNow al
            //başlangıç Tarihi Bitiş Tarihi , Senin UserID akış admininin userıd si ile bir delegasyon kaydı oluştur
        }

        /// <summary>
        /// FlowAdmin işlemini Handle etmek için kullanılan salt method
        /// </summary>
        /// <param name="Objs"></param>
        protected void FlowAdminOperationChecking(WorkFlowEntites.FlowAdminOprObject Objs)
        {
            //CurrentActionTaskInstance.WfStateInstance.WfStateDef.ToStateDefFWfTransitionDefList[0].Condition.ToString

            long RealUserId = Objs.RealUserId;
            long DelegateUserId = Objs.DelegateUserId;
            long FlowAdminUserId = ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.LoginId;
            DateTime StartDate = Objs.OperationStartTime;
            DateTime EndDate = DateTime.Now;
            if (IsFlowAdmin && FlowAdmin.LoginId != RealUserId)
            {
                if (DelegateUserId > 0)
                {
                    /// Delegasyonu Parçalıyoruz
                    WorkFlowEntites.DelegationObject dlgReq = new WorkFlowEntites.DelegationObject(DelegateUserId);
                    WorkflowDelegationHelper.EndDelegation(RealUserId, DelegateUserId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, StartDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu",
                        StartDate,
                        EndDate);
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        DelegateUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        dlgReq.DelegationComment + "(Akış Admini işlemi Sonrası devam ettirildi.)",
                        EndDate,
                        dlgReq.EndTime);
                }
                else
                {
                    /// Akış Admini için Delegasyon üretiyoruz
                    WorkflowDelegationHelper.StartDelegation(
                        RealUserId,
                        FlowAdminUserId,
                        CurrentWfIns.WfWorkflowDef.WfWorkflowDefId,
                        "Akış Admini Delegasyonu oluşturuldu",
                        StartDate,
                        EndDate);
                }
            }
        }

        #endregion FlowAdminConvertion

        #region Custom Funtions

        /// <summary>
        /// Listbox icersiidneki itemlari ; ile ayirarak stringe yazar
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public string GetMacrosFromListBox(ListBox listBox)
        {
            string result = "";

            foreach (ListItem item in listBox.Items)
            {
                result += item.Value + ";";
            }
            return result;
        }

        /// <summary>
        /// string icerisinde ; ile ayrilarak bulunan itemlari listboxa doldurur.
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        public void GetListBoxItemsFromMacros(string macros, ref ListBox list)
        {
            if (string.IsNullOrEmpty(macros))
                return;

            string[] items = macros.Split(';');

            foreach (string item in items)
            {
                list.Items.Add(item);
                list.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// SharePoint Üzerine dosya yazmak için kullanılır ((sözleşme))
        /// </summary>
        /// <param name="dosya"></param>
        /// <param name="OnlyFileName"></param>
        /// <returns></returns>
        public string SharepointWrite(string dosya, string OnlyFileName)
        {

            //gonderilen sharepoint listesine ilgili dosyayı yazar (sözleşme)
            string SharePointPath = System.Configuration.ConfigurationManager.AppSettings["SharePointList"];
            char[] b = new char[dosyaUploadYasakliKarakterler.Length];
            b = dosyaUploadYasakliKarakterler.ToCharArray();
            foreach (char item in b)
            {
                if (OnlyFileName.IndexOf(item) != -1)
                {
                    return null;
                }
            }
            SharePointPath = SharePointPath + OnlyFileName;
            //SharePointList
            WebResponse response = null;
            try
            {
                WebRequest request = WebRequest.Create(SharePointPath);
                request.Credentials = CredentialCache.DefaultCredentials;
                request.Method = "PUT";
                byte[] buffer = new byte[1024];
                using (Stream stream = request.GetRequestStream())
                using (FileStream fsWorkbook = File.Open(dosya,
                FileMode.Open, FileAccess.Read))
                {
                    int i = fsWorkbook.Read(buffer, 0, buffer.Length);
                    while (i > 0)
                    {
                        stream.Write(buffer, 0, i);
                        i = fsWorkbook.Read(buffer, 0, buffer.Length);
                    }
                }
                request.Credentials = new NetworkCredential(ConfigurationManager.AppSettings["Web.Services.UserName"], ConfigurationManager.AppSettings["Web.Services.Password"], ConfigurationManager.AppSettings["Web.Services.Domain"]);
                response = request.GetResponse();
            }
            catch (Exception ex)
            {
                return null;
            }
            finally
            {
                response.Close();
            }
            return SharePointPath;
        }

        /// <summary>
        /// SharePoint Üzerine dosya yazmak için kullanılır ((path gönderiliyor))
        /// </summary>
        /// <param name="dosya"></param>
        /// <param name="OnlyFileName"></param>
        /// <returns></returns>
        public string SharepointWrite(string dosya, string OnlyFileName, string SharePointPath)
        {

            //gonderilen sharepoint listesine ilgili dosyayı yazar (path gönderiliyor)
            //string SharePointPath = System.Configuration.ConfigurationManager.AppSettings["SharePointList"];
            char[] b = new char[dosyaUploadYasakliKarakterler.Length];
            b = dosyaUploadYasakliKarakterler.ToCharArray();
            foreach (char item in b)
            {
                if (OnlyFileName.IndexOf(item) != -1)
                {
                    return null;
                }
            }
            SharePointPath = SharePointPath + OnlyFileName;
            //SharePointList
            WebResponse response = null;
            try
            {
                WebRequest request = WebRequest.Create(SharePointPath);
                request.Credentials = CredentialCache.DefaultCredentials;
                request.Method = "PUT";
                byte[] buffer = new byte[1024];
                using (Stream stream = request.GetRequestStream())
                using (FileStream fsWorkbook = File.Open(dosya,
                FileMode.Open, FileAccess.Read))
                {
                    int i = fsWorkbook.Read(buffer, 0, buffer.Length);
                    while (i > 0)
                    {
                        stream.Write(buffer, 0, i);
                        i = fsWorkbook.Read(buffer, 0, buffer.Length);
                    }
                }
                request.Credentials = new NetworkCredential(ConfigurationManager.AppSettings["Web.Services.UserName"], ConfigurationManager.AppSettings["Web.Services.Password"], ConfigurationManager.AppSettings["Web.Services.Domain"]);
                response = request.GetResponse();
            }
            catch (Exception ex)
            {
                return null;
            }
            finally
            {
                response.Close();
            }
            return SharePointPath;
        }

        /// <summary>
        /// SharePoint Üzerinde Verilen Path'e dosya yazmak için kullanılır
        /// </summary>
        /// <param name="dosya"></param>
        /// <param name="OnlyFileName"></param>
        /// <returns></returns>
        public string SharepointWrite(System.Web.UI.WebControls.FileUpload FileUpp, string SharePointPath)
        {
            //
            string ResultFile = "";
            string uploadPath = System.IO.Path.GetTempPath();
            string dosya_adi = FileUpp.FileName;
            string[] FileList = dosya_adi.Split('.');
            string uzanti = FileList[FileList.Length - 1];
            dosya_adi = "";
            for (int i = 0; i < FileList.Length - 1; i++)
            {
                dosya_adi = dosya_adi + FileList[i];
            }
            dosya_adi = dosya_adi + "_" + System.Guid.NewGuid() + '.' + uzanti;
            char[] b = new char[dosyaUploadYasakliKarakterler.Length];
            b = dosyaUploadYasakliKarakterler.ToCharArray();
            foreach (char item in b)
            {
                if (dosya_adi.IndexOf(item) != -1)
                {
                    return null;
                }
            }
            if (FileUpp.HasFile)
            {
                try
                {
                    FileUpp.SaveAs(uploadPath + dosya_adi);
                    SharePointPath = SharePointPath + dosya_adi;
                    WebResponse response = null;
                    try
                    {
                        WebRequest request = WebRequest.Create(SharePointPath);
                        request.Credentials = CredentialCache.DefaultCredentials;
                        request.Method = "PUT";
                        byte[] buffer = new byte[1024];
                        using (Stream stream = request.GetRequestStream())
                        using (FileStream fsWorkbook = File.Open(uploadPath + dosya_adi,
                        FileMode.Open, FileAccess.Read))
                        {
                            int i = fsWorkbook.Read(buffer, 0, buffer.Length);
                            while (i > 0)
                            {
                                stream.Write(buffer, 0, i);
                                i = fsWorkbook.Read(buffer, 0, buffer.Length);
                            }
                        }
                        request.Credentials = new NetworkCredential(ConfigurationManager.AppSettings["Web.Services.UserName"], ConfigurationManager.AppSettings["Web.Services.Password"], ConfigurationManager.AppSettings["Web.Services.Domain"]);
                        response = request.GetResponse();
                        File.Delete(uploadPath + dosya_adi);
                    }
                    catch (Exception ex)
                    {
                        return null;
                    }
                    finally
                    {
                        if (System.IO.File.Exists(uploadPath + dosya_adi))
                        {
                            System.IO.File.Delete(uploadPath + dosya_adi);
                        }
                        response.Close();
                    }
                }
                catch (Exception ex)
                {
                    return null;
                }
            }
            if (System.IO.File.Exists(uploadPath + dosya_adi))
            {
                System.IO.File.Delete(uploadPath + dosya_adi);
            }
            return SharePointPath;
        }

        /// <summary>
        /// SharePoint Üzerinde Verilen Path üzerinden dosyayı kaldırmak için kullanılır
        /// </summary>
        /// <param name="SharePointPath"></param>
        /// <returns></returns>
        public string SharepointRemove(string SharePointPath)
        {

            //
            string ResultFile = "";

            try
            {
                WebResponse response = null;
                try
                {
                    WebRequest request = WebRequest.Create(SharePointPath);
                    request.Credentials = CredentialCache.DefaultCredentials;
                    request.Method = "DELETE";
                    using (Stream stream = request.GetRequestStream())
                        request.Credentials = new NetworkCredential(ConfigurationManager.AppSettings["Web.Services.UserName"], ConfigurationManager.AppSettings["Web.Services.Password"], ConfigurationManager.AppSettings["Web.Services.Domain"]);
                    response = request.GetResponse();
                }
                catch (Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    response.Close();
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }

            return ResultFile;
        }

        /// Verilen Path'e dosyayı kaydeder.
        /// </summary>
        /// <param name="FileUpp"></param>
        /// <param name="KayitKlasoru"></param>
        /// <returns></returns>
        public string SaveFile(System.Web.UI.WebControls.FileUpload FileUpp, string KayitKlasoru)
        {
            if (KayitKlasoru.Substring(KayitKlasoru.Length) != @"\")
            {
                KayitKlasoru += @"\";
            }
            string dosya_adi = FileUpp.FileName;
            string[] FileList = dosya_adi.Split('.');
            string uzanti = FileList[FileList.Length - 1];
            dosya_adi = "";
            for (int i = 0; i < FileList.Length - 1; i++)
            {
                dosya_adi = dosya_adi + FileList[i];
            }
            dosya_adi = dosya_adi + "_" + System.Guid.NewGuid().ToString().Replace(".", "").Replace("-", "") + '.' + uzanti;
            char[] b = new char[dosyaUploadYasakliKarakterler.Length];
            b = dosyaUploadYasakliKarakterler.ToCharArray();
            foreach (char item in b)
            {
                if (dosya_adi.IndexOf(item) != -1)
                {
                    return null;
                }
            }
            if (FileUpp.HasFile)
            {
                try
                {
                    FileUpp.SaveAs(KayitKlasoru + dosya_adi);
                }
                catch (Exception ex)
                {
                    throw ex;
                }
            }
            //Yalnizca kayit edilen dosyanin "yeni adını" döndürür
            return dosya_adi;
        }
        /// Verilen Path'e dosyayı kaydeder.
        /// </summary>
        /// <param name="FileUpp"></param>
        /// <param name="KayitKlasoru"></param>
        /// <returns></returns>
        public string SaveFile(AjaxControlToolkit.AsyncFileUpload FileUpp, string KayitKlasoru)
        {
            //gönderilen dosyayı temp klasöre kaydeder, sonrasında share point listesine yazar
            string ResultFile = "";
            string uploadPath = System.IO.Path.GetTempPath();

            string dosya_adi = FileUpp.FileName;

            #region dosya adi kontrolü

            char[] b = new char[dosyaUploadYasakliKarakterler.Length];
            b = dosyaUploadYasakliKarakterler.ToCharArray();
            foreach (char item in b)
            {
                if (dosya_adi.IndexOf(item) != -1)
                {
                    return null;
                }
            }

            #endregion dosya adi kontrolü

            string[] FileList = dosya_adi.Split('.');
            string uzanti = FileList[FileList.Length - 1];
            dosya_adi = "";
            for (int i = 0; i < FileList.Length - 1; i++)
            {
                dosya_adi = dosya_adi + FileList[i];
            }
            dosya_adi = dosya_adi + "_" + System.Guid.NewGuid() + '.' + uzanti;
            if (FileUpp.HasFile)
            {
                try
                {
                    FileUpp.SaveAs(uploadPath + dosya_adi);
                    ResultFile = SharepointWrite(uploadPath + dosya_adi, dosya_adi, KayitKlasoru);
                    if (System.IO.File.Exists(uploadPath + dosya_adi))
                    {
                        System.IO.File.Delete(uploadPath + dosya_adi);
                    }
                }
                catch (Exception ex)
                {
                    return null;
                    //throw ex;
                }
            }
            return ResultFile;
        }

        /// <summary>
        /// Bütçe kodlarını treeview e eklemeyi sağlar
        /// </summary>
        /// <param name="dtBudget"></param>
        public TreeView FillBudgetTreeList(TreeView BudgetTreeView)
        {
            //Ana bütçe kodları çekilir
            DataTable dtMainBudgets = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.PurchaseHelper.dtGetMainBudgets(((AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName);
            for (int i = 0; i < dtMainBudgets.Rows.Count; i++)
            {
                string Text = dtMainBudgets.Rows[i]["HS_ADI"].ToString();
                string Value = dtMainBudgets.Rows[i]["HESAP_KODU"].ToString();
                TreeNode tr = new TreeNode(Text + " [" + Value + "]", Value);
                //Ana bütçe kodları treeview a eklenir
                BudgetTreeView.Nodes.Add(tr);
                //Alt bütçe kodları çekilir
                DataTable dtSubBugdets = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformations.PurchaseHelper.dtGetSubBudgets(Value, ((AuthenticationResult)Session[SessionUserVariable]).LoginObject.DomainUserName);
                for (int j = 0; j < dtSubBugdets.Rows.Count; j++)
                {
                    string Department = dtSubBugdets.Rows[j]["DEPARTMAN"].ToString();
                    string SubText = dtSubBugdets.Rows[j]["HS_ADI"].ToString();
                    string SubValue = dtSubBugdets.Rows[j]["HESAP_KODU"].ToString();
                    TreeNode trSub = new TreeNode(SubText + " [" + SubValue + "]", SubValue);
                    //Alt bütçe kodları treeview a eklenir
                    BudgetTreeView.Nodes[i].ChildNodes.Add(trSub);
                }
            }
            return BudgetTreeView;
        }

        /// <summary>
        /// Kontrolu Recursive(Icice) olarak arar
        /// </summary>
        /// <param name="ParentControl">Bakilacak Ust Kontrol</param>
        /// <param name="SearchId">Aranacak Detay Kontrol Idsi</param>
        /// <returns></returns>
        public System.Web.UI.Control FindControl_Recursive(System.Web.UI.Control ParentControl, String SearchId)
        {
            System.Web.UI.Control vReturn = null;

            if (ParentControl.Controls != null && ParentControl.Controls.Count > 0)
            {
                for (int i = 0; i < ParentControl.Controls.Count; i++)
                {
                    vReturn = FindControl_Recursive(ParentControl.Controls[i], SearchId);
                    if (vReturn != null && vReturn.ID == SearchId)
                    {
                        break;
                    }
                }
            }
            if (vReturn != null && vReturn.ID == SearchId)
            {
                ; // vReturn = vReturn;
            }
            else if (ParentControl != null && ParentControl.ID == SearchId)
            {
                vReturn = ParentControl;
            }

            return vReturn;
        }
        private string PageNameLocal
        {
            get
            {
                //if (Request.QueryString["RequestType"] != null)
                //{
                //    if (Request.QueryString["RequestType"] == "0")
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "1")
                //    {
                //        return "OutsourceRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "2")
                //    {
                //        return "StajyerRequest.aspx".ToLower();
                //    }
                //    else
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //}
                if (Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower() == "nemployeerequest.aspx")
                {
                    return "EmployeeRequest.aspx".ToLower();

                }
                else
                {
                    return Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower();
                }
            }
        }

        #endregion Custom Funtions
        private string SetPageName(string pageName)
        {
            pageName = pageName.Contains("_mobil") ? pageName.Replace("_mobil", "") : pageName;
            pageName = pageName.Contains("_Mobil") ? pageName.Replace("_Mobil", "") : pageName;
            pageName = pageName.Contains("_onay2") ? pageName.Replace("_onay2", "") : pageName;

            return pageName;
        }
        public string PageName
        {
            get
            {
                //if (Request.QueryString["RequestType"] != null)
                //{
                //    if (Request.QueryString["RequestType"] == "0")
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "1")
                //    {
                //        return "OutsourceRequest.aspx".ToLower();
                //    }
                //    else if (Request.QueryString["RequestType"] == "2")
                //    {
                //        return "StajyerRequest.aspx".ToLower();
                //    }
                //    else
                //    {
                //        return "EmployeeRequest.aspx".ToLower();
                //    }
                //}
                if (Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower() == "nemployeerequest.aspx")
                {
                    return "EmployeeRequest.aspx".ToLower();

                    //if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1222)
                    //{
                    //    return "EmployeeRequest.aspx".ToLower();
                    //}
                    //else if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1243)
                    //{
                    //    return "StajyerRequest.aspx".ToLower();
                    //}
                    //else if (((WorkFlowPage)this.Page).CurrentWfIns.WfWorkflowDef.WfWorkflowDefId == 1244)
                    //{
                    //    return "OutsourceRequest.aspx".ToLower();
                    //}
                    //else
                    //{
                    //    return "EmployeeRequest.aspx".ToLower();
                    //}
                }
                else
                {
                    string pageName = Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower().Replace("_mobil", "").Replace("_Mobil", "").Replace("_onay2", "");
                    return pageName;
                    //return Request.Url.Segments[Request.Url.Segments.Length - 1].ToLower();
                }
            }
        }

    }

}