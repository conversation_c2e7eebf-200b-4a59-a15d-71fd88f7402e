﻿using Digiturk.Workflow.Digiflow.WebCore.MasterPage;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public class ReportBasePage : SecurePage
    {
        public ReportAdminType ReportAdmin
        {
            get
            {
                if (Session["ReportAdminType"] != null)
                {
                    return (ReportAdminType)Session["ReportAdminType"];
                }
                else
                {
                    return ReportAdminType.User;
                }
            }
            set
            {
                Session["ReportAdminType"] = value;
            }
        }

        public void SetReportAdmin(ReportAdminType AdminType)
        {
            //_ReportAdmin = AdminType;
        }

        public long AdminLogicalGroupId { get; set; }
        //public

        protected override void OnInit(EventArgs e)
        {
            string ReportName = Request.Url.Segments[Request.Url.Segments.Length - 1].ToString().Replace(".aspx", "");
            ////WorkFlowTraceWorker.OracleLog(UserInformation.LoginObject.DomainUserName, "ReportPage - " + ReportName, "OnInit");
            long ReportAdminLogicalGroup = Digiturk.Workflow.Digiflow.WorkFlowHelpers.ReportInformations.ReportAdminLogicalGroup.GetLogiCalGroup(ReportName);
            Digiturk.Workflow.Common.WorkflowLoginHelper wfHelper = new Digiturk.Workflow.Common.WorkflowLoginHelper();
            if (Digiflow.WorkFlowHelpers.LogicalGroupHelper.IsExistLogicalGroup(ReportAdminLogicalGroup, UserInformation.LoginObject.LoginId))
            {
                ReportAdmin = ReportAdminType.Admin;
            }
            else if (wfHelper.IsManager(UserInformation.LoginObject.LoginId))
            {
                ReportAdmin = ReportAdminType.Manager;
            }
            else
            {
                ReportAdmin = ReportAdminType.User;
            }
            Session["ReportAdminType"] = ReportAdmin;
            base.OnInit(e);
        }
    }
}