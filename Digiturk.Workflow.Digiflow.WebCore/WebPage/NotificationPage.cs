﻿using Digiturk.Workflow.Common;
using Digiturk.Workflow.Digiflow.CoreHelpers;
using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using Digiturk.Workflow.Engine;
using Digiturk.Workflow.Entities;
using Digiturk.Workflow.Repository;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public class NotificationPage : SecurePage
    {
        #region WorkFlowForm Properties

        /// <summary>
        /// Akışın Durdurulup Durdurulmadığını bilir.
        /// </summary>
        public bool FlowIsSuspend { get; set; }

        /// <summary>
        /// Mail Gönderme işleminde WFContex i doldurmak için kullanılır.
        /// </summary>
        public ContextObject SenderObj { get; set; }

        /// <summary>
        /// Maili Göndericek kullanıcıların listesini tutar
        /// </summary>
        public List<FLogin> ToList { get; set; }

        /// <summary>
        ///  Sayfanın Page Modunu Belirler
        /// </summary>
        //public PageMode CurrentPageMode
        //{
        //    get
        //    {
        //        if (Request.QueryString["PageMode"] != null)
        //        {
        //            return WorkFlowInformationHelper.GetPageMode(InstanceId, UserInformation.LoginObject.LoginId, Request.QueryString["PageMode"]);
        //        }
        //        else
        //        {
        //            return WorkFlowInformationHelper.GetPageMode(InstanceId, UserInformation.LoginObject.LoginId, "");
        //        }
        //    }
        //}
        /// <summary>
        /// Sayfayla İlgili Mesajları Base de tutar
        /// </summary>
        public string PageShowInformation { get; set; }

        /// <summary>
        /// Form Üzerine Parametre ile gönderilen InstaceId değerini tutar. Devamlı Readonlydir.
        /// </summary>
        public long InstanceId
        {
            get
            {
                if (Request.QueryString[QueryStringInstanceId] != null)
                {
                    return ConvertionHelper.ConvertValue<long>(Request.QueryString[QueryStringInstanceId]);
                }
                else
                {
                    return 0;
                }
            }
        }

        /// <summary>
        ///  Bu Forma Ait Başka bir Kullanıcıya bir delegasyon olup olmadığını tespit eder.
        /// </summary>
        public bool FormDelegation
        {
            get
            {
                if (InstanceId == 0) return false;
                List<FLogin> AssignToIdList = Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginList(InstanceId);
                foreach (var item in AssignToIdList)
                {
                    if (WorkFlowInformationHelper.DelegationCheck(InstanceId, item.LoginId, UserInformation.LoginObject.LoginId, DateTime.Now))
                    {
                        return true;
                    }
                }
                return false;
            }
        }

        /// <summary>
        /// Delege edilen kişi
        /// </summary>
        public bool OwnDelegation
        {
            get
            {
                if (InstanceId == 0) return false;
                return WorkFlowInformationHelper.DelegateCheck(InstanceId, UserInformation.LoginObject.LoginId, DateTime.Now);
            }
        }

        /// <summary>
        ///  Bu Properties Forma Ait Akış Örneği nesnesini tutar
        /// </summary>
        public FWfWorkflowInstance CurrentWfIns
        {
            get
            {
                if (InstanceId > 0)
                {
                    try
                    {
                        if (Session[SessionFWfWorkflowInstance] != null)
                        {
                            if (((FWfWorkflowInstance)Session[SessionFWfWorkflowInstance]).WfWorkflowInstanceId != InstanceId)
                            {
                                Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                            }
                        }
                        else
                        {
                            Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                        }
                    }
                    catch (Exception)
                    {
                        Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                    }
                }
                else
                {
                    return null;
                }
                return ((FWfWorkflowInstance)Session[SessionFWfWorkflowInstance]);
            }
        }

        public FWfWorkflowDef CurrentWfDef
        {
            get
            {
                return WFRepository<FWfWorkflowDef>.GetEntity(CurrentWfIns.WfWorkflowDef.WfWorkflowDefId);
            }
        }

        /// <summary>
        ///  Bu Properties Forma Ait Action task instance örneğini tutar.
        /// </summary>
        public FWfActionTaskInstance CurrentActionTaskInstance
        {
            get
            {
                using (UnitOfWork.Start())
                {
                    try
                    {
                        if (Session[SessionFWfActionTaskInstance] != null)
                        {
                            if (((FWfActionTaskInstance)Session[SessionFWfActionTaskInstance]).WfActionInstanceId != CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value || CurrentWfIns.WfCurrentState != null)
                            {
                                Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                            }
                        }
                        else
                        {
                            if (CurrentWfIns.WfCurrentState != null)
                            {
                                Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                            }
                            else
                            {
                                return null;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Session[SessionFWfWorkflowInstance] = null;
                        Session[SessionFWfActionTaskInstance] = WFRepository<FWfActionTaskInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfCurrentActionInstanceId.Value);
                        System.Exception exs = ex;
                    }
                }
                return ((FWfActionTaskInstance)Session[SessionFWfActionTaskInstance]);
            }
        }

        /// <summary>
        /// Bu Properties WFContext Nesnesini Tutar
        /// </summary>
        public WFContext CurrentWFContext
        {
            get
            {
                if (Session[SessionwfContext] == null)
                {
                    using (UnitOfWork.Start())
                    {
                        FWfWorkflowInstance LocalIns = CurrentWfIns;
                        FWfActionTaskInstance LocalActionTaskIns = CurrentActionTaskInstance;
                        checked
                        {
                            Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
                            Session[SessionwfContext] = new WFContext(CurrentWfIns);
                        }
                    }
                }
                return ((WFContext)Session[SessionwfContext]);
            }

            set
            {
                Session[SessionwfContext] = value;
            }
        }

        //WFContext _CurrentWFContext;
        //public WFContext CurrentWFContextx
        //{
        //    get
        //    {
        //            using (UnitOfWork.Start())
        //            {
        //                checked
        //                {
        //                    Session[SessionFWfWorkflowInstance] = WFRepository<FWfWorkflowInstance>.GetEntity(InstanceId);
        //                    _CurrentWFContext = new WFContext(CurrentWfIns);
        //                    return _CurrentWFContext;
        //                }
        //            }
        //    }
        //    set
        //    {
        //        _CurrentWFContext = new WFContext(CurrentWfIns);
        //        _CurrentWFContext = value;
        //    }
        //}

        /// <summary>
        ///  Şuanda üzerinde bulunan state instance
        /// </summary>
        public FWfStateInstance CurrentStateIns
        {
            get
            {
                if (CurrentWfIns.WfCurrentState != null)
                {
                    Digiturk.Workflow.Entities.FWfStateInstance StateIns = WFRepository<FWfStateInstance>.GetEntity(CurrentWfIns.WfCurrentState.WfStateInstanceId);
                    return StateIns;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        ///  Şuanda üzerinde bulunan state Definition
        /// </summary>
        public FWfStateDef CurrentStateDef
        {
            get
            {
                Digiturk.Workflow.Entities.FWfStateDef StateDef = WFRepository<FWfStateDef>.GetEntity(CurrentStateIns.WfStateDef.WfStateDefId);
                return StateDef;
            }
        }

        /// <summary>
        /// Bu Properties Bu Akışa Assign edilmiş kullanıcıyı getirir.
        /// </summary>
        public FLogin AssignedUser
        {
            get
            {
                return FormInformationHelper.GetAssignedUser(CurrentActionTaskInstance);
            }
        }

        /// <summary>
        /// Bu Properties Akış Adminin Properties ini alır
        /// </summary>
        public FLogin FlowAdmin
        {
            get
            {
                if (Session[SessionFlowAdmin] == null)
                {
                    if (CurrentWfIns != null)
                    {
                        using (UnitOfWork.Start())
                        {
                            IList<FLogin> assignments = AssignmentHelper.GetAssignedList("", FWfAssignmentTypeValues.WorkflowModify, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), InstanceId.ToString());
                            //IList<FLogin> assignments = AssignmentHelper.GetAssignedList("", "WFVIEW", wfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), wfInstanceId.ToString());
                            var admin = (from s in assignments
                                         where s.LoginId == UserInformation.LoginObject.LoginId
                                         select s).FirstOrDefault();
                            Session[SessionFlowAdmin] = admin;
                        }
                    }
                }
                return (FLogin)Session[SessionFlowAdmin];
            }
        }

        /// <summary>
        /// Login olan Kişinin Akış Admini olup olmadığını Tespit eder.
        /// </summary>
        public bool IsFlowAdmin
        {
            get
            {
                if (FlowAdmin == null)
                { return false; }
                else
                { return true; }
            }
        }

        /// <summary>
        /// Atanan kullanıcı
        /// </summary>
        public long AssignToId
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetAssignToLoginId(InstanceId);
            }
        }

        /// <summary>
        /// Yoruma gönderilen kullanıcı
        /// </summary>
        public long CommendToId
        {
            get
            {
                return Digiflow.DataAccessLayer.CheckingWorker.GetCommentToLoginId(InstanceId);
            }
        }

        /// <summary>
        /// Delege edilmiş kullanıcı
        /// </summary>
        public long DelegationUserId
        {
            get
            {
                //Todo Delegasyon Change
                return WorkflowRecursiveDelegationHelper.GetActiveDelegateWithRecursive(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
                //return WorkflowDelegationHelper.GetActiveDelegate(AssignToId, CurrentWfIns.WfWorkflowDef.WfWorkflowDefId, DateTime.Now);
            }
        }

        /// <summary>
        /// Son işlem yapmış kullanıcı
        /// </summary>
        public long LastSendTaskLoginId
        {
            get
            {
                return DataAccessLayer.CheckingWorker.GetLastActionToLoginId(InstanceId);
            }
        }

        /// <summary>
        /// Son İşlem yapmış kullanıcı mı?
        /// </summary>
        public bool IsLastSendTaskLoginUser
        {
            get
            {
                return LastSendTaskLoginId == UserInformation.LoginObject.LoginId;
            }
        }

        #endregion WorkFlowForm Properties

        /// <summary>
        /// Email Gönderme Methodu
        /// </summary>
        /// <param name="ActionTypeValue"></param>
        /// <param name="WfInstanceId"></param>
        /// <param name="LastActionUserLoginId"></param>
        /// <param name="AssigtoLoginId"></param>
        /// <param name="WfContext"></param>
        public void SendEmail(long WfInstanceId, long LastActionUserLoginId, long AssigtoLoginId, Digiturk.Workflow.Common.WFContext WfContext, string NotificationNote)
        {
            using (Digiturk.Workflow.Repository.UnitOfWork.Start())
            {
                Digiturk.Workflow.Entities.FWfWorkflowInstance WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                Digiturk.Workflow.Entities.FWfStateInstance WfState = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfStateInstance>.GetEntity(WfIns.WfCurrentState.WfStateInstanceId);
                Digiturk.Workflow.Entities.FWfActionInstance ActIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfActionInstance>.GetEntity(WfState.WfCurrentActionInstanceId.Value);
                WfIns = Digiturk.Workflow.Common.WFRepository<Digiturk.Workflow.Entities.FWfWorkflowInstance>.GetEntity(WfInstanceId);
                long TemplateMailId = ConvertionHelper.ConvertValue<long>(System.Configuration.ConfigurationManager.AppSettings["NotificationTemplateID"]);
                IList<Digiturk.Workflow.Entities.FLogin> ToList = new List<Digiturk.Workflow.Entities.FLogin>();
                IList<Digiturk.Workflow.Entities.FLogin> CcList = new List<Digiturk.Workflow.Entities.FLogin>();
                FLogin ToLogin = WFRepository<FLogin>.GetEntity(AssigtoLoginId);
                ToList.Add(ToLogin);
                string CreateUserNameSurName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(WfIns.OwnerLogin.LoginId);
                string AssignedUserNameSurname = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(AssigtoLoginId);
                string LastActionUserNameSurName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(LastActionUserLoginId);
                string GetFinalLoginId = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginList = Digiturk.Workflow.Digiflow.WorkFlowHelpers.FormInformationHelper.GetDefinitionAttribute(WfIns.WfWorkflowDef.WfWorkflowDefId.ToString(), "ONFLOWENDINFORMATIONTOFIRSTLST");
                string GetFinalLoginNameSurName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(Convert.ToInt64(GetFinalLoginId));
                string NotificationUserNameSurName = Digiturk.Workflow.Digiflow.WorkFlowHelpers.WfDataHelpers.LoginNameSurnameGet(UserInformation.LoginObject.LoginId);
                string StateName = CurrentStateDef.Name;
                WfContext.Parameters.AddOrChangeItem("CreateUserNameSurName", CreateUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("AssinedUserNameSurname", AssignedUserNameSurname);
                WfContext.Parameters.AddOrChangeItem("LastActionUserNameSurName", LastActionUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("GetFinalLoginNameSurName", GetFinalLoginNameSurName);
                WfContext.Parameters.AddOrChangeItem("StateName", StateName);
                WfContext.Parameters.AddOrChangeItem("NotificationUserNameSurName", NotificationUserNameSurName);
                WfContext.Parameters.AddOrChangeItem("NotificationNote", NotificationNote);
                Digiturk.Workflow.Engine.MailHelper.SendMail(TemplateMailId, ActIns, WfContext, ToList, CcList);
            }
        }
    }
}