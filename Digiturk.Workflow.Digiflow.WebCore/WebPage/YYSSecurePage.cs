﻿using Digiturk.Workflow.Digiflow.WorkFlowHelpers;
using System;

namespace Digiturk.Workflow.Digiflow.WebCore
{
    public class YYSSecurePage : SecurePage
    {
        public YYSAdminType AdminType
        {
            get
            {
                if (Session[AdminUser] == null)
                {
                    Session[AdminUser] = Digiflow.WorkFlowHelpers.WorkFlowInformationHelper.GetFlowAdminType(UserInformation.LoginObject.LoginId);
                }
                return (YYSAdminType)Session[AdminUser];
            }
        }

        protected override void OnInit(EventArgs e)
        {
            if (AdminType == YYSAdminType.None)
            {
                SetSessionError("YYS Admin tanımınız bulunmamaktadır", "Kullanıcı bilgileriniz doğrulanamadı.9", null);
                Response.Redirect("Error.aspx", false);
            }
            base.OnInit(e);
        }
    }
}