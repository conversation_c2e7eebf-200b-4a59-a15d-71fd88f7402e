﻿namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    /// <summary>
    /// Prosess İşlemlerinin Yapıldığı web Servisinin Proxy Classıdır
    /// </summary>
    public class PermissionProcess
    {
        private Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess.PermissionProcess _PermissionProcessObj;

        //Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess.PermissionProcessSoapClient _PermissionProcessObj;
        /// Prosess İşlemlerinin Yapıldığı web Servisinin Proxy Nesnesidir
        public Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess.PermissionProcess PermissionProcessObj
        {
            get
            {
                _PermissionProcessObj = new Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess.PermissionProcess();
                _PermissionProcessObj.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                _PermissionProcessObj.Proxy = WebServicesProxyHelper.ServicesProxy;
                return _PermissionProcessObj;
            }
        }
    }
}