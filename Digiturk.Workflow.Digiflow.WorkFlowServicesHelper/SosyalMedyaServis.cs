﻿using Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.SosyalMedyaService;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public static class SosyalMedyaServis
    {
       
        public static string MetinVerSosyalMedya(string sirket, string donulecek, long? documentID)
        {
            string sonuc = "";
            SosyalMedyaService.PersonalDataProtectionBS client = new PersonalDataProtectionBS();
            client.Url = System.Configuration.ConfigurationManager.AppSettings["SosyalMedyaServisi"];
            var token = client.SystemAuthenticate("SYSIQ", "SYSIQ111", "DIGITURK", "DIGIPORT", sirket);

            GetPermissionTextRequest request = new GetPermissionTextRequest();
            request.ClientName = "PC";
            //if (documentID != null)
            //{
            //    request.DocumentId = documentID;
            //}
            //else
            //{
            //    request.PermissionTypes = new DataPermissionType[] { DataPermissionType.VERI_PAYLASMA_IZNI };
            //}

           request.PermissionTypes = new DataPermissionType[] { DataPermissionType.SOSYAL_MEDYA_METNI };

            var response = client.GetPermissionText(request, token);

            if (response != null && response.IsSuccess)
            {
                if (response.Data != null && response.Data.Result != null)
                {
                    foreach (var item in response.Data.Result)
                    {
                        if (donulecek == "Content")
                        {
                            sonuc += item.Content;
                        }
                        else if (donulecek == "DocumentId")
                        {
                            sonuc += item.DocumentId;
                        }
                    }
                }
            }
            return sonuc;
        }

        public static string MetinVerCikarCatismasi(string sirket, string donulecek, long? documentID)
        {
            string sonuc = "";
            SosyalMedyaService.PersonalDataProtectionBS client = new PersonalDataProtectionBS();
            client.Url = System.Configuration.ConfigurationManager.AppSettings["SosyalMedyaServisi"];
            var token = client.SystemAuthenticate("SYSIQ", "SYSIQ111", "DIGITURK", "DIGIPORT", sirket);

            GetPermissionTextRequest request = new GetPermissionTextRequest();
            request.ClientName = "PC";
            request.PermissionTypes = new DataPermissionType[] { DataPermissionType.CIKAR_CATISMASI_METNI };

            var response = client.GetPermissionText(request, token);

            if (response != null && response.IsSuccess)
            {
                if (response.Data != null && response.Data.Result != null)
                {
                    foreach (var item in response.Data.Result)
                    {
                        if (donulecek == "Content")
                        {
                            sonuc += item.Content;
                        }
                        else if (donulecek == "DocumentId")
                        {
                            sonuc += item.DocumentId;
                        }
                    }
                }
            }
            return sonuc;
        }
    }
}