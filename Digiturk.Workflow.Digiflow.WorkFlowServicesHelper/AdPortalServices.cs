﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace ApPortalServices.AdPortal.LdapServices
{
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;


    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name = "AdPortalServicesSoap", Namespace = "http://tempuri.org/")]
    public partial class AdPortalServices : System.Web.Services.Protocols.SoapHttpClientProtocol
    {

        private System.Threading.SendOrPostCallback ClosePersonelOperationCompleted;

        private System.Threading.SendOrPostCallback GetGroupOfUserListOperationCompleted;

        private System.Threading.SendOrPostCallback GetGroupListOperationCompleted;

        private bool useDefaultCredentialsSetExplicitly;

        /// <remarks/>
        public AdPortalServices()
        {
            this.Url = System.Configuration.ConfigurationSettings.AppSettings["AdPortalServices"];
            if ((this.IsLocalFileSystemWebService(this.Url) == true))
            {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else
            {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }

        public new string Url
        {
            get
            {
                return base.Url;
            }
            set
            {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true)
                            && (this.useDefaultCredentialsSetExplicitly == false))
                            && (this.IsLocalFileSystemWebService(value) == false)))
                {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }

        public new bool UseDefaultCredentials
        {
            get
            {
                return base.UseDefaultCredentials;
            }
            set
            {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }

        /// <remarks/>
        public event ClosePersonelCompletedEventHandler ClosePersonelCompleted;

        /// <remarks/>
        public event GetGroupOfUserListCompletedEventHandler GetGroupOfUserListCompleted;

        /// <remarks/>
        public event GetGroupListCompletedEventHandler GetGroupListCompleted;

        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/ClosePersonel", RequestNamespace = "http://tempuri.org/", ResponseNamespace = "http://tempuri.org/", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string ClosePersonel(string UserName, string Password, string OperationUser, string CloseUserName)
        {
            object[] results = this.Invoke("ClosePersonel", new object[] {
                        UserName,
                        Password,
                        OperationUser,
                        CloseUserName});
            return ((string)(results[0]));
        }

        /// <remarks/>
        public void ClosePersonelAsync(string UserName, string Password, string OperationUser, string CloseUserName)
        {
            this.ClosePersonelAsync(UserName, Password, OperationUser, CloseUserName, null);
        }

        /// <remarks/>
        public void ClosePersonelAsync(string UserName, string Password, string OperationUser, string CloseUserName, object userState)
        {
            if ((this.ClosePersonelOperationCompleted == null))
            {
                this.ClosePersonelOperationCompleted = new System.Threading.SendOrPostCallback(this.OnClosePersonelOperationCompleted);
            }
            this.InvokeAsync("ClosePersonel", new object[] {
                        UserName,
                        Password,
                        OperationUser,
                        CloseUserName}, this.ClosePersonelOperationCompleted, userState);
        }

        private void OnClosePersonelOperationCompleted(object arg)
        {
            if ((this.ClosePersonelCompleted != null))
            {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ClosePersonelCompleted(this, new ClosePersonelCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }

        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetGroupOfUserList", RequestNamespace = "http://tempuri.org/", ResponseNamespace = "http://tempuri.org/", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public UserInformation[] GetGroupOfUserList(string GroupName)
        {
            object[] results = this.Invoke("GetGroupOfUserList", new object[] {
                        GroupName});
            return ((UserInformation[])(results[0]));
        }

        /// <remarks/>
        public void GetGroupOfUserListAsync(string GroupName)
        {
            this.GetGroupOfUserListAsync(GroupName, null);
        }

        /// <remarks/>
        public void GetGroupOfUserListAsync(string GroupName, object userState)
        {
            if ((this.GetGroupOfUserListOperationCompleted == null))
            {
                this.GetGroupOfUserListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetGroupOfUserListOperationCompleted);
            }
            this.InvokeAsync("GetGroupOfUserList", new object[] {
                        GroupName}, this.GetGroupOfUserListOperationCompleted, userState);
        }

        private void OnGetGroupOfUserListOperationCompleted(object arg)
        {
            if ((this.GetGroupOfUserListCompleted != null))
            {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetGroupOfUserListCompleted(this, new GetGroupOfUserListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }

        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetGroupList", RequestNamespace = "http://tempuri.org/", ResponseNamespace = "http://tempuri.org/", Use = System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle = System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public GroupInformation[] GetGroupList()
        {
            object[] results = this.Invoke("GetGroupList", new object[0]);
            return ((GroupInformation[])(results[0]));
        }

        /// <remarks/>
        public void GetGroupListAsync()
        {
            this.GetGroupListAsync(null);
        }

        /// <remarks/>
        public void GetGroupListAsync(object userState)
        {
            if ((this.GetGroupListOperationCompleted == null))
            {
                this.GetGroupListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetGroupListOperationCompleted);
            }
            this.InvokeAsync("GetGroupList", new object[0], this.GetGroupListOperationCompleted, userState);
        }

        private void OnGetGroupListOperationCompleted(object arg)
        {
            if ((this.GetGroupListCompleted != null))
            {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetGroupListCompleted(this, new GetGroupListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }

        /// <remarks/>
        public new void CancelAsync(object userState)
        {
            base.CancelAsync(userState);
        }

        private bool IsLocalFileSystemWebService(string url)
        {
            if (((url == null)
                        || (url == string.Empty)))
            {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024)
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0)))
            {
                return true;
            }
            return false;
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://tempuri.org/")]
    public partial class UserInformation
    {

        private string userNameField;

        private string nameSurNameField;

        private string displayNameField;

        /// <remarks/>
        public string UserName
        {
            get
            {
                return this.userNameField;
            }
            set
            {
                this.userNameField = value;
            }
        }

        /// <remarks/>
        public string NameSurName
        {
            get
            {
                return this.nameSurNameField;
            }
            set
            {
                this.nameSurNameField = value;
            }
        }

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace = "http://tempuri.org/")]
    public partial class GroupInformation
    {

        private string groupNameField;

        private string groupEmailField;

        private string displayNameField;

        /// <remarks/>
        public string GroupName
        {
            get
            {
                return this.groupNameField;
            }
            set
            {
                this.groupNameField = value;
            }
        }

        /// <remarks/>
        public string GroupEmail
        {
            get
            {
                return this.groupEmailField;
            }
            set
            {
                this.groupEmailField = value;
            }
        }

        /// <remarks/>
        public string DisplayName
        {
            get
            {
                return this.displayNameField;
            }
            set
            {
                this.displayNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void ClosePersonelCompletedEventHandler(object sender, ClosePersonelCompletedEventArgs e);

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ClosePersonelCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
    {

        private object[] results;

        internal ClosePersonelCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
                base(exception, cancelled, userState)
        {
            this.results = results;
        }

        /// <remarks/>
        public string Result
        {
            get
            {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetGroupOfUserListCompletedEventHandler(object sender, GetGroupOfUserListCompletedEventArgs e);

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetGroupOfUserListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
    {

        private object[] results;

        internal GetGroupOfUserListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
                base(exception, cancelled, userState)
        {
            this.results = results;
        }

        /// <remarks/>
        public UserInformation[] Result
        {
            get
            {
                this.RaiseExceptionIfNecessary();
                return ((UserInformation[])(this.results[0]));
            }
        }
    }

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetGroupListCompletedEventHandler(object sender, GetGroupListCompletedEventArgs e);

    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetGroupListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs
    {

        private object[] results;

        internal GetGroupListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) :
                base(exception, cancelled, userState)
        {
            this.results = results;
        }

        /// <remarks/>
        public GroupInformation[] Result
        {
            get
            {
                this.RaiseExceptionIfNecessary();
                return ((GroupInformation[])(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591