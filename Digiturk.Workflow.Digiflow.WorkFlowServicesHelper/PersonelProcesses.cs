﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    /// <summary>
    /// İşe giriş ve Çıkış İşlemlerini poldyden yapılması içindir
    /// </summary>
    public class PersonelProcesses
    {
        public static string Username { get; set; }
        public static string Password { get; set; }

        public enum UserType { Poldy, PoldyBasin, None };

        /// <summary>
        /// Yeni bir Servis Nesnesi Üretir
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        private static Digiturk.Services.IseGirisPoldy.PersonelBilgisi CreateClient(UserType KullaniciGrubu)
        {
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = new Digiturk.Services.IseGirisPoldy.PersonelBilgisi();
            System.ServiceModel.EndpointAddress Adress;
            string adres = "";

            switch (KullaniciGrubu)
            {
                case UserType.Poldy:

                    Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointDefaul"]);
                    adres = System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointDefaul"];

                    break;

                case UserType.PoldyBasin:
                    Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointMedia"]);
                    adres = System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointMedia"];
                    break;

                default:
                    break;
            }
            Client.Url = adres;
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            return Client;
        }

        public static bool CheckUser(string TCKN, string Firma, UserType KullaniciGrubu)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.PersonelBilgileriListele(Username, Password, "A", TCKN);
            if (DS != null && DS.Tables.Count != 0 && DS.Tables[0].Rows.Count != 0)
            {
                //  foreach (DataRow DRow in DS.Tables[0].Rows)
                //     if (DRow["Firma"].ToString().Equals(Firma) && !DRow["yis_akt"].ToString().Equals("C"))
                return true;
            }
            else
                return false;
        }

        public static string InsertUser(DataTable Infos, UserType KullaniciGrubu)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string Sicil = Client.PersonelYeniGirisEkle(Username, Password, Infos);
            return Sicil;
        }

        public class DSource
        {
            public DSource(string _ID, string _ADI)
            {
                ID = _ID;
                ADI = _ADI;
            }

            public string ID { get; set; }
            public string ADI { get; set; }
        }

        public static List<DSource> GetLocations(UserType KullaniciGrubu)
        {
            List<DSource> ret = new List<DSource>();
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.LokasyonBilgileriListele(Username, Password);
            if (DS.Tables.Count > 0)
            {
                ret.Add(new DSource("-1", "---Seçiniz---"));
                foreach (DataRow DR in DS.Tables[0].Select("", "adi"))
                    ret.Add(new DSource(DR["location_kodu"].ToString(), DR["adi"].ToString().Trim()));
            }
            return ret;
        }

        public static List<DSource> GetTitle(UserType KullaniciGrubu)
        {
            List<DSource> ret = new List<DSource>();
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.ParamTitleListe(Username, Password);
            if (DS.Tables.Count > 0)
            {
                ret.Add(new DSource("-1", "---Seçiniz---"));
                foreach (DataRow DR in DS.Tables[0].Select("", "title_adi"))
                    ret.Add(new DSource(DR["title_kodu"].ToString(), DR["title_adi"].ToString().Trim()));
            }
            return ret;
        }

        public static DataTable GetPersonelTitleBilgi(UserType KullaniciGrubu, string sicilNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.PersonelTitleBilgi(Username, Password, sicilNo);
            DataTable dt = DS.Tables[0];
            return dt;
        }


        public static List<DSource> GetCompany(UserType KullaniciGrubu)
        {
            List<DSource> ret = new List<DSource>();
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.SirketBilgileriListele(Username, Password);
            if (DS.Tables.Count > 0)
            {
                ret.Add(new DSource("-1", "---Seçiniz---"));
                foreach (DataRow DR in DS.Tables[0].Select("", "kisa_adi"))
                    ret.Add(new DSource(DR["sirket_kodu"].ToString(), DR["kisa_adi"].ToString().Trim()));
                //     DataRow[] a = DS.Tables[0].Select("", "kisa_adi");
                //   for (int i = 0; i < a.Length; i++)
                //     ret.Add(new DSource(a[i]["sirket_kodu"].ToString(), a[i]["kisa_adi"].ToString().Trim()));
            }
            return ret;
        }

        public static DataTable GetAcil(UserType KullaniciGrubu, string SicilNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.PersonelAcilDurumListe(Username, Password, SicilNo);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static string UpdateAcil(UserType KullaniciGrubu, string SicilNo, int KisiNo, string AdSoyad, string IsTel, string EvTel, string CepTel, string Yakinlik, string Adres, string Mail)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string update = Client.PersonelAcilDurumGuncelle(Username, Password, SicilNo, KisiNo, AdSoyad, IsTel, EvTel, CepTel, Yakinlik, Adres, Mail);
            return update;
        }

        public static string DeleteAcil(UserType KullaniciGrubu, string SicilNo, int KisiNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string delete = Client.PersonelAcilDurumSil(Username, Password, SicilNo, KisiNo);
            return delete;
        }

        public static string DeleteCocuk(UserType KullaniciGrubu, string SicilNo, int KisiNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string delete = Client.PersonelCocukSil(Username, Password, SicilNo, KisiNo);
            return delete;
        }

        public static DataTable GetCocuk(UserType KullaniciGrubu, string SicilNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.PersonelCocukListe(Username, Password, SicilNo);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static string UpdateCocuk(UserType KullaniciGrubu, string SicilNo, int KayitId, string Adi, string Soyadi, string Cinsiyet, DateTime DogumTarihi, string Tahsil, string saglik, string tahsildevam, string indirim, string tcKimlikNo)
        {
            //return "";
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string update = Client.PersonelCocukGuncelle(Username, Password, SicilNo, KayitId, Adi, Soyadi, Cinsiyet, DogumTarihi, Tahsil, tcKimlikNo);
            return update;
        }

        public static string UpdateEs(UserType KullaniciGrubu, string SicilNo, string Adi, string Soyadi, DateTime DogumTarihi, string Tahsil, string saglik, string calisiyormu, string indirim, string tcKimlikNo)
        {
            //return "";
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            string update = Client.PersonelEsGuncelle(Username, Password, SicilNo, Adi, Soyadi, DogumTarihi, Tahsil, calisiyormu, tcKimlikNo);
            return update;
        }

        public static DataTable GetTahsil(UserType KullaniciGrubu, string sicilNo, string tipi)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.TahsilOkulTuruParametresiGetir(Username, Password, sicilNo, tipi);
            DataTable dt = DS.Tables[0];
            if (DS.Tables.Count > 0)
            {
                DataRow a = dt.NewRow();
                a["okul_tur_kodu"] = "-1";
                a["adi"] = "---Seçiniz---";
                dt.Rows.InsertAt(a, 0);
            }
            return dt;
        }

        public static DataTable GetOkul(UserType KullaniciGrubu, string sicilNo, string tipi)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.TahsilOkulParametresiGetir(Username, Password, sicilNo, tipi);
            DataTable dt = DS.Tables[0];
            if (DS.Tables.Count > 0)
            {
                DataRow a = dt.NewRow();
                a["okul_kodu"] = "-1";
                a["adi"] = "---Seçiniz---";
                dt.Rows.InsertAt(a, 0);
            }
            return dt;
        }

        public static DataTable GetFakulte(UserType KullaniciGrubu, string sicilNo, string tipi)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.TahsilFakulteParametresiGetir(Username, Password, sicilNo, tipi);
            DataTable dt = DS.Tables[0];
            if (DS.Tables.Count > 0)
            {
                DataRow a = dt.NewRow();
                a["fakulte_kodu"] = "-1";
                a["adi"] = "---Seçiniz---";
                dt.Rows.InsertAt(a, 0);
            }
            return dt;
        }

        public static DataTable GetBolum(UserType KullaniciGrubu, string sicilNo, string tipi)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.TahsilOkulBolumParametresiGetir(Username, Password, sicilNo, tipi);
            DataTable dt = DS.Tables[0];
            if (DS.Tables.Count > 0)
            {
                DataRow a = dt.NewRow();
                a["bolum_kodu"] = "-1";
                a["adi"] = "---Seçiniz---";
                dt.Rows.InsertAt(a, 0);
            }
            return dt;
        }

        public static int AddUpdateOgrenim(UserType KullaniciGrubu, string sicilNo, int idno, string okulKodu, string fakulteKodu, string bolumKodu, short girisYili, short cikisYili, string tahsilKodu)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            int AddUpdate = Client.OgrenimBilgileriEkleGuncelle(Username, Password, sicilNo, idno, okulKodu, fakulteKodu, bolumKodu, girisYili, cikisYili, tahsilKodu);
            return AddUpdate;
        }

        public static DataTable GetOgrenimBilgileri(UserType KullaniciGrubu, string sicilNo)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.OgrenimBilgileriGetir(Username, Password, sicilNo);
            DataTable dt = DS.Tables[0];
            return dt;
        }

        public static bool DeleteOgrenimBilgileri(UserType KullaniciGrubu, string SicilNo, int Idno)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient(KullaniciGrubu);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            Client.OgrenimBilgileriSil(Username, Password, SicilNo, Idno);
            bool returnDelete = true;
            return returnDelete;
            // poldy firmasının hazırladığı webservisindeki silme metodunda boş dataset döndüğü için her türlü durumda
            // default true atama yapıldı
        }

        //public static string GetKanGrubu()
        //{
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";
        //    Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = CreateClient();
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    string Sicil = Client..PersonelYeniGirisEkle(Username, Password, Infos);
        //    return Sicil;
        //}

        ///// <summary>
        /////  Kullanıcının Sicilini bulur.
        ///// </summary>
        ///// <param name="Sicil"></param>
        ///// <returns></returns>
        //public static bool CheckSicilDefault(string Sicil)
        //{
        //    string Cmd = "Select DEPT_FLAG,Sicil  from DT_WORKFLOW.DP_HR_USERS   where Sicil='" + Sicil + "'";
        //    string Result = ModelWorking.GetOnlyColumnSQL<string>("FrameworkConnection", Cmd, new List<DataAccessLayer.CustomParameterList>());
        //    if (Result == "1")
        //    {
        //        return true;
        //    }
        //    else
        //    {
        //        return false;
        //    }
        //}
        ///// <summary>
        ///// İzin Tiplerini Döndürür
        ///// </summary>
        ///// <returns></returns>
        //public static DataTable DtbLeaveType()
        //{
        //    Digiturk.Services.HRConnect.Service1 Client = new Digiturk.Services.HRConnect.Service1();
        //    //TODO TARIK
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    return Client.getVacationItem().Tables[0];
        //}
        ///// <summary>
        ///// Döviz Tiplerini Döndürür
        ///// </summary>
        ///// <returns></returns>
        //public static DataTable GetDoviz()
        //{
        //    Digiturk.Services.HRConnect.Service1 Client = new Digiturk.Services.HRConnect.Service1();
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    return Client.getDoviz().Tables[0];
        //}
        ///// <summary>
        ///// İzin Türlerini ve Kotalarını Döndürür Sicile Göre
        ///// </summary>
        ///// <param name="Sicil"></param>
        ///// <returns></returns>
        //public static DataTable DtbLeaveInformationGet(string Sicil)
        //{
        //    Sicil = Sicil.PadLeft(5, '0');
        //    Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(Sicil);
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    return Client.IzinBilgileriGetir(Username, Password, Sicil).Tables[0];
        //}
        ///// <summary>
        ///// İzin Bilgilerini Servise Yazar
        ///// </summary>
        ///// <param name="sicil"></param>
        ///// <param name="izinyili"></param>
        ///// <param name="baslangic_tarihi"></param>
        ///// <param name="bitis_tarihi"></param>
        ///// <param name="izin_tur_kodu"></param>
        ///// <param name="brut_toplam"></param>
        ///// <param name="hafta_tatili"></param>
        ///// <param name="bayram_tatili"></param>
        ///// <param name="net_izin"></param>
        ///// <returns></returns>
        //public static DataTable DtbLeaveInformationSet(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin)
        //{
        //    Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(sicil);
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    DataSet ds = Client.IzinBilgileriEkle(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin);
        //    if (ds.Tables.Count > 0)
        //    {
        //        return ds.Tables[0];
        //    }
        //    else
        //    {
        //        return new DataTable();
        //    }
        //}
        ///// <summary>
        ///// İzin formu iptal edildiğinde kullanılan izini geri eklemesi için kullanılır
        ///// </summary>
        ///// <param name="sicil"></param>
        ///// <param name="izinyili"></param>
        ///// <param name="baslangic_tarihi"></param>
        ///// <param name="bitis_tarihi"></param>
        ///// <param name="izin_tur_kodu"></param>
        //public static void DtbLeaveInformationCancelWFCorrection(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu)
        //{
        //    Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(sicil);
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    Client.IzinBilgileriSil(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu);
        //}
        ///// <summary>
        ///// İzin Bilgilerini Servise Yazar.
        ///// </summary>
        ///// <param name="Sicil"></param>
        ///// <param name="StartDate"></param>
        ///// <param name="EndDate"></param>
        ///// <param name="TurKodu"></param>
        ///// <param name="Burut"></param>
        ///// <param name="HaftaTatili"></param>
        ///// <param name="ResmiTatil"></param>
        ///// <param name="NetIzin"></param>
        //public static void ExecuteLeave(string Sicil, DateTime StartDate, DateTime EndDate, string TurKodu, long Burut, long HaftaTatili, long ResmiTatil, long NetIzin)
        //{
        //    Username = "WEBSERVICE";
        //    Password = "w17211s";

        //    Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(Sicil);
        //    Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //    Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //    System.Data.DataSet dts = Client.IzinBilgileriEkle(Username, Password, Sicil, StartDate.Year, StartDate, EndDate, TurKodu, Burut, HaftaTatili, ResmiTatil, NetIzin);
        //}
    }
}