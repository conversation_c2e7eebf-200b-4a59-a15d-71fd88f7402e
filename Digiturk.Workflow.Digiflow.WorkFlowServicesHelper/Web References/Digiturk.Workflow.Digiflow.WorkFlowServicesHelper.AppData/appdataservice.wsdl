<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="Test">
        <s:complexType />
      </s:element>
      <s:element name="TestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TestResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DBOraTest">
        <s:complexType />
      </s:element>
      <s:element name="DBOraTestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DBOraTestResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DBOraNetsTest">
        <s:complexType />
      </s:element>
      <s:element name="DBOraNetsTestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DBOraNetsTestResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DBSQLTest">
        <s:complexType />
      </s:element>
      <s:element name="DBSQLTestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DBSQLTestResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KategoriListesi_String">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KategoriListesi_StringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="KategoriListesi_StringResult" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UrunListesi_String">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UrunListesi_StringResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UrunListesi_StringResult" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KategoriListesi_String_test">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KategoriListesi_String_testResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="KategoriListesi_String_testResult" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UrunListesi_String_test">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UrunListesi_String_testResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="UrunListesi_String_testResult" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="prmData" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PurchesTransfer">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="WorkflowInstanceId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="MasterEntity">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
            <s:element minOccurs="0" maxOccurs="1" name="DetailEntitiy">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PurchesTransferResponse">
        <s:complexType />
      </s:element>
      <s:element name="SatinalmaFormunuAktar">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="FormID" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SatinalmaFormunuAktarResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="SatinalmaFormunuAktarResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DigiportTalepDurumDegistirFlow">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="prmDigiportFormID" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="prmMail" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DigiportTalepDurumDegistirFlowResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="DigiportTalepDurumDegistirFlowResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MailGonder">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="pFrom" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="pTo" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="pKonu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="pText" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MailGonderResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="MailGonderResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="TestSoapIn">
    <wsdl:part name="parameters" element="tns:Test" />
  </wsdl:message>
  <wsdl:message name="TestSoapOut">
    <wsdl:part name="parameters" element="tns:TestResponse" />
  </wsdl:message>
  <wsdl:message name="DBOraTestSoapIn">
    <wsdl:part name="parameters" element="tns:DBOraTest" />
  </wsdl:message>
  <wsdl:message name="DBOraTestSoapOut">
    <wsdl:part name="parameters" element="tns:DBOraTestResponse" />
  </wsdl:message>
  <wsdl:message name="DBOraNetsTestSoapIn">
    <wsdl:part name="parameters" element="tns:DBOraNetsTest" />
  </wsdl:message>
  <wsdl:message name="DBOraNetsTestSoapOut">
    <wsdl:part name="parameters" element="tns:DBOraNetsTestResponse" />
  </wsdl:message>
  <wsdl:message name="DBSQLTestSoapIn">
    <wsdl:part name="parameters" element="tns:DBSQLTest" />
  </wsdl:message>
  <wsdl:message name="DBSQLTestSoapOut">
    <wsdl:part name="parameters" element="tns:DBSQLTestResponse" />
  </wsdl:message>
  <wsdl:message name="KategoriListesi_StringSoapIn">
    <wsdl:part name="parameters" element="tns:KategoriListesi_String" />
  </wsdl:message>
  <wsdl:message name="KategoriListesi_StringSoapOut">
    <wsdl:part name="parameters" element="tns:KategoriListesi_StringResponse" />
  </wsdl:message>
  <wsdl:message name="UrunListesi_StringSoapIn">
    <wsdl:part name="parameters" element="tns:UrunListesi_String" />
  </wsdl:message>
  <wsdl:message name="UrunListesi_StringSoapOut">
    <wsdl:part name="parameters" element="tns:UrunListesi_StringResponse" />
  </wsdl:message>
  <wsdl:message name="KategoriListesi_String_testSoapIn">
    <wsdl:part name="parameters" element="tns:KategoriListesi_String_test" />
  </wsdl:message>
  <wsdl:message name="KategoriListesi_String_testSoapOut">
    <wsdl:part name="parameters" element="tns:KategoriListesi_String_testResponse" />
  </wsdl:message>
  <wsdl:message name="UrunListesi_String_testSoapIn">
    <wsdl:part name="parameters" element="tns:UrunListesi_String_test" />
  </wsdl:message>
  <wsdl:message name="UrunListesi_String_testSoapOut">
    <wsdl:part name="parameters" element="tns:UrunListesi_String_testResponse" />
  </wsdl:message>
  <wsdl:message name="PurchesTransferSoapIn">
    <wsdl:part name="parameters" element="tns:PurchesTransfer" />
  </wsdl:message>
  <wsdl:message name="PurchesTransferSoapOut">
    <wsdl:part name="parameters" element="tns:PurchesTransferResponse" />
  </wsdl:message>
  <wsdl:message name="SatinalmaFormunuAktarSoapIn">
    <wsdl:part name="parameters" element="tns:SatinalmaFormunuAktar" />
  </wsdl:message>
  <wsdl:message name="SatinalmaFormunuAktarSoapOut">
    <wsdl:part name="parameters" element="tns:SatinalmaFormunuAktarResponse" />
  </wsdl:message>
  <wsdl:message name="DigiportTalepDurumDegistirFlowSoapIn">
    <wsdl:part name="parameters" element="tns:DigiportTalepDurumDegistirFlow" />
  </wsdl:message>
  <wsdl:message name="DigiportTalepDurumDegistirFlowSoapOut">
    <wsdl:part name="parameters" element="tns:DigiportTalepDurumDegistirFlowResponse" />
  </wsdl:message>
  <wsdl:message name="MailGonderSoapIn">
    <wsdl:part name="parameters" element="tns:MailGonder" />
  </wsdl:message>
  <wsdl:message name="MailGonderSoapOut">
    <wsdl:part name="parameters" element="tns:MailGonderResponse" />
  </wsdl:message>
  <wsdl:portType name="AppDataServiceSoap">
    <wsdl:operation name="Test">
      <wsdl:input message="tns:TestSoapIn" />
      <wsdl:output message="tns:TestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DBOraTest">
      <wsdl:input message="tns:DBOraTestSoapIn" />
      <wsdl:output message="tns:DBOraTestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DBOraNetsTest">
      <wsdl:input message="tns:DBOraNetsTestSoapIn" />
      <wsdl:output message="tns:DBOraNetsTestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DBSQLTest">
      <wsdl:input message="tns:DBSQLTestSoapIn" />
      <wsdl:output message="tns:DBSQLTestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String">
      <wsdl:input message="tns:KategoriListesi_StringSoapIn" />
      <wsdl:output message="tns:KategoriListesi_StringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String">
      <wsdl:input message="tns:UrunListesi_StringSoapIn" />
      <wsdl:output message="tns:UrunListesi_StringSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String_test">
      <wsdl:input message="tns:KategoriListesi_String_testSoapIn" />
      <wsdl:output message="tns:KategoriListesi_String_testSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String_test">
      <wsdl:input message="tns:UrunListesi_String_testSoapIn" />
      <wsdl:output message="tns:UrunListesi_String_testSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PurchesTransfer">
      <wsdl:input message="tns:PurchesTransferSoapIn" />
      <wsdl:output message="tns:PurchesTransferSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SatinalmaFormunuAktar">
      <wsdl:input message="tns:SatinalmaFormunuAktarSoapIn" />
      <wsdl:output message="tns:SatinalmaFormunuAktarSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DigiportTalepDurumDegistirFlow">
      <wsdl:input message="tns:DigiportTalepDurumDegistirFlowSoapIn" />
      <wsdl:output message="tns:DigiportTalepDurumDegistirFlowSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="MailGonder">
      <wsdl:input message="tns:MailGonderSoapIn" />
      <wsdl:output message="tns:MailGonderSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="AppDataServiceSoap" type="tns:AppDataServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Test">
      <soap:operation soapAction="http://tempuri.org/Test" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBOraTest">
      <soap:operation soapAction="http://tempuri.org/DBOraTest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBOraNetsTest">
      <soap:operation soapAction="http://tempuri.org/DBOraNetsTest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBSQLTest">
      <soap:operation soapAction="http://tempuri.org/DBSQLTest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String">
      <soap:operation soapAction="http://tempuri.org/KategoriListesi_String" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String">
      <soap:operation soapAction="http://tempuri.org/UrunListesi_String" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String_test">
      <soap:operation soapAction="http://tempuri.org/KategoriListesi_String_test" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String_test">
      <soap:operation soapAction="http://tempuri.org/UrunListesi_String_test" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PurchesTransfer">
      <soap:operation soapAction="http://tempuri.org/PurchesTransfer" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SatinalmaFormunuAktar">
      <soap:operation soapAction="http://tempuri.org/SatinalmaFormunuAktar" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DigiportTalepDurumDegistirFlow">
      <soap:operation soapAction="http://tempuri.org/DigiportTalepDurumDegistirFlow" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MailGonder">
      <soap:operation soapAction="http://tempuri.org/MailGonder" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="AppDataServiceSoap12" type="tns:AppDataServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Test">
      <soap12:operation soapAction="http://tempuri.org/Test" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBOraTest">
      <soap12:operation soapAction="http://tempuri.org/DBOraTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBOraNetsTest">
      <soap12:operation soapAction="http://tempuri.org/DBOraNetsTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DBSQLTest">
      <soap12:operation soapAction="http://tempuri.org/DBSQLTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String">
      <soap12:operation soapAction="http://tempuri.org/KategoriListesi_String" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String">
      <soap12:operation soapAction="http://tempuri.org/UrunListesi_String" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KategoriListesi_String_test">
      <soap12:operation soapAction="http://tempuri.org/KategoriListesi_String_test" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UrunListesi_String_test">
      <soap12:operation soapAction="http://tempuri.org/UrunListesi_String_test" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PurchesTransfer">
      <soap12:operation soapAction="http://tempuri.org/PurchesTransfer" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SatinalmaFormunuAktar">
      <soap12:operation soapAction="http://tempuri.org/SatinalmaFormunuAktar" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DigiportTalepDurumDegistirFlow">
      <soap12:operation soapAction="http://tempuri.org/DigiportTalepDurumDegistirFlow" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="MailGonder">
      <soap12:operation soapAction="http://tempuri.org/MailGonder" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="AppDataService">
    <wsdl:port name="AppDataServiceSoap" binding="tns:AppDataServiceSoap">
      <soap:address location="http://dtl1iis4:3333/appdataservice.asmx" />
    </wsdl:port>
    <wsdl:port name="AppDataServiceSoap12" binding="tns:AppDataServiceSoap12">
      <soap12:address location="http://dtl1iis4:3333/appdataservice.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>