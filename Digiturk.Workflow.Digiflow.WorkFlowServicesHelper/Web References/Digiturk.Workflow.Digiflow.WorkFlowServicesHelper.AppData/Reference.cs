﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="AppDataServiceSoap", Namespace="http://tempuri.org/")]
    public partial class AppDataService : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback TestOperationCompleted;
        
        private System.Threading.SendOrPostCallback DBOraTestOperationCompleted;
        
        private System.Threading.SendOrPostCallback DBOraNetsTestOperationCompleted;
        
        private System.Threading.SendOrPostCallback DBSQLTestOperationCompleted;
        
        private System.Threading.SendOrPostCallback KategoriListesi_StringOperationCompleted;
        
        private System.Threading.SendOrPostCallback UrunListesi_StringOperationCompleted;
        
        private System.Threading.SendOrPostCallback KategoriListesi_String_testOperationCompleted;
        
        private System.Threading.SendOrPostCallback UrunListesi_String_testOperationCompleted;
        
        private System.Threading.SendOrPostCallback PurchesTransferOperationCompleted;
        
        private System.Threading.SendOrPostCallback SatinalmaFormunuAktarOperationCompleted;
        
        private System.Threading.SendOrPostCallback DigiportTalepDurumDegistirFlowOperationCompleted;
        
        private System.Threading.SendOrPostCallback MailGonderOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public AppDataService() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AppData_AppDataService;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event TestCompletedEventHandler TestCompleted;
        
        /// <remarks/>
        public event DBOraTestCompletedEventHandler DBOraTestCompleted;
        
        /// <remarks/>
        public event DBOraNetsTestCompletedEventHandler DBOraNetsTestCompleted;
        
        /// <remarks/>
        public event DBSQLTestCompletedEventHandler DBSQLTestCompleted;
        
        /// <remarks/>
        public event KategoriListesi_StringCompletedEventHandler KategoriListesi_StringCompleted;
        
        /// <remarks/>
        public event UrunListesi_StringCompletedEventHandler UrunListesi_StringCompleted;
        
        /// <remarks/>
        public event KategoriListesi_String_testCompletedEventHandler KategoriListesi_String_testCompleted;
        
        /// <remarks/>
        public event UrunListesi_String_testCompletedEventHandler UrunListesi_String_testCompleted;
        
        /// <remarks/>
        public event PurchesTransferCompletedEventHandler PurchesTransferCompleted;
        
        /// <remarks/>
        public event SatinalmaFormunuAktarCompletedEventHandler SatinalmaFormunuAktarCompleted;
        
        /// <remarks/>
        public event DigiportTalepDurumDegistirFlowCompletedEventHandler DigiportTalepDurumDegistirFlowCompleted;
        
        /// <remarks/>
        public event MailGonderCompletedEventHandler MailGonderCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Test", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string Test() {
            object[] results = this.Invoke("Test", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void TestAsync() {
            this.TestAsync(null);
        }
        
        /// <remarks/>
        public void TestAsync(object userState) {
            if ((this.TestOperationCompleted == null)) {
                this.TestOperationCompleted = new System.Threading.SendOrPostCallback(this.OnTestOperationCompleted);
            }
            this.InvokeAsync("Test", new object[0], this.TestOperationCompleted, userState);
        }
        
        private void OnTestOperationCompleted(object arg) {
            if ((this.TestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.TestCompleted(this, new TestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DBOraTest", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string DBOraTest() {
            object[] results = this.Invoke("DBOraTest", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void DBOraTestAsync() {
            this.DBOraTestAsync(null);
        }
        
        /// <remarks/>
        public void DBOraTestAsync(object userState) {
            if ((this.DBOraTestOperationCompleted == null)) {
                this.DBOraTestOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDBOraTestOperationCompleted);
            }
            this.InvokeAsync("DBOraTest", new object[0], this.DBOraTestOperationCompleted, userState);
        }
        
        private void OnDBOraTestOperationCompleted(object arg) {
            if ((this.DBOraTestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DBOraTestCompleted(this, new DBOraTestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DBOraNetsTest", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string DBOraNetsTest() {
            object[] results = this.Invoke("DBOraNetsTest", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void DBOraNetsTestAsync() {
            this.DBOraNetsTestAsync(null);
        }
        
        /// <remarks/>
        public void DBOraNetsTestAsync(object userState) {
            if ((this.DBOraNetsTestOperationCompleted == null)) {
                this.DBOraNetsTestOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDBOraNetsTestOperationCompleted);
            }
            this.InvokeAsync("DBOraNetsTest", new object[0], this.DBOraNetsTestOperationCompleted, userState);
        }
        
        private void OnDBOraNetsTestOperationCompleted(object arg) {
            if ((this.DBOraNetsTestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DBOraNetsTestCompleted(this, new DBOraNetsTestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DBSQLTest", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string DBSQLTest() {
            object[] results = this.Invoke("DBSQLTest", new object[0]);
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void DBSQLTestAsync() {
            this.DBSQLTestAsync(null);
        }
        
        /// <remarks/>
        public void DBSQLTestAsync(object userState) {
            if ((this.DBSQLTestOperationCompleted == null)) {
                this.DBSQLTestOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDBSQLTestOperationCompleted);
            }
            this.InvokeAsync("DBSQLTest", new object[0], this.DBSQLTestOperationCompleted, userState);
        }
        
        private void OnDBSQLTestOperationCompleted(object arg) {
            if ((this.DBSQLTestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DBSQLTestCompleted(this, new DBSQLTestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/KategoriListesi_String", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int KategoriListesi_String(ref string prmData) {
            object[] results = this.Invoke("KategoriListesi_String", new object[] {
                        prmData});
            prmData = ((string)(results[1]));
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void KategoriListesi_StringAsync(string prmData) {
            this.KategoriListesi_StringAsync(prmData, null);
        }
        
        /// <remarks/>
        public void KategoriListesi_StringAsync(string prmData, object userState) {
            if ((this.KategoriListesi_StringOperationCompleted == null)) {
                this.KategoriListesi_StringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnKategoriListesi_StringOperationCompleted);
            }
            this.InvokeAsync("KategoriListesi_String", new object[] {
                        prmData}, this.KategoriListesi_StringOperationCompleted, userState);
        }
        
        private void OnKategoriListesi_StringOperationCompleted(object arg) {
            if ((this.KategoriListesi_StringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.KategoriListesi_StringCompleted(this, new KategoriListesi_StringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UrunListesi_String", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int UrunListesi_String(ref string prmData) {
            object[] results = this.Invoke("UrunListesi_String", new object[] {
                        prmData});
            prmData = ((string)(results[1]));
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void UrunListesi_StringAsync(string prmData) {
            this.UrunListesi_StringAsync(prmData, null);
        }
        
        /// <remarks/>
        public void UrunListesi_StringAsync(string prmData, object userState) {
            if ((this.UrunListesi_StringOperationCompleted == null)) {
                this.UrunListesi_StringOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUrunListesi_StringOperationCompleted);
            }
            this.InvokeAsync("UrunListesi_String", new object[] {
                        prmData}, this.UrunListesi_StringOperationCompleted, userState);
        }
        
        private void OnUrunListesi_StringOperationCompleted(object arg) {
            if ((this.UrunListesi_StringCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UrunListesi_StringCompleted(this, new UrunListesi_StringCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/KategoriListesi_String_test", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int KategoriListesi_String_test(ref string prmData) {
            object[] results = this.Invoke("KategoriListesi_String_test", new object[] {
                        prmData});
            prmData = ((string)(results[1]));
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void KategoriListesi_String_testAsync(string prmData) {
            this.KategoriListesi_String_testAsync(prmData, null);
        }
        
        /// <remarks/>
        public void KategoriListesi_String_testAsync(string prmData, object userState) {
            if ((this.KategoriListesi_String_testOperationCompleted == null)) {
                this.KategoriListesi_String_testOperationCompleted = new System.Threading.SendOrPostCallback(this.OnKategoriListesi_String_testOperationCompleted);
            }
            this.InvokeAsync("KategoriListesi_String_test", new object[] {
                        prmData}, this.KategoriListesi_String_testOperationCompleted, userState);
        }
        
        private void OnKategoriListesi_String_testOperationCompleted(object arg) {
            if ((this.KategoriListesi_String_testCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.KategoriListesi_String_testCompleted(this, new KategoriListesi_String_testCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/UrunListesi_String_test", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int UrunListesi_String_test(ref string prmData) {
            object[] results = this.Invoke("UrunListesi_String_test", new object[] {
                        prmData});
            prmData = ((string)(results[1]));
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void UrunListesi_String_testAsync(string prmData) {
            this.UrunListesi_String_testAsync(prmData, null);
        }
        
        /// <remarks/>
        public void UrunListesi_String_testAsync(string prmData, object userState) {
            if ((this.UrunListesi_String_testOperationCompleted == null)) {
                this.UrunListesi_String_testOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUrunListesi_String_testOperationCompleted);
            }
            this.InvokeAsync("UrunListesi_String_test", new object[] {
                        prmData}, this.UrunListesi_String_testOperationCompleted, userState);
        }
        
        private void OnUrunListesi_String_testOperationCompleted(object arg) {
            if ((this.UrunListesi_String_testCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UrunListesi_String_testCompleted(this, new UrunListesi_String_testCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PurchesTransfer", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public void PurchesTransfer(long WorkflowInstanceId, System.Data.DataTable MasterEntity, System.Data.DataTable DetailEntitiy) {
            this.Invoke("PurchesTransfer", new object[] {
                        WorkflowInstanceId,
                        MasterEntity,
                        DetailEntitiy});
        }
        
        /// <remarks/>
        public void PurchesTransferAsync(long WorkflowInstanceId, System.Data.DataTable MasterEntity, System.Data.DataTable DetailEntitiy) {
            this.PurchesTransferAsync(WorkflowInstanceId, MasterEntity, DetailEntitiy, null);
        }
        
        /// <remarks/>
        public void PurchesTransferAsync(long WorkflowInstanceId, System.Data.DataTable MasterEntity, System.Data.DataTable DetailEntitiy, object userState) {
            if ((this.PurchesTransferOperationCompleted == null)) {
                this.PurchesTransferOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPurchesTransferOperationCompleted);
            }
            this.InvokeAsync("PurchesTransfer", new object[] {
                        WorkflowInstanceId,
                        MasterEntity,
                        DetailEntitiy}, this.PurchesTransferOperationCompleted, userState);
        }
        
        private void OnPurchesTransferOperationCompleted(object arg) {
            if ((this.PurchesTransferCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PurchesTransferCompleted(this, new System.ComponentModel.AsyncCompletedEventArgs(invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/SatinalmaFormunuAktar", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int SatinalmaFormunuAktar(int FormID) {
            object[] results = this.Invoke("SatinalmaFormunuAktar", new object[] {
                        FormID});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void SatinalmaFormunuAktarAsync(int FormID) {
            this.SatinalmaFormunuAktarAsync(FormID, null);
        }
        
        /// <remarks/>
        public void SatinalmaFormunuAktarAsync(int FormID, object userState) {
            if ((this.SatinalmaFormunuAktarOperationCompleted == null)) {
                this.SatinalmaFormunuAktarOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSatinalmaFormunuAktarOperationCompleted);
            }
            this.InvokeAsync("SatinalmaFormunuAktar", new object[] {
                        FormID}, this.SatinalmaFormunuAktarOperationCompleted, userState);
        }
        
        private void OnSatinalmaFormunuAktarOperationCompleted(object arg) {
            if ((this.SatinalmaFormunuAktarCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SatinalmaFormunuAktarCompleted(this, new SatinalmaFormunuAktarCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/DigiportTalepDurumDegistirFlow", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int DigiportTalepDurumDegistirFlow(string prmDigiportFormID, string prmMail) {
            object[] results = this.Invoke("DigiportTalepDurumDegistirFlow", new object[] {
                        prmDigiportFormID,
                        prmMail});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void DigiportTalepDurumDegistirFlowAsync(string prmDigiportFormID, string prmMail) {
            this.DigiportTalepDurumDegistirFlowAsync(prmDigiportFormID, prmMail, null);
        }
        
        /// <remarks/>
        public void DigiportTalepDurumDegistirFlowAsync(string prmDigiportFormID, string prmMail, object userState) {
            if ((this.DigiportTalepDurumDegistirFlowOperationCompleted == null)) {
                this.DigiportTalepDurumDegistirFlowOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDigiportTalepDurumDegistirFlowOperationCompleted);
            }
            this.InvokeAsync("DigiportTalepDurumDegistirFlow", new object[] {
                        prmDigiportFormID,
                        prmMail}, this.DigiportTalepDurumDegistirFlowOperationCompleted, userState);
        }
        
        private void OnDigiportTalepDurumDegistirFlowOperationCompleted(object arg) {
            if ((this.DigiportTalepDurumDegistirFlowCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DigiportTalepDurumDegistirFlowCompleted(this, new DigiportTalepDurumDegistirFlowCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/MailGonder", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public int MailGonder(string pFrom, string pTo, string pKonu, string pText) {
            object[] results = this.Invoke("MailGonder", new object[] {
                        pFrom,
                        pTo,
                        pKonu,
                        pText});
            return ((int)(results[0]));
        }
        
        /// <remarks/>
        public void MailGonderAsync(string pFrom, string pTo, string pKonu, string pText) {
            this.MailGonderAsync(pFrom, pTo, pKonu, pText, null);
        }
        
        /// <remarks/>
        public void MailGonderAsync(string pFrom, string pTo, string pKonu, string pText, object userState) {
            if ((this.MailGonderOperationCompleted == null)) {
                this.MailGonderOperationCompleted = new System.Threading.SendOrPostCallback(this.OnMailGonderOperationCompleted);
            }
            this.InvokeAsync("MailGonder", new object[] {
                        pFrom,
                        pTo,
                        pKonu,
                        pText}, this.MailGonderOperationCompleted, userState);
        }
        
        private void OnMailGonderOperationCompleted(object arg) {
            if ((this.MailGonderCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.MailGonderCompleted(this, new MailGonderCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void TestCompletedEventHandler(object sender, TestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class TestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal TestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void DBOraTestCompletedEventHandler(object sender, DBOraTestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DBOraTestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DBOraTestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void DBOraNetsTestCompletedEventHandler(object sender, DBOraNetsTestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DBOraNetsTestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DBOraNetsTestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void DBSQLTestCompletedEventHandler(object sender, DBSQLTestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DBSQLTestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DBSQLTestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void KategoriListesi_StringCompletedEventHandler(object sender, KategoriListesi_StringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class KategoriListesi_StringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal KategoriListesi_StringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public string prmData {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void UrunListesi_StringCompletedEventHandler(object sender, UrunListesi_StringCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UrunListesi_StringCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UrunListesi_StringCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public string prmData {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void KategoriListesi_String_testCompletedEventHandler(object sender, KategoriListesi_String_testCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class KategoriListesi_String_testCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal KategoriListesi_String_testCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public string prmData {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void UrunListesi_String_testCompletedEventHandler(object sender, UrunListesi_String_testCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UrunListesi_String_testCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UrunListesi_String_testCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
        
        /// <remarks/>
        public string prmData {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[1]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PurchesTransferCompletedEventHandler(object sender, System.ComponentModel.AsyncCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SatinalmaFormunuAktarCompletedEventHandler(object sender, SatinalmaFormunuAktarCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SatinalmaFormunuAktarCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SatinalmaFormunuAktarCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void DigiportTalepDurumDegistirFlowCompletedEventHandler(object sender, DigiportTalepDurumDegistirFlowCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DigiportTalepDurumDegistirFlowCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DigiportTalepDurumDegistirFlowCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void MailGonderCompletedEventHandler(object sender, MailGonderCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class MailGonderCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal MailGonderCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public int Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((int)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591