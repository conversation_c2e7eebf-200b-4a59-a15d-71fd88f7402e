﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.SosyalMedyaService {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="BasicHttpBinding_IPersonalDataProtectionBS", Namespace="http://tempuri.org/")]
    public partial class PersonalDataProtectionBS : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback PingOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateTokenOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetAccountPermissionsOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetPermissionTextOperationCompleted;
        
        private System.Threading.SendOrPostCallback IsPermissionExistOperationCompleted;
        
        private System.Threading.SendOrPostCallback SaveAccountPermissionsOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetAccountPermissionsV2OperationCompleted;
        
        private System.Threading.SendOrPostCallback SaveAccountPermissionsV2OperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public PersonalDataProtectionBS() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_SosyalMedyaService_PersonalDataProtectionBS;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event PingCompletedEventHandler PingCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateCompletedEventHandler SystemAuthenticateCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureCompletedEventHandler SystemAuthenticateByCultureCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateWithExpireCompletedEventHandler SystemAuthenticateWithExpireCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureWithExpireCompletedEventHandler SystemAuthenticateByCultureWithExpireCompleted;
        
        /// <remarks/>
        public event SystemValidateTokenCompletedEventHandler SystemValidateTokenCompleted;
        
        /// <remarks/>
        public event SystemValidateCultureCompletedEventHandler SystemValidateCultureCompleted;
        
        /// <remarks/>
        public event GetAccountPermissionsCompletedEventHandler GetAccountPermissionsCompleted;
        
        /// <remarks/>
        public event GetPermissionTextCompletedEventHandler GetPermissionTextCompleted;
        
        /// <remarks/>
        public event IsPermissionExistCompletedEventHandler IsPermissionExistCompleted;
        
        /// <remarks/>
        public event SaveAccountPermissionsCompletedEventHandler SaveAccountPermissionsCompleted;
        
        /// <remarks/>
        public event GetAccountPermissionsV2CompletedEventHandler GetAccountPermissionsV2Completed;
        
        /// <remarks/>
        public event SaveAccountPermissionsV2CompletedEventHandler SaveAccountPermissionsV2Completed;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/Ping", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public long Ping() {
            object[] results = this.Invoke("Ping", new object[0]);
            return ((long)(results[0]));
        }
        
        /// <remarks/>
        public void PingAsync() {
            this.PingAsync(null);
        }
        
        /// <remarks/>
        public void PingAsync(object userState) {
            if ((this.PingOperationCompleted == null)) {
                this.PingOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPingOperationCompleted);
            }
            this.InvokeAsync("Ping", new object[0], this.PingOperationCompleted, userState);
        }
        
        private void OnPingOperationCompleted(object arg) {
            if ((this.PingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PingCompleted(this, new PingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticate", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticate(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateOperationCompleted == null)) {
                this.SystemAuthenticateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateOperationCompleted(object arg) {
            if ((this.SystemAuthenticateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateCompleted(this, new SystemAuthenticateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticateByCulture(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureOperationCompleted == null)) {
                this.SystemAuthenticateByCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureCompleted(this, new SystemAuthenticateByCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateWithExpire(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateWithExpireAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateWithExpireCompleted(this, new SystemAuthenticateWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCultureWithExpir" +
            "e", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateByCultureWithExpire(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureWithExpireAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateByCultureWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureWithExpireCompleted(this, new SystemAuthenticateByCultureWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemValidateToken", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateToken(string token) {
            object[] results = this.Invoke("SystemValidateToken", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token) {
            this.SystemValidateTokenAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token, object userState) {
            if ((this.SystemValidateTokenOperationCompleted == null)) {
                this.SystemValidateTokenOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateTokenOperationCompleted);
            }
            this.InvokeAsync("SystemValidateToken", new object[] {
                        token}, this.SystemValidateTokenOperationCompleted, userState);
        }
        
        private void OnSystemValidateTokenOperationCompleted(object arg) {
            if ((this.SystemValidateTokenCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateTokenCompleted(this, new SystemValidateTokenCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SystemValidateCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateCulture(string token) {
            object[] results = this.Invoke("SystemValidateCulture", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token) {
            this.SystemValidateCultureAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token, object userState) {
            if ((this.SystemValidateCultureOperationCompleted == null)) {
                this.SystemValidateCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateCultureOperationCompleted);
            }
            this.InvokeAsync("SystemValidateCulture", new object[] {
                        token}, this.SystemValidateCultureOperationCompleted, userState);
        }
        
        private void OnSystemValidateCultureOperationCompleted(object arg) {
            if ((this.SystemValidateCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateCultureCompleted(this, new SystemValidateCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissions", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfGetAccountPermissionsResponse GetAccountPermissions(GetAccountPermissionsRequest request, string token) {
            object[] results = this.Invoke("GetAccountPermissions", new object[] {
                        request,
                        token});
            return ((ResponseWrapperOfGetAccountPermissionsResponse)(results[0]));
        }
        
        /// <remarks/>
        public void GetAccountPermissionsAsync(GetAccountPermissionsRequest request, string token) {
            this.GetAccountPermissionsAsync(request, token, null);
        }
        
        /// <remarks/>
        public void GetAccountPermissionsAsync(GetAccountPermissionsRequest request, string token, object userState) {
            if ((this.GetAccountPermissionsOperationCompleted == null)) {
                this.GetAccountPermissionsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetAccountPermissionsOperationCompleted);
            }
            this.InvokeAsync("GetAccountPermissions", new object[] {
                        request,
                        token}, this.GetAccountPermissionsOperationCompleted, userState);
        }
        
        private void OnGetAccountPermissionsOperationCompleted(object arg) {
            if ((this.GetAccountPermissionsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetAccountPermissionsCompleted(this, new GetAccountPermissionsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/GetPermissionText", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfGetPermissionTextResponse GetPermissionText(GetPermissionTextRequest request, string token) {
            object[] results = this.Invoke("GetPermissionText", new object[] {
                        request,
                        token});
            return ((ResponseWrapperOfGetPermissionTextResponse)(results[0]));
        }
        
        /// <remarks/>
        public void GetPermissionTextAsync(GetPermissionTextRequest request, string token) {
            this.GetPermissionTextAsync(request, token, null);
        }
        
        /// <remarks/>
        public void GetPermissionTextAsync(GetPermissionTextRequest request, string token, object userState) {
            if ((this.GetPermissionTextOperationCompleted == null)) {
                this.GetPermissionTextOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetPermissionTextOperationCompleted);
            }
            this.InvokeAsync("GetPermissionText", new object[] {
                        request,
                        token}, this.GetPermissionTextOperationCompleted, userState);
        }
        
        private void OnGetPermissionTextOperationCompleted(object arg) {
            if ((this.GetPermissionTextCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetPermissionTextCompleted(this, new GetPermissionTextCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/IsPermissionExist", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfIsPermissionExistResponse IsPermissionExist(IsPermissionExistRequest request, string token) {
            object[] results = this.Invoke("IsPermissionExist", new object[] {
                        request,
                        token});
            return ((ResponseWrapperOfIsPermissionExistResponse)(results[0]));
        }
        
        /// <remarks/>
        public void IsPermissionExistAsync(IsPermissionExistRequest request, string token) {
            this.IsPermissionExistAsync(request, token, null);
        }
        
        /// <remarks/>
        public void IsPermissionExistAsync(IsPermissionExistRequest request, string token, object userState) {
            if ((this.IsPermissionExistOperationCompleted == null)) {
                this.IsPermissionExistOperationCompleted = new System.Threading.SendOrPostCallback(this.OnIsPermissionExistOperationCompleted);
            }
            this.InvokeAsync("IsPermissionExist", new object[] {
                        request,
                        token}, this.IsPermissionExistOperationCompleted, userState);
        }
        
        private void OnIsPermissionExistOperationCompleted(object arg) {
            if ((this.IsPermissionExistCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.IsPermissionExistCompleted(this, new IsPermissionExistCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissions", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfSaveAccountPermissionsResponse SaveAccountPermissions(SaveAccountPermissionsRequest request, string token) {
            object[] results = this.Invoke("SaveAccountPermissions", new object[] {
                        request,
                        token});
            return ((ResponseWrapperOfSaveAccountPermissionsResponse)(results[0]));
        }
        
        /// <remarks/>
        public void SaveAccountPermissionsAsync(SaveAccountPermissionsRequest request, string token) {
            this.SaveAccountPermissionsAsync(request, token, null);
        }
        
        /// <remarks/>
        public void SaveAccountPermissionsAsync(SaveAccountPermissionsRequest request, string token, object userState) {
            if ((this.SaveAccountPermissionsOperationCompleted == null)) {
                this.SaveAccountPermissionsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSaveAccountPermissionsOperationCompleted);
            }
            this.InvokeAsync("SaveAccountPermissions", new object[] {
                        request,
                        token}, this.SaveAccountPermissionsOperationCompleted, userState);
        }
        
        private void OnSaveAccountPermissionsOperationCompleted(object arg) {
            if ((this.SaveAccountPermissionsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SaveAccountPermissionsCompleted(this, new SaveAccountPermissionsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissionsV2", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfGetAccountPermissionsResponseV2 GetAccountPermissionsV2(GetAccountPermissionsRequest request, string token, string sessionKey) {
            object[] results = this.Invoke("GetAccountPermissionsV2", new object[] {
                        request,
                        token,
                        sessionKey});
            return ((ResponseWrapperOfGetAccountPermissionsResponseV2)(results[0]));
        }
        
        /// <remarks/>
        public void GetAccountPermissionsV2Async(GetAccountPermissionsRequest request, string token, string sessionKey) {
            this.GetAccountPermissionsV2Async(request, token, sessionKey, null);
        }
        
        /// <remarks/>
        public void GetAccountPermissionsV2Async(GetAccountPermissionsRequest request, string token, string sessionKey, object userState) {
            if ((this.GetAccountPermissionsV2OperationCompleted == null)) {
                this.GetAccountPermissionsV2OperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetAccountPermissionsV2OperationCompleted);
            }
            this.InvokeAsync("GetAccountPermissionsV2", new object[] {
                        request,
                        token,
                        sessionKey}, this.GetAccountPermissionsV2OperationCompleted, userState);
        }
        
        private void OnGetAccountPermissionsV2OperationCompleted(object arg) {
            if ((this.GetAccountPermissionsV2Completed != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetAccountPermissionsV2Completed(this, new GetAccountPermissionsV2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissionsV2", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseWrapperOfSaveAccountPermissionsResponse SaveAccountPermissionsV2(SaveAccountPermissionsRequestV2 request, string token, string sessionKey) {
            object[] results = this.Invoke("SaveAccountPermissionsV2", new object[] {
                        request,
                        token,
                        sessionKey});
            return ((ResponseWrapperOfSaveAccountPermissionsResponse)(results[0]));
        }
        
        /// <remarks/>
        public void SaveAccountPermissionsV2Async(SaveAccountPermissionsRequestV2 request, string token, string sessionKey) {
            this.SaveAccountPermissionsV2Async(request, token, sessionKey, null);
        }
        
        /// <remarks/>
        public void SaveAccountPermissionsV2Async(SaveAccountPermissionsRequestV2 request, string token, string sessionKey, object userState) {
            if ((this.SaveAccountPermissionsV2OperationCompleted == null)) {
                this.SaveAccountPermissionsV2OperationCompleted = new System.Threading.SendOrPostCallback(this.OnSaveAccountPermissionsV2OperationCompleted);
            }
            this.InvokeAsync("SaveAccountPermissionsV2", new object[] {
                        request,
                        token,
                        sessionKey}, this.SaveAccountPermissionsV2OperationCompleted, userState);
        }
        
        private void OnSaveAccountPermissionsV2OperationCompleted(object arg) {
            if ((this.SaveAccountPermissionsV2Completed != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SaveAccountPermissionsV2Completed(this, new SaveAccountPermissionsV2CompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class TokenData {
        
        private string tokenField;
        
        private System.DateTime expireAtField;
        
        /// <remarks/>
        public string Token {
            get {
                return this.tokenField;
            }
            set {
                this.tokenField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ExpireAt {
            get {
                return this.expireAtField;
            }
            set {
                this.expireAtField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseWrapperOfGetAccountPermissionsResponseV2 {
        
        private bool isSuccessField;
        
        private string[] messagesField;
        
        private GetAccountPermissionsResponseV2 dataField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string[] Messages {
            get {
                return this.messagesField;
            }
            set {
                this.messagesField = value;
            }
        }
        
        /// <remarks/>
        public GetAccountPermissionsResponseV2 Data {
            get {
                return this.dataField;
            }
            set {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAccountPermissionsResponseV2 : GetAccountPermissionsResponse {
        
        private ContactMediumPermission[] contactMediumPermissionsField;
        
        /// <remarks/>
        public ContactMediumPermission[] ContactMediumPermissions {
            get {
                return this.contactMediumPermissionsField;
            }
            set {
                this.contactMediumPermissionsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ContactMediumPermission {
        
        private long contactMediumIdField;
        
        private string recipientField;
        
        private bool isConfirmedField;
        
        private bool isRegisteredField;
        
        private IYSContactChannelType contactTypeField;
        
        private string confirmationStatusOnIYSField;
        
        /// <remarks/>
        public long ContactMediumId {
            get {
                return this.contactMediumIdField;
            }
            set {
                this.contactMediumIdField = value;
            }
        }
        
        /// <remarks/>
        public string Recipient {
            get {
                return this.recipientField;
            }
            set {
                this.recipientField = value;
            }
        }
        
        /// <remarks/>
        public bool IsConfirmed {
            get {
                return this.isConfirmedField;
            }
            set {
                this.isConfirmedField = value;
            }
        }
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        public IYSContactChannelType ContactType {
            get {
                return this.contactTypeField;
            }
            set {
                this.contactTypeField = value;
            }
        }
        
        /// <remarks/>
        public string ConfirmationStatusOnIYS {
            get {
                return this.confirmationStatusOnIYSField;
            }
            set {
                this.confirmationStatusOnIYSField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public enum IYSContactChannelType {
        
        /// <remarks/>
        ARAMA,
        
        /// <remarks/>
        EPOSTA,
        
        /// <remarks/>
        MESAJ,
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GetAccountPermissionsResponseV2))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAccountPermissionsResponse {
        
        private Item[] itemsField;
        
        /// <remarks/>
        public Item[] Items {
            get {
                return this.itemsField;
            }
            set {
                this.itemsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Item {
        
        private DataPermissionType permissionTypeField;
        
        private System.Nullable<long> billAccountPreferenceIdField;
        
        private System.Nullable<long> documentIdField;
        
        private System.Nullable<long> confirmedDocumentIdField;
        
        private string contentField;
        
        private bool isConfirmedField;
        
        private bool isLatestDocumentField;
        
        private System.Nullable<long> applicationIdField;
        
        private System.Nullable<long> clientIdField;
        
        private System.Nullable<long> channelIdField;
        
        private System.Nullable<System.DateTime> validFromField;
        
        private System.Nullable<System.DateTime> validThruField;
        
        private System.Nullable<System.DateTime> creationDateField;
        
        private string applicationNameField;
        
        private string clientNameField;
        
        private string channelNameField;
        
        private string createdByField;
        
        /// <remarks/>
        public DataPermissionType PermissionType {
            get {
                return this.permissionTypeField;
            }
            set {
                this.permissionTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> BillAccountPreferenceId {
            get {
                return this.billAccountPreferenceIdField;
            }
            set {
                this.billAccountPreferenceIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ConfirmedDocumentId {
            get {
                return this.confirmedDocumentIdField;
            }
            set {
                this.confirmedDocumentIdField = value;
            }
        }
        
        /// <remarks/>
        public string Content {
            get {
                return this.contentField;
            }
            set {
                this.contentField = value;
            }
        }
        
        /// <remarks/>
        public bool IsConfirmed {
            get {
                return this.isConfirmedField;
            }
            set {
                this.isConfirmedField = value;
            }
        }
        
        /// <remarks/>
        public bool IsLatestDocument {
            get {
                return this.isLatestDocumentField;
            }
            set {
                this.isLatestDocumentField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ApplicationId {
            get {
                return this.applicationIdField;
            }
            set {
                this.applicationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ClientId {
            get {
                return this.clientIdField;
            }
            set {
                this.clientIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ChannelId {
            get {
                return this.channelIdField;
            }
            set {
                this.channelIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationName {
            get {
                return this.applicationNameField;
            }
            set {
                this.applicationNameField = value;
            }
        }
        
        /// <remarks/>
        public string ClientName {
            get {
                return this.clientNameField;
            }
            set {
                this.clientNameField = value;
            }
        }
        
        /// <remarks/>
        public string ChannelName {
            get {
                return this.channelNameField;
            }
            set {
                this.channelNameField = value;
            }
        }
        
        /// <remarks/>
        public string CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public enum DataPermissionType {
        
        /// <remarks/>
        VERI_ISLEME_IZNI,
        
        /// <remarks/>
        VERI_PAYLASMA_IZNI,
        
        /// <remarks/>
        ILETISIM_IZNI,
        
        /// <remarks/>
        COOKIE_IZNI,
        
        /// <remarks/>
        AYDINLATMA_METNI,
        
        /// <remarks/>
        MESAFELI_SATIS,
        
        /// <remarks/>
        HASSAS_VERI_IZNI,
        
        /// <remarks/>
        UYELIK_SOZLESME_IZNI,
        
        /// <remarks/>
        KVKK_IZNI,
        
        /// <remarks/>
        TELIF_IZNI,
        
        /// <remarks/>
        IZIN_BILGILENDIRME_METNI,
        
        /// <remarks/>
        SOSYAL_MEDYA_METNI,
        
        /// <remarks/>
        TRAFIK_VERI_IZNI,
        
        /// <remarks/>
        MESAFELI_ON_BILGILENDIRME_SATIS,
        
        /// <remarks/>
        CIKAR_CATISMASI_METNI,
        
        /// <remarks/>
        MESAFELI_SATIS_IZNI,
        
        /// <remarks/>
        ISP_POTANSIYEL_AYDINLATMA_METNI,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SaveAccountPermissionsResponse {
        
        private bool isRecordedField;
        
        private long recordIdField;
        
        /// <remarks/>
        public bool IsRecorded {
            get {
                return this.isRecordedField;
            }
            set {
                this.isRecordedField = value;
            }
        }
        
        /// <remarks/>
        public long RecordId {
            get {
                return this.recordIdField;
            }
            set {
                this.recordIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseWrapperOfSaveAccountPermissionsResponse {
        
        private bool isSuccessField;
        
        private string[] messagesField;
        
        private SaveAccountPermissionsResponse dataField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string[] Messages {
            get {
                return this.messagesField;
            }
            set {
                this.messagesField = value;
            }
        }
        
        /// <remarks/>
        public SaveAccountPermissionsResponse Data {
            get {
                return this.dataField;
            }
            set {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ContactMediumPermissionSave {
        
        private long contactMediumIdField;
        
        private bool isConfirmedField;
        
        private IYSContactChannelType contactTypeField;
        
        /// <remarks/>
        public long ContactMediumId {
            get {
                return this.contactMediumIdField;
            }
            set {
                this.contactMediumIdField = value;
            }
        }
        
        /// <remarks/>
        public bool IsConfirmed {
            get {
                return this.isConfirmedField;
            }
            set {
                this.isConfirmedField = value;
            }
        }
        
        /// <remarks/>
        public IYSContactChannelType ContactType {
            get {
                return this.contactTypeField;
            }
            set {
                this.contactTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class KeyValueAttribute {
        
        private string nameField;
        
        private string valueField;
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SaveAccountPermissionsRequestV2))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SaveAccountPermissionsRequest {
        
        private long partyRoleIdField;
        
        private DataPermissionType permissionTypeField;
        
        private bool isConfirmedField;
        
        private long documentIdField;
        
        private KeyValueAttribute[] keyValueListField;
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public DataPermissionType PermissionType {
            get {
                return this.permissionTypeField;
            }
            set {
                this.permissionTypeField = value;
            }
        }
        
        /// <remarks/>
        public bool IsConfirmed {
            get {
                return this.isConfirmedField;
            }
            set {
                this.isConfirmedField = value;
            }
        }
        
        /// <remarks/>
        public long DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        public KeyValueAttribute[] KeyValueList {
            get {
                return this.keyValueListField;
            }
            set {
                this.keyValueListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SaveAccountPermissionsRequestV2 : SaveAccountPermissionsRequest {
        
        private ContactMediumPermissionSave[] contactMediumPermissionsField;
        
        /// <remarks/>
        public ContactMediumPermissionSave[] ContactMediumPermissions {
            get {
                return this.contactMediumPermissionsField;
            }
            set {
                this.contactMediumPermissionsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IsPermissionExistResponse {
        
        private bool isExistField;
        
        private System.Nullable<bool> isConfirmedField;
        
        /// <remarks/>
        public bool IsExist {
            get {
                return this.isExistField;
            }
            set {
                this.isExistField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<bool> IsConfirmed {
            get {
                return this.isConfirmedField;
            }
            set {
                this.isConfirmedField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseWrapperOfIsPermissionExistResponse {
        
        private bool isSuccessField;
        
        private string[] messagesField;
        
        private IsPermissionExistResponse dataField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string[] Messages {
            get {
                return this.messagesField;
            }
            set {
                this.messagesField = value;
            }
        }
        
        /// <remarks/>
        public IsPermissionExistResponse Data {
            get {
                return this.dataField;
            }
            set {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IsPermissionExistRequest {
        
        private long partyRoleIdField;
        
        private System.Nullable<long> documentIdField;
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PermissionItem {
        
        private DataPermissionType permissionTypeField;
        
        private string nameField;
        
        private bool isExistField;
        
        private long documentIdField;
        
        private string contentField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        /// <remarks/>
        public DataPermissionType PermissionType {
            get {
                return this.permissionTypeField;
            }
            set {
                this.permissionTypeField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public bool IsExist {
            get {
                return this.isExistField;
            }
            set {
                this.isExistField = value;
            }
        }
        
        /// <remarks/>
        public long DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        public string Content {
            get {
                return this.contentField;
            }
            set {
                this.contentField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GetPermissionTextResponse {
        
        private PermissionItem[] resultField;
        
        /// <remarks/>
        public PermissionItem[] Result {
            get {
                return this.resultField;
            }
            set {
                this.resultField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseWrapperOfGetPermissionTextResponse {
        
        private bool isSuccessField;
        
        private string[] messagesField;
        
        private GetPermissionTextResponse dataField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string[] Messages {
            get {
                return this.messagesField;
            }
            set {
                this.messagesField = value;
            }
        }
        
        /// <remarks/>
        public GetPermissionTextResponse Data {
            get {
                return this.dataField;
            }
            set {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GetPermissionTextRequest {
        
        private string clientNameField;
        
        private DataPermissionType[] permissionTypesField;
        
        private System.Nullable<long> documentIdField;
        
        /// <remarks/>
        public string ClientName {
            get {
                return this.clientNameField;
            }
            set {
                this.clientNameField = value;
            }
        }
        
        /// <remarks/>
        public DataPermissionType[] PermissionTypes {
            get {
                return this.permissionTypesField;
            }
            set {
                this.permissionTypesField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseWrapperOfGetAccountPermissionsResponse {
        
        private bool isSuccessField;
        
        private string[] messagesField;
        
        private GetAccountPermissionsResponse dataField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string[] Messages {
            get {
                return this.messagesField;
            }
            set {
                this.messagesField = value;
            }
        }
        
        /// <remarks/>
        public GetAccountPermissionsResponse Data {
            get {
                return this.dataField;
            }
            set {
                this.dataField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GetAccountPermissionsRequest {
        
        private long partyRoleIdField;
        
        private DataPermissionType[] permissionTypesField;
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public DataPermissionType[] PermissionTypes {
            get {
                return this.permissionTypesField;
            }
            set {
                this.permissionTypesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PingCompletedEventHandler(object sender, PingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public long Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((long)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateCompletedEventHandler(object sender, SystemAuthenticateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureCompletedEventHandler(object sender, SystemAuthenticateByCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateWithExpireCompletedEventHandler(object sender, SystemAuthenticateWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureWithExpireCompletedEventHandler(object sender, SystemAuthenticateByCultureWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateTokenCompletedEventHandler(object sender, SystemValidateTokenCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateTokenCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateTokenCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateCultureCompletedEventHandler(object sender, SystemValidateCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetAccountPermissionsCompletedEventHandler(object sender, GetAccountPermissionsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetAccountPermissionsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetAccountPermissionsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfGetAccountPermissionsResponse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfGetAccountPermissionsResponse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetPermissionTextCompletedEventHandler(object sender, GetPermissionTextCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetPermissionTextCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetPermissionTextCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfGetPermissionTextResponse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfGetPermissionTextResponse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void IsPermissionExistCompletedEventHandler(object sender, IsPermissionExistCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class IsPermissionExistCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal IsPermissionExistCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfIsPermissionExistResponse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfIsPermissionExistResponse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SaveAccountPermissionsCompletedEventHandler(object sender, SaveAccountPermissionsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SaveAccountPermissionsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SaveAccountPermissionsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfSaveAccountPermissionsResponse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfSaveAccountPermissionsResponse)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetAccountPermissionsV2CompletedEventHandler(object sender, GetAccountPermissionsV2CompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetAccountPermissionsV2CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetAccountPermissionsV2CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfGetAccountPermissionsResponseV2 Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfGetAccountPermissionsResponseV2)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SaveAccountPermissionsV2CompletedEventHandler(object sender, SaveAccountPermissionsV2CompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SaveAccountPermissionsV2CompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SaveAccountPermissionsV2CompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseWrapperOfSaveAccountPermissionsResponse Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseWrapperOfSaveAccountPermissionsResponse)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591