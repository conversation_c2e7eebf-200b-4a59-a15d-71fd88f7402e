﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.Services.HRConnect {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="Service1Soap", Namespace="http://tempuri.org/")]
    public partial class Service1 : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback getVacationItemOperationCompleted;
        
        private System.Threading.SendOrPostCallback getVacationItemAllOperationCompleted;
        
        private System.Threading.SendOrPostCallback getVacationItemTestOperationCompleted;
        
        private System.Threading.SendOrPostCallback getVacationItemLimitTestOperationCompleted;
        
        private System.Threading.SendOrPostCallback getVacationItemLimitOperationCompleted;
        
        private System.Threading.SendOrPostCallback getDovizOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public Service1() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRConnect_Service1;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event getVacationItemCompletedEventHandler getVacationItemCompleted;
        
        /// <remarks/>
        public event getVacationItemAllCompletedEventHandler getVacationItemAllCompleted;
        
        /// <remarks/>
        public event getVacationItemTestCompletedEventHandler getVacationItemTestCompleted;
        
        /// <remarks/>
        public event getVacationItemLimitTestCompletedEventHandler getVacationItemLimitTestCompleted;
        
        /// <remarks/>
        public event getVacationItemLimitCompletedEventHandler getVacationItemLimitCompleted;
        
        /// <remarks/>
        public event getDovizCompletedEventHandler getDovizCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getVacationItem", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getVacationItem() {
            object[] results = this.Invoke("getVacationItem", new object[0]);
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getVacationItemAsync() {
            this.getVacationItemAsync(null);
        }
        
        /// <remarks/>
        public void getVacationItemAsync(object userState) {
            if ((this.getVacationItemOperationCompleted == null)) {
                this.getVacationItemOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetVacationItemOperationCompleted);
            }
            this.InvokeAsync("getVacationItem", new object[0], this.getVacationItemOperationCompleted, userState);
        }
        
        private void OngetVacationItemOperationCompleted(object arg) {
            if ((this.getVacationItemCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getVacationItemCompleted(this, new getVacationItemCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getVacationItemAll", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getVacationItemAll() {
            object[] results = this.Invoke("getVacationItemAll", new object[0]);
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getVacationItemAllAsync() {
            this.getVacationItemAllAsync(null);
        }
        
        /// <remarks/>
        public void getVacationItemAllAsync(object userState) {
            if ((this.getVacationItemAllOperationCompleted == null)) {
                this.getVacationItemAllOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetVacationItemAllOperationCompleted);
            }
            this.InvokeAsync("getVacationItemAll", new object[0], this.getVacationItemAllOperationCompleted, userState);
        }
        
        private void OngetVacationItemAllOperationCompleted(object arg) {
            if ((this.getVacationItemAllCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getVacationItemAllCompleted(this, new getVacationItemAllCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getVacationItemTest", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getVacationItemTest() {
            object[] results = this.Invoke("getVacationItemTest", new object[0]);
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getVacationItemTestAsync() {
            this.getVacationItemTestAsync(null);
        }
        
        /// <remarks/>
        public void getVacationItemTestAsync(object userState) {
            if ((this.getVacationItemTestOperationCompleted == null)) {
                this.getVacationItemTestOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetVacationItemTestOperationCompleted);
            }
            this.InvokeAsync("getVacationItemTest", new object[0], this.getVacationItemTestOperationCompleted, userState);
        }
        
        private void OngetVacationItemTestOperationCompleted(object arg) {
            if ((this.getVacationItemTestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getVacationItemTestCompleted(this, new getVacationItemTestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getVacationItemLimitTest", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getVacationItemLimitTest(string izinTurKod) {
            object[] results = this.Invoke("getVacationItemLimitTest", new object[] {
                        izinTurKod});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getVacationItemLimitTestAsync(string izinTurKod) {
            this.getVacationItemLimitTestAsync(izinTurKod, null);
        }
        
        /// <remarks/>
        public void getVacationItemLimitTestAsync(string izinTurKod, object userState) {
            if ((this.getVacationItemLimitTestOperationCompleted == null)) {
                this.getVacationItemLimitTestOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetVacationItemLimitTestOperationCompleted);
            }
            this.InvokeAsync("getVacationItemLimitTest", new object[] {
                        izinTurKod}, this.getVacationItemLimitTestOperationCompleted, userState);
        }
        
        private void OngetVacationItemLimitTestOperationCompleted(object arg) {
            if ((this.getVacationItemLimitTestCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getVacationItemLimitTestCompleted(this, new getVacationItemLimitTestCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getVacationItemLimit", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getVacationItemLimit(string izinTurKod) {
            object[] results = this.Invoke("getVacationItemLimit", new object[] {
                        izinTurKod});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getVacationItemLimitAsync(string izinTurKod) {
            this.getVacationItemLimitAsync(izinTurKod, null);
        }
        
        /// <remarks/>
        public void getVacationItemLimitAsync(string izinTurKod, object userState) {
            if ((this.getVacationItemLimitOperationCompleted == null)) {
                this.getVacationItemLimitOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetVacationItemLimitOperationCompleted);
            }
            this.InvokeAsync("getVacationItemLimit", new object[] {
                        izinTurKod}, this.getVacationItemLimitOperationCompleted, userState);
        }
        
        private void OngetVacationItemLimitOperationCompleted(object arg) {
            if ((this.getVacationItemLimitCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getVacationItemLimitCompleted(this, new getVacationItemLimitCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/getDoviz", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet getDoviz() {
            object[] results = this.Invoke("getDoviz", new object[0]);
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void getDovizAsync() {
            this.getDovizAsync(null);
        }
        
        /// <remarks/>
        public void getDovizAsync(object userState) {
            if ((this.getDovizOperationCompleted == null)) {
                this.getDovizOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetDovizOperationCompleted);
            }
            this.InvokeAsync("getDoviz", new object[0], this.getDovizOperationCompleted, userState);
        }
        
        private void OngetDovizOperationCompleted(object arg) {
            if ((this.getDovizCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getDovizCompleted(this, new getDovizCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getVacationItemCompletedEventHandler(object sender, getVacationItemCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getVacationItemCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getVacationItemCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getVacationItemAllCompletedEventHandler(object sender, getVacationItemAllCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getVacationItemAllCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getVacationItemAllCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getVacationItemTestCompletedEventHandler(object sender, getVacationItemTestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getVacationItemTestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getVacationItemTestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getVacationItemLimitTestCompletedEventHandler(object sender, getVacationItemLimitTestCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getVacationItemLimitTestCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getVacationItemLimitTestCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getVacationItemLimitCompletedEventHandler(object sender, getVacationItemLimitCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getVacationItemLimitCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getVacationItemLimitCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void getDovizCompletedEventHandler(object sender, getDovizCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getDovizCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getDovizCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591