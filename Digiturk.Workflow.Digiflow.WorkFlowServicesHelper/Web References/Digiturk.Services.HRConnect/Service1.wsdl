<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="getVacationItem">
        <s:complexType />
      </s:element>
      <s:element name="getVacationItemResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getVacationItemResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemAll">
        <s:complexType />
      </s:element>
      <s:element name="getVacationItemAllResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getVacationItemAllResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemTest">
        <s:complexType />
      </s:element>
      <s:element name="getVacationItemTestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getVacationItemTestResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemLimitTest">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="izinTurKod" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemLimitTestResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getVacationItemLimitTestResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemLimit">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="izinTurKod" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getVacationItemLimitResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getVacationItemLimitResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="getDoviz">
        <s:complexType />
      </s:element>
      <s:element name="getDovizResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="getDovizResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="getVacationItemSoapIn">
    <wsdl:part name="parameters" element="tns:getVacationItem" />
  </wsdl:message>
  <wsdl:message name="getVacationItemSoapOut">
    <wsdl:part name="parameters" element="tns:getVacationItemResponse" />
  </wsdl:message>
  <wsdl:message name="getVacationItemAllSoapIn">
    <wsdl:part name="parameters" element="tns:getVacationItemAll" />
  </wsdl:message>
  <wsdl:message name="getVacationItemAllSoapOut">
    <wsdl:part name="parameters" element="tns:getVacationItemAllResponse" />
  </wsdl:message>
  <wsdl:message name="getVacationItemTestSoapIn">
    <wsdl:part name="parameters" element="tns:getVacationItemTest" />
  </wsdl:message>
  <wsdl:message name="getVacationItemTestSoapOut">
    <wsdl:part name="parameters" element="tns:getVacationItemTestResponse" />
  </wsdl:message>
  <wsdl:message name="getVacationItemLimitTestSoapIn">
    <wsdl:part name="parameters" element="tns:getVacationItemLimitTest" />
  </wsdl:message>
  <wsdl:message name="getVacationItemLimitTestSoapOut">
    <wsdl:part name="parameters" element="tns:getVacationItemLimitTestResponse" />
  </wsdl:message>
  <wsdl:message name="getVacationItemLimitSoapIn">
    <wsdl:part name="parameters" element="tns:getVacationItemLimit" />
  </wsdl:message>
  <wsdl:message name="getVacationItemLimitSoapOut">
    <wsdl:part name="parameters" element="tns:getVacationItemLimitResponse" />
  </wsdl:message>
  <wsdl:message name="getDovizSoapIn">
    <wsdl:part name="parameters" element="tns:getDoviz" />
  </wsdl:message>
  <wsdl:message name="getDovizSoapOut">
    <wsdl:part name="parameters" element="tns:getDovizResponse" />
  </wsdl:message>
  <wsdl:portType name="Service1Soap">
    <wsdl:operation name="getVacationItem">
      <wsdl:input message="tns:getVacationItemSoapIn" />
      <wsdl:output message="tns:getVacationItemSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getVacationItemAll">
      <wsdl:input message="tns:getVacationItemAllSoapIn" />
      <wsdl:output message="tns:getVacationItemAllSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getVacationItemTest">
      <wsdl:input message="tns:getVacationItemTestSoapIn" />
      <wsdl:output message="tns:getVacationItemTestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimitTest">
      <wsdl:input message="tns:getVacationItemLimitTestSoapIn" />
      <wsdl:output message="tns:getVacationItemLimitTestSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimit">
      <wsdl:input message="tns:getVacationItemLimitSoapIn" />
      <wsdl:output message="tns:getVacationItemLimitSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="getDoviz">
      <wsdl:input message="tns:getDovizSoapIn" />
      <wsdl:output message="tns:getDovizSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="Service1Soap" type="tns:Service1Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getVacationItem">
      <soap:operation soapAction="http://tempuri.org/getVacationItem" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemAll">
      <soap:operation soapAction="http://tempuri.org/getVacationItemAll" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemTest">
      <soap:operation soapAction="http://tempuri.org/getVacationItemTest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimitTest">
      <soap:operation soapAction="http://tempuri.org/getVacationItemLimitTest" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimit">
      <soap:operation soapAction="http://tempuri.org/getVacationItemLimit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDoviz">
      <soap:operation soapAction="http://tempuri.org/getDoviz" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="Service1Soap12" type="tns:Service1Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="getVacationItem">
      <soap12:operation soapAction="http://tempuri.org/getVacationItem" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemAll">
      <soap12:operation soapAction="http://tempuri.org/getVacationItemAll" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemTest">
      <soap12:operation soapAction="http://tempuri.org/getVacationItemTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimitTest">
      <soap12:operation soapAction="http://tempuri.org/getVacationItemLimitTest" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVacationItemLimit">
      <soap12:operation soapAction="http://tempuri.org/getVacationItemLimit" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getDoviz">
      <soap12:operation soapAction="http://tempuri.org/getDoviz" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service1">
    <wsdl:port name="Service1Soap" binding="tns:Service1Soap">
      <soap:address location="http://dtl1iis4:3332/Service1.asmx" />
    </wsdl:port>
    <wsdl:port name="Service1Soap12" binding="tns:Service1Soap12">
      <soap12:address location="http://dtl1iis4:3332/Service1.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>