<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="IrisDealerSdpBS" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc?xsd=xsd1" namespace="http://microsoft.com/wsdl/types/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IIrisDealerSdpBS_Ping_InputMessage">
    <wsdl:part name="parameters" element="tns:Ping" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_Ping_OutputMessage">
    <wsdl:part name="parameters" element="tns:PingResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticate_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticate" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticate_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateByCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCulture" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateByCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemValidateToken_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateToken" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemValidateToken_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateTokenResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemValidateCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCulture" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemValidateCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpire" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateByCultureWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpire" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SystemAuthenticateByCultureWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoListResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetCaseReasonDealerRelList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCaseReasonDealerRelList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetCaseReasonDealerRelList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCaseReasonDealerRelListResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoListByCriteria_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoListByCriteria" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoListByCriteria_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoListByCriteriaResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerCollectionQuotaInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerCollectionQuotaInfo" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerCollectionQuotaInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerCollectionQuotaInfoResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerPartyRoleSRelList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerPartyRoleSRelList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerPartyRoleSRelList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerPartyRoleSRelListResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerSiteVisitInfo_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerSiteVisitInfo" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerSiteVisitInfo_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerSiteVisitInfoResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SaveDealerSiteVisit_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveDealerSiteVisit" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_SaveDealerSiteVisit_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveDealerSiteVisitResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerZoneLocationList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerZoneLocationList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerZoneLocationList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerZoneLocationListResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerCharacteristics_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerCharacteristics" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerCharacteristics_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerCharacteristicsResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoListBySpecCodeList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoListBySpecCodeList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerServiceInfoListBySpecCodeList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerServiceInfoListBySpecCodeListResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerList" />
  </wsdl:message>
  <wsdl:message name="IIrisDealerSdpBS_GetDealerList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDealerListResponse" />
  </wsdl:message>
  <wsdl:portType name="IIrisDealerSdpBS">
    <wsdl:operation name="Ping">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/Ping" message="tns:IIrisDealerSdpBS_Ping_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/PingResponse" message="tns:IIrisDealerSdpBS_Ping_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticate" message="tns:IIrisDealerSdpBS_SystemAuthenticate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateResponse" message="tns:IIrisDealerSdpBS_SystemAuthenticate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCulture" message="tns:IIrisDealerSdpBS_SystemAuthenticateByCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCultureResponse" message="tns:IIrisDealerSdpBS_SystemAuthenticateByCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemValidateToken" message="tns:IIrisDealerSdpBS_SystemValidateToken_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemValidateTokenResponse" message="tns:IIrisDealerSdpBS_SystemValidateToken_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemValidateCulture" message="tns:IIrisDealerSdpBS_SystemValidateCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemValidateCultureResponse" message="tns:IIrisDealerSdpBS_SystemValidateCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateWithExpire" message="tns:IIrisDealerSdpBS_SystemAuthenticateWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateWithExpireResponse" message="tns:IIrisDealerSdpBS_SystemAuthenticateWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCultureWithExpire" message="tns:IIrisDealerSdpBS_SystemAuthenticateByCultureWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCultureWithExpireResponse" message="tns:IIrisDealerSdpBS_SystemAuthenticateByCultureWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoList" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListResponse" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCaseReasonDealerRelList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetCaseReasonDealerRelList" message="tns:IIrisDealerSdpBS_GetCaseReasonDealerRelList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetCaseReasonDealerRelListResponse" message="tns:IIrisDealerSdpBS_GetCaseReasonDealerRelList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoListByCriteria">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListByCriteria" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoListByCriteria_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListByCriteriaResponse" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoListByCriteria_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerCollectionQuotaInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerCollectionQuotaInfo" message="tns:IIrisDealerSdpBS_GetDealerCollectionQuotaInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerCollectionQuotaInfoResponse" message="tns:IIrisDealerSdpBS_GetDealerCollectionQuotaInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerPartyRoleSRelList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerPartyRoleSRelList" message="tns:IIrisDealerSdpBS_GetDealerPartyRoleSRelList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerPartyRoleSRelListResponse" message="tns:IIrisDealerSdpBS_GetDealerPartyRoleSRelList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerSiteVisitInfo">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerSiteVisitInfo" message="tns:IIrisDealerSdpBS_GetDealerSiteVisitInfo_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerSiteVisitInfoResponse" message="tns:IIrisDealerSdpBS_GetDealerSiteVisitInfo_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveDealerSiteVisit">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SaveDealerSiteVisit" message="tns:IIrisDealerSdpBS_SaveDealerSiteVisit_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/SaveDealerSiteVisitResponse" message="tns:IIrisDealerSdpBS_SaveDealerSiteVisit_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerZoneLocationList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerZoneLocationList" message="tns:IIrisDealerSdpBS_GetDealerZoneLocationList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerZoneLocationListResponse" message="tns:IIrisDealerSdpBS_GetDealerZoneLocationList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerCharacteristics">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerCharacteristics" message="tns:IIrisDealerSdpBS_GetDealerCharacteristics_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerCharacteristicsResponse" message="tns:IIrisDealerSdpBS_GetDealerCharacteristics_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoListBySpecCodeList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListBySpecCodeList" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoListBySpecCodeList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListBySpecCodeListResponse" message="tns:IIrisDealerSdpBS_GetDealerServiceInfoListBySpecCodeList_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDealerList">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerList" message="tns:IIrisDealerSdpBS_GetDealerList_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisDealerSdpBS/GetDealerListResponse" message="tns:IIrisDealerSdpBS_GetDealerList_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IIrisDealerSdpBS" type="tns:IIrisDealerSdpBS">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/Ping" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemValidateToken" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemValidateCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCultureWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetCaseReasonDealerRelList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetCaseReasonDealerRelList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoListByCriteria">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListByCriteria" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerCollectionQuotaInfo">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerCollectionQuotaInfo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerPartyRoleSRelList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerPartyRoleSRelList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerSiteVisitInfo">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerSiteVisitInfo" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveDealerSiteVisit">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/SaveDealerSiteVisit" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerZoneLocationList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerZoneLocationList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerCharacteristics">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerCharacteristics" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerServiceInfoListBySpecCodeList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListBySpecCodeList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDealerList">
      <soap:operation soapAction="http://tempuri.org/IIrisDealerSdpBS/GetDealerList" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="IrisDealerSdpBS">
    <wsdl:port name="BasicHttpBinding_IIrisDealerSdpBS" binding="tns:BasicHttpBinding_IIrisDealerSdpBS">
      <soap:address location="http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>