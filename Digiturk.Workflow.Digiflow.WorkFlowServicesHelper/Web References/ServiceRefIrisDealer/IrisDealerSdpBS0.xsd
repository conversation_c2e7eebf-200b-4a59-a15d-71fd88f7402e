<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc?xsd=xsd1" namespace="http://microsoft.com/wsdl/types/" />
  <xs:element name="Ping">
    <xs:complexType />
  </xs:element>
  <xs:element name="PingResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="PingResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticate">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateToken">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateTokenResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateTokenResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="TokenData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Token" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="ExpireAt" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SystemAuthenticateByCultureWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerServiceInfoList">
    <xs:complexType />
  </xs:element>
  <xs:element name="GetDealerServiceInfoListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerServiceInfoListResult" type="tns:ArrayOfDealerServiceInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfDealerServiceInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DealerServiceInfo" nillable="true" type="tns:DealerServiceInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DealerServiceInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DealerType" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="FTBrandStatus" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="FaceToFaceAgentStatus" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ServiceRegionList" type="tns:ArrayOfDealerServiceRegion" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationCdDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationRegionDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="TaxNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="TaxOfficeName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerAddress" type="tns:AddressInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerGpsInfo" type="tns:GpsInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="MailList" type="tns:ArrayOfString" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCodeList" type="tns:ArrayOfString" />
          <xs:element minOccurs="0" maxOccurs="1" name="PhoneList" type="tns:ArrayOfPhone" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerPartyRoleId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerSubTypeDescription" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerSubTypeCd" type="xs:long" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="BaseClass" />
  <xs:complexType name="Phone">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="Type" type="tns:PhoneType" />
          <xs:element minOccurs="0" maxOccurs="1" name="CountryPrefixNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AreaPrefixNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PhoneNumber" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:simpleType name="PhoneType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="EV" />
      <xs:enumeration value="IS" />
      <xs:enumeration value="CEP" />
      <xs:enumeration value="FAX" />
    </xs:restriction>
  </xs:simpleType>
  <xs:complexType name="GpsInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="Distance" nillable="true" type="xs:double" />
          <xs:element minOccurs="1" maxOccurs="1" name="Latitude" type="xs:double" />
          <xs:element minOccurs="1" maxOccurs="1" name="Longitude" type="xs:double" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ContactMedium">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="ContactMediumId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumPurposeTCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ContactMediumRelId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumTechTypeCD" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumStatusTCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Description" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AddressInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:ContactMedium">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine2" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine3" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="City" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CityId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="Country" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CountryId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="County" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CountyId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="District" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DistrictId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoAddressId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoLocationName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoLocationTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsRegistered" type="xs:boolean" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleContactMedRId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ProcessDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="State" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="StateId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UserId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="ZipCode" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="AccountLogId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="Complex" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ComplexBlock" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentFlatNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Avenue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Street" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressFullText" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="GpsInfo" type="tns:GpsInfo" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GeoLocationInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoLocationTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoLocationName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Description" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="District" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="County" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="City" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="State" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Country" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ParentId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoLocationCode" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="DealerServiceRegion">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:GeoLocationInfo">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerZoneSpecId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerZoneSpecCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfDealerServiceRegion">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DealerServiceRegion" nillable="true" type="tns:DealerServiceRegion" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfString">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfPhone">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Phone" nillable="true" type="tns:Phone" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetCaseReasonDealerRelList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="dealerCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="caseReasonCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="SessionToken">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="SessionId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="SessionGuidId" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetCaseReasonDealerRelListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetCaseReasonDealerRelListResult" type="tns:ArrayOfCaseReasonDealerRel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfCaseReasonDealerRel">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="CaseReasonDealerRel" nillable="true" type="tns:CaseReasonDealerRel" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="CaseReasonDealerRel">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="CaseReasonDealerRelId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CaseReasonId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CaseReasonCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CaseReasonName" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerPartyRoleId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreateDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdateDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedBy" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="LogicalDeleteKey" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetDealerServiceInfoListByCriteria">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="dealerCriteria" type="tns:DealerCriteria" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="DealerCriteria">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCdEq" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CountryNameEq" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CityNameEq" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CountyNameEq" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DistrictNameEq" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationTypeEq" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerNameLike" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="RegionIdEq" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="ReferansDealerCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsBranchesRequired" type="xs:boolean" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerPartyRoleIdEq" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsNeedCharacteristics" type="xs:boolean" />
          <xs:element minOccurs="0" maxOccurs="1" name="Gps" type="tns:GpsData" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GpsData">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="Id" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="Speed" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="MeasurementReferanceType" type="xs:long" />
          <xs:element xmlns:q1="http://microsoft.com/wsdl/types/" minOccurs="1" maxOccurs="1" name="MeasurementReferanceLastRecord" type="q1:char" />
          <xs:element minOccurs="1" maxOccurs="1" name="MeasurementReferanceNumber" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="MeasurementTaskCode" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="Latitude" type="xs:float" />
          <xs:element minOccurs="1" maxOccurs="1" name="Longitude" type="xs:float" />
          <xs:element minOccurs="1" maxOccurs="1" name="HorzontalDilutionofPrecision" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PositionDilutionofPrecision" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="Heading" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GpsTime" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="Altitude" type="xs:float" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdateDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreationSessionId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PersonalId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="VehicleLocation" type="tns:VehicleGpsLocation" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="VehicleGpsLocation">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="Latitude" nillable="true" type="xs:float" />
          <xs:element minOccurs="1" maxOccurs="1" name="Longitude" nillable="true" type="xs:float" />
          <xs:element minOccurs="0" maxOccurs="1" name="DriverCode" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="LocationDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="Plate" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetDealerServiceInfoListByCriteriaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerServiceInfoListByCriteriaResult" type="tns:ArrayOfDealerServiceInfoIttp" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfDealerServiceInfoIttp">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DealerServiceInfoIttp" nillable="true" type="tns:DealerServiceInfoIttp" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DealerServiceInfoIttp">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:DealerServiceInfo">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrgDefaultResourceLocSpecId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchList" type="tns:ArrayOfDealerServiceInfoIttp" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsBranch" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="LoginId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCharacteristics" type="tns:ArrayOfPartyRoleCharValue" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyRoleCharValue">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyRoleCharValue" nillable="true" type="tns:PartyRoleCharValue" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyRoleCharValue">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecDescription" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleCharValueId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecUseId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValueCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCategoryCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecDataTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecValueId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LovTableRowCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleTypeCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetDealerCollectionQuotaInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="dealerCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerCollectionQuotaInfoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerCollectionQuotaInfoResult" type="tns:DealerCollectionQuotaInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="DealerCollectionQuotaInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CollectionQuota" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CollectionQuotaCurrency" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ProcessDate" type="xs:dateTime" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetDealerPartyRoleSRelList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="dealerPartyRoleId" type="xs:long" />
        <xs:element minOccurs="1" maxOccurs="1" name="toPartyRoleId" nillable="true" type="xs:long" />
        <xs:element minOccurs="0" maxOccurs="1" name="toPartyRoleSRelTypeCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="fromPartyRoleSRelStatus" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="toPartyRoleStatus" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
        <xs:element minOccurs="0" maxOccurs="1" name="gpsData" type="tns:GpsData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerPartyRoleSRelListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerPartyRoleSRelListResult" type="tns:ArrayOfPartyRole" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfPartyRole">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyRole" nillable="true" type="tns:PartyRole" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyRole">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleServiceProviderCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleStatusTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="LoginId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="LoginGroupId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsBranch" nillable="true" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="IndividualId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DbsSalesAgentCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DbsUserCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContractProfileType" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="SatelliteTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="MemberTypePartyProfileName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationInfo" type="tns:Organisation" />
          <xs:element minOccurs="0" maxOccurs="1" name="IndividualInfo" type="tns:Individual" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountList" type="tns:ArrayOfPartyRoleAccount" />
          <xs:element minOccurs="0" maxOccurs="1" name="ServiceAccountList" type="tns:ArrayOfPartyRoleAccount" />
          <xs:element minOccurs="0" maxOccurs="1" name="BillAccountList" type="tns:ArrayOfPartyRoleAccount" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceLocationSpecList" type="tns:ArrayOfResourceLocationSpec" />
          <xs:element minOccurs="0" maxOccurs="1" name="AccountAccessPermissions" type="tns:ArrayOfKeyValueItem" />
          <xs:element minOccurs="0" maxOccurs="1" name="EmailList" type="tns:ArrayOfAccountEmail" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressInfoList" type="tns:ArrayOfAddressInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="PhoneList" type="tns:ArrayOfPhoneInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="IrisPlusUserInfo" type="tns:IrisPlusUserInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleCharValueList" type="tns:ArrayOfPartyRoleCharValue" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyCharValueList" type="tns:ArrayOfPartyCharValue" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyProfileMemberInfoList" type="tns:ArrayOfPartyProfileMemberInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LoginUserCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourcesInfoList" type="tns:ArrayOfResourcesInfo" />
          <xs:element minOccurs="0" maxOccurs="1" name="TechnicalServicePaymentStatus" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Organisation">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:AccountInfo">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DbsDealerCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CommercialName" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="LicenseAcquireDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="LicenseIssuerId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="LicenseManicipulatyId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="LicenseOtherManicipulaty" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LicenseRegisterNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OtherTaxOffice" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ArabicName" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="AccountInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="Characteristics" type="tns:ArrayOfKeyValueItem" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyProfileCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="SatelliteTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="IndividualId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="BillAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="DbsAccountNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="AccountLogId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CountryId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="TaxNumber" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="TaxOfficeId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleServiceProviderCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountSpecTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleStatusTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="StatusTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ContractProfileType" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="MemberTypePartyProfileName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AccountPartyRole" type="tns:PartyRole" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfKeyValueItem">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueItem" nillable="true" type="tns:KeyValueItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="KeyValueItem">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="Key" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Value" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="Individual">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:AccountInfo">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="BirthDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="BirthPlace" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CitizenNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DisplayName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DisplaySurname" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="FatherName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="FirstName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="GenderTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LanguageTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="MaritalStatusTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="MotherMaidenSurname" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="MotherName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="NationalityTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OtherProfession" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OtherTaxOffice" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PassportNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ProfessionTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="RegisteredBookNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="RegisteredCity" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="RegisteredCountry" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="RegisteredFamilySequenceNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="RegisteredSequenceNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="SurName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="TitleTypeCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyRoleAccount">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyRoleAccount" nillable="true" type="tns:PartyRoleAccount" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyRoleAccount">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="ParentId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleAccountId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountSpecTCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountStatusTCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AccountName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AccountDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DbsOutletLocation" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DbsAccountNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="SatelliteTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Characteristics" type="tns:ArrayOfKeyValueItem" />
          <xs:element minOccurs="1" maxOccurs="1" name="ResourceLocationSpecId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="StbTipi" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsAccountHero" type="xs:boolean" />
          <xs:element minOccurs="0" maxOccurs="1" name="BillAmountCurrencyTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsDefault" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoAddressId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="MembershipType" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PaymentInstrumentList" type="tns:ArrayOfPaymentInstrument" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccountCharValueList" type="tns:ArrayOfPartyRoleAccountCharValue" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccPartyRoleRelList" type="tns:ArrayOfPartyRoleAccPartyRoleRel" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPaymentInstrument">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PaymentInstrument" nillable="true" type="tns:PaymentInstrument" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PaymentInstrument">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="PaymentInstrumentId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PaymentInstrumentTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="IBAN" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="VanNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BankAccountNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BankCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchCode" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BankGovernmentCode" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BankPaymentSourceCode" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="BillAccountId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CountryId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PaymentInstrStatusTCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreditCardOwnerName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreditCardExpireDate" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreditCardCvvNumber" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreditCardId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="SwiftBIC" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreditorId" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyRoleAccountCharValue">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyRoleAccountCharValue" nillable="true" type="tns:PartyRoleAccountCharValue" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyRoleAccountCharValue">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleAccountCharValueId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleAccountId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecUseId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValueCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCategoryCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecDataTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecValueId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LovTableRowCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyRoleAccPartyRoleRel">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyRoleAccPartyRoleRel" nillable="true" type="tns:PartyRoleAccPartyRoleRel" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyRoleAccPartyRoleRel">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleAccountId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleAccPartyRoleRelTypeCd" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="DealerCd" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="OrganisationName" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfResourceLocationSpec">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ResourceLocationSpec" nillable="true" type="tns:ResourceLocationSpec" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ResourceLocationSpec">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="ResourceLocationSpecId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResLocTypeSpecCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoAddressId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DbsDepoKodu" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Description" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdateDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreatedBy" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedBy" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="LogicalDeleteKey" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyDefault" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationDefault" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ParentId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="HouseStatusType" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="DbsAccountNumber" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DbsOutletLocation" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="WarehouseAddress" type="tns:GeoAddress" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="GeoAddress">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine1" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine2" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressLine3" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressText" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="City" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Country" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="Description" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="District" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="County" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Complex" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ComplexBlock" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ApartmentFlatNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Avenue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Street" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoAddressHeadlineText" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoAddressId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="GeoAddressTypeCD" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId_City" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId_Country" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId_State" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="GeoLocationId_County" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsDefault" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="1" name="LocationText" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LogicalDeleteKey" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationName" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ParentAddressId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleGeoLocRelTypeCD" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleGeoLocRelTypeName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PostCode" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Statu" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="StateId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="State" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AddressFullText" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfAccountEmail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="AccountEmail" nillable="true" type="tns:AccountEmail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="AccountEmail">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:ContactMedium">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="IsRegistered" type="xs:boolean" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="Email" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="AccountLogId" nillable="true" type="xs:long" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfAddressInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="AddressInfo" nillable="true" type="tns:AddressInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfPhoneInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PhoneInfo" nillable="true" type="tns:PhoneInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PhoneInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:ContactMedium">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="CountryPrefixNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="AreaPrefixNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PhoneNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ExtensionNumber" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsRegistered" type="xs:boolean" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ProcessDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="AccountLogId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrderPriority" type="xs:int" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="IrisPlusUserInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="BayiIrisId" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="BayiIrisPersonelId" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="1" name="DTKimlikNo" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PersonelFotografAdi" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="PersonelId" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="SubContractorId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PersonnelPhoto" type="tns:DocumentInfo" />
          <xs:element minOccurs="1" maxOccurs="1" name="UserId" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="1" name="UserType" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="DocumentInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="DocumentSpecName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DocumentFileName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DocumentExtension" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DocumentBinaryContent" type="xs:base64Binary" />
          <xs:element minOccurs="0" maxOccurs="1" name="Title" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Description" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ReferenceId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreaterName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="Ip" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreateDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="Status" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyCharValue">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyCharValue" nillable="true" type="tns:PartyCharValue" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyCharValue">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecDescription" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyCharValueId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="IndividualId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyApplicationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecUseId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValueCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecCategoryCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecUseTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharSpecDataTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CharSpecValueId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CharValue" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="LovTableRowCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleTypeCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfPartyProfileMemberInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PartyProfileMemberInfo" nillable="true" type="tns:PartyProfileMemberInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PartyProfileMemberInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="PartyProfileMemberId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleAccountId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="PartyProfileId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyProfileTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyRoleTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyProfileName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="PartyProfileDescription" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfResourcesInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ResourcesInfo" nillable="true" type="tns:ResourcesInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ResourcesInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="ResourcesId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ResourceLocationSpecId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ResourceSpecId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="ProductId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="StockSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="StockSpecName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="StockSpecUnitTypeCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="Quantity" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceLocationTypeSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="SerialNumber" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceSpecName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceSpecDescription" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceSpecCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceSpecTypeCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="ResourceStatusTypeCd" type="xs:string" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="GetDealerSiteVisitInfo">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="dealerPartyRoleId" nillable="true" type="xs:long" />
        <xs:element minOccurs="1" maxOccurs="1" name="userPartyRoleId" nillable="true" type="xs:long" />
        <xs:element minOccurs="0" maxOccurs="1" name="dealerSiteVisitStatusTCd" type="xs:string" />
        <xs:element minOccurs="1" maxOccurs="1" name="siteVisitRowCount" type="xs:int" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerSiteVisitInfoResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerSiteVisitInfoResult" type="tns:ArrayOfDealerSiteVisit" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfDealerSiteVisit">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DealerSiteVisit" nillable="true" type="tns:DealerSiteVisit" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DealerSiteVisit">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="1" maxOccurs="1" name="DealerSiteVisitId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UserPartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerPartyRoleId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="OrganisationId" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="OrganisationName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="BranchCd" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="IsBranch" type="xs:int" />
          <xs:element minOccurs="1" maxOccurs="1" name="SiteVisitStartDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerSiteVisitStatusTCd" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="SiteVisitOpenDescription" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="SiteVisitEndDate" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" maxOccurs="1" name="SiteVisitCloseDescription" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="InitiationLatitude" type="xs:decimal" />
          <xs:element minOccurs="1" maxOccurs="1" name="TerminationLatitude" type="xs:decimal" />
          <xs:element minOccurs="1" maxOccurs="1" name="InitiationLongitude" type="xs:decimal" />
          <xs:element minOccurs="1" maxOccurs="1" name="TerminationLongitude" type="xs:decimal" />
          <xs:element minOccurs="1" maxOccurs="1" name="SiteVisitVoteType" nillable="true" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="LogicalDeleteKey" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdateDate" type="xs:dateTime" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdatedBy" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="CreateIrisSessionId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="UpdateIrisSessionId" type="xs:long" />
          <xs:element minOccurs="0" maxOccurs="1" name="CreatedByName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerGpsInfo" type="tns:GpsInfo" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="SaveDealerSiteVisit">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="dealerSiteVisit" type="tns:DealerSiteVisit" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SaveDealerSiteVisitResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="SaveDealerSiteVisitResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerZoneLocationList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="dealerPartyRoleId" type="xs:long" />
        <xs:element minOccurs="0" maxOccurs="1" name="dealerZoneSpecCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerZoneLocationListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerZoneLocationListResult" type="tns:ArrayOfDealerServiceRegion" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerCharacteristics">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="dealerCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="charSpecCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionToken" type="tns:SessionToken" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerCharacteristicsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerCharacteristicsResult" type="tns:ArrayOfPartyRoleCharValue" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerServiceInfoListBySpecCodeList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="charSpecCodeList" type="tns:ArrayOfString" />
        <xs:element minOccurs="1" maxOccurs="1" name="isAndOperator" nillable="true" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerServiceInfoListBySpecCodeListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerServiceInfoListBySpecCodeListResult" type="tns:ArrayOfDealerServiceInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerList">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="referanceDealerCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="referanceCountryCd" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="charSpecCodeListIn" type="tns:ArrayOfString" />
        <xs:element minOccurs="0" maxOccurs="1" name="charSpecCodeListNotIn" type="tns:ArrayOfString" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetDealerListResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetDealerListResult" type="tns:ArrayOfDealerInfo" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ArrayOfDealerInfo">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DealerInfo" nillable="true" type="tns:DealerInfo" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DealerInfo">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:BaseClass">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="DealerCode" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="DealerName" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="City" type="xs:string" />
          <xs:element minOccurs="0" maxOccurs="1" name="District" type="xs:string" />
          <xs:element minOccurs="1" maxOccurs="1" name="StockLocationId" type="xs:long" />
          <xs:element minOccurs="1" maxOccurs="1" name="DealerPartyRoleId" type="xs:long" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
</xs:schema>