﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.ServiceRefIrisDealer {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="BasicHttpBinding_IIrisDealerSdpBS", Namespace="http://tempuri.org/")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BaseClass))]
    public partial class IrisDealerSdpBS : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback PingOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateTokenOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerServiceInfoListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetCaseReasonDealerRelListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerServiceInfoListByCriteriaOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerCollectionQuotaInfoOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerPartyRoleSRelListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerSiteVisitInfoOperationCompleted;
        
        private System.Threading.SendOrPostCallback SaveDealerSiteVisitOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerZoneLocationListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerCharacteristicsOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerServiceInfoListBySpecCodeListOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDealerListOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public IrisDealerSdpBS() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIrisDealer_IrisDealerSdpBS;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event PingCompletedEventHandler PingCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateCompletedEventHandler SystemAuthenticateCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureCompletedEventHandler SystemAuthenticateByCultureCompleted;
        
        /// <remarks/>
        public event SystemValidateTokenCompletedEventHandler SystemValidateTokenCompleted;
        
        /// <remarks/>
        public event SystemValidateCultureCompletedEventHandler SystemValidateCultureCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateWithExpireCompletedEventHandler SystemAuthenticateWithExpireCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureWithExpireCompletedEventHandler SystemAuthenticateByCultureWithExpireCompleted;
        
        /// <remarks/>
        public event GetDealerServiceInfoListCompletedEventHandler GetDealerServiceInfoListCompleted;
        
        /// <remarks/>
        public event GetCaseReasonDealerRelListCompletedEventHandler GetCaseReasonDealerRelListCompleted;
        
        /// <remarks/>
        public event GetDealerServiceInfoListByCriteriaCompletedEventHandler GetDealerServiceInfoListByCriteriaCompleted;
        
        /// <remarks/>
        public event GetDealerCollectionQuotaInfoCompletedEventHandler GetDealerCollectionQuotaInfoCompleted;
        
        /// <remarks/>
        public event GetDealerPartyRoleSRelListCompletedEventHandler GetDealerPartyRoleSRelListCompleted;
        
        /// <remarks/>
        public event GetDealerSiteVisitInfoCompletedEventHandler GetDealerSiteVisitInfoCompleted;
        
        /// <remarks/>
        public event SaveDealerSiteVisitCompletedEventHandler SaveDealerSiteVisitCompleted;
        
        /// <remarks/>
        public event GetDealerZoneLocationListCompletedEventHandler GetDealerZoneLocationListCompleted;
        
        /// <remarks/>
        public event GetDealerCharacteristicsCompletedEventHandler GetDealerCharacteristicsCompleted;
        
        /// <remarks/>
        public event GetDealerServiceInfoListBySpecCodeListCompletedEventHandler GetDealerServiceInfoListBySpecCodeListCompleted;
        
        /// <remarks/>
        public event GetDealerListCompletedEventHandler GetDealerListCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/Ping", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public long Ping() {
            object[] results = this.Invoke("Ping", new object[0]);
            return ((long)(results[0]));
        }
        
        /// <remarks/>
        public void PingAsync() {
            this.PingAsync(null);
        }
        
        /// <remarks/>
        public void PingAsync(object userState) {
            if ((this.PingOperationCompleted == null)) {
                this.PingOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPingOperationCompleted);
            }
            this.InvokeAsync("Ping", new object[0], this.PingOperationCompleted, userState);
        }
        
        private void OnPingOperationCompleted(object arg) {
            if ((this.PingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PingCompleted(this, new PingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticate", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticate(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateOperationCompleted == null)) {
                this.SystemAuthenticateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateOperationCompleted(object arg) {
            if ((this.SystemAuthenticateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateCompleted(this, new SystemAuthenticateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticateByCulture(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureOperationCompleted == null)) {
                this.SystemAuthenticateByCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureCompleted(this, new SystemAuthenticateByCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemValidateToken", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateToken(string token) {
            object[] results = this.Invoke("SystemValidateToken", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token) {
            this.SystemValidateTokenAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token, object userState) {
            if ((this.SystemValidateTokenOperationCompleted == null)) {
                this.SystemValidateTokenOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateTokenOperationCompleted);
            }
            this.InvokeAsync("SystemValidateToken", new object[] {
                        token}, this.SystemValidateTokenOperationCompleted, userState);
        }
        
        private void OnSystemValidateTokenOperationCompleted(object arg) {
            if ((this.SystemValidateTokenCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateTokenCompleted(this, new SystemValidateTokenCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemValidateCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateCulture(string token) {
            object[] results = this.Invoke("SystemValidateCulture", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token) {
            this.SystemValidateCultureAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token, object userState) {
            if ((this.SystemValidateCultureOperationCompleted == null)) {
                this.SystemValidateCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateCultureOperationCompleted);
            }
            this.InvokeAsync("SystemValidateCulture", new object[] {
                        token}, this.SystemValidateCultureOperationCompleted, userState);
        }
        
        private void OnSystemValidateCultureOperationCompleted(object arg) {
            if ((this.SystemValidateCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateCultureCompleted(this, new SystemValidateCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateWithExpire(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateWithExpireAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateWithExpireCompleted(this, new SystemAuthenticateWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SystemAuthenticateByCultureWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateByCultureWithExpire(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureWithExpireAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateByCultureWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureWithExpireCompleted(this, new SystemAuthenticateByCultureWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerServiceInfo[] GetDealerServiceInfoList() {
            object[] results = this.Invoke("GetDealerServiceInfoList", new object[0]);
            return ((DealerServiceInfo[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListAsync() {
            this.GetDealerServiceInfoListAsync(null);
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListAsync(object userState) {
            if ((this.GetDealerServiceInfoListOperationCompleted == null)) {
                this.GetDealerServiceInfoListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerServiceInfoListOperationCompleted);
            }
            this.InvokeAsync("GetDealerServiceInfoList", new object[0], this.GetDealerServiceInfoListOperationCompleted, userState);
        }
        
        private void OnGetDealerServiceInfoListOperationCompleted(object arg) {
            if ((this.GetDealerServiceInfoListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerServiceInfoListCompleted(this, new GetDealerServiceInfoListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetCaseReasonDealerRelList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public CaseReasonDealerRel[] GetCaseReasonDealerRelList(string dealerCd, string caseReasonCd, SessionToken sessionToken) {
            object[] results = this.Invoke("GetCaseReasonDealerRelList", new object[] {
                        dealerCd,
                        caseReasonCd,
                        sessionToken});
            return ((CaseReasonDealerRel[])(results[0]));
        }
        
        /// <remarks/>
        public void GetCaseReasonDealerRelListAsync(string dealerCd, string caseReasonCd, SessionToken sessionToken) {
            this.GetCaseReasonDealerRelListAsync(dealerCd, caseReasonCd, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetCaseReasonDealerRelListAsync(string dealerCd, string caseReasonCd, SessionToken sessionToken, object userState) {
            if ((this.GetCaseReasonDealerRelListOperationCompleted == null)) {
                this.GetCaseReasonDealerRelListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetCaseReasonDealerRelListOperationCompleted);
            }
            this.InvokeAsync("GetCaseReasonDealerRelList", new object[] {
                        dealerCd,
                        caseReasonCd,
                        sessionToken}, this.GetCaseReasonDealerRelListOperationCompleted, userState);
        }
        
        private void OnGetCaseReasonDealerRelListOperationCompleted(object arg) {
            if ((this.GetCaseReasonDealerRelListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetCaseReasonDealerRelListCompleted(this, new GetCaseReasonDealerRelListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListByCriteria", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerServiceInfoIttp[] GetDealerServiceInfoListByCriteria(DealerCriteria dealerCriteria, SessionToken sessionToken) {
            object[] results = this.Invoke("GetDealerServiceInfoListByCriteria", new object[] {
                        dealerCriteria,
                        sessionToken});
            return ((DealerServiceInfoIttp[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListByCriteriaAsync(DealerCriteria dealerCriteria, SessionToken sessionToken) {
            this.GetDealerServiceInfoListByCriteriaAsync(dealerCriteria, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListByCriteriaAsync(DealerCriteria dealerCriteria, SessionToken sessionToken, object userState) {
            if ((this.GetDealerServiceInfoListByCriteriaOperationCompleted == null)) {
                this.GetDealerServiceInfoListByCriteriaOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerServiceInfoListByCriteriaOperationCompleted);
            }
            this.InvokeAsync("GetDealerServiceInfoListByCriteria", new object[] {
                        dealerCriteria,
                        sessionToken}, this.GetDealerServiceInfoListByCriteriaOperationCompleted, userState);
        }
        
        private void OnGetDealerServiceInfoListByCriteriaOperationCompleted(object arg) {
            if ((this.GetDealerServiceInfoListByCriteriaCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerServiceInfoListByCriteriaCompleted(this, new GetDealerServiceInfoListByCriteriaCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerCollectionQuotaInfo", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerCollectionQuotaInfo GetDealerCollectionQuotaInfo(string dealerCd, SessionToken sessionToken) {
            object[] results = this.Invoke("GetDealerCollectionQuotaInfo", new object[] {
                        dealerCd,
                        sessionToken});
            return ((DealerCollectionQuotaInfo)(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerCollectionQuotaInfoAsync(string dealerCd, SessionToken sessionToken) {
            this.GetDealerCollectionQuotaInfoAsync(dealerCd, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetDealerCollectionQuotaInfoAsync(string dealerCd, SessionToken sessionToken, object userState) {
            if ((this.GetDealerCollectionQuotaInfoOperationCompleted == null)) {
                this.GetDealerCollectionQuotaInfoOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerCollectionQuotaInfoOperationCompleted);
            }
            this.InvokeAsync("GetDealerCollectionQuotaInfo", new object[] {
                        dealerCd,
                        sessionToken}, this.GetDealerCollectionQuotaInfoOperationCompleted, userState);
        }
        
        private void OnGetDealerCollectionQuotaInfoOperationCompleted(object arg) {
            if ((this.GetDealerCollectionQuotaInfoCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerCollectionQuotaInfoCompleted(this, new GetDealerCollectionQuotaInfoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerPartyRoleSRelList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public PartyRole[] GetDealerPartyRoleSRelList(long dealerPartyRoleId, [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> toPartyRoleId, string toPartyRoleSRelTypeCd, string fromPartyRoleSRelStatus, string toPartyRoleStatus, SessionToken sessionToken, GpsData gpsData) {
            object[] results = this.Invoke("GetDealerPartyRoleSRelList", new object[] {
                        dealerPartyRoleId,
                        toPartyRoleId,
                        toPartyRoleSRelTypeCd,
                        fromPartyRoleSRelStatus,
                        toPartyRoleStatus,
                        sessionToken,
                        gpsData});
            return ((PartyRole[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerPartyRoleSRelListAsync(long dealerPartyRoleId, System.Nullable<long> toPartyRoleId, string toPartyRoleSRelTypeCd, string fromPartyRoleSRelStatus, string toPartyRoleStatus, SessionToken sessionToken, GpsData gpsData) {
            this.GetDealerPartyRoleSRelListAsync(dealerPartyRoleId, toPartyRoleId, toPartyRoleSRelTypeCd, fromPartyRoleSRelStatus, toPartyRoleStatus, sessionToken, gpsData, null);
        }
        
        /// <remarks/>
        public void GetDealerPartyRoleSRelListAsync(long dealerPartyRoleId, System.Nullable<long> toPartyRoleId, string toPartyRoleSRelTypeCd, string fromPartyRoleSRelStatus, string toPartyRoleStatus, SessionToken sessionToken, GpsData gpsData, object userState) {
            if ((this.GetDealerPartyRoleSRelListOperationCompleted == null)) {
                this.GetDealerPartyRoleSRelListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerPartyRoleSRelListOperationCompleted);
            }
            this.InvokeAsync("GetDealerPartyRoleSRelList", new object[] {
                        dealerPartyRoleId,
                        toPartyRoleId,
                        toPartyRoleSRelTypeCd,
                        fromPartyRoleSRelStatus,
                        toPartyRoleStatus,
                        sessionToken,
                        gpsData}, this.GetDealerPartyRoleSRelListOperationCompleted, userState);
        }
        
        private void OnGetDealerPartyRoleSRelListOperationCompleted(object arg) {
            if ((this.GetDealerPartyRoleSRelListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerPartyRoleSRelListCompleted(this, new GetDealerPartyRoleSRelListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerSiteVisitInfo", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerSiteVisit[] GetDealerSiteVisitInfo([System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> dealerPartyRoleId, [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<long> userPartyRoleId, string dealerSiteVisitStatusTCd, int siteVisitRowCount, SessionToken sessionToken) {
            object[] results = this.Invoke("GetDealerSiteVisitInfo", new object[] {
                        dealerPartyRoleId,
                        userPartyRoleId,
                        dealerSiteVisitStatusTCd,
                        siteVisitRowCount,
                        sessionToken});
            return ((DealerSiteVisit[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerSiteVisitInfoAsync(System.Nullable<long> dealerPartyRoleId, System.Nullable<long> userPartyRoleId, string dealerSiteVisitStatusTCd, int siteVisitRowCount, SessionToken sessionToken) {
            this.GetDealerSiteVisitInfoAsync(dealerPartyRoleId, userPartyRoleId, dealerSiteVisitStatusTCd, siteVisitRowCount, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetDealerSiteVisitInfoAsync(System.Nullable<long> dealerPartyRoleId, System.Nullable<long> userPartyRoleId, string dealerSiteVisitStatusTCd, int siteVisitRowCount, SessionToken sessionToken, object userState) {
            if ((this.GetDealerSiteVisitInfoOperationCompleted == null)) {
                this.GetDealerSiteVisitInfoOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerSiteVisitInfoOperationCompleted);
            }
            this.InvokeAsync("GetDealerSiteVisitInfo", new object[] {
                        dealerPartyRoleId,
                        userPartyRoleId,
                        dealerSiteVisitStatusTCd,
                        siteVisitRowCount,
                        sessionToken}, this.GetDealerSiteVisitInfoOperationCompleted, userState);
        }
        
        private void OnGetDealerSiteVisitInfoOperationCompleted(object arg) {
            if ((this.GetDealerSiteVisitInfoCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerSiteVisitInfoCompleted(this, new GetDealerSiteVisitInfoCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/SaveDealerSiteVisit", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool SaveDealerSiteVisit(DealerSiteVisit dealerSiteVisit, SessionToken sessionToken) {
            object[] results = this.Invoke("SaveDealerSiteVisit", new object[] {
                        dealerSiteVisit,
                        sessionToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void SaveDealerSiteVisitAsync(DealerSiteVisit dealerSiteVisit, SessionToken sessionToken) {
            this.SaveDealerSiteVisitAsync(dealerSiteVisit, sessionToken, null);
        }
        
        /// <remarks/>
        public void SaveDealerSiteVisitAsync(DealerSiteVisit dealerSiteVisit, SessionToken sessionToken, object userState) {
            if ((this.SaveDealerSiteVisitOperationCompleted == null)) {
                this.SaveDealerSiteVisitOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSaveDealerSiteVisitOperationCompleted);
            }
            this.InvokeAsync("SaveDealerSiteVisit", new object[] {
                        dealerSiteVisit,
                        sessionToken}, this.SaveDealerSiteVisitOperationCompleted, userState);
        }
        
        private void OnSaveDealerSiteVisitOperationCompleted(object arg) {
            if ((this.SaveDealerSiteVisitCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SaveDealerSiteVisitCompleted(this, new SaveDealerSiteVisitCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerZoneLocationList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerServiceRegion[] GetDealerZoneLocationList(long dealerPartyRoleId, string dealerZoneSpecCd, SessionToken sessionToken) {
            object[] results = this.Invoke("GetDealerZoneLocationList", new object[] {
                        dealerPartyRoleId,
                        dealerZoneSpecCd,
                        sessionToken});
            return ((DealerServiceRegion[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerZoneLocationListAsync(long dealerPartyRoleId, string dealerZoneSpecCd, SessionToken sessionToken) {
            this.GetDealerZoneLocationListAsync(dealerPartyRoleId, dealerZoneSpecCd, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetDealerZoneLocationListAsync(long dealerPartyRoleId, string dealerZoneSpecCd, SessionToken sessionToken, object userState) {
            if ((this.GetDealerZoneLocationListOperationCompleted == null)) {
                this.GetDealerZoneLocationListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerZoneLocationListOperationCompleted);
            }
            this.InvokeAsync("GetDealerZoneLocationList", new object[] {
                        dealerPartyRoleId,
                        dealerZoneSpecCd,
                        sessionToken}, this.GetDealerZoneLocationListOperationCompleted, userState);
        }
        
        private void OnGetDealerZoneLocationListOperationCompleted(object arg) {
            if ((this.GetDealerZoneLocationListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerZoneLocationListCompleted(this, new GetDealerZoneLocationListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerCharacteristics", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public PartyRoleCharValue[] GetDealerCharacteristics(string dealerCd, string charSpecCd, SessionToken sessionToken) {
            object[] results = this.Invoke("GetDealerCharacteristics", new object[] {
                        dealerCd,
                        charSpecCd,
                        sessionToken});
            return ((PartyRoleCharValue[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerCharacteristicsAsync(string dealerCd, string charSpecCd, SessionToken sessionToken) {
            this.GetDealerCharacteristicsAsync(dealerCd, charSpecCd, sessionToken, null);
        }
        
        /// <remarks/>
        public void GetDealerCharacteristicsAsync(string dealerCd, string charSpecCd, SessionToken sessionToken, object userState) {
            if ((this.GetDealerCharacteristicsOperationCompleted == null)) {
                this.GetDealerCharacteristicsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerCharacteristicsOperationCompleted);
            }
            this.InvokeAsync("GetDealerCharacteristics", new object[] {
                        dealerCd,
                        charSpecCd,
                        sessionToken}, this.GetDealerCharacteristicsOperationCompleted, userState);
        }
        
        private void OnGetDealerCharacteristicsOperationCompleted(object arg) {
            if ((this.GetDealerCharacteristicsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerCharacteristicsCompleted(this, new GetDealerCharacteristicsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerServiceInfoListBySpecCodeList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerServiceInfo[] GetDealerServiceInfoListBySpecCodeList(string[] charSpecCodeList, [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)] System.Nullable<bool> isAndOperator) {
            object[] results = this.Invoke("GetDealerServiceInfoListBySpecCodeList", new object[] {
                        charSpecCodeList,
                        isAndOperator});
            return ((DealerServiceInfo[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListBySpecCodeListAsync(string[] charSpecCodeList, System.Nullable<bool> isAndOperator) {
            this.GetDealerServiceInfoListBySpecCodeListAsync(charSpecCodeList, isAndOperator, null);
        }
        
        /// <remarks/>
        public void GetDealerServiceInfoListBySpecCodeListAsync(string[] charSpecCodeList, System.Nullable<bool> isAndOperator, object userState) {
            if ((this.GetDealerServiceInfoListBySpecCodeListOperationCompleted == null)) {
                this.GetDealerServiceInfoListBySpecCodeListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerServiceInfoListBySpecCodeListOperationCompleted);
            }
            this.InvokeAsync("GetDealerServiceInfoListBySpecCodeList", new object[] {
                        charSpecCodeList,
                        isAndOperator}, this.GetDealerServiceInfoListBySpecCodeListOperationCompleted, userState);
        }
        
        private void OnGetDealerServiceInfoListBySpecCodeListOperationCompleted(object arg) {
            if ((this.GetDealerServiceInfoListBySpecCodeListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerServiceInfoListBySpecCodeListCompleted(this, new GetDealerServiceInfoListBySpecCodeListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisDealerSdpBS/GetDealerList", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DealerInfo[] GetDealerList(string referanceDealerCd, string referanceCountryCd, string[] charSpecCodeListIn, string[] charSpecCodeListNotIn) {
            object[] results = this.Invoke("GetDealerList", new object[] {
                        referanceDealerCd,
                        referanceCountryCd,
                        charSpecCodeListIn,
                        charSpecCodeListNotIn});
            return ((DealerInfo[])(results[0]));
        }
        
        /// <remarks/>
        public void GetDealerListAsync(string referanceDealerCd, string referanceCountryCd, string[] charSpecCodeListIn, string[] charSpecCodeListNotIn) {
            this.GetDealerListAsync(referanceDealerCd, referanceCountryCd, charSpecCodeListIn, charSpecCodeListNotIn, null);
        }
        
        /// <remarks/>
        public void GetDealerListAsync(string referanceDealerCd, string referanceCountryCd, string[] charSpecCodeListIn, string[] charSpecCodeListNotIn, object userState) {
            if ((this.GetDealerListOperationCompleted == null)) {
                this.GetDealerListOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDealerListOperationCompleted);
            }
            this.InvokeAsync("GetDealerList", new object[] {
                        referanceDealerCd,
                        referanceCountryCd,
                        charSpecCodeListIn,
                        charSpecCodeListNotIn}, this.GetDealerListOperationCompleted, userState);
        }
        
        private void OnGetDealerListOperationCompleted(object arg) {
            if ((this.GetDealerListCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDealerListCompleted(this, new GetDealerListCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class TokenData {
        
        private string tokenField;
        
        private System.DateTime expireAtField;
        
        /// <remarks/>
        public string Token {
            get {
                return this.tokenField;
            }
            set {
                this.tokenField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ExpireAt {
            get {
                return this.expireAtField;
            }
            set {
                this.expireAtField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccPartyRoleRel {
        
        private long partyRoleAccountIdField;
        
        private string partyRoleAccPartyRoleRelTypeCdField;
        
        private long partyRoleIdField;
        
        private string dealerCdField;
        
        private string organisationNameField;
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccPartyRoleRelTypeCd {
            get {
                return this.partyRoleAccPartyRoleRelTypeCdField;
            }
            set {
                this.partyRoleAccPartyRoleRelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationName {
            get {
                return this.organisationNameField;
            }
            set {
                this.organisationNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerSiteVisit))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourcesInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyProfileMemberInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DocumentInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IrisPlusUserInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GeoAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourceLocationSpec))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleAccountCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PaymentInstrument))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleAccount))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KeyValueItem))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Individual))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisation))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRole))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerCollectionQuotaInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(VehicleGpsLocation))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GpsData))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerCriteria))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(CaseReasonDealerRel))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionToken))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Phone))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GpsInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ContactMedium))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PhoneInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountEmail))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AddressInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GeoLocationInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerServiceRegion))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerServiceInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerServiceInfoIttp))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class BaseClass {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerInfo : BaseClass {
        
        private string dealerCodeField;
        
        private string dealerNameField;
        
        private string cityField;
        
        private string districtField;
        
        private long stockLocationIdField;
        
        private long dealerPartyRoleIdField;
        
        /// <remarks/>
        public string DealerCode {
            get {
                return this.dealerCodeField;
            }
            set {
                this.dealerCodeField = value;
            }
        }
        
        /// <remarks/>
        public string DealerName {
            get {
                return this.dealerNameField;
            }
            set {
                this.dealerNameField = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        public long StockLocationId {
            get {
                return this.stockLocationIdField;
            }
            set {
                this.stockLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public long DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerSiteVisit : BaseClass {
        
        private System.Nullable<long> dealerSiteVisitIdField;
        
        private System.Nullable<long> userPartyRoleIdField;
        
        private System.Nullable<long> dealerPartyRoleIdField;
        
        private string organisationCdField;
        
        private System.Nullable<long> organisationIdField;
        
        private string organisationNameField;
        
        private string branchNameField;
        
        private string branchCdField;
        
        private int isBranchField;
        
        private System.Nullable<System.DateTime> siteVisitStartDateField;
        
        private string dealerSiteVisitStatusTCdField;
        
        private string siteVisitOpenDescriptionField;
        
        private System.Nullable<System.DateTime> siteVisitEndDateField;
        
        private string siteVisitCloseDescriptionField;
        
        private decimal initiationLatitudeField;
        
        private decimal terminationLatitudeField;
        
        private decimal initiationLongitudeField;
        
        private decimal terminationLongitudeField;
        
        private System.Nullable<long> siteVisitVoteTypeField;
        
        private string logicalDeleteKeyField;
        
        private System.DateTime creationDateField;
        
        private System.DateTime updateDateField;
        
        private long createdByField;
        
        private long updatedByField;
        
        private long createIrisSessionIdField;
        
        private long updateIrisSessionIdField;
        
        private string createdByNameField;
        
        private GpsInfo dealerGpsInfoField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerSiteVisitId {
            get {
                return this.dealerSiteVisitIdField;
            }
            set {
                this.dealerSiteVisitIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> UserPartyRoleId {
            get {
                return this.userPartyRoleIdField;
            }
            set {
                this.userPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationCd {
            get {
                return this.organisationCdField;
            }
            set {
                this.organisationCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationName {
            get {
                return this.organisationNameField;
            }
            set {
                this.organisationNameField = value;
            }
        }
        
        /// <remarks/>
        public string BranchName {
            get {
                return this.branchNameField;
            }
            set {
                this.branchNameField = value;
            }
        }
        
        /// <remarks/>
        public string BranchCd {
            get {
                return this.branchCdField;
            }
            set {
                this.branchCdField = value;
            }
        }
        
        /// <remarks/>
        public int IsBranch {
            get {
                return this.isBranchField;
            }
            set {
                this.isBranchField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> SiteVisitStartDate {
            get {
                return this.siteVisitStartDateField;
            }
            set {
                this.siteVisitStartDateField = value;
            }
        }
        
        /// <remarks/>
        public string DealerSiteVisitStatusTCd {
            get {
                return this.dealerSiteVisitStatusTCdField;
            }
            set {
                this.dealerSiteVisitStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string SiteVisitOpenDescription {
            get {
                return this.siteVisitOpenDescriptionField;
            }
            set {
                this.siteVisitOpenDescriptionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> SiteVisitEndDate {
            get {
                return this.siteVisitEndDateField;
            }
            set {
                this.siteVisitEndDateField = value;
            }
        }
        
        /// <remarks/>
        public string SiteVisitCloseDescription {
            get {
                return this.siteVisitCloseDescriptionField;
            }
            set {
                this.siteVisitCloseDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public decimal InitiationLatitude {
            get {
                return this.initiationLatitudeField;
            }
            set {
                this.initiationLatitudeField = value;
            }
        }
        
        /// <remarks/>
        public decimal TerminationLatitude {
            get {
                return this.terminationLatitudeField;
            }
            set {
                this.terminationLatitudeField = value;
            }
        }
        
        /// <remarks/>
        public decimal InitiationLongitude {
            get {
                return this.initiationLongitudeField;
            }
            set {
                this.initiationLongitudeField = value;
            }
        }
        
        /// <remarks/>
        public decimal TerminationLongitude {
            get {
                return this.terminationLongitudeField;
            }
            set {
                this.terminationLongitudeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> SiteVisitVoteType {
            get {
                return this.siteVisitVoteTypeField;
            }
            set {
                this.siteVisitVoteTypeField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public long CreateIrisSessionId {
            get {
                return this.createIrisSessionIdField;
            }
            set {
                this.createIrisSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public long UpdateIrisSessionId {
            get {
                return this.updateIrisSessionIdField;
            }
            set {
                this.updateIrisSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string CreatedByName {
            get {
                return this.createdByNameField;
            }
            set {
                this.createdByNameField = value;
            }
        }
        
        /// <remarks/>
        public GpsInfo DealerGpsInfo {
            get {
                return this.dealerGpsInfoField;
            }
            set {
                this.dealerGpsInfoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GpsInfo : BaseClass {
        
        private System.Nullable<double> distanceField;
        
        private double latitudeField;
        
        private double longitudeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<double> Distance {
            get {
                return this.distanceField;
            }
            set {
                this.distanceField = value;
            }
        }
        
        /// <remarks/>
        public double Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
            }
        }
        
        /// <remarks/>
        public double Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResourcesInfo : BaseClass {
        
        private long resourcesIdField;
        
        private long resourceLocationSpecIdField;
        
        private long resourceSpecIdField;
        
        private System.Nullable<long> productIdField;
        
        private string stockSpecCdField;
        
        private string stockSpecNameField;
        
        private string stockSpecUnitTypeCdField;
        
        private System.Nullable<int> quantityField;
        
        private string resourceLocationTypeSpecCdField;
        
        private string serialNumberField;
        
        private string resourceSpecNameField;
        
        private string resourceSpecDescriptionField;
        
        private string resourceSpecCdField;
        
        private string resourceSpecTypeCdField;
        
        private string resourceStatusTypeCdField;
        
        /// <remarks/>
        public long ResourcesId {
            get {
                return this.resourcesIdField;
            }
            set {
                this.resourcesIdField = value;
            }
        }
        
        /// <remarks/>
        public long ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public long ResourceSpecId {
            get {
                return this.resourceSpecIdField;
            }
            set {
                this.resourceSpecIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ProductId {
            get {
                return this.productIdField;
            }
            set {
                this.productIdField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecCd {
            get {
                return this.stockSpecCdField;
            }
            set {
                this.stockSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecName {
            get {
                return this.stockSpecNameField;
            }
            set {
                this.stockSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecUnitTypeCd {
            get {
                return this.stockSpecUnitTypeCdField;
            }
            set {
                this.stockSpecUnitTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> Quantity {
            get {
                return this.quantityField;
            }
            set {
                this.quantityField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceLocationTypeSpecCd {
            get {
                return this.resourceLocationTypeSpecCdField;
            }
            set {
                this.resourceLocationTypeSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string SerialNumber {
            get {
                return this.serialNumberField;
            }
            set {
                this.serialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecName {
            get {
                return this.resourceSpecNameField;
            }
            set {
                this.resourceSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecDescription {
            get {
                return this.resourceSpecDescriptionField;
            }
            set {
                this.resourceSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecCd {
            get {
                return this.resourceSpecCdField;
            }
            set {
                this.resourceSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecTypeCd {
            get {
                return this.resourceSpecTypeCdField;
            }
            set {
                this.resourceSpecTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceStatusTypeCd {
            get {
                return this.resourceStatusTypeCdField;
            }
            set {
                this.resourceStatusTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyProfileMemberInfo : BaseClass {
        
        private long partyProfileMemberIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> partyRoleAccountIdField;
        
        private System.Nullable<long> partyProfileIdField;
        
        private string partyProfileTypeCdField;
        
        private string partyRoleTypeCdField;
        
        private string partyProfileNameField;
        
        private string partyProfileDescriptionField;
        
        /// <remarks/>
        public long PartyProfileMemberId {
            get {
                return this.partyProfileMemberIdField;
            }
            set {
                this.partyProfileMemberIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyProfileId {
            get {
                return this.partyProfileIdField;
            }
            set {
                this.partyProfileIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileTypeCd {
            get {
                return this.partyProfileTypeCdField;
            }
            set {
                this.partyProfileTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileName {
            get {
                return this.partyProfileNameField;
            }
            set {
                this.partyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileDescription {
            get {
                return this.partyProfileDescriptionField;
            }
            set {
                this.partyProfileDescriptionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyCharValue : BaseClass {
        
        private long charSpecIdField;
        
        private string charSpecCdField;
        
        private string charSpecNameField;
        
        private string charSpecDescriptionField;
        
        private long partyCharValueIdField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private System.Nullable<long> partyApplicationIdField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        private string partyRoleTypeCdField;
        
        /// <remarks/>
        public long CharSpecId {
            get {
                return this.charSpecIdField;
            }
            set {
                this.charSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCd {
            get {
                return this.charSpecCdField;
            }
            set {
                this.charSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecName {
            get {
                return this.charSpecNameField;
            }
            set {
                this.charSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDescription {
            get {
                return this.charSpecDescriptionField;
            }
            set {
                this.charSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public long PartyCharValueId {
            get {
                return this.partyCharValueIdField;
            }
            set {
                this.partyCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyApplicationId {
            get {
                return this.partyApplicationIdField;
            }
            set {
                this.partyApplicationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DocumentInfo : BaseClass {
        
        private System.Nullable<long> documentIdField;
        
        private string documentSpecNameField;
        
        private string documentFileNameField;
        
        private string documentExtensionField;
        
        private byte[] documentBinaryContentField;
        
        private string titleField;
        
        private string descriptionField;
        
        private string nameField;
        
        private System.Nullable<long> referenceIdField;
        
        private string createrNameField;
        
        private string ipField;
        
        private System.Nullable<System.DateTime> createDateField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentSpecName {
            get {
                return this.documentSpecNameField;
            }
            set {
                this.documentSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentFileName {
            get {
                return this.documentFileNameField;
            }
            set {
                this.documentFileNameField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentExtension {
            get {
                return this.documentExtensionField;
            }
            set {
                this.documentExtensionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] DocumentBinaryContent {
            get {
                return this.documentBinaryContentField;
            }
            set {
                this.documentBinaryContentField = value;
            }
        }
        
        /// <remarks/>
        public string Title {
            get {
                return this.titleField;
            }
            set {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ReferenceId {
            get {
                return this.referenceIdField;
            }
            set {
                this.referenceIdField = value;
            }
        }
        
        /// <remarks/>
        public string CreaterName {
            get {
                return this.createrNameField;
            }
            set {
                this.createrNameField = value;
            }
        }
        
        /// <remarks/>
        public string Ip {
            get {
                return this.ipField;
            }
            set {
                this.ipField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> CreateDate {
            get {
                return this.createDateField;
            }
            set {
                this.createDateField = value;
            }
        }
        
        /// <remarks/>
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IrisPlusUserInfo : BaseClass {
        
        private int bayiIrisIdField;
        
        private int bayiIrisPersonelIdField;
        
        private string dTKimlikNoField;
        
        private string personelFotografAdiField;
        
        private int personelIdField;
        
        private System.Nullable<long> subContractorIdField;
        
        private DocumentInfo personnelPhotoField;
        
        private int userIdField;
        
        private string userTypeField;
        
        /// <remarks/>
        public int BayiIrisId {
            get {
                return this.bayiIrisIdField;
            }
            set {
                this.bayiIrisIdField = value;
            }
        }
        
        /// <remarks/>
        public int BayiIrisPersonelId {
            get {
                return this.bayiIrisPersonelIdField;
            }
            set {
                this.bayiIrisPersonelIdField = value;
            }
        }
        
        /// <remarks/>
        public string DTKimlikNo {
            get {
                return this.dTKimlikNoField;
            }
            set {
                this.dTKimlikNoField = value;
            }
        }
        
        /// <remarks/>
        public string PersonelFotografAdi {
            get {
                return this.personelFotografAdiField;
            }
            set {
                this.personelFotografAdiField = value;
            }
        }
        
        /// <remarks/>
        public int PersonelId {
            get {
                return this.personelIdField;
            }
            set {
                this.personelIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> SubContractorId {
            get {
                return this.subContractorIdField;
            }
            set {
                this.subContractorIdField = value;
            }
        }
        
        /// <remarks/>
        public DocumentInfo PersonnelPhoto {
            get {
                return this.personnelPhotoField;
            }
            set {
                this.personnelPhotoField = value;
            }
        }
        
        /// <remarks/>
        public int UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
            }
        }
        
        /// <remarks/>
        public string UserType {
            get {
                return this.userTypeField;
            }
            set {
                this.userTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GeoAddress : BaseClass {
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string addressLine3Field;
        
        private string addressNameField;
        
        private string addressTextField;
        
        private string cityField;
        
        private string countryField;
        
        private long createdByField;
        
        private System.DateTime creationDateField;
        
        private string descriptionField;
        
        private string districtField;
        
        private string countyField;
        
        private string complexField;
        
        private string complexBlockField;
        
        private string apartmentNameField;
        
        private string apartmentNumberField;
        
        private string apartmentFlatNumberField;
        
        private string avenueField;
        
        private string streetField;
        
        private string geoAddressHeadlineTextField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private string geoAddressTypeCDField;
        
        private long geoLocationIdField;
        
        private long geoLocationId_CityField;
        
        private long geoLocationId_CountryField;
        
        private System.Nullable<long> geoLocationId_StateField;
        
        private System.Nullable<long> geoLocationId_CountyField;
        
        private int isDefaultField;
        
        private string locationTextField;
        
        private string logicalDeleteKeyField;
        
        private long organisationIdField;
        
        private string organisationNameField;
        
        private System.Nullable<long> parentAddressIdField;
        
        private string partyRoleGeoLocRelTypeCDField;
        
        private string partyRoleGeoLocRelTypeNameField;
        
        private string postCodeField;
        
        private string statuField;
        
        private long updatedByField;
        
        private System.DateTime updatedDateField;
        
        private System.Nullable<long> stateIdField;
        
        private string stateField;
        
        private string addressFullTextField;
        
        /// <remarks/>
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine3 {
            get {
                return this.addressLine3Field;
            }
            set {
                this.addressLine3Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressName {
            get {
                return this.addressNameField;
            }
            set {
                this.addressNameField = value;
            }
        }
        
        /// <remarks/>
        public string AddressText {
            get {
                return this.addressTextField;
            }
            set {
                this.addressTextField = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        public string County {
            get {
                return this.countyField;
            }
            set {
                this.countyField = value;
            }
        }
        
        /// <remarks/>
        public string Complex {
            get {
                return this.complexField;
            }
            set {
                this.complexField = value;
            }
        }
        
        /// <remarks/>
        public string ComplexBlock {
            get {
                return this.complexBlockField;
            }
            set {
                this.complexBlockField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentName {
            get {
                return this.apartmentNameField;
            }
            set {
                this.apartmentNameField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentNumber {
            get {
                return this.apartmentNumberField;
            }
            set {
                this.apartmentNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentFlatNumber {
            get {
                return this.apartmentFlatNumberField;
            }
            set {
                this.apartmentFlatNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Avenue {
            get {
                return this.avenueField;
            }
            set {
                this.avenueField = value;
            }
        }
        
        /// <remarks/>
        public string Street {
            get {
                return this.streetField;
            }
            set {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        public string GeoAddressHeadlineText {
            get {
                return this.geoAddressHeadlineTextField;
            }
            set {
                this.geoAddressHeadlineTextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoAddressTypeCD {
            get {
                return this.geoAddressTypeCDField;
            }
            set {
                this.geoAddressTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId {
            get {
                return this.geoLocationIdField;
            }
            set {
                this.geoLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId_City {
            get {
                return this.geoLocationId_CityField;
            }
            set {
                this.geoLocationId_CityField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId_Country {
            get {
                return this.geoLocationId_CountryField;
            }
            set {
                this.geoLocationId_CountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId_State {
            get {
                return this.geoLocationId_StateField;
            }
            set {
                this.geoLocationId_StateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId_County {
            get {
                return this.geoLocationId_CountyField;
            }
            set {
                this.geoLocationId_CountyField = value;
            }
        }
        
        /// <remarks/>
        public int IsDefault {
            get {
                return this.isDefaultField;
            }
            set {
                this.isDefaultField = value;
            }
        }
        
        /// <remarks/>
        public string LocationText {
            get {
                return this.locationTextField;
            }
            set {
                this.locationTextField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public long OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationName {
            get {
                return this.organisationNameField;
            }
            set {
                this.organisationNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentAddressId {
            get {
                return this.parentAddressIdField;
            }
            set {
                this.parentAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleGeoLocRelTypeCD {
            get {
                return this.partyRoleGeoLocRelTypeCDField;
            }
            set {
                this.partyRoleGeoLocRelTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleGeoLocRelTypeName {
            get {
                return this.partyRoleGeoLocRelTypeNameField;
            }
            set {
                this.partyRoleGeoLocRelTypeNameField = value;
            }
        }
        
        /// <remarks/>
        public string PostCode {
            get {
                return this.postCodeField;
            }
            set {
                this.postCodeField = value;
            }
        }
        
        /// <remarks/>
        public string Statu {
            get {
                return this.statuField;
            }
            set {
                this.statuField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdatedDate {
            get {
                return this.updatedDateField;
            }
            set {
                this.updatedDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> StateId {
            get {
                return this.stateIdField;
            }
            set {
                this.stateIdField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        public string AddressFullText {
            get {
                return this.addressFullTextField;
            }
            set {
                this.addressFullTextField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResourceLocationSpec : BaseClass {
        
        private System.Nullable<long> resourceLocationSpecIdField;
        
        private string resLocTypeSpecCdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private string dbsDepoKoduField;
        
        private string descriptionField;
        
        private System.DateTime creationDateField;
        
        private System.DateTime updateDateField;
        
        private System.Nullable<long> createdByField;
        
        private System.Nullable<long> updatedByField;
        
        private string logicalDeleteKeyField;
        
        private string nameField;
        
        private string partyDefaultField;
        
        private string organisationDefaultField;
        
        private System.Nullable<long> parentIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private string houseStatusTypeField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private string dbsOutletLocationField;
        
        private GeoAddress warehouseAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string ResLocTypeSpecCd {
            get {
                return this.resLocTypeSpecCdField;
            }
            set {
                this.resLocTypeSpecCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsDepoKodu {
            get {
                return this.dbsDepoKoduField;
            }
            set {
                this.dbsDepoKoduField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyDefault {
            get {
                return this.partyDefaultField;
            }
            set {
                this.partyDefaultField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationDefault {
            get {
                return this.organisationDefaultField;
            }
            set {
                this.organisationDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentId {
            get {
                return this.parentIdField;
            }
            set {
                this.parentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string HouseStatusType {
            get {
                return this.houseStatusTypeField;
            }
            set {
                this.houseStatusTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        public GeoAddress WarehouseAddress {
            get {
                return this.warehouseAddressField;
            }
            set {
                this.warehouseAddressField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccountCharValue : BaseClass {
        
        private long partyRoleAccountCharValueIdField;
        
        private long partyRoleAccountIdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        /// <remarks/>
        public long PartyRoleAccountCharValueId {
            get {
                return this.partyRoleAccountCharValueIdField;
            }
            set {
                this.partyRoleAccountCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PaymentInstrument : BaseClass {
        
        private long paymentInstrumentIdField;
        
        private string paymentInstrumentTypeCdField;
        
        private string nameField;
        
        private string iBANField;
        
        private string vanNumberField;
        
        private string bankAccountNumberField;
        
        private string bankCdField;
        
        private string branchCodeField;
        
        private string bankGovernmentCodeField;
        
        private string bankPaymentSourceCodeField;
        
        private long billAccountIdField;
        
        private long countryIdField;
        
        private string paymentInstrStatusTCdField;
        
        private string creditCardOwnerNameField;
        
        private string creditCardExpireDateField;
        
        private string creditCardCvvNumberField;
        
        private System.Nullable<long> creditCardIdField;
        
        private string swiftBICField;
        
        private string creditorIdField;
        
        /// <remarks/>
        public long PaymentInstrumentId {
            get {
                return this.paymentInstrumentIdField;
            }
            set {
                this.paymentInstrumentIdField = value;
            }
        }
        
        /// <remarks/>
        public string PaymentInstrumentTypeCd {
            get {
                return this.paymentInstrumentTypeCdField;
            }
            set {
                this.paymentInstrumentTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string IBAN {
            get {
                return this.iBANField;
            }
            set {
                this.iBANField = value;
            }
        }
        
        /// <remarks/>
        public string VanNumber {
            get {
                return this.vanNumberField;
            }
            set {
                this.vanNumberField = value;
            }
        }
        
        /// <remarks/>
        public string BankAccountNumber {
            get {
                return this.bankAccountNumberField;
            }
            set {
                this.bankAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string BankCd {
            get {
                return this.bankCdField;
            }
            set {
                this.bankCdField = value;
            }
        }
        
        /// <remarks/>
        public string BranchCode {
            get {
                return this.branchCodeField;
            }
            set {
                this.branchCodeField = value;
            }
        }
        
        /// <remarks/>
        public string BankGovernmentCode {
            get {
                return this.bankGovernmentCodeField;
            }
            set {
                this.bankGovernmentCodeField = value;
            }
        }
        
        /// <remarks/>
        public string BankPaymentSourceCode {
            get {
                return this.bankPaymentSourceCodeField;
            }
            set {
                this.bankPaymentSourceCodeField = value;
            }
        }
        
        /// <remarks/>
        public long BillAccountId {
            get {
                return this.billAccountIdField;
            }
            set {
                this.billAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public long CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string PaymentInstrStatusTCd {
            get {
                return this.paymentInstrStatusTCdField;
            }
            set {
                this.paymentInstrStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardOwnerName {
            get {
                return this.creditCardOwnerNameField;
            }
            set {
                this.creditCardOwnerNameField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardExpireDate {
            get {
                return this.creditCardExpireDateField;
            }
            set {
                this.creditCardExpireDateField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardCvvNumber {
            get {
                return this.creditCardCvvNumberField;
            }
            set {
                this.creditCardCvvNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CreditCardId {
            get {
                return this.creditCardIdField;
            }
            set {
                this.creditCardIdField = value;
            }
        }
        
        /// <remarks/>
        public string SwiftBIC {
            get {
                return this.swiftBICField;
            }
            set {
                this.swiftBICField = value;
            }
        }
        
        /// <remarks/>
        public string CreditorId {
            get {
                return this.creditorIdField;
            }
            set {
                this.creditorIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccount : BaseClass {
        
        private long parentIdField;
        
        private long partyRoleAccountIdField;
        
        private string partyRoleAccountSpecCdField;
        
        private string partyRoleAccountSpecTCdField;
        
        private string partyRoleAccountStatusTCdField;
        
        private string accountNameField;
        
        private string accountDescriptionField;
        
        private string dbsOutletLocationField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private string satelliteTypeCdField;
        
        private KeyValueItem[] characteristicsField;
        
        private System.Nullable<long> resourceLocationSpecIdField;
        
        private string stbTipiField;
        
        private bool isAccountHeroField;
        
        private string billAmountCurrencyTypeCdField;
        
        private int isDefaultField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private string membershipTypeField;
        
        private PaymentInstrument[] paymentInstrumentListField;
        
        private PartyRoleAccountCharValue[] partyRoleAccountCharValueListField;
        
        private PartyRoleAccPartyRoleRel[] partyRoleAccPartyRoleRelListField;
        
        /// <remarks/>
        public long ParentId {
            get {
                return this.parentIdField;
            }
            set {
                this.parentIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecCd {
            get {
                return this.partyRoleAccountSpecCdField;
            }
            set {
                this.partyRoleAccountSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecTCd {
            get {
                return this.partyRoleAccountSpecTCdField;
            }
            set {
                this.partyRoleAccountSpecTCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountStatusTCd {
            get {
                return this.partyRoleAccountStatusTCdField;
            }
            set {
                this.partyRoleAccountStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string AccountName {
            get {
                return this.accountNameField;
            }
            set {
                this.accountNameField = value;
            }
        }
        
        /// <remarks/>
        public string AccountDescription {
            get {
                return this.accountDescriptionField;
            }
            set {
                this.accountDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public KeyValueItem[] Characteristics {
            get {
                return this.characteristicsField;
            }
            set {
                this.characteristicsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string StbTipi {
            get {
                return this.stbTipiField;
            }
            set {
                this.stbTipiField = value;
            }
        }
        
        /// <remarks/>
        public bool IsAccountHero {
            get {
                return this.isAccountHeroField;
            }
            set {
                this.isAccountHeroField = value;
            }
        }
        
        /// <remarks/>
        public string BillAmountCurrencyTypeCd {
            get {
                return this.billAmountCurrencyTypeCdField;
            }
            set {
                this.billAmountCurrencyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public int IsDefault {
            get {
                return this.isDefaultField;
            }
            set {
                this.isDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string MembershipType {
            get {
                return this.membershipTypeField;
            }
            set {
                this.membershipTypeField = value;
            }
        }
        
        /// <remarks/>
        public PaymentInstrument[] PaymentInstrumentList {
            get {
                return this.paymentInstrumentListField;
            }
            set {
                this.paymentInstrumentListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccountCharValue[] PartyRoleAccountCharValueList {
            get {
                return this.partyRoleAccountCharValueListField;
            }
            set {
                this.partyRoleAccountCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccPartyRoleRel[] PartyRoleAccPartyRoleRelList {
            get {
                return this.partyRoleAccPartyRoleRelListField;
            }
            set {
                this.partyRoleAccPartyRoleRelListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class KeyValueItem : BaseClass {
        
        private string keyField;
        
        private string valueField;
        
        /// <remarks/>
        public string Key {
            get {
                return this.keyField;
            }
            set {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Individual))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisation))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AccountInfo : BaseClass {
        
        private KeyValueItem[] characteristicsField;
        
        private string partyTypeCdField;
        
        private string partyProfileCdField;
        
        private string satelliteTypeCdField;
        
        private System.Nullable<long> partyIdField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> billAccountIdField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private System.Nullable<long> accountLogIdField;
        
        private System.Nullable<long> countryIdField;
        
        private string taxNumberField;
        
        private System.Nullable<long> taxOfficeIdField;
        
        private string partyRoleServiceProviderCdField;
        
        private string partyRoleAccountSpecCdField;
        
        private string partyRoleAccountSpecTypeCdField;
        
        private string partyRoleStatusTypeCdField;
        
        private string partyRoleAccountNameField;
        
        private string partyRoleAccountDescriptionField;
        
        private string statusTypeCdField;
        
        private string partyRoleTypeCdField;
        
        private string contractProfileTypeField;
        
        private string memberTypePartyProfileNameField;
        
        private PartyRole accountPartyRoleField;
        
        /// <remarks/>
        public KeyValueItem[] Characteristics {
            get {
                return this.characteristicsField;
            }
            set {
                this.characteristicsField = value;
            }
        }
        
        /// <remarks/>
        public string PartyTypeCd {
            get {
                return this.partyTypeCdField;
            }
            set {
                this.partyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileCd {
            get {
                return this.partyProfileCdField;
            }
            set {
                this.partyProfileCdField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyId {
            get {
                return this.partyIdField;
            }
            set {
                this.partyIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> BillAccountId {
            get {
                return this.billAccountIdField;
            }
            set {
                this.billAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string TaxNumber {
            get {
                return this.taxNumberField;
            }
            set {
                this.taxNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> TaxOfficeId {
            get {
                return this.taxOfficeIdField;
            }
            set {
                this.taxOfficeIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleServiceProviderCd {
            get {
                return this.partyRoleServiceProviderCdField;
            }
            set {
                this.partyRoleServiceProviderCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecCd {
            get {
                return this.partyRoleAccountSpecCdField;
            }
            set {
                this.partyRoleAccountSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecTypeCd {
            get {
                return this.partyRoleAccountSpecTypeCdField;
            }
            set {
                this.partyRoleAccountSpecTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleStatusTypeCd {
            get {
                return this.partyRoleStatusTypeCdField;
            }
            set {
                this.partyRoleStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountName {
            get {
                return this.partyRoleAccountNameField;
            }
            set {
                this.partyRoleAccountNameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountDescription {
            get {
                return this.partyRoleAccountDescriptionField;
            }
            set {
                this.partyRoleAccountDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string StatusTypeCd {
            get {
                return this.statusTypeCdField;
            }
            set {
                this.statusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContractProfileType {
            get {
                return this.contractProfileTypeField;
            }
            set {
                this.contractProfileTypeField = value;
            }
        }
        
        /// <remarks/>
        public string MemberTypePartyProfileName {
            get {
                return this.memberTypePartyProfileNameField;
            }
            set {
                this.memberTypePartyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole AccountPartyRole {
            get {
                return this.accountPartyRoleField;
            }
            set {
                this.accountPartyRoleField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRole : BaseClass {
        
        private System.Nullable<long> partyRoleIdField;
        
        private string partyRoleCdField;
        
        private string partyRoleTypeCdField;
        
        private string partyTypeCdField;
        
        private string partyRoleServiceProviderCdField;
        
        private string partyRoleStatusTypeCdField;
        
        private System.Nullable<long> loginIdField;
        
        private System.Nullable<long> loginGroupIdField;
        
        private System.Nullable<int> isBranchField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private string dbsSalesAgentCdField;
        
        private string dbsUserCdField;
        
        private string contractProfileTypeField;
        
        private string satelliteTypeCdField;
        
        private string memberTypePartyProfileNameField;
        
        private Organisation organisationInfoField;
        
        private Individual individualInfoField;
        
        private PartyRoleAccount[] partyRoleAccountListField;
        
        private PartyRoleAccount[] serviceAccountListField;
        
        private PartyRoleAccount[] billAccountListField;
        
        private ResourceLocationSpec[] resourceLocationSpecListField;
        
        private KeyValueItem[] accountAccessPermissionsField;
        
        private AccountEmail[] emailListField;
        
        private AddressInfo[] addressInfoListField;
        
        private PhoneInfo[] phoneListField;
        
        private IrisPlusUserInfo irisPlusUserInfoField;
        
        private PartyRoleCharValue[] partyRoleCharValueListField;
        
        private PartyCharValue[] partyCharValueListField;
        
        private PartyProfileMemberInfo[] partyProfileMemberInfoListField;
        
        private string branchNameField;
        
        private string branchDescriptionField;
        
        private string loginUserCdField;
        
        private ResourcesInfo[] resourcesInfoListField;
        
        private string technicalServicePaymentStatusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleCd {
            get {
                return this.partyRoleCdField;
            }
            set {
                this.partyRoleCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyTypeCd {
            get {
                return this.partyTypeCdField;
            }
            set {
                this.partyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleServiceProviderCd {
            get {
                return this.partyRoleServiceProviderCdField;
            }
            set {
                this.partyRoleServiceProviderCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleStatusTypeCd {
            get {
                return this.partyRoleStatusTypeCdField;
            }
            set {
                this.partyRoleStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LoginId {
            get {
                return this.loginIdField;
            }
            set {
                this.loginIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LoginGroupId {
            get {
                return this.loginGroupIdField;
            }
            set {
                this.loginGroupIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> IsBranch {
            get {
                return this.isBranchField;
            }
            set {
                this.isBranchField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsSalesAgentCd {
            get {
                return this.dbsSalesAgentCdField;
            }
            set {
                this.dbsSalesAgentCdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsUserCd {
            get {
                return this.dbsUserCdField;
            }
            set {
                this.dbsUserCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContractProfileType {
            get {
                return this.contractProfileTypeField;
            }
            set {
                this.contractProfileTypeField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MemberTypePartyProfileName {
            get {
                return this.memberTypePartyProfileNameField;
            }
            set {
                this.memberTypePartyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public Organisation OrganisationInfo {
            get {
                return this.organisationInfoField;
            }
            set {
                this.organisationInfoField = value;
            }
        }
        
        /// <remarks/>
        public Individual IndividualInfo {
            get {
                return this.individualInfoField;
            }
            set {
                this.individualInfoField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] PartyRoleAccountList {
            get {
                return this.partyRoleAccountListField;
            }
            set {
                this.partyRoleAccountListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] ServiceAccountList {
            get {
                return this.serviceAccountListField;
            }
            set {
                this.serviceAccountListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] BillAccountList {
            get {
                return this.billAccountListField;
            }
            set {
                this.billAccountListField = value;
            }
        }
        
        /// <remarks/>
        public ResourceLocationSpec[] ResourceLocationSpecList {
            get {
                return this.resourceLocationSpecListField;
            }
            set {
                this.resourceLocationSpecListField = value;
            }
        }
        
        /// <remarks/>
        public KeyValueItem[] AccountAccessPermissions {
            get {
                return this.accountAccessPermissionsField;
            }
            set {
                this.accountAccessPermissionsField = value;
            }
        }
        
        /// <remarks/>
        public AccountEmail[] EmailList {
            get {
                return this.emailListField;
            }
            set {
                this.emailListField = value;
            }
        }
        
        /// <remarks/>
        public AddressInfo[] AddressInfoList {
            get {
                return this.addressInfoListField;
            }
            set {
                this.addressInfoListField = value;
            }
        }
        
        /// <remarks/>
        public PhoneInfo[] PhoneList {
            get {
                return this.phoneListField;
            }
            set {
                this.phoneListField = value;
            }
        }
        
        /// <remarks/>
        public IrisPlusUserInfo IrisPlusUserInfo {
            get {
                return this.irisPlusUserInfoField;
            }
            set {
                this.irisPlusUserInfoField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleCharValue[] PartyRoleCharValueList {
            get {
                return this.partyRoleCharValueListField;
            }
            set {
                this.partyRoleCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyCharValue[] PartyCharValueList {
            get {
                return this.partyCharValueListField;
            }
            set {
                this.partyCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyProfileMemberInfo[] PartyProfileMemberInfoList {
            get {
                return this.partyProfileMemberInfoListField;
            }
            set {
                this.partyProfileMemberInfoListField = value;
            }
        }
        
        /// <remarks/>
        public string BranchName {
            get {
                return this.branchNameField;
            }
            set {
                this.branchNameField = value;
            }
        }
        
        /// <remarks/>
        public string BranchDescription {
            get {
                return this.branchDescriptionField;
            }
            set {
                this.branchDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string LoginUserCd {
            get {
                return this.loginUserCdField;
            }
            set {
                this.loginUserCdField = value;
            }
        }
        
        /// <remarks/>
        public ResourcesInfo[] ResourcesInfoList {
            get {
                return this.resourcesInfoListField;
            }
            set {
                this.resourcesInfoListField = value;
            }
        }
        
        /// <remarks/>
        public string TechnicalServicePaymentStatus {
            get {
                return this.technicalServicePaymentStatusField;
            }
            set {
                this.technicalServicePaymentStatusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Organisation : AccountInfo {
        
        private string dbsDealerCdField;
        
        private string commercialNameField;
        
        private System.Nullable<System.DateTime> licenseAcquireDateField;
        
        private System.Nullable<long> licenseIssuerIdField;
        
        private System.Nullable<long> licenseManicipulatyIdField;
        
        private string licenseOtherManicipulatyField;
        
        private string licenseRegisterNumberField;
        
        private string nameField;
        
        private string organisationCdField;
        
        private string organisationTypeCdField;
        
        private string otherTaxOfficeField;
        
        private string arabicNameField;
        
        /// <remarks/>
        public string DbsDealerCd {
            get {
                return this.dbsDealerCdField;
            }
            set {
                this.dbsDealerCdField = value;
            }
        }
        
        /// <remarks/>
        public string CommercialName {
            get {
                return this.commercialNameField;
            }
            set {
                this.commercialNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> LicenseAcquireDate {
            get {
                return this.licenseAcquireDateField;
            }
            set {
                this.licenseAcquireDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LicenseIssuerId {
            get {
                return this.licenseIssuerIdField;
            }
            set {
                this.licenseIssuerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LicenseManicipulatyId {
            get {
                return this.licenseManicipulatyIdField;
            }
            set {
                this.licenseManicipulatyIdField = value;
            }
        }
        
        /// <remarks/>
        public string LicenseOtherManicipulaty {
            get {
                return this.licenseOtherManicipulatyField;
            }
            set {
                this.licenseOtherManicipulatyField = value;
            }
        }
        
        /// <remarks/>
        public string LicenseRegisterNumber {
            get {
                return this.licenseRegisterNumberField;
            }
            set {
                this.licenseRegisterNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationCd {
            get {
                return this.organisationCdField;
            }
            set {
                this.organisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationTypeCd {
            get {
                return this.organisationTypeCdField;
            }
            set {
                this.organisationTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string OtherTaxOffice {
            get {
                return this.otherTaxOfficeField;
            }
            set {
                this.otherTaxOfficeField = value;
            }
        }
        
        /// <remarks/>
        public string ArabicName {
            get {
                return this.arabicNameField;
            }
            set {
                this.arabicNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Individual : AccountInfo {
        
        private System.Nullable<System.DateTime> birthDateField;
        
        private string birthPlaceField;
        
        private string citizenNumberField;
        
        private string displayNameField;
        
        private string displaySurnameField;
        
        private string fatherNameField;
        
        private string firstNameField;
        
        private string genderTypeCdField;
        
        private string languageTypeCdField;
        
        private string maritalStatusTypeCdField;
        
        private string motherMaidenSurnameField;
        
        private string motherNameField;
        
        private string nationalityTypeCdField;
        
        private string otherProfessionField;
        
        private string otherTaxOfficeField;
        
        private string passportNumberField;
        
        private string professionTypeCdField;
        
        private System.Nullable<long> registeredBookNumberField;
        
        private string registeredCityField;
        
        private string registeredCountryField;
        
        private System.Nullable<long> registeredFamilySequenceNumberField;
        
        private System.Nullable<long> registeredSequenceNumberField;
        
        private string surNameField;
        
        private string titleTypeCdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> BirthDate {
            get {
                return this.birthDateField;
            }
            set {
                this.birthDateField = value;
            }
        }
        
        /// <remarks/>
        public string BirthPlace {
            get {
                return this.birthPlaceField;
            }
            set {
                this.birthPlaceField = value;
            }
        }
        
        /// <remarks/>
        public string CitizenNumber {
            get {
                return this.citizenNumberField;
            }
            set {
                this.citizenNumberField = value;
            }
        }
        
        /// <remarks/>
        public string DisplayName {
            get {
                return this.displayNameField;
            }
            set {
                this.displayNameField = value;
            }
        }
        
        /// <remarks/>
        public string DisplaySurname {
            get {
                return this.displaySurnameField;
            }
            set {
                this.displaySurnameField = value;
            }
        }
        
        /// <remarks/>
        public string FatherName {
            get {
                return this.fatherNameField;
            }
            set {
                this.fatherNameField = value;
            }
        }
        
        /// <remarks/>
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        public string GenderTypeCd {
            get {
                return this.genderTypeCdField;
            }
            set {
                this.genderTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string LanguageTypeCd {
            get {
                return this.languageTypeCdField;
            }
            set {
                this.languageTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MaritalStatusTypeCd {
            get {
                return this.maritalStatusTypeCdField;
            }
            set {
                this.maritalStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MotherMaidenSurname {
            get {
                return this.motherMaidenSurnameField;
            }
            set {
                this.motherMaidenSurnameField = value;
            }
        }
        
        /// <remarks/>
        public string MotherName {
            get {
                return this.motherNameField;
            }
            set {
                this.motherNameField = value;
            }
        }
        
        /// <remarks/>
        public string NationalityTypeCd {
            get {
                return this.nationalityTypeCdField;
            }
            set {
                this.nationalityTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string OtherProfession {
            get {
                return this.otherProfessionField;
            }
            set {
                this.otherProfessionField = value;
            }
        }
        
        /// <remarks/>
        public string OtherTaxOffice {
            get {
                return this.otherTaxOfficeField;
            }
            set {
                this.otherTaxOfficeField = value;
            }
        }
        
        /// <remarks/>
        public string PassportNumber {
            get {
                return this.passportNumberField;
            }
            set {
                this.passportNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ProfessionTypeCd {
            get {
                return this.professionTypeCdField;
            }
            set {
                this.professionTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredBookNumber {
            get {
                return this.registeredBookNumberField;
            }
            set {
                this.registeredBookNumberField = value;
            }
        }
        
        /// <remarks/>
        public string RegisteredCity {
            get {
                return this.registeredCityField;
            }
            set {
                this.registeredCityField = value;
            }
        }
        
        /// <remarks/>
        public string RegisteredCountry {
            get {
                return this.registeredCountryField;
            }
            set {
                this.registeredCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredFamilySequenceNumber {
            get {
                return this.registeredFamilySequenceNumberField;
            }
            set {
                this.registeredFamilySequenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredSequenceNumber {
            get {
                return this.registeredSequenceNumberField;
            }
            set {
                this.registeredSequenceNumberField = value;
            }
        }
        
        /// <remarks/>
        public string SurName {
            get {
                return this.surNameField;
            }
            set {
                this.surNameField = value;
            }
        }
        
        /// <remarks/>
        public string TitleTypeCd {
            get {
                return this.titleTypeCdField;
            }
            set {
                this.titleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AccountEmail : ContactMedium {
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private string emailField;
        
        private System.Nullable<long> accountLogIdField;
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PhoneInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountEmail))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AddressInfo))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ContactMedium : BaseClass {
        
        private System.Nullable<long> contactMediumIdField;
        
        private string contactMediumPurposeTCdField;
        
        private System.Nullable<long> contactMediumRelIdField;
        
        private string contactMediumTypeCdField;
        
        private string contactMediumTechTypeCDField;
        
        private string contactMediumStatusTCdField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ContactMediumId {
            get {
                return this.contactMediumIdField;
            }
            set {
                this.contactMediumIdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumPurposeTCd {
            get {
                return this.contactMediumPurposeTCdField;
            }
            set {
                this.contactMediumPurposeTCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ContactMediumRelId {
            get {
                return this.contactMediumRelIdField;
            }
            set {
                this.contactMediumRelIdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumTypeCd {
            get {
                return this.contactMediumTypeCdField;
            }
            set {
                this.contactMediumTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumTechTypeCD {
            get {
                return this.contactMediumTechTypeCDField;
            }
            set {
                this.contactMediumTechTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumStatusTCd {
            get {
                return this.contactMediumStatusTCdField;
            }
            set {
                this.contactMediumStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PhoneInfo : ContactMedium {
        
        private string countryPrefixNumberField;
        
        private string areaPrefixNumberField;
        
        private string phoneNumberField;
        
        private string extensionNumberField;
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<System.DateTime> processDateField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> accountLogIdField;
        
        private int orderPriorityField;
        
        /// <remarks/>
        public string CountryPrefixNumber {
            get {
                return this.countryPrefixNumberField;
            }
            set {
                this.countryPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string AreaPrefixNumber {
            get {
                return this.areaPrefixNumberField;
            }
            set {
                this.areaPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ExtensionNumber {
            get {
                return this.extensionNumberField;
            }
            set {
                this.extensionNumberField = value;
            }
        }
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ProcessDate {
            get {
                return this.processDateField;
            }
            set {
                this.processDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        public int OrderPriority {
            get {
                return this.orderPriorityField;
            }
            set {
                this.orderPriorityField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AddressInfo : ContactMedium {
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string addressLine3Field;
        
        private string cityField;
        
        private System.Nullable<long> cityIdField;
        
        private string countryField;
        
        private System.Nullable<long> countryIdField;
        
        private string countyField;
        
        private System.Nullable<long> countyIdField;
        
        private string districtField;
        
        private System.Nullable<long> districtIdField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private System.Nullable<long> geoLocationIdField;
        
        private string geoLocationNameField;
        
        private string geoLocationTypeCdField;
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleContactMedRIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<System.DateTime> processDateField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private string stateField;
        
        private System.Nullable<long> stateIdField;
        
        private System.Nullable<long> userIdField;
        
        private string zipCodeField;
        
        private System.Nullable<long> accountLogIdField;
        
        private string complexField;
        
        private string complexBlockField;
        
        private string apartmentNameField;
        
        private string apartmentNumberField;
        
        private string apartmentFlatNumberField;
        
        private string avenueField;
        
        private string streetField;
        
        private string addressFullTextField;
        
        private GpsInfo gpsInfoField;
        
        /// <remarks/>
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine3 {
            get {
                return this.addressLine3Field;
            }
            set {
                this.addressLine3Field = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CityId {
            get {
                return this.cityIdField;
            }
            set {
                this.cityIdField = value;
            }
        }
        
        /// <remarks/>
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string County {
            get {
                return this.countyField;
            }
            set {
                this.countyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountyId {
            get {
                return this.countyIdField;
            }
            set {
                this.countyIdField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DistrictId {
            get {
                return this.districtIdField;
            }
            set {
                this.districtIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId {
            get {
                return this.geoLocationIdField;
            }
            set {
                this.geoLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationName {
            get {
                return this.geoLocationNameField;
            }
            set {
                this.geoLocationNameField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationTypeCd {
            get {
                return this.geoLocationTypeCdField;
            }
            set {
                this.geoLocationTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleContactMedRId {
            get {
                return this.partyRoleContactMedRIdField;
            }
            set {
                this.partyRoleContactMedRIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ProcessDate {
            get {
                return this.processDateField;
            }
            set {
                this.processDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> StateId {
            get {
                return this.stateIdField;
            }
            set {
                this.stateIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
            }
        }
        
        /// <remarks/>
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        public string Complex {
            get {
                return this.complexField;
            }
            set {
                this.complexField = value;
            }
        }
        
        /// <remarks/>
        public string ComplexBlock {
            get {
                return this.complexBlockField;
            }
            set {
                this.complexBlockField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentName {
            get {
                return this.apartmentNameField;
            }
            set {
                this.apartmentNameField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentNumber {
            get {
                return this.apartmentNumberField;
            }
            set {
                this.apartmentNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentFlatNumber {
            get {
                return this.apartmentFlatNumberField;
            }
            set {
                this.apartmentFlatNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Avenue {
            get {
                return this.avenueField;
            }
            set {
                this.avenueField = value;
            }
        }
        
        /// <remarks/>
        public string Street {
            get {
                return this.streetField;
            }
            set {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        public string AddressFullText {
            get {
                return this.addressFullTextField;
            }
            set {
                this.addressFullTextField = value;
            }
        }
        
        /// <remarks/>
        public GpsInfo GpsInfo {
            get {
                return this.gpsInfoField;
            }
            set {
                this.gpsInfoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleCharValue : BaseClass {
        
        private long charSpecIdField;
        
        private string charSpecCdField;
        
        private string charSpecNameField;
        
        private string charSpecDescriptionField;
        
        private long partyRoleCharValueIdField;
        
        private long partyRoleIdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        private string partyRoleTypeCdField;
        
        /// <remarks/>
        public long CharSpecId {
            get {
                return this.charSpecIdField;
            }
            set {
                this.charSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCd {
            get {
                return this.charSpecCdField;
            }
            set {
                this.charSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecName {
            get {
                return this.charSpecNameField;
            }
            set {
                this.charSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDescription {
            get {
                return this.charSpecDescriptionField;
            }
            set {
                this.charSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleCharValueId {
            get {
                return this.partyRoleCharValueIdField;
            }
            set {
                this.partyRoleCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerCollectionQuotaInfo : BaseClass {
        
        private string dealerCdField;
        
        private long collectionQuotaField;
        
        private string collectionQuotaCurrencyField;
        
        private System.DateTime processDateField;
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public long CollectionQuota {
            get {
                return this.collectionQuotaField;
            }
            set {
                this.collectionQuotaField = value;
            }
        }
        
        /// <remarks/>
        public string CollectionQuotaCurrency {
            get {
                return this.collectionQuotaCurrencyField;
            }
            set {
                this.collectionQuotaCurrencyField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ProcessDate {
            get {
                return this.processDateField;
            }
            set {
                this.processDateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class VehicleGpsLocation : BaseClass {
        
        private System.Nullable<float> latitudeField;
        
        private System.Nullable<float> longitudeField;
        
        private string driverCodeField;
        
        private System.Nullable<System.DateTime> locationDateField;
        
        private string plateField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<float> Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<float> Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
            }
        }
        
        /// <remarks/>
        public string DriverCode {
            get {
                return this.driverCodeField;
            }
            set {
                this.driverCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> LocationDate {
            get {
                return this.locationDateField;
            }
            set {
                this.locationDateField = value;
            }
        }
        
        /// <remarks/>
        public string Plate {
            get {
                return this.plateField;
            }
            set {
                this.plateField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GpsData : BaseClass {
        
        private long idField;
        
        private System.DateTime validThruField;
        
        private long speedField;
        
        private long measurementReferanceTypeField;
        
        private char measurementReferanceLastRecordField;
        
        private long measurementReferanceNumberField;
        
        private string measurementTaskCodeField;
        
        private float latitudeField;
        
        private float longitudeField;
        
        private long horzontalDilutionofPrecisionField;
        
        private long positionDilutionofPrecisionField;
        
        private long headingField;
        
        private System.DateTime gpsTimeField;
        
        private float altitudeField;
        
        private long updatedByField;
        
        private System.DateTime updateDateField;
        
        private System.DateTime validFromField;
        
        private long createdByField;
        
        private System.DateTime creationDateField;
        
        private long creationSessionIdField;
        
        private long personalIdField;
        
        private VehicleGpsLocation vehicleLocationField;
        
        /// <remarks/>
        public long Id {
            get {
                return this.idField;
            }
            set {
                this.idField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        public long Speed {
            get {
                return this.speedField;
            }
            set {
                this.speedField = value;
            }
        }
        
        /// <remarks/>
        public long MeasurementReferanceType {
            get {
                return this.measurementReferanceTypeField;
            }
            set {
                this.measurementReferanceTypeField = value;
            }
        }
        
        /// <remarks/>
        public char MeasurementReferanceLastRecord {
            get {
                return this.measurementReferanceLastRecordField;
            }
            set {
                this.measurementReferanceLastRecordField = value;
            }
        }
        
        /// <remarks/>
        public long MeasurementReferanceNumber {
            get {
                return this.measurementReferanceNumberField;
            }
            set {
                this.measurementReferanceNumberField = value;
            }
        }
        
        /// <remarks/>
        public string MeasurementTaskCode {
            get {
                return this.measurementTaskCodeField;
            }
            set {
                this.measurementTaskCodeField = value;
            }
        }
        
        /// <remarks/>
        public float Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
            }
        }
        
        /// <remarks/>
        public float Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
            }
        }
        
        /// <remarks/>
        public long HorzontalDilutionofPrecision {
            get {
                return this.horzontalDilutionofPrecisionField;
            }
            set {
                this.horzontalDilutionofPrecisionField = value;
            }
        }
        
        /// <remarks/>
        public long PositionDilutionofPrecision {
            get {
                return this.positionDilutionofPrecisionField;
            }
            set {
                this.positionDilutionofPrecisionField = value;
            }
        }
        
        /// <remarks/>
        public long Heading {
            get {
                return this.headingField;
            }
            set {
                this.headingField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime GpsTime {
            get {
                return this.gpsTimeField;
            }
            set {
                this.gpsTimeField = value;
            }
        }
        
        /// <remarks/>
        public float Altitude {
            get {
                return this.altitudeField;
            }
            set {
                this.altitudeField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public long CreationSessionId {
            get {
                return this.creationSessionIdField;
            }
            set {
                this.creationSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public long PersonalId {
            get {
                return this.personalIdField;
            }
            set {
                this.personalIdField = value;
            }
        }
        
        /// <remarks/>
        public VehicleGpsLocation VehicleLocation {
            get {
                return this.vehicleLocationField;
            }
            set {
                this.vehicleLocationField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerCriteria : BaseClass {
        
        private string dealerCdEqField;
        
        private string countryNameEqField;
        
        private string cityNameEqField;
        
        private string countyNameEqField;
        
        private string districtNameEqField;
        
        private System.Nullable<int> organisationTypeEqField;
        
        private string dealerNameLikeField;
        
        private System.Nullable<long> regionIdEqField;
        
        private string referansDealerCdField;
        
        private bool isBranchesRequiredField;
        
        private System.Nullable<long> dealerPartyRoleIdEqField;
        
        private bool isNeedCharacteristicsField;
        
        private GpsData gpsField;
        
        /// <remarks/>
        public string DealerCdEq {
            get {
                return this.dealerCdEqField;
            }
            set {
                this.dealerCdEqField = value;
            }
        }
        
        /// <remarks/>
        public string CountryNameEq {
            get {
                return this.countryNameEqField;
            }
            set {
                this.countryNameEqField = value;
            }
        }
        
        /// <remarks/>
        public string CityNameEq {
            get {
                return this.cityNameEqField;
            }
            set {
                this.cityNameEqField = value;
            }
        }
        
        /// <remarks/>
        public string CountyNameEq {
            get {
                return this.countyNameEqField;
            }
            set {
                this.countyNameEqField = value;
            }
        }
        
        /// <remarks/>
        public string DistrictNameEq {
            get {
                return this.districtNameEqField;
            }
            set {
                this.districtNameEqField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> OrganisationTypeEq {
            get {
                return this.organisationTypeEqField;
            }
            set {
                this.organisationTypeEqField = value;
            }
        }
        
        /// <remarks/>
        public string DealerNameLike {
            get {
                return this.dealerNameLikeField;
            }
            set {
                this.dealerNameLikeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegionIdEq {
            get {
                return this.regionIdEqField;
            }
            set {
                this.regionIdEqField = value;
            }
        }
        
        /// <remarks/>
        public string ReferansDealerCd {
            get {
                return this.referansDealerCdField;
            }
            set {
                this.referansDealerCdField = value;
            }
        }
        
        /// <remarks/>
        public bool IsBranchesRequired {
            get {
                return this.isBranchesRequiredField;
            }
            set {
                this.isBranchesRequiredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPartyRoleIdEq {
            get {
                return this.dealerPartyRoleIdEqField;
            }
            set {
                this.dealerPartyRoleIdEqField = value;
            }
        }
        
        /// <remarks/>
        public bool IsNeedCharacteristics {
            get {
                return this.isNeedCharacteristicsField;
            }
            set {
                this.isNeedCharacteristicsField = value;
            }
        }
        
        /// <remarks/>
        public GpsData Gps {
            get {
                return this.gpsField;
            }
            set {
                this.gpsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class CaseReasonDealerRel : BaseClass {
        
        private System.Nullable<long> caseReasonDealerRelIdField;
        
        private long caseReasonIdField;
        
        private string caseReasonCdField;
        
        private string caseReasonNameField;
        
        private long dealerPartyRoleIdField;
        
        private string dealerCdField;
        
        private System.DateTime createDateField;
        
        private System.DateTime updateDateField;
        
        private long createdByField;
        
        private long updatedByField;
        
        private string logicalDeleteKeyField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CaseReasonDealerRelId {
            get {
                return this.caseReasonDealerRelIdField;
            }
            set {
                this.caseReasonDealerRelIdField = value;
            }
        }
        
        /// <remarks/>
        public long CaseReasonId {
            get {
                return this.caseReasonIdField;
            }
            set {
                this.caseReasonIdField = value;
            }
        }
        
        /// <remarks/>
        public string CaseReasonCd {
            get {
                return this.caseReasonCdField;
            }
            set {
                this.caseReasonCdField = value;
            }
        }
        
        /// <remarks/>
        public string CaseReasonName {
            get {
                return this.caseReasonNameField;
            }
            set {
                this.caseReasonNameField = value;
            }
        }
        
        /// <remarks/>
        public long DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreateDate {
            get {
                return this.createDateField;
            }
            set {
                this.createDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SessionToken : BaseClass {
        
        private long sessionIdField;
        
        private string sessionGuidIdField;
        
        /// <remarks/>
        public long SessionId {
            get {
                return this.sessionIdField;
            }
            set {
                this.sessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionGuidId {
            get {
                return this.sessionGuidIdField;
            }
            set {
                this.sessionGuidIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Phone : BaseClass {
        
        private PhoneType typeField;
        
        private string countryPrefixNumberField;
        
        private string areaPrefixNumberField;
        
        private string phoneNumberField;
        
        /// <remarks/>
        public PhoneType Type {
            get {
                return this.typeField;
            }
            set {
                this.typeField = value;
            }
        }
        
        /// <remarks/>
        public string CountryPrefixNumber {
            get {
                return this.countryPrefixNumberField;
            }
            set {
                this.countryPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string AreaPrefixNumber {
            get {
                return this.areaPrefixNumberField;
            }
            set {
                this.areaPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public enum PhoneType {
        
        /// <remarks/>
        EV,
        
        /// <remarks/>
        IS,
        
        /// <remarks/>
        CEP,
        
        /// <remarks/>
        FAX,
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerServiceRegion))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GeoLocationInfo : BaseClass {
        
        private long geoLocationIdField;
        
        private string geoLocationTypeCdField;
        
        private string geoLocationNameField;
        
        private string descriptionField;
        
        private string districtField;
        
        private string countyField;
        
        private string cityField;
        
        private string stateField;
        
        private string countryField;
        
        private System.Nullable<long> parentIdField;
        
        private string geoLocationCodeField;
        
        /// <remarks/>
        public long GeoLocationId {
            get {
                return this.geoLocationIdField;
            }
            set {
                this.geoLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationTypeCd {
            get {
                return this.geoLocationTypeCdField;
            }
            set {
                this.geoLocationTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationName {
            get {
                return this.geoLocationNameField;
            }
            set {
                this.geoLocationNameField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        public string County {
            get {
                return this.countyField;
            }
            set {
                this.countyField = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentId {
            get {
                return this.parentIdField;
            }
            set {
                this.parentIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationCode {
            get {
                return this.geoLocationCodeField;
            }
            set {
                this.geoLocationCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerServiceRegion : GeoLocationInfo {
        
        private string dealerCdField;
        
        private long dealerZoneSpecIdField;
        
        private string dealerZoneSpecCdField;
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public long DealerZoneSpecId {
            get {
                return this.dealerZoneSpecIdField;
            }
            set {
                this.dealerZoneSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string DealerZoneSpecCd {
            get {
                return this.dealerZoneSpecCdField;
            }
            set {
                this.dealerZoneSpecCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DealerServiceInfoIttp))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerServiceInfo : BaseClass {
        
        private string dealerTypeField;
        
        private string fTBrandStatusField;
        
        private string faceToFaceAgentStatusField;
        
        private DealerServiceRegion[] serviceRegionListField;
        
        private string dealerCdField;
        
        private string nameField;
        
        private string organisationCdField;
        
        private string organisationCdDescriptionField;
        
        private string organisationRegionDescriptionField;
        
        private string taxNumberField;
        
        private string taxOfficeNameField;
        
        private AddressInfo dealerAddressField;
        
        private GpsInfo dealerGpsInfoField;
        
        private string[] mailListField;
        
        private string[] charSpecCodeListField;
        
        private Phone[] phoneListField;
        
        private long dealerPartyRoleIdField;
        
        private string dealerSubTypeDescriptionField;
        
        private long dealerSubTypeCdField;
        
        /// <remarks/>
        public string DealerType {
            get {
                return this.dealerTypeField;
            }
            set {
                this.dealerTypeField = value;
            }
        }
        
        /// <remarks/>
        public string FTBrandStatus {
            get {
                return this.fTBrandStatusField;
            }
            set {
                this.fTBrandStatusField = value;
            }
        }
        
        /// <remarks/>
        public string FaceToFaceAgentStatus {
            get {
                return this.faceToFaceAgentStatusField;
            }
            set {
                this.faceToFaceAgentStatusField = value;
            }
        }
        
        /// <remarks/>
        public DealerServiceRegion[] ServiceRegionList {
            get {
                return this.serviceRegionListField;
            }
            set {
                this.serviceRegionListField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationCd {
            get {
                return this.organisationCdField;
            }
            set {
                this.organisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationCdDescription {
            get {
                return this.organisationCdDescriptionField;
            }
            set {
                this.organisationCdDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationRegionDescription {
            get {
                return this.organisationRegionDescriptionField;
            }
            set {
                this.organisationRegionDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string TaxNumber {
            get {
                return this.taxNumberField;
            }
            set {
                this.taxNumberField = value;
            }
        }
        
        /// <remarks/>
        public string TaxOfficeName {
            get {
                return this.taxOfficeNameField;
            }
            set {
                this.taxOfficeNameField = value;
            }
        }
        
        /// <remarks/>
        public AddressInfo DealerAddress {
            get {
                return this.dealerAddressField;
            }
            set {
                this.dealerAddressField = value;
            }
        }
        
        /// <remarks/>
        public GpsInfo DealerGpsInfo {
            get {
                return this.dealerGpsInfoField;
            }
            set {
                this.dealerGpsInfoField = value;
            }
        }
        
        /// <remarks/>
        public string[] MailList {
            get {
                return this.mailListField;
            }
            set {
                this.mailListField = value;
            }
        }
        
        /// <remarks/>
        public string[] CharSpecCodeList {
            get {
                return this.charSpecCodeListField;
            }
            set {
                this.charSpecCodeListField = value;
            }
        }
        
        /// <remarks/>
        public Phone[] PhoneList {
            get {
                return this.phoneListField;
            }
            set {
                this.phoneListField = value;
            }
        }
        
        /// <remarks/>
        public long DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string DealerSubTypeDescription {
            get {
                return this.dealerSubTypeDescriptionField;
            }
            set {
                this.dealerSubTypeDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public long DealerSubTypeCd {
            get {
                return this.dealerSubTypeCdField;
            }
            set {
                this.dealerSubTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DealerServiceInfoIttp : DealerServiceInfo {
        
        private long partyRoleIdField;
        
        private long organisationIdField;
        
        private System.Nullable<long> orgDefaultResourceLocSpecIdField;
        
        private DealerServiceInfoIttp[] branchListField;
        
        private int isBranchField;
        
        private System.Nullable<long> loginIdField;
        
        private PartyRoleCharValue[] dealerCharacteristicsField;
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public long OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrgDefaultResourceLocSpecId {
            get {
                return this.orgDefaultResourceLocSpecIdField;
            }
            set {
                this.orgDefaultResourceLocSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public DealerServiceInfoIttp[] BranchList {
            get {
                return this.branchListField;
            }
            set {
                this.branchListField = value;
            }
        }
        
        /// <remarks/>
        public int IsBranch {
            get {
                return this.isBranchField;
            }
            set {
                this.isBranchField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LoginId {
            get {
                return this.loginIdField;
            }
            set {
                this.loginIdField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleCharValue[] DealerCharacteristics {
            get {
                return this.dealerCharacteristicsField;
            }
            set {
                this.dealerCharacteristicsField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PingCompletedEventHandler(object sender, PingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public long Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((long)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateCompletedEventHandler(object sender, SystemAuthenticateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureCompletedEventHandler(object sender, SystemAuthenticateByCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateTokenCompletedEventHandler(object sender, SystemValidateTokenCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateTokenCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateTokenCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateCultureCompletedEventHandler(object sender, SystemValidateCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateWithExpireCompletedEventHandler(object sender, SystemAuthenticateWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureWithExpireCompletedEventHandler(object sender, SystemAuthenticateByCultureWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerServiceInfoListCompletedEventHandler(object sender, GetDealerServiceInfoListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerServiceInfoListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerServiceInfoListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerServiceInfo[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerServiceInfo[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetCaseReasonDealerRelListCompletedEventHandler(object sender, GetCaseReasonDealerRelListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetCaseReasonDealerRelListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetCaseReasonDealerRelListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public CaseReasonDealerRel[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((CaseReasonDealerRel[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerServiceInfoListByCriteriaCompletedEventHandler(object sender, GetDealerServiceInfoListByCriteriaCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerServiceInfoListByCriteriaCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerServiceInfoListByCriteriaCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerServiceInfoIttp[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerServiceInfoIttp[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerCollectionQuotaInfoCompletedEventHandler(object sender, GetDealerCollectionQuotaInfoCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerCollectionQuotaInfoCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerCollectionQuotaInfoCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerCollectionQuotaInfo Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerCollectionQuotaInfo)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerPartyRoleSRelListCompletedEventHandler(object sender, GetDealerPartyRoleSRelListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerPartyRoleSRelListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerPartyRoleSRelListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public PartyRole[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((PartyRole[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerSiteVisitInfoCompletedEventHandler(object sender, GetDealerSiteVisitInfoCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerSiteVisitInfoCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerSiteVisitInfoCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerSiteVisit[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerSiteVisit[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SaveDealerSiteVisitCompletedEventHandler(object sender, SaveDealerSiteVisitCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SaveDealerSiteVisitCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SaveDealerSiteVisitCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerZoneLocationListCompletedEventHandler(object sender, GetDealerZoneLocationListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerZoneLocationListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerZoneLocationListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerServiceRegion[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerServiceRegion[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerCharacteristicsCompletedEventHandler(object sender, GetDealerCharacteristicsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerCharacteristicsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerCharacteristicsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public PartyRoleCharValue[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((PartyRoleCharValue[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerServiceInfoListBySpecCodeListCompletedEventHandler(object sender, GetDealerServiceInfoListBySpecCodeListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerServiceInfoListBySpecCodeListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerServiceInfoListBySpecCodeListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerServiceInfo[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerServiceInfo[])(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetDealerListCompletedEventHandler(object sender, GetDealerListCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDealerListCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDealerListCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DealerInfo[] Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DealerInfo[])(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591