<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Ping">
    <xs:complexType />
  </xs:element>
  <xs:element name="PingResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="PingResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticate">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="TokenData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Token" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="ExpireAt" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SystemAuthenticateByCultureWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateToken">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateTokenResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateTokenResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CancelOrganisationalSaleRecord">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="requestModel" type="tns:CancelOrganisationalSaleRecordRequestModel" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="CancelOrganisationalSaleRecordRequestModel">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="AccountNumber" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ProspectNumber" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ProductBusinessInterId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="AlterationBusinessInterId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="DigiportCancelDescription" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="IrisCancelDescription" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsDigiportRequest" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="IrisRecordId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="DbsOutletLocation" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="DealerCode" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CancelOrganisationalSaleRecordResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="CancelOrganisationalSaleRecordResult" type="tns:CancelOrganisationalSaleRecordResponseModel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="CancelOrganisationalSaleRecordResponseModel">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="ErrorMessage" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ErrorCode" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseOrganisationalSaleRecord">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="requestModel" type="tns:CloseOrganisationalSaleRecordRequestModel" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="CloseOrganisationalSaleRecordRequestModel">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="AccountNumber" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ProspectNumber" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ProductBusinessInterId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="AlterationBusinessInterId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="DigiportStatus" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsDigiportRequest" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="IrisRecordId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="DbsOutletLocation" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="DealerCode" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="SatelliteType" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="CloseOrganisationalSaleRecordResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="CloseOrganisationalSaleRecordResult" type="tns:CloseOrganisationalSaleRecordResponseModel" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="CloseOrganisationalSaleRecordResponseModel">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="ErrorMessage" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ErrorCode" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>