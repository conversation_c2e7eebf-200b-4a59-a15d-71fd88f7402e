﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.OrganisationalSaleRecordBS {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="BasicHttpBinding_IOrganisationalSaleRecordBS", Namespace="http://tempuri.org/")]
    public partial class OrganisationalSaleRecordBS : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback PingOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateTokenOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback CancelOrganisationalSaleRecordOperationCompleted;
        
        private System.Threading.SendOrPostCallback CloseOrganisationalSaleRecordOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public OrganisationalSaleRecordBS() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_OrganisationalSaleRecordBS_OrganisationalSaleRecordBS;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event PingCompletedEventHandler PingCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateCompletedEventHandler SystemAuthenticateCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureCompletedEventHandler SystemAuthenticateByCultureCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateWithExpireCompletedEventHandler SystemAuthenticateWithExpireCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureWithExpireCompletedEventHandler SystemAuthenticateByCultureWithExpireCompleted;
        
        /// <remarks/>
        public event SystemValidateTokenCompletedEventHandler SystemValidateTokenCompleted;
        
        /// <remarks/>
        public event SystemValidateCultureCompletedEventHandler SystemValidateCultureCompleted;
        
        /// <remarks/>
        public event CancelOrganisationalSaleRecordCompletedEventHandler CancelOrganisationalSaleRecordCompleted;
        
        /// <remarks/>
        public event CloseOrganisationalSaleRecordCompletedEventHandler CloseOrganisationalSaleRecordCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/Ping", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public long Ping() {
            object[] results = this.Invoke("Ping", new object[0]);
            return ((long)(results[0]));
        }
        
        /// <remarks/>
        public void PingAsync() {
            this.PingAsync(null);
        }
        
        /// <remarks/>
        public void PingAsync(object userState) {
            if ((this.PingOperationCompleted == null)) {
                this.PingOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPingOperationCompleted);
            }
            this.InvokeAsync("Ping", new object[0], this.PingOperationCompleted, userState);
        }
        
        private void OnPingOperationCompleted(object arg) {
            if ((this.PingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PingCompleted(this, new PingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticate", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticate(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateOperationCompleted == null)) {
                this.SystemAuthenticateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateOperationCompleted(object arg) {
            if ((this.SystemAuthenticateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateCompleted(this, new SystemAuthenticateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticateByCulture(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureOperationCompleted == null)) {
                this.SystemAuthenticateByCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureCompleted(this, new SystemAuthenticateByCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateWithExpire(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateWithExpireAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateWithExpireCompleted(this, new SystemAuthenticateWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCultureWithExp" +
            "ire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateByCultureWithExpire(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureWithExpireAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateByCultureWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureWithExpireCompleted(this, new SystemAuthenticateByCultureWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateToken", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateToken(string token) {
            object[] results = this.Invoke("SystemValidateToken", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token) {
            this.SystemValidateTokenAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token, object userState) {
            if ((this.SystemValidateTokenOperationCompleted == null)) {
                this.SystemValidateTokenOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateTokenOperationCompleted);
            }
            this.InvokeAsync("SystemValidateToken", new object[] {
                        token}, this.SystemValidateTokenOperationCompleted, userState);
        }
        
        private void OnSystemValidateTokenOperationCompleted(object arg) {
            if ((this.SystemValidateTokenCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateTokenCompleted(this, new SystemValidateTokenCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateCulture(string token) {
            object[] results = this.Invoke("SystemValidateCulture", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token) {
            this.SystemValidateCultureAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token, object userState) {
            if ((this.SystemValidateCultureOperationCompleted == null)) {
                this.SystemValidateCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateCultureOperationCompleted);
            }
            this.InvokeAsync("SystemValidateCulture", new object[] {
                        token}, this.SystemValidateCultureOperationCompleted, userState);
        }
        
        private void OnSystemValidateCultureOperationCompleted(object arg) {
            if ((this.SystemValidateCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateCultureCompleted(this, new SystemValidateCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/CancelOrganisationalSaleRecord", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public CancelOrganisationalSaleRecordResponseModel CancelOrganisationalSaleRecord(CancelOrganisationalSaleRecordRequestModel requestModel, string token) {
            object[] results = this.Invoke("CancelOrganisationalSaleRecord", new object[] {
                        requestModel,
                        token});
            return ((CancelOrganisationalSaleRecordResponseModel)(results[0]));
        }
        
        /// <remarks/>
        public void CancelOrganisationalSaleRecordAsync(CancelOrganisationalSaleRecordRequestModel requestModel, string token) {
            this.CancelOrganisationalSaleRecordAsync(requestModel, token, null);
        }
        
        /// <remarks/>
        public void CancelOrganisationalSaleRecordAsync(CancelOrganisationalSaleRecordRequestModel requestModel, string token, object userState) {
            if ((this.CancelOrganisationalSaleRecordOperationCompleted == null)) {
                this.CancelOrganisationalSaleRecordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCancelOrganisationalSaleRecordOperationCompleted);
            }
            this.InvokeAsync("CancelOrganisationalSaleRecord", new object[] {
                        requestModel,
                        token}, this.CancelOrganisationalSaleRecordOperationCompleted, userState);
        }
        
        private void OnCancelOrganisationalSaleRecordOperationCompleted(object arg) {
            if ((this.CancelOrganisationalSaleRecordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CancelOrganisationalSaleRecordCompleted(this, new CancelOrganisationalSaleRecordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IOrganisationalSaleRecordBS/CloseOrganisationalSaleRecord", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public CloseOrganisationalSaleRecordResponseModel CloseOrganisationalSaleRecord(CloseOrganisationalSaleRecordRequestModel requestModel, string token) {
            object[] results = this.Invoke("CloseOrganisationalSaleRecord", new object[] {
                        requestModel,
                        token});
            return ((CloseOrganisationalSaleRecordResponseModel)(results[0]));
        }
        
        /// <remarks/>
        public void CloseOrganisationalSaleRecordAsync(CloseOrganisationalSaleRecordRequestModel requestModel, string token) {
            this.CloseOrganisationalSaleRecordAsync(requestModel, token, null);
        }
        
        /// <remarks/>
        public void CloseOrganisationalSaleRecordAsync(CloseOrganisationalSaleRecordRequestModel requestModel, string token, object userState) {
            if ((this.CloseOrganisationalSaleRecordOperationCompleted == null)) {
                this.CloseOrganisationalSaleRecordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCloseOrganisationalSaleRecordOperationCompleted);
            }
            this.InvokeAsync("CloseOrganisationalSaleRecord", new object[] {
                        requestModel,
                        token}, this.CloseOrganisationalSaleRecordOperationCompleted, userState);
        }
        
        private void OnCloseOrganisationalSaleRecordOperationCompleted(object arg) {
            if ((this.CloseOrganisationalSaleRecordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CloseOrganisationalSaleRecordCompleted(this, new CloseOrganisationalSaleRecordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class TokenData {
        
        private string tokenField;
        
        private System.DateTime expireAtField;
        
        /// <remarks/>
        public string Token {
            get {
                return this.tokenField;
            }
            set {
                this.tokenField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ExpireAt {
            get {
                return this.expireAtField;
            }
            set {
                this.expireAtField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class CloseOrganisationalSaleRecordResponseModel {
        
        private bool isSuccessField;
        
        private string errorMessageField;
        
        private string errorCodeField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string ErrorMessage {
            get {
                return this.errorMessageField;
            }
            set {
                this.errorMessageField = value;
            }
        }
        
        /// <remarks/>
        public string ErrorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class CloseOrganisationalSaleRecordRequestModel {
        
        private System.Nullable<long> accountNumberField;
        
        private System.Nullable<long> prospectNumberField;
        
        private long serviceAccountIdField;
        
        private long productBusinessInterIdField;
        
        private long alterationBusinessInterIdField;
        
        private string digiportStatusField;
        
        private bool isDigiportRequestField;
        
        private long irisRecordIdField;
        
        private string dbsOutletLocationField;
        
        private string dealerCodeField;
        
        private string satelliteTypeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountNumber {
            get {
                return this.accountNumberField;
            }
            set {
                this.accountNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ProspectNumber {
            get {
                return this.prospectNumberField;
            }
            set {
                this.prospectNumberField = value;
            }
        }
        
        /// <remarks/>
        public long ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public long ProductBusinessInterId {
            get {
                return this.productBusinessInterIdField;
            }
            set {
                this.productBusinessInterIdField = value;
            }
        }
        
        /// <remarks/>
        public long AlterationBusinessInterId {
            get {
                return this.alterationBusinessInterIdField;
            }
            set {
                this.alterationBusinessInterIdField = value;
            }
        }
        
        /// <remarks/>
        public string DigiportStatus {
            get {
                return this.digiportStatusField;
            }
            set {
                this.digiportStatusField = value;
            }
        }
        
        /// <remarks/>
        public bool IsDigiportRequest {
            get {
                return this.isDigiportRequestField;
            }
            set {
                this.isDigiportRequestField = value;
            }
        }
        
        /// <remarks/>
        public long IrisRecordId {
            get {
                return this.irisRecordIdField;
            }
            set {
                this.irisRecordIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCode {
            get {
                return this.dealerCodeField;
            }
            set {
                this.dealerCodeField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteType {
            get {
                return this.satelliteTypeField;
            }
            set {
                this.satelliteTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class CancelOrganisationalSaleRecordResponseModel {
        
        private bool isSuccessField;
        
        private string errorMessageField;
        
        private string errorCodeField;
        
        /// <remarks/>
        public bool IsSuccess {
            get {
                return this.isSuccessField;
            }
            set {
                this.isSuccessField = value;
            }
        }
        
        /// <remarks/>
        public string ErrorMessage {
            get {
                return this.errorMessageField;
            }
            set {
                this.errorMessageField = value;
            }
        }
        
        /// <remarks/>
        public string ErrorCode {
            get {
                return this.errorCodeField;
            }
            set {
                this.errorCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class CancelOrganisationalSaleRecordRequestModel {
        
        private System.Nullable<long> accountNumberField;
        
        private long prospectNumberField;
        
        private long serviceAccountIdField;
        
        private long productBusinessInterIdField;
        
        private long alterationBusinessInterIdField;
        
        private string digiportCancelDescriptionField;
        
        private string irisCancelDescriptionField;
        
        private bool isDigiportRequestField;
        
        private long irisRecordIdField;
        
        private string dbsOutletLocationField;
        
        private string dealerCodeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountNumber {
            get {
                return this.accountNumberField;
            }
            set {
                this.accountNumberField = value;
            }
        }
        
        /// <remarks/>
        public long ProspectNumber {
            get {
                return this.prospectNumberField;
            }
            set {
                this.prospectNumberField = value;
            }
        }
        
        /// <remarks/>
        public long ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public long ProductBusinessInterId {
            get {
                return this.productBusinessInterIdField;
            }
            set {
                this.productBusinessInterIdField = value;
            }
        }
        
        /// <remarks/>
        public long AlterationBusinessInterId {
            get {
                return this.alterationBusinessInterIdField;
            }
            set {
                this.alterationBusinessInterIdField = value;
            }
        }
        
        /// <remarks/>
        public string DigiportCancelDescription {
            get {
                return this.digiportCancelDescriptionField;
            }
            set {
                this.digiportCancelDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string IrisCancelDescription {
            get {
                return this.irisCancelDescriptionField;
            }
            set {
                this.irisCancelDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public bool IsDigiportRequest {
            get {
                return this.isDigiportRequestField;
            }
            set {
                this.isDigiportRequestField = value;
            }
        }
        
        /// <remarks/>
        public long IrisRecordId {
            get {
                return this.irisRecordIdField;
            }
            set {
                this.irisRecordIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCode {
            get {
                return this.dealerCodeField;
            }
            set {
                this.dealerCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PingCompletedEventHandler(object sender, PingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public long Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((long)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateCompletedEventHandler(object sender, SystemAuthenticateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureCompletedEventHandler(object sender, SystemAuthenticateByCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateWithExpireCompletedEventHandler(object sender, SystemAuthenticateWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureWithExpireCompletedEventHandler(object sender, SystemAuthenticateByCultureWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateTokenCompletedEventHandler(object sender, SystemValidateTokenCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateTokenCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateTokenCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateCultureCompletedEventHandler(object sender, SystemValidateCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CancelOrganisationalSaleRecordCompletedEventHandler(object sender, CancelOrganisationalSaleRecordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CancelOrganisationalSaleRecordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CancelOrganisationalSaleRecordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public CancelOrganisationalSaleRecordResponseModel Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((CancelOrganisationalSaleRecordResponseModel)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CloseOrganisationalSaleRecordCompletedEventHandler(object sender, CloseOrganisationalSaleRecordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CloseOrganisationalSaleRecordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CloseOrganisationalSaleRecordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public CloseOrganisationalSaleRecordResponseModel Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((CloseOrganisationalSaleRecordResponseModel)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591