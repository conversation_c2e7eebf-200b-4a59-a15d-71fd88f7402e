<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="OrganisationalSaleRecordBS" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc?xsd=xsd0" namespace="http://tempuri.org/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IOrganisationalSaleRecordBS_Ping_InputMessage">
    <wsdl:part name="parameters" element="tns:Ping" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_Ping_OutputMessage">
    <wsdl:part name="parameters" element="tns:PingResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticate_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticate" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticate_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateByCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCulture" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateByCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpire" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateByCultureWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpire" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemAuthenticateByCultureWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemValidateToken_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateToken" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemValidateToken_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateTokenResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemValidateCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCulture" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_SystemValidateCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_CancelOrganisationalSaleRecord_InputMessage">
    <wsdl:part name="parameters" element="tns:CancelOrganisationalSaleRecord" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_CancelOrganisationalSaleRecord_OutputMessage">
    <wsdl:part name="parameters" element="tns:CancelOrganisationalSaleRecordResponse" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_CloseOrganisationalSaleRecord_InputMessage">
    <wsdl:part name="parameters" element="tns:CloseOrganisationalSaleRecord" />
  </wsdl:message>
  <wsdl:message name="IOrganisationalSaleRecordBS_CloseOrganisationalSaleRecord_OutputMessage">
    <wsdl:part name="parameters" element="tns:CloseOrganisationalSaleRecordResponse" />
  </wsdl:message>
  <wsdl:portType name="IOrganisationalSaleRecordBS">
    <wsdl:operation name="Ping">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/Ping" message="tns:IOrganisationalSaleRecordBS_Ping_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/PingResponse" message="tns:IOrganisationalSaleRecordBS_Ping_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticate" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateResponse" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCulture" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateByCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCultureResponse" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateByCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateWithExpire" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateWithExpireResponse" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCultureWithExpire" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateByCultureWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCultureWithExpireResponse" message="tns:IOrganisationalSaleRecordBS_SystemAuthenticateByCultureWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateToken" message="tns:IOrganisationalSaleRecordBS_SystemValidateToken_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateTokenResponse" message="tns:IOrganisationalSaleRecordBS_SystemValidateToken_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateCulture" message="tns:IOrganisationalSaleRecordBS_SystemValidateCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateCultureResponse" message="tns:IOrganisationalSaleRecordBS_SystemValidateCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CancelOrganisationalSaleRecord">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/CancelOrganisationalSaleRecord" message="tns:IOrganisationalSaleRecordBS_CancelOrganisationalSaleRecord_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/CancelOrganisationalSaleRecordResponse" message="tns:IOrganisationalSaleRecordBS_CancelOrganisationalSaleRecord_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CloseOrganisationalSaleRecord">
      <wsdl:input wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/CloseOrganisationalSaleRecord" message="tns:IOrganisationalSaleRecordBS_CloseOrganisationalSaleRecord_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IOrganisationalSaleRecordBS/CloseOrganisationalSaleRecordResponse" message="tns:IOrganisationalSaleRecordBS_CloseOrganisationalSaleRecord_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IOrganisationalSaleRecordBS" type="tns:IOrganisationalSaleRecordBS">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/Ping" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemAuthenticateByCultureWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateToken" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/SystemValidateCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CancelOrganisationalSaleRecord">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/CancelOrganisationalSaleRecord" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CloseOrganisationalSaleRecord">
      <soap:operation soapAction="http://tempuri.org/IOrganisationalSaleRecordBS/CloseOrganisationalSaleRecord" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="OrganisationalSaleRecordBS">
    <wsdl:port name="BasicHttpBinding_IOrganisationalSaleRecordBS" binding="tns:BasicHttpBinding_IOrganisationalSaleRecordBS">
      <soap:address location="http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>