<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="Ping">
    <xs:complexType />
  </xs:element>
  <xs:element name="PingResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" maxOccurs="1" name="PingResult" type="xs:long" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticate">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="TokenData">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Token" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="ExpireAt" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SystemAuthenticateByCultureWithExpire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="username" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="password" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="companyName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="applicationName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="channelName" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="cultureCode" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemAuthenticateByCultureWithExpireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemAuthenticateByCultureWithExpireResult" type="tns:TokenData" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateToken">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateTokenResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateTokenResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCulture">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="SystemValidateCultureResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SystemValidateCultureResult" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAccountPermissions">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:GetAccountPermissionsRequest" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="GetAccountPermissionsRequest">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="PermissionTypes" type="tns:ArrayOfDataPermissionType" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfDataPermissionType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DataPermissionType" type="tns:DataPermissionType" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="DataPermissionType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="VERI_ISLEME_IZNI" />
      <xs:enumeration value="VERI_PAYLASMA_IZNI" />
      <xs:enumeration value="ILETISIM_IZNI" />
      <xs:enumeration value="COOKIE_IZNI" />
      <xs:enumeration value="AYDINLATMA_METNI" />
      <xs:enumeration value="MESAFELI_SATIS" />
      <xs:enumeration value="HASSAS_VERI_IZNI" />
      <xs:enumeration value="UYELIK_SOZLESME_IZNI" />
      <xs:enumeration value="KVKK_IZNI" />
      <xs:enumeration value="TELIF_IZNI" />
      <xs:enumeration value="IZIN_BILGILENDIRME_METNI" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="GetAccountPermissionsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetAccountPermissionsResult" type="tns:ResponseWrapperOfGetAccountPermissionsResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ResponseWrapperOfGetAccountPermissionsResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="Messages" type="tns:ArrayOfString" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" type="tns:GetAccountPermissionsResponse" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfString">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="GetAccountPermissionsResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Items" type="tns:ArrayOfItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfItem">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Item" nillable="true" type="tns:Item" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Item">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PermissionType" type="tns:DataPermissionType" />
      <xs:element minOccurs="1" maxOccurs="1" name="BillAccountPreferenceId" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ConfirmedDocumentId" nillable="true" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="Content" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsConfirmed" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsLatestDocument" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="ApplicationId" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ClientId" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ChannelId" nillable="true" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="ApplicationName" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ClientName" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ChannelName" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="CreatedBy" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetPermissionText">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:GetPermissionTextRequest" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="GetPermissionTextRequest">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="ClientName" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="PermissionTypes" type="tns:ArrayOfDataPermissionType" />
      <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" nillable="true" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetPermissionTextResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetPermissionTextResult" type="tns:ResponseWrapperOfGetPermissionTextResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ResponseWrapperOfGetPermissionTextResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="Messages" type="tns:ArrayOfString" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" type="tns:GetPermissionTextResponse" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="GetPermissionTextResponse">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Result" type="tns:ArrayOfPermissionItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfPermissionItem">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PermissionItem" nillable="true" type="tns:PermissionItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PermissionItem">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PermissionType" type="tns:DataPermissionType" />
      <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsExist" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="Content" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="ValidFrom" type="xs:dateTime" />
      <xs:element minOccurs="1" maxOccurs="1" name="ValidThru" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IsPermissionExist">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:IsPermissionExistRequest" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="IsPermissionExistRequest">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" nillable="true" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="IsPermissionExistResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="IsPermissionExistResult" type="tns:ResponseWrapperOfIsPermissionExistResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ResponseWrapperOfIsPermissionExistResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="Messages" type="tns:ArrayOfString" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" type="tns:IsPermissionExistResponse" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="IsPermissionExistResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsExist" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsConfirmed" nillable="true" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SaveAccountPermissions">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:SaveAccountPermissionsRequest" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="SaveAccountPermissionsRequest">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="PartyRoleId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="PermissionType" type="tns:DataPermissionType" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsConfirmed" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="DocumentId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="KeyValueList" type="tns:ArrayOfKeyValueAttribute" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfKeyValueAttribute">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueAttribute" nillable="true" type="tns:KeyValueAttribute" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="KeyValueAttribute">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Name" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Value" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SaveAccountPermissionsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SaveAccountPermissionsResult" type="tns:ResponseWrapperOfSaveAccountPermissionsResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ResponseWrapperOfSaveAccountPermissionsResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="Messages" type="tns:ArrayOfString" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" type="tns:SaveAccountPermissionsResponse" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="SaveAccountPermissionsResponse">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsRecorded" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="RecordId" type="xs:long" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GetAccountPermissionsV2">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:GetAccountPermissionsRequest" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionKey" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetAccountPermissionsV2Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="GetAccountPermissionsV2Result" type="tns:ResponseWrapperOfGetAccountPermissionsResponseV2" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ResponseWrapperOfGetAccountPermissionsResponseV2">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="IsSuccess" type="xs:boolean" />
      <xs:element minOccurs="0" maxOccurs="1" name="Messages" type="tns:ArrayOfString" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" type="tns:GetAccountPermissionsResponseV2" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="GetAccountPermissionsResponseV2">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:GetAccountPermissionsResponse">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumPermissions" type="tns:ArrayOfContactMediumPermission" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfContactMediumPermission">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ContactMediumPermission" nillable="true" type="tns:ContactMediumPermission" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ContactMediumPermission">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="ContactMediumId" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="Recipient" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsConfirmed" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsRegistered" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="ContactType" type="tns:IYSContactChannelType" />
      <xs:element minOccurs="0" maxOccurs="1" name="ConfirmationStatusOnIYS" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:simpleType name="IYSContactChannelType">
    <xs:restriction base="xs:string">
      <xs:enumeration value="ARAMA" />
      <xs:enumeration value="EPOSTA" />
      <xs:enumeration value="MESAJ" />
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="SaveAccountPermissionsV2">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="request" type="tns:SaveAccountPermissionsRequestV2" />
        <xs:element minOccurs="0" maxOccurs="1" name="token" type="xs:string" />
        <xs:element minOccurs="0" maxOccurs="1" name="sessionKey" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="SaveAccountPermissionsRequestV2">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:SaveAccountPermissionsRequest">
        <xs:sequence>
          <xs:element minOccurs="0" maxOccurs="1" name="ContactMediumPermissions" type="tns:ArrayOfContactMediumPermissionSave" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:complexType name="ArrayOfContactMediumPermissionSave">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ContactMediumPermissionSave" nillable="true" type="tns:ContactMediumPermissionSave" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ContactMediumPermissionSave">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="ContactMediumId" type="xs:long" />
      <xs:element minOccurs="1" maxOccurs="1" name="IsConfirmed" type="xs:boolean" />
      <xs:element minOccurs="1" maxOccurs="1" name="ContactType" type="tns:IYSContactChannelType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SaveAccountPermissionsV2Response">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="SaveAccountPermissionsV2Result" type="tns:ResponseWrapperOfSaveAccountPermissionsResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>