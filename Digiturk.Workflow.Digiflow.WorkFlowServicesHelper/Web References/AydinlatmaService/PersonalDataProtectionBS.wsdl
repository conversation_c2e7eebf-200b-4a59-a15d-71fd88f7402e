<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="PersonalDataProtectionBS" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc?xsd=xsd0" namespace="http://tempuri.org/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IPersonalDataProtectionBS_Ping_InputMessage">
    <wsdl:part name="parameters" element="tns:Ping" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_Ping_OutputMessage">
    <wsdl:part name="parameters" element="tns:PingResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticate_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticate" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticate_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateByCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCulture" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateByCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpire" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateByCultureWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpire" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemAuthenticateByCultureWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemValidateToken_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateToken" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemValidateToken_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateTokenResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemValidateCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCulture" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SystemValidateCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetAccountPermissions_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAccountPermissions" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetAccountPermissions_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAccountPermissionsResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetPermissionText_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPermissionText" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetPermissionText_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPermissionTextResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_IsPermissionExist_InputMessage">
    <wsdl:part name="parameters" element="tns:IsPermissionExist" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_IsPermissionExist_OutputMessage">
    <wsdl:part name="parameters" element="tns:IsPermissionExistResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SaveAccountPermissions_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveAccountPermissions" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SaveAccountPermissions_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveAccountPermissionsResponse" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetAccountPermissionsV2_InputMessage">
    <wsdl:part name="parameters" element="tns:GetAccountPermissionsV2" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_GetAccountPermissionsV2_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetAccountPermissionsV2Response" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SaveAccountPermissionsV2_InputMessage">
    <wsdl:part name="parameters" element="tns:SaveAccountPermissionsV2" />
  </wsdl:message>
  <wsdl:message name="IPersonalDataProtectionBS_SaveAccountPermissionsV2_OutputMessage">
    <wsdl:part name="parameters" element="tns:SaveAccountPermissionsV2Response" />
  </wsdl:message>
  <wsdl:portType name="IPersonalDataProtectionBS">
    <wsdl:operation name="Ping">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/Ping" message="tns:IPersonalDataProtectionBS_Ping_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/PingResponse" message="tns:IPersonalDataProtectionBS_Ping_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticate" message="tns:IPersonalDataProtectionBS_SystemAuthenticate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateResponse" message="tns:IPersonalDataProtectionBS_SystemAuthenticate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCulture" message="tns:IPersonalDataProtectionBS_SystemAuthenticateByCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCultureResponse" message="tns:IPersonalDataProtectionBS_SystemAuthenticateByCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateWithExpire" message="tns:IPersonalDataProtectionBS_SystemAuthenticateWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateWithExpireResponse" message="tns:IPersonalDataProtectionBS_SystemAuthenticateWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCultureWithExpire" message="tns:IPersonalDataProtectionBS_SystemAuthenticateByCultureWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCultureWithExpireResponse" message="tns:IPersonalDataProtectionBS_SystemAuthenticateByCultureWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateToken" message="tns:IPersonalDataProtectionBS_SystemValidateToken_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateTokenResponse" message="tns:IPersonalDataProtectionBS_SystemValidateToken_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateCulture" message="tns:IPersonalDataProtectionBS_SystemValidateCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateCultureResponse" message="tns:IPersonalDataProtectionBS_SystemValidateCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAccountPermissions">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissions" message="tns:IPersonalDataProtectionBS_GetAccountPermissions_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissionsResponse" message="tns:IPersonalDataProtectionBS_GetAccountPermissions_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPermissionText">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetPermissionText" message="tns:IPersonalDataProtectionBS_GetPermissionText_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetPermissionTextResponse" message="tns:IPersonalDataProtectionBS_GetPermissionText_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="IsPermissionExist">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/IsPermissionExist" message="tns:IPersonalDataProtectionBS_IsPermissionExist_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/IsPermissionExistResponse" message="tns:IPersonalDataProtectionBS_IsPermissionExist_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveAccountPermissions">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissions" message="tns:IPersonalDataProtectionBS_SaveAccountPermissions_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissionsResponse" message="tns:IPersonalDataProtectionBS_SaveAccountPermissions_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetAccountPermissionsV2">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissionsV2" message="tns:IPersonalDataProtectionBS_GetAccountPermissionsV2_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissionsV2Response" message="tns:IPersonalDataProtectionBS_GetAccountPermissionsV2_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SaveAccountPermissionsV2">
      <wsdl:input wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissionsV2" message="tns:IPersonalDataProtectionBS_SaveAccountPermissionsV2_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissionsV2Response" message="tns:IPersonalDataProtectionBS_SaveAccountPermissionsV2_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IPersonalDataProtectionBS" type="tns:IPersonalDataProtectionBS">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/Ping" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemAuthenticateByCultureWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateToken" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SystemValidateCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountPermissions">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetPermissionText">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/GetPermissionText" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsPermissionExist">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/IsPermissionExist" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveAccountPermissions">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissions" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAccountPermissionsV2">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/GetAccountPermissionsV2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SaveAccountPermissionsV2">
      <soap:operation soapAction="http://tempuri.org/IPersonalDataProtectionBS/SaveAccountPermissionsV2" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PersonalDataProtectionBS">
    <wsdl:port name="BasicHttpBinding_IPersonalDataProtectionBS" binding="tns:BasicHttpBinding_IPersonalDataProtectionBS">
      <soap:address location="http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>