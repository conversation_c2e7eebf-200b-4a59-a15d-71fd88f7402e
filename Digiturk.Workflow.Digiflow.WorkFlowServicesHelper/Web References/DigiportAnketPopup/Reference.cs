﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.DigiportAnketPopup {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="DigiportAnketPopupSoap", Namespace="http://tempuri.org/")]
    public partial class DigiportAnketPopup : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback GetAnketPopupInfoYetkiKontrolluOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetAnketPopupInfoYetkiKontrolsuzOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public DigiportAnketPopup() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_DigiportAnketPopup_DigiportAnketPopup;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event GetAnketPopupInfoYetkiKontrolluCompletedEventHandler GetAnketPopupInfoYetkiKontrolluCompleted;
        
        /// <remarks/>
        public event GetAnketPopupInfoYetkiKontrolsuzCompletedEventHandler GetAnketPopupInfoYetkiKontrolsuzCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetAnketPopupInfoYetkiKontrollu", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetAnketPopupInfoYetkiKontrollu(long loginId, string tarih) {
            object[] results = this.Invoke("GetAnketPopupInfoYetkiKontrollu", new object[] {
                        loginId,
                        tarih});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetAnketPopupInfoYetkiKontrolluAsync(long loginId, string tarih) {
            this.GetAnketPopupInfoYetkiKontrolluAsync(loginId, tarih, null);
        }
        
        /// <remarks/>
        public void GetAnketPopupInfoYetkiKontrolluAsync(long loginId, string tarih, object userState) {
            if ((this.GetAnketPopupInfoYetkiKontrolluOperationCompleted == null)) {
                this.GetAnketPopupInfoYetkiKontrolluOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetAnketPopupInfoYetkiKontrolluOperationCompleted);
            }
            this.InvokeAsync("GetAnketPopupInfoYetkiKontrollu", new object[] {
                        loginId,
                        tarih}, this.GetAnketPopupInfoYetkiKontrolluOperationCompleted, userState);
        }
        
        private void OnGetAnketPopupInfoYetkiKontrolluOperationCompleted(object arg) {
            if ((this.GetAnketPopupInfoYetkiKontrolluCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetAnketPopupInfoYetkiKontrolluCompleted(this, new GetAnketPopupInfoYetkiKontrolluCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/GetAnketPopupInfoYetkiKontrolsuz", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string GetAnketPopupInfoYetkiKontrolsuz(long loginId, string tarih) {
            object[] results = this.Invoke("GetAnketPopupInfoYetkiKontrolsuz", new object[] {
                        loginId,
                        tarih});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void GetAnketPopupInfoYetkiKontrolsuzAsync(long loginId, string tarih) {
            this.GetAnketPopupInfoYetkiKontrolsuzAsync(loginId, tarih, null);
        }
        
        /// <remarks/>
        public void GetAnketPopupInfoYetkiKontrolsuzAsync(long loginId, string tarih, object userState) {
            if ((this.GetAnketPopupInfoYetkiKontrolsuzOperationCompleted == null)) {
                this.GetAnketPopupInfoYetkiKontrolsuzOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetAnketPopupInfoYetkiKontrolsuzOperationCompleted);
            }
            this.InvokeAsync("GetAnketPopupInfoYetkiKontrolsuz", new object[] {
                        loginId,
                        tarih}, this.GetAnketPopupInfoYetkiKontrolsuzOperationCompleted, userState);
        }
        
        private void OnGetAnketPopupInfoYetkiKontrolsuzOperationCompleted(object arg) {
            if ((this.GetAnketPopupInfoYetkiKontrolsuzCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetAnketPopupInfoYetkiKontrolsuzCompleted(this, new GetAnketPopupInfoYetkiKontrolsuzCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetAnketPopupInfoYetkiKontrolluCompletedEventHandler(object sender, GetAnketPopupInfoYetkiKontrolluCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetAnketPopupInfoYetkiKontrolluCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetAnketPopupInfoYetkiKontrolluCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetAnketPopupInfoYetkiKontrolsuzCompletedEventHandler(object sender, GetAnketPopupInfoYetkiKontrolsuzCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetAnketPopupInfoYetkiKontrolsuzCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetAnketPopupInfoYetkiKontrolsuzCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591