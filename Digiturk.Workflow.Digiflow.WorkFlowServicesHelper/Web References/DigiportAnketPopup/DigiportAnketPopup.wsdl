<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="GetAnketPopupInfoYetkiKontrollu">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="loginId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="tarih" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAnketPopupInfoYetkiKontrolluResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAnketPopupInfoYetkiKontrolluResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAnketPopupInfoYetkiKontrolsuz">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="loginId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="tarih" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetAnketPopupInfoYetkiKontrolsuzResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetAnketPopupInfoYetkiKontrolsuzResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluSoapIn">
    <wsdl:part name="parameters" element="tns:GetAnketPopupInfoYetkiKontrollu" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluSoapOut">
    <wsdl:part name="parameters" element="tns:GetAnketPopupInfoYetkiKontrolluResponse" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzSoapIn">
    <wsdl:part name="parameters" element="tns:GetAnketPopupInfoYetkiKontrolsuz" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzSoapOut">
    <wsdl:part name="parameters" element="tns:GetAnketPopupInfoYetkiKontrolsuzResponse" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluHttpGetIn">
    <wsdl:part name="loginId" type="s:string" />
    <wsdl:part name="tarih" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzHttpGetIn">
    <wsdl:part name="loginId" type="s:string" />
    <wsdl:part name="tarih" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluHttpPostIn">
    <wsdl:part name="loginId" type="s:string" />
    <wsdl:part name="tarih" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolluHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzHttpPostIn">
    <wsdl:part name="loginId" type="s:string" />
    <wsdl:part name="tarih" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GetAnketPopupInfoYetkiKontrolsuzHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="DigiportAnketPopupSoap">
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolluSoapIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolluSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolsuzSoapIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolsuzSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DigiportAnketPopupHttpGet">
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolluHttpGetIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolluHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolsuzHttpGetIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolsuzHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="DigiportAnketPopupHttpPost">
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolluHttpPostIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolluHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <wsdl:input message="tns:GetAnketPopupInfoYetkiKontrolsuzHttpPostIn" />
      <wsdl:output message="tns:GetAnketPopupInfoYetkiKontrolsuzHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="DigiportAnketPopupSoap" type="tns:DigiportAnketPopupSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <soap:operation soapAction="http://tempuri.org/GetAnketPopupInfoYetkiKontrollu" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <soap:operation soapAction="http://tempuri.org/GetAnketPopupInfoYetkiKontrolsuz" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DigiportAnketPopupSoap12" type="tns:DigiportAnketPopupSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <soap12:operation soapAction="http://tempuri.org/GetAnketPopupInfoYetkiKontrollu" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <soap12:operation soapAction="http://tempuri.org/GetAnketPopupInfoYetkiKontrolsuz" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DigiportAnketPopupHttpGet" type="tns:DigiportAnketPopupHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <http:operation location="/GetAnketPopupInfoYetkiKontrollu" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <http:operation location="/GetAnketPopupInfoYetkiKontrolsuz" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="DigiportAnketPopupHttpPost" type="tns:DigiportAnketPopupHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrollu">
      <http:operation location="/GetAnketPopupInfoYetkiKontrollu" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetAnketPopupInfoYetkiKontrolsuz">
      <http:operation location="/GetAnketPopupInfoYetkiKontrolsuz" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="DigiportAnketPopup">
    <wsdl:port name="DigiportAnketPopupSoap" binding="tns:DigiportAnketPopupSoap">
      <soap:address location="http://dtl4sptst1:93/WebServices/DigiportAnketPopup.asmx" />
    </wsdl:port>
    <wsdl:port name="DigiportAnketPopupSoap12" binding="tns:DigiportAnketPopupSoap12">
      <soap12:address location="http://dtl4sptst1:93/WebServices/DigiportAnketPopup.asmx" />
    </wsdl:port>
    <wsdl:port name="DigiportAnketPopupHttpGet" binding="tns:DigiportAnketPopupHttpGet">
      <http:address location="http://dtl4sptst1:93/WebServices/DigiportAnketPopup.asmx" />
    </wsdl:port>
    <wsdl:port name="DigiportAnketPopupHttpPost" binding="tns:DigiportAnketPopupHttpPost">
      <http:address location="http://dtl4sptst1:93/WebServices/DigiportAnketPopup.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>