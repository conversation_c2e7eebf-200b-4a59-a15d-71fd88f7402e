﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.ServiceRefIris {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="BasicHttpBinding_IIrisSessionSdpBS", Namespace="http://tempuri.org/")]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(BaseClass))]
    public partial class IrisSessionSdpBS : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback PingOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateTokenOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemValidateCultureOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback SystemAuthenticateByCultureWithExpireOperationCompleted;
        
        private System.Threading.SendOrPostCallback ChangePasswordOperationCompleted;
        
        private System.Threading.SendOrPostCallback CheckSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback CloseSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback OpenSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback SessionTransferOperationCompleted;
        
        private System.Threading.SendOrPostCallback SendResetPasswordKeyOperationCompleted;
        
        private System.Threading.SendOrPostCallback ResetPasswordOperationCompleted;
        
        private System.Threading.SendOrPostCallback OpenSimpleSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback CheckSessionByTokenOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetSessionByIrisPlusSessionOperationCompleted;
        
        private System.Threading.SendOrPostCallback CheckLoginIpPermissionOperationCompleted;
        
        private System.Threading.SendOrPostCallback CheckLoginHourPermissionOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public IrisSessionSdpBS() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIris_IrisSessionSdpBS;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event PingCompletedEventHandler PingCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateCompletedEventHandler SystemAuthenticateCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureCompletedEventHandler SystemAuthenticateByCultureCompleted;
        
        /// <remarks/>
        public event SystemValidateTokenCompletedEventHandler SystemValidateTokenCompleted;
        
        /// <remarks/>
        public event SystemValidateCultureCompletedEventHandler SystemValidateCultureCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateWithExpireCompletedEventHandler SystemAuthenticateWithExpireCompleted;
        
        /// <remarks/>
        public event SystemAuthenticateByCultureWithExpireCompletedEventHandler SystemAuthenticateByCultureWithExpireCompleted;
        
        /// <remarks/>
        public event ChangePasswordCompletedEventHandler ChangePasswordCompleted;
        
        /// <remarks/>
        public event CheckSessionCompletedEventHandler CheckSessionCompleted;
        
        /// <remarks/>
        public event CloseSessionCompletedEventHandler CloseSessionCompleted;
        
        /// <remarks/>
        public event OpenSessionCompletedEventHandler OpenSessionCompleted;
        
        /// <remarks/>
        public event SessionTransferCompletedEventHandler SessionTransferCompleted;
        
        /// <remarks/>
        public event SendResetPasswordKeyCompletedEventHandler SendResetPasswordKeyCompleted;
        
        /// <remarks/>
        public event ResetPasswordCompletedEventHandler ResetPasswordCompleted;
        
        /// <remarks/>
        public event OpenSimpleSessionCompletedEventHandler OpenSimpleSessionCompleted;
        
        /// <remarks/>
        public event CheckSessionByTokenCompletedEventHandler CheckSessionByTokenCompleted;
        
        /// <remarks/>
        public event GetSessionByIrisPlusSessionCompletedEventHandler GetSessionByIrisPlusSessionCompleted;
        
        /// <remarks/>
        public event CheckLoginIpPermissionCompletedEventHandler CheckLoginIpPermissionCompleted;
        
        /// <remarks/>
        public event CheckLoginHourPermissionCompletedEventHandler CheckLoginHourPermissionCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/Ping", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public long Ping() {
            object[] results = this.Invoke("Ping", new object[0]);
            return ((long)(results[0]));
        }
        
        /// <remarks/>
        public void PingAsync() {
            this.PingAsync(null);
        }
        
        /// <remarks/>
        public void PingAsync(object userState) {
            if ((this.PingOperationCompleted == null)) {
                this.PingOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPingOperationCompleted);
            }
            this.InvokeAsync("Ping", new object[0], this.PingOperationCompleted, userState);
        }
        
        private void OnPingOperationCompleted(object arg) {
            if ((this.PingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PingCompleted(this, new PingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticate", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticate(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateOperationCompleted == null)) {
                this.SystemAuthenticateOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticate", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateOperationCompleted(object arg) {
            if ((this.SystemAuthenticateCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateCompleted(this, new SystemAuthenticateCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemAuthenticateByCulture(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureOperationCompleted == null)) {
                this.SystemAuthenticateByCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCulture", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureCompleted(this, new SystemAuthenticateByCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemValidateToken", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateToken(string token) {
            object[] results = this.Invoke("SystemValidateToken", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token) {
            this.SystemValidateTokenAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateTokenAsync(string token, object userState) {
            if ((this.SystemValidateTokenOperationCompleted == null)) {
                this.SystemValidateTokenOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateTokenOperationCompleted);
            }
            this.InvokeAsync("SystemValidateToken", new object[] {
                        token}, this.SystemValidateTokenOperationCompleted, userState);
        }
        
        private void OnSystemValidateTokenOperationCompleted(object arg) {
            if ((this.SystemValidateTokenCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateTokenCompleted(this, new SystemValidateTokenCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemValidateCulture", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string SystemValidateCulture(string token) {
            object[] results = this.Invoke("SystemValidateCulture", new object[] {
                        token});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token) {
            this.SystemValidateCultureAsync(token, null);
        }
        
        /// <remarks/>
        public void SystemValidateCultureAsync(string token, object userState) {
            if ((this.SystemValidateCultureOperationCompleted == null)) {
                this.SystemValidateCultureOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemValidateCultureOperationCompleted);
            }
            this.InvokeAsync("SystemValidateCulture", new object[] {
                        token}, this.SystemValidateCultureOperationCompleted, userState);
        }
        
        private void OnSystemValidateCultureOperationCompleted(object arg) {
            if ((this.SystemValidateCultureCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemValidateCultureCompleted(this, new SystemValidateCultureCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateWithExpire(string username, string password, string companyName, string applicationName, string channelName) {
            object[] results = this.Invoke("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName) {
            this.SystemAuthenticateWithExpireAsync(username, password, companyName, applicationName, channelName, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, object userState) {
            if ((this.SystemAuthenticateWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName}, this.SystemAuthenticateWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateWithExpireCompleted(this, new SystemAuthenticateWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCultureWithExpire", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public TokenData SystemAuthenticateByCultureWithExpire(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            object[] results = this.Invoke("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode});
            return ((TokenData)(results[0]));
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode) {
            this.SystemAuthenticateByCultureWithExpireAsync(username, password, companyName, applicationName, channelName, cultureCode, null);
        }
        
        /// <remarks/>
        public void SystemAuthenticateByCultureWithExpireAsync(string username, string password, string companyName, string applicationName, string channelName, string cultureCode, object userState) {
            if ((this.SystemAuthenticateByCultureWithExpireOperationCompleted == null)) {
                this.SystemAuthenticateByCultureWithExpireOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSystemAuthenticateByCultureWithExpireOperationCompleted);
            }
            this.InvokeAsync("SystemAuthenticateByCultureWithExpire", new object[] {
                        username,
                        password,
                        companyName,
                        applicationName,
                        channelName,
                        cultureCode}, this.SystemAuthenticateByCultureWithExpireOperationCompleted, userState);
        }
        
        private void OnSystemAuthenticateByCultureWithExpireOperationCompleted(object arg) {
            if ((this.SystemAuthenticateByCultureWithExpireCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SystemAuthenticateByCultureWithExpireCompleted(this, new SystemAuthenticateByCultureWithExpireCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/ChangePassword", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ResponseBase ChangePassword(string NewPassword, SessionToken sessionToken) {
            object[] results = this.Invoke("ChangePassword", new object[] {
                        NewPassword,
                        sessionToken});
            return ((ResponseBase)(results[0]));
        }
        
        /// <remarks/>
        public void ChangePasswordAsync(string NewPassword, SessionToken sessionToken) {
            this.ChangePasswordAsync(NewPassword, sessionToken, null);
        }
        
        /// <remarks/>
        public void ChangePasswordAsync(string NewPassword, SessionToken sessionToken, object userState) {
            if ((this.ChangePasswordOperationCompleted == null)) {
                this.ChangePasswordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnChangePasswordOperationCompleted);
            }
            this.InvokeAsync("ChangePassword", new object[] {
                        NewPassword,
                        sessionToken}, this.ChangePasswordOperationCompleted, userState);
        }
        
        private void OnChangePasswordOperationCompleted(object arg) {
            if ((this.ChangePasswordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ChangePasswordCompleted(this, new ChangePasswordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/CheckSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public IrisSession CheckSession(SessionRequest checkSessionRequest) {
            object[] results = this.Invoke("CheckSession", new object[] {
                        checkSessionRequest});
            return ((IrisSession)(results[0]));
        }
        
        /// <remarks/>
        public void CheckSessionAsync(SessionRequest checkSessionRequest) {
            this.CheckSessionAsync(checkSessionRequest, null);
        }
        
        /// <remarks/>
        public void CheckSessionAsync(SessionRequest checkSessionRequest, object userState) {
            if ((this.CheckSessionOperationCompleted == null)) {
                this.CheckSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCheckSessionOperationCompleted);
            }
            this.InvokeAsync("CheckSession", new object[] {
                        checkSessionRequest}, this.CheckSessionOperationCompleted, userState);
        }
        
        private void OnCheckSessionOperationCompleted(object arg) {
            if ((this.CheckSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CheckSessionCompleted(this, new CheckSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/CloseSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool CloseSession(SessionToken sessionToken) {
            object[] results = this.Invoke("CloseSession", new object[] {
                        sessionToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void CloseSessionAsync(SessionToken sessionToken) {
            this.CloseSessionAsync(sessionToken, null);
        }
        
        /// <remarks/>
        public void CloseSessionAsync(SessionToken sessionToken, object userState) {
            if ((this.CloseSessionOperationCompleted == null)) {
                this.CloseSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCloseSessionOperationCompleted);
            }
            this.InvokeAsync("CloseSession", new object[] {
                        sessionToken}, this.CloseSessionOperationCompleted, userState);
        }
        
        private void OnCloseSessionOperationCompleted(object arg) {
            if ((this.CloseSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CloseSessionCompleted(this, new CloseSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/OpenSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public IrisSession OpenSession(LoginRequest loginRequest) {
            object[] results = this.Invoke("OpenSession", new object[] {
                        loginRequest});
            return ((IrisSession)(results[0]));
        }
        
        /// <remarks/>
        public void OpenSessionAsync(LoginRequest loginRequest) {
            this.OpenSessionAsync(loginRequest, null);
        }
        
        /// <remarks/>
        public void OpenSessionAsync(LoginRequest loginRequest, object userState) {
            if ((this.OpenSessionOperationCompleted == null)) {
                this.OpenSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOpenSessionOperationCompleted);
            }
            this.InvokeAsync("OpenSession", new object[] {
                        loginRequest}, this.OpenSessionOperationCompleted, userState);
        }
        
        private void OnOpenSessionOperationCompleted(object arg) {
            if ((this.OpenSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OpenSessionCompleted(this, new OpenSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SessionTransfer", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public SessionInfo SessionTransfer(SessionToken sessionToken, SessionRequest newSessionRequest) {
            object[] results = this.Invoke("SessionTransfer", new object[] {
                        sessionToken,
                        newSessionRequest});
            return ((SessionInfo)(results[0]));
        }
        
        /// <remarks/>
        public void SessionTransferAsync(SessionToken sessionToken, SessionRequest newSessionRequest) {
            this.SessionTransferAsync(sessionToken, newSessionRequest, null);
        }
        
        /// <remarks/>
        public void SessionTransferAsync(SessionToken sessionToken, SessionRequest newSessionRequest, object userState) {
            if ((this.SessionTransferOperationCompleted == null)) {
                this.SessionTransferOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSessionTransferOperationCompleted);
            }
            this.InvokeAsync("SessionTransfer", new object[] {
                        sessionToken,
                        newSessionRequest}, this.SessionTransferOperationCompleted, userState);
        }
        
        private void OnSessionTransferOperationCompleted(object arg) {
            if ((this.SessionTransferCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SessionTransferCompleted(this, new SessionTransferCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/SendResetPasswordKey", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool SendResetPasswordKey(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, ClientToken clientToken) {
            object[] results = this.Invoke("SendResetPasswordKey", new object[] {
                        userLoginCd,
                        mailAddress,
                        phoneInfo,
                        clientToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void SendResetPasswordKeyAsync(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, ClientToken clientToken) {
            this.SendResetPasswordKeyAsync(userLoginCd, mailAddress, phoneInfo, clientToken, null);
        }
        
        /// <remarks/>
        public void SendResetPasswordKeyAsync(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, ClientToken clientToken, object userState) {
            if ((this.SendResetPasswordKeyOperationCompleted == null)) {
                this.SendResetPasswordKeyOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSendResetPasswordKeyOperationCompleted);
            }
            this.InvokeAsync("SendResetPasswordKey", new object[] {
                        userLoginCd,
                        mailAddress,
                        phoneInfo,
                        clientToken}, this.SendResetPasswordKeyOperationCompleted, userState);
        }
        
        private void OnSendResetPasswordKeyOperationCompleted(object arg) {
            if ((this.SendResetPasswordKeyCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SendResetPasswordKeyCompleted(this, new SendResetPasswordKeyCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/ResetPassword", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool ResetPassword(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, string passwordResetKey, string newPassword, ClientToken clientToken) {
            object[] results = this.Invoke("ResetPassword", new object[] {
                        userLoginCd,
                        mailAddress,
                        phoneInfo,
                        passwordResetKey,
                        newPassword,
                        clientToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void ResetPasswordAsync(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, string passwordResetKey, string newPassword, ClientToken clientToken) {
            this.ResetPasswordAsync(userLoginCd, mailAddress, phoneInfo, passwordResetKey, newPassword, clientToken, null);
        }
        
        /// <remarks/>
        public void ResetPasswordAsync(string userLoginCd, string mailAddress, PhoneInfo phoneInfo, string passwordResetKey, string newPassword, ClientToken clientToken, object userState) {
            if ((this.ResetPasswordOperationCompleted == null)) {
                this.ResetPasswordOperationCompleted = new System.Threading.SendOrPostCallback(this.OnResetPasswordOperationCompleted);
            }
            this.InvokeAsync("ResetPassword", new object[] {
                        userLoginCd,
                        mailAddress,
                        phoneInfo,
                        passwordResetKey,
                        newPassword,
                        clientToken}, this.ResetPasswordOperationCompleted, userState);
        }
        
        private void OnResetPasswordOperationCompleted(object arg) {
            if ((this.ResetPasswordCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ResetPasswordCompleted(this, new ResetPasswordCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/OpenSimpleSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public SessionToken OpenSimpleSession(ClientSimpleLoginRequest clientSimpleLoginRequest) {
            object[] results = this.Invoke("OpenSimpleSession", new object[] {
                        clientSimpleLoginRequest});
            return ((SessionToken)(results[0]));
        }
        
        /// <remarks/>
        public void OpenSimpleSessionAsync(ClientSimpleLoginRequest clientSimpleLoginRequest) {
            this.OpenSimpleSessionAsync(clientSimpleLoginRequest, null);
        }
        
        /// <remarks/>
        public void OpenSimpleSessionAsync(ClientSimpleLoginRequest clientSimpleLoginRequest, object userState) {
            if ((this.OpenSimpleSessionOperationCompleted == null)) {
                this.OpenSimpleSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnOpenSimpleSessionOperationCompleted);
            }
            this.InvokeAsync("OpenSimpleSession", new object[] {
                        clientSimpleLoginRequest}, this.OpenSimpleSessionOperationCompleted, userState);
        }
        
        private void OnOpenSimpleSessionOperationCompleted(object arg) {
            if ((this.OpenSimpleSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.OpenSimpleSessionCompleted(this, new OpenSimpleSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/CheckSessionByToken", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool CheckSessionByToken(SessionToken sessionToken) {
            object[] results = this.Invoke("CheckSessionByToken", new object[] {
                        sessionToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void CheckSessionByTokenAsync(SessionToken sessionToken) {
            this.CheckSessionByTokenAsync(sessionToken, null);
        }
        
        /// <remarks/>
        public void CheckSessionByTokenAsync(SessionToken sessionToken, object userState) {
            if ((this.CheckSessionByTokenOperationCompleted == null)) {
                this.CheckSessionByTokenOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCheckSessionByTokenOperationCompleted);
            }
            this.InvokeAsync("CheckSessionByToken", new object[] {
                        sessionToken}, this.CheckSessionByTokenOperationCompleted, userState);
        }
        
        private void OnCheckSessionByTokenOperationCompleted(object arg) {
            if ((this.CheckSessionByTokenCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CheckSessionByTokenCompleted(this, new CheckSessionByTokenCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/GetSessionByIrisPlusSession", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public IrisSession GetSessionByIrisPlusSession(SessionToken irisPlusSessionToken) {
            object[] results = this.Invoke("GetSessionByIrisPlusSession", new object[] {
                        irisPlusSessionToken});
            return ((IrisSession)(results[0]));
        }
        
        /// <remarks/>
        public void GetSessionByIrisPlusSessionAsync(SessionToken irisPlusSessionToken) {
            this.GetSessionByIrisPlusSessionAsync(irisPlusSessionToken, null);
        }
        
        /// <remarks/>
        public void GetSessionByIrisPlusSessionAsync(SessionToken irisPlusSessionToken, object userState) {
            if ((this.GetSessionByIrisPlusSessionOperationCompleted == null)) {
                this.GetSessionByIrisPlusSessionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetSessionByIrisPlusSessionOperationCompleted);
            }
            this.InvokeAsync("GetSessionByIrisPlusSession", new object[] {
                        irisPlusSessionToken}, this.GetSessionByIrisPlusSessionOperationCompleted, userState);
        }
        
        private void OnGetSessionByIrisPlusSessionOperationCompleted(object arg) {
            if ((this.GetSessionByIrisPlusSessionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetSessionByIrisPlusSessionCompleted(this, new GetSessionByIrisPlusSessionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/CheckLoginIpPermission", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool CheckLoginIpPermission(string clientIp, SessionToken sessionToken) {
            object[] results = this.Invoke("CheckLoginIpPermission", new object[] {
                        clientIp,
                        sessionToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void CheckLoginIpPermissionAsync(string clientIp, SessionToken sessionToken) {
            this.CheckLoginIpPermissionAsync(clientIp, sessionToken, null);
        }
        
        /// <remarks/>
        public void CheckLoginIpPermissionAsync(string clientIp, SessionToken sessionToken, object userState) {
            if ((this.CheckLoginIpPermissionOperationCompleted == null)) {
                this.CheckLoginIpPermissionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCheckLoginIpPermissionOperationCompleted);
            }
            this.InvokeAsync("CheckLoginIpPermission", new object[] {
                        clientIp,
                        sessionToken}, this.CheckLoginIpPermissionOperationCompleted, userState);
        }
        
        private void OnCheckLoginIpPermissionOperationCompleted(object arg) {
            if ((this.CheckLoginIpPermissionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CheckLoginIpPermissionCompleted(this, new CheckLoginIpPermissionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/IIrisSessionSdpBS/CheckLoginHourPermission", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public bool CheckLoginHourPermission(string clientHour, SessionToken sessionToken) {
            object[] results = this.Invoke("CheckLoginHourPermission", new object[] {
                        clientHour,
                        sessionToken});
            return ((bool)(results[0]));
        }
        
        /// <remarks/>
        public void CheckLoginHourPermissionAsync(string clientHour, SessionToken sessionToken) {
            this.CheckLoginHourPermissionAsync(clientHour, sessionToken, null);
        }
        
        /// <remarks/>
        public void CheckLoginHourPermissionAsync(string clientHour, SessionToken sessionToken, object userState) {
            if ((this.CheckLoginHourPermissionOperationCompleted == null)) {
                this.CheckLoginHourPermissionOperationCompleted = new System.Threading.SendOrPostCallback(this.OnCheckLoginHourPermissionOperationCompleted);
            }
            this.InvokeAsync("CheckLoginHourPermission", new object[] {
                        clientHour,
                        sessionToken}, this.CheckLoginHourPermissionOperationCompleted, userState);
        }
        
        private void OnCheckLoginHourPermissionOperationCompleted(object arg) {
            if ((this.CheckLoginHourPermissionCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.CheckLoginHourPermissionCompleted(this, new CheckLoginHourPermissionCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class TokenData {
        
        private string tokenField;
        
        private System.DateTime expireAtField;
        
        /// <remarks/>
        public string Token {
            get {
                return this.tokenField;
            }
            set {
                this.tokenField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ExpireAt {
            get {
                return this.expireAtField;
            }
            set {
                this.expireAtField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ClientToken {
        
        private string applicationCdField;
        
        private string channelTypeCdField;
        
        private string clientTypeCdField;
        
        private string clientIpField;
        
        private string clientSerialNumberField;
        
        private string clientOperatingSystemField;
        
        private string cultureTypeCdField;
        
        /// <remarks/>
        public string ApplicationCd {
            get {
                return this.applicationCdField;
            }
            set {
                this.applicationCdField = value;
            }
        }
        
        /// <remarks/>
        public string ChannelTypeCd {
            get {
                return this.channelTypeCdField;
            }
            set {
                this.channelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientTypeCd {
            get {
                return this.clientTypeCdField;
            }
            set {
                this.clientTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientIp {
            get {
                return this.clientIpField;
            }
            set {
                this.clientIpField = value;
            }
        }
        
        /// <remarks/>
        public string ClientSerialNumber {
            get {
                return this.clientSerialNumberField;
            }
            set {
                this.clientSerialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ClientOperatingSystem {
            get {
                return this.clientOperatingSystemField;
            }
            set {
                this.clientOperatingSystemField = value;
            }
        }
        
        /// <remarks/>
        public string CultureTypeCd {
            get {
                return this.cultureTypeCdField;
            }
            set {
                this.cultureTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccPartyRoleRel {
        
        private long partyRoleAccountIdField;
        
        private string partyRoleAccPartyRoleRelTypeCdField;
        
        private long partyRoleIdField;
        
        private string dealerCdField;
        
        private string organisationNameField;
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccPartyRoleRelTypeCd {
            get {
                return this.partyRoleAccPartyRoleRelTypeCdField;
            }
            set {
                this.partyRoleAccPartyRoleRelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string DealerCd {
            get {
                return this.dealerCdField;
            }
            set {
                this.dealerCdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationName {
            get {
                return this.organisationNameField;
            }
            set {
                this.organisationNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClientSimpleLoginRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClientLoginRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LoginRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IrisPlusSession))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourcesInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyProfileMemberInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(DocumentInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IrisPlusUserInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GpsInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ContactMedium))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AddressInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PhoneInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountEmail))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(GeoAddress))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResourceLocationSpec))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleAccountCharValue))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PaymentInstrument))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRoleAccount))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(KeyValueItem))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Individual))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisation))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PartyRole))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(IrisSession))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ResponseBase))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionToken))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClientSessionRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionRequest))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class BaseClass {
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ClientSimpleLoginRequest : BaseClass {
        
        private string userLoginCdField;
        
        private string userPasswordField;
        
        /// <remarks/>
        public string UserLoginCd {
            get {
                return this.userLoginCdField;
            }
            set {
                this.userLoginCdField = value;
            }
        }
        
        /// <remarks/>
        public string UserPassword {
            get {
                return this.userPasswordField;
            }
            set {
                this.userPasswordField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(LoginRequest))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ClientLoginRequest : BaseClass {
        
        private string userOrganisationCdField;
        
        private string userLoginCdField;
        
        private string userPasswordField;
        
        private string channelTypeCdField;
        
        private string clientIpField;
        
        private string clientTypeCdField;
        
        private string clientSerialNumberField;
        
        private string clientOperatingSystemField;
        
        private string cultureTypeCdField;
        
        private string applicationCdField;
        
        /// <remarks/>
        public string UserOrganisationCd {
            get {
                return this.userOrganisationCdField;
            }
            set {
                this.userOrganisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string UserLoginCd {
            get {
                return this.userLoginCdField;
            }
            set {
                this.userLoginCdField = value;
            }
        }
        
        /// <remarks/>
        public string UserPassword {
            get {
                return this.userPasswordField;
            }
            set {
                this.userPasswordField = value;
            }
        }
        
        /// <remarks/>
        public string ChannelTypeCd {
            get {
                return this.channelTypeCdField;
            }
            set {
                this.channelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientIp {
            get {
                return this.clientIpField;
            }
            set {
                this.clientIpField = value;
            }
        }
        
        /// <remarks/>
        public string ClientTypeCd {
            get {
                return this.clientTypeCdField;
            }
            set {
                this.clientTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientSerialNumber {
            get {
                return this.clientSerialNumberField;
            }
            set {
                this.clientSerialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ClientOperatingSystem {
            get {
                return this.clientOperatingSystemField;
            }
            set {
                this.clientOperatingSystemField = value;
            }
        }
        
        /// <remarks/>
        public string CultureTypeCd {
            get {
                return this.cultureTypeCdField;
            }
            set {
                this.cultureTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationCd {
            get {
                return this.applicationCdField;
            }
            set {
                this.applicationCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class LoginRequest : ClientLoginRequest {
        
        private string serverHostNameField;
        
        private string serverSessionIdField;
        
        /// <remarks/>
        public string ServerHostName {
            get {
                return this.serverHostNameField;
            }
            set {
                this.serverHostNameField = value;
            }
        }
        
        /// <remarks/>
        public string ServerSessionId {
            get {
                return this.serverSessionIdField;
            }
            set {
                this.serverSessionIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IrisPlusSession : BaseClass {
        
        private long irisPlusSessionIdField;
        
        private string irisPlusSessionGuidIdField;
        
        private string bayiKoduField;
        
        private string kullaniciKoduField;
        
        private string bayiAdiField;
        
        private int bayiIrisIdField;
        
        private int bayiIrisPersonelIdField;
        
        private string personelAdSoyadField;
        
        private System.DateTime sonIslemTarihiField;
        
        private string uygulamaKoduField;
        
        private string sessionDurumField;
        
        private string userTypeField;
        
        private string personelFotografAdiField;
        
        private string ipField;
        
        /// <remarks/>
        public long IrisPlusSessionId {
            get {
                return this.irisPlusSessionIdField;
            }
            set {
                this.irisPlusSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string IrisPlusSessionGuidId {
            get {
                return this.irisPlusSessionGuidIdField;
            }
            set {
                this.irisPlusSessionGuidIdField = value;
            }
        }
        
        /// <remarks/>
        public string BayiKodu {
            get {
                return this.bayiKoduField;
            }
            set {
                this.bayiKoduField = value;
            }
        }
        
        /// <remarks/>
        public string KullaniciKodu {
            get {
                return this.kullaniciKoduField;
            }
            set {
                this.kullaniciKoduField = value;
            }
        }
        
        /// <remarks/>
        public string BayiAdi {
            get {
                return this.bayiAdiField;
            }
            set {
                this.bayiAdiField = value;
            }
        }
        
        /// <remarks/>
        public int BayiIrisId {
            get {
                return this.bayiIrisIdField;
            }
            set {
                this.bayiIrisIdField = value;
            }
        }
        
        /// <remarks/>
        public int BayiIrisPersonelId {
            get {
                return this.bayiIrisPersonelIdField;
            }
            set {
                this.bayiIrisPersonelIdField = value;
            }
        }
        
        /// <remarks/>
        public string PersonelAdSoyad {
            get {
                return this.personelAdSoyadField;
            }
            set {
                this.personelAdSoyadField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime SonIslemTarihi {
            get {
                return this.sonIslemTarihiField;
            }
            set {
                this.sonIslemTarihiField = value;
            }
        }
        
        /// <remarks/>
        public string UygulamaKodu {
            get {
                return this.uygulamaKoduField;
            }
            set {
                this.uygulamaKoduField = value;
            }
        }
        
        /// <remarks/>
        public string SessionDurum {
            get {
                return this.sessionDurumField;
            }
            set {
                this.sessionDurumField = value;
            }
        }
        
        /// <remarks/>
        public string UserType {
            get {
                return this.userTypeField;
            }
            set {
                this.userTypeField = value;
            }
        }
        
        /// <remarks/>
        public string PersonelFotografAdi {
            get {
                return this.personelFotografAdiField;
            }
            set {
                this.personelFotografAdiField = value;
            }
        }
        
        /// <remarks/>
        public string Ip {
            get {
                return this.ipField;
            }
            set {
                this.ipField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResourcesInfo : BaseClass {
        
        private long resourcesIdField;
        
        private long resourceLocationSpecIdField;
        
        private long resourceSpecIdField;
        
        private System.Nullable<long> productIdField;
        
        private string stockSpecCdField;
        
        private string stockSpecNameField;
        
        private string stockSpecUnitTypeCdField;
        
        private System.Nullable<int> quantityField;
        
        private string resourceLocationTypeSpecCdField;
        
        private string serialNumberField;
        
        private string resourceSpecNameField;
        
        private string resourceSpecDescriptionField;
        
        private string resourceSpecCdField;
        
        private string resourceSpecTypeCdField;
        
        private string resourceStatusTypeCdField;
        
        /// <remarks/>
        public long ResourcesId {
            get {
                return this.resourcesIdField;
            }
            set {
                this.resourcesIdField = value;
            }
        }
        
        /// <remarks/>
        public long ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public long ResourceSpecId {
            get {
                return this.resourceSpecIdField;
            }
            set {
                this.resourceSpecIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ProductId {
            get {
                return this.productIdField;
            }
            set {
                this.productIdField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecCd {
            get {
                return this.stockSpecCdField;
            }
            set {
                this.stockSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecName {
            get {
                return this.stockSpecNameField;
            }
            set {
                this.stockSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string StockSpecUnitTypeCd {
            get {
                return this.stockSpecUnitTypeCdField;
            }
            set {
                this.stockSpecUnitTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> Quantity {
            get {
                return this.quantityField;
            }
            set {
                this.quantityField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceLocationTypeSpecCd {
            get {
                return this.resourceLocationTypeSpecCdField;
            }
            set {
                this.resourceLocationTypeSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string SerialNumber {
            get {
                return this.serialNumberField;
            }
            set {
                this.serialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecName {
            get {
                return this.resourceSpecNameField;
            }
            set {
                this.resourceSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecDescription {
            get {
                return this.resourceSpecDescriptionField;
            }
            set {
                this.resourceSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecCd {
            get {
                return this.resourceSpecCdField;
            }
            set {
                this.resourceSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceSpecTypeCd {
            get {
                return this.resourceSpecTypeCdField;
            }
            set {
                this.resourceSpecTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ResourceStatusTypeCd {
            get {
                return this.resourceStatusTypeCdField;
            }
            set {
                this.resourceStatusTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyProfileMemberInfo : BaseClass {
        
        private long partyProfileMemberIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> partyRoleAccountIdField;
        
        private System.Nullable<long> partyProfileIdField;
        
        private string partyProfileTypeCdField;
        
        private string partyRoleTypeCdField;
        
        private string partyProfileNameField;
        
        private string partyProfileDescriptionField;
        
        /// <remarks/>
        public long PartyProfileMemberId {
            get {
                return this.partyProfileMemberIdField;
            }
            set {
                this.partyProfileMemberIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyProfileId {
            get {
                return this.partyProfileIdField;
            }
            set {
                this.partyProfileIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileTypeCd {
            get {
                return this.partyProfileTypeCdField;
            }
            set {
                this.partyProfileTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileName {
            get {
                return this.partyProfileNameField;
            }
            set {
                this.partyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileDescription {
            get {
                return this.partyProfileDescriptionField;
            }
            set {
                this.partyProfileDescriptionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyCharValue : BaseClass {
        
        private long charSpecIdField;
        
        private string charSpecCdField;
        
        private string charSpecNameField;
        
        private string charSpecDescriptionField;
        
        private long partyCharValueIdField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private System.Nullable<long> partyApplicationIdField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        private string partyRoleTypeCdField;
        
        /// <remarks/>
        public long CharSpecId {
            get {
                return this.charSpecIdField;
            }
            set {
                this.charSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCd {
            get {
                return this.charSpecCdField;
            }
            set {
                this.charSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecName {
            get {
                return this.charSpecNameField;
            }
            set {
                this.charSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDescription {
            get {
                return this.charSpecDescriptionField;
            }
            set {
                this.charSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public long PartyCharValueId {
            get {
                return this.partyCharValueIdField;
            }
            set {
                this.partyCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyApplicationId {
            get {
                return this.partyApplicationIdField;
            }
            set {
                this.partyApplicationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleCharValue : BaseClass {
        
        private long charSpecIdField;
        
        private string charSpecCdField;
        
        private string charSpecNameField;
        
        private string charSpecDescriptionField;
        
        private long partyRoleCharValueIdField;
        
        private long partyRoleIdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        private string partyRoleTypeCdField;
        
        /// <remarks/>
        public long CharSpecId {
            get {
                return this.charSpecIdField;
            }
            set {
                this.charSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCd {
            get {
                return this.charSpecCdField;
            }
            set {
                this.charSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecName {
            get {
                return this.charSpecNameField;
            }
            set {
                this.charSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDescription {
            get {
                return this.charSpecDescriptionField;
            }
            set {
                this.charSpecDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleCharValueId {
            get {
                return this.partyRoleCharValueIdField;
            }
            set {
                this.partyRoleCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class DocumentInfo : BaseClass {
        
        private System.Nullable<long> documentIdField;
        
        private string documentSpecNameField;
        
        private string documentFileNameField;
        
        private string documentExtensionField;
        
        private byte[] documentBinaryContentField;
        
        private string titleField;
        
        private string descriptionField;
        
        private string nameField;
        
        private System.Nullable<long> referenceIdField;
        
        private string createrNameField;
        
        private string ipField;
        
        private System.Nullable<System.DateTime> createDateField;
        
        private string statusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentSpecName {
            get {
                return this.documentSpecNameField;
            }
            set {
                this.documentSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentFileName {
            get {
                return this.documentFileNameField;
            }
            set {
                this.documentFileNameField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentExtension {
            get {
                return this.documentExtensionField;
            }
            set {
                this.documentExtensionField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] DocumentBinaryContent {
            get {
                return this.documentBinaryContentField;
            }
            set {
                this.documentBinaryContentField = value;
            }
        }
        
        /// <remarks/>
        public string Title {
            get {
                return this.titleField;
            }
            set {
                this.titleField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ReferenceId {
            get {
                return this.referenceIdField;
            }
            set {
                this.referenceIdField = value;
            }
        }
        
        /// <remarks/>
        public string CreaterName {
            get {
                return this.createrNameField;
            }
            set {
                this.createrNameField = value;
            }
        }
        
        /// <remarks/>
        public string Ip {
            get {
                return this.ipField;
            }
            set {
                this.ipField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> CreateDate {
            get {
                return this.createDateField;
            }
            set {
                this.createDateField = value;
            }
        }
        
        /// <remarks/>
        public string Status {
            get {
                return this.statusField;
            }
            set {
                this.statusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IrisPlusUserInfo : BaseClass {
        
        private int bayiIrisIdField;
        
        private int bayiIrisPersonelIdField;
        
        private string dTKimlikNoField;
        
        private string personelFotografAdiField;
        
        private int personelIdField;
        
        private System.Nullable<long> subContractorIdField;
        
        private DocumentInfo personnelPhotoField;
        
        private int userIdField;
        
        private string userTypeField;
        
        /// <remarks/>
        public int BayiIrisId {
            get {
                return this.bayiIrisIdField;
            }
            set {
                this.bayiIrisIdField = value;
            }
        }
        
        /// <remarks/>
        public int BayiIrisPersonelId {
            get {
                return this.bayiIrisPersonelIdField;
            }
            set {
                this.bayiIrisPersonelIdField = value;
            }
        }
        
        /// <remarks/>
        public string DTKimlikNo {
            get {
                return this.dTKimlikNoField;
            }
            set {
                this.dTKimlikNoField = value;
            }
        }
        
        /// <remarks/>
        public string PersonelFotografAdi {
            get {
                return this.personelFotografAdiField;
            }
            set {
                this.personelFotografAdiField = value;
            }
        }
        
        /// <remarks/>
        public int PersonelId {
            get {
                return this.personelIdField;
            }
            set {
                this.personelIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> SubContractorId {
            get {
                return this.subContractorIdField;
            }
            set {
                this.subContractorIdField = value;
            }
        }
        
        /// <remarks/>
        public DocumentInfo PersonnelPhoto {
            get {
                return this.personnelPhotoField;
            }
            set {
                this.personnelPhotoField = value;
            }
        }
        
        /// <remarks/>
        public int UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
            }
        }
        
        /// <remarks/>
        public string UserType {
            get {
                return this.userTypeField;
            }
            set {
                this.userTypeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GpsInfo : BaseClass {
        
        private System.Nullable<double> distanceField;
        
        private double latitudeField;
        
        private double longitudeField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<double> Distance {
            get {
                return this.distanceField;
            }
            set {
                this.distanceField = value;
            }
        }
        
        /// <remarks/>
        public double Latitude {
            get {
                return this.latitudeField;
            }
            set {
                this.latitudeField = value;
            }
        }
        
        /// <remarks/>
        public double Longitude {
            get {
                return this.longitudeField;
            }
            set {
                this.longitudeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AddressInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(PhoneInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(AccountEmail))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ContactMedium : BaseClass {
        
        private System.Nullable<long> contactMediumIdField;
        
        private string contactMediumPurposeTCdField;
        
        private System.Nullable<long> contactMediumRelIdField;
        
        private string contactMediumTypeCdField;
        
        private string contactMediumTechTypeCDField;
        
        private string contactMediumStatusTCdField;
        
        private string descriptionField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ContactMediumId {
            get {
                return this.contactMediumIdField;
            }
            set {
                this.contactMediumIdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumPurposeTCd {
            get {
                return this.contactMediumPurposeTCdField;
            }
            set {
                this.contactMediumPurposeTCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ContactMediumRelId {
            get {
                return this.contactMediumRelIdField;
            }
            set {
                this.contactMediumRelIdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumTypeCd {
            get {
                return this.contactMediumTypeCdField;
            }
            set {
                this.contactMediumTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumTechTypeCD {
            get {
                return this.contactMediumTechTypeCDField;
            }
            set {
                this.contactMediumTechTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public string ContactMediumStatusTCd {
            get {
                return this.contactMediumStatusTCdField;
            }
            set {
                this.contactMediumStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AddressInfo : ContactMedium {
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string addressLine3Field;
        
        private string cityField;
        
        private System.Nullable<long> cityIdField;
        
        private string countryField;
        
        private System.Nullable<long> countryIdField;
        
        private string countyField;
        
        private System.Nullable<long> countyIdField;
        
        private string districtField;
        
        private System.Nullable<long> districtIdField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private System.Nullable<long> geoLocationIdField;
        
        private string geoLocationNameField;
        
        private string geoLocationTypeCdField;
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleContactMedRIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<System.DateTime> processDateField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private string stateField;
        
        private System.Nullable<long> stateIdField;
        
        private System.Nullable<long> userIdField;
        
        private string zipCodeField;
        
        private System.Nullable<long> accountLogIdField;
        
        private string complexField;
        
        private string complexBlockField;
        
        private string apartmentNameField;
        
        private string apartmentNumberField;
        
        private string apartmentFlatNumberField;
        
        private string avenueField;
        
        private string streetField;
        
        private string addressFullTextField;
        
        private GpsInfo gpsInfoField;
        
        /// <remarks/>
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine3 {
            get {
                return this.addressLine3Field;
            }
            set {
                this.addressLine3Field = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CityId {
            get {
                return this.cityIdField;
            }
            set {
                this.cityIdField = value;
            }
        }
        
        /// <remarks/>
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string County {
            get {
                return this.countyField;
            }
            set {
                this.countyField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountyId {
            get {
                return this.countyIdField;
            }
            set {
                this.countyIdField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DistrictId {
            get {
                return this.districtIdField;
            }
            set {
                this.districtIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId {
            get {
                return this.geoLocationIdField;
            }
            set {
                this.geoLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationName {
            get {
                return this.geoLocationNameField;
            }
            set {
                this.geoLocationNameField = value;
            }
        }
        
        /// <remarks/>
        public string GeoLocationTypeCd {
            get {
                return this.geoLocationTypeCdField;
            }
            set {
                this.geoLocationTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleContactMedRId {
            get {
                return this.partyRoleContactMedRIdField;
            }
            set {
                this.partyRoleContactMedRIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ProcessDate {
            get {
                return this.processDateField;
            }
            set {
                this.processDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> StateId {
            get {
                return this.stateIdField;
            }
            set {
                this.stateIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> UserId {
            get {
                return this.userIdField;
            }
            set {
                this.userIdField = value;
            }
        }
        
        /// <remarks/>
        public string ZipCode {
            get {
                return this.zipCodeField;
            }
            set {
                this.zipCodeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        public string Complex {
            get {
                return this.complexField;
            }
            set {
                this.complexField = value;
            }
        }
        
        /// <remarks/>
        public string ComplexBlock {
            get {
                return this.complexBlockField;
            }
            set {
                this.complexBlockField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentName {
            get {
                return this.apartmentNameField;
            }
            set {
                this.apartmentNameField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentNumber {
            get {
                return this.apartmentNumberField;
            }
            set {
                this.apartmentNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentFlatNumber {
            get {
                return this.apartmentFlatNumberField;
            }
            set {
                this.apartmentFlatNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Avenue {
            get {
                return this.avenueField;
            }
            set {
                this.avenueField = value;
            }
        }
        
        /// <remarks/>
        public string Street {
            get {
                return this.streetField;
            }
            set {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        public string AddressFullText {
            get {
                return this.addressFullTextField;
            }
            set {
                this.addressFullTextField = value;
            }
        }
        
        /// <remarks/>
        public GpsInfo GpsInfo {
            get {
                return this.gpsInfoField;
            }
            set {
                this.gpsInfoField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PhoneInfo : ContactMedium {
        
        private string countryPrefixNumberField;
        
        private string areaPrefixNumberField;
        
        private string phoneNumberField;
        
        private string extensionNumberField;
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<System.DateTime> processDateField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> accountLogIdField;
        
        private int orderPriorityField;
        
        /// <remarks/>
        public string CountryPrefixNumber {
            get {
                return this.countryPrefixNumberField;
            }
            set {
                this.countryPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string AreaPrefixNumber {
            get {
                return this.areaPrefixNumberField;
            }
            set {
                this.areaPrefixNumberField = value;
            }
        }
        
        /// <remarks/>
        public string PhoneNumber {
            get {
                return this.phoneNumberField;
            }
            set {
                this.phoneNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ExtensionNumber {
            get {
                return this.extensionNumberField;
            }
            set {
                this.extensionNumberField = value;
            }
        }
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> ProcessDate {
            get {
                return this.processDateField;
            }
            set {
                this.processDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        public int OrderPriority {
            get {
                return this.orderPriorityField;
            }
            set {
                this.orderPriorityField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AccountEmail : ContactMedium {
        
        private bool isRegisteredField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private string emailField;
        
        private System.Nullable<long> accountLogIdField;
        
        /// <remarks/>
        public bool IsRegistered {
            get {
                return this.isRegisteredField;
            }
            set {
                this.isRegisteredField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string Email {
            get {
                return this.emailField;
            }
            set {
                this.emailField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class GeoAddress : BaseClass {
        
        private string addressLine1Field;
        
        private string addressLine2Field;
        
        private string addressLine3Field;
        
        private string addressNameField;
        
        private string addressTextField;
        
        private string cityField;
        
        private string countryField;
        
        private long createdByField;
        
        private System.DateTime creationDateField;
        
        private string descriptionField;
        
        private string districtField;
        
        private string countyField;
        
        private string complexField;
        
        private string complexBlockField;
        
        private string apartmentNameField;
        
        private string apartmentNumberField;
        
        private string apartmentFlatNumberField;
        
        private string avenueField;
        
        private string streetField;
        
        private string geoAddressHeadlineTextField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private string geoAddressTypeCDField;
        
        private long geoLocationIdField;
        
        private long geoLocationId_CityField;
        
        private long geoLocationId_CountryField;
        
        private System.Nullable<long> geoLocationId_StateField;
        
        private System.Nullable<long> geoLocationId_CountyField;
        
        private int isDefaultField;
        
        private string locationTextField;
        
        private string logicalDeleteKeyField;
        
        private long organisationIdField;
        
        private string organisationNameField;
        
        private System.Nullable<long> parentAddressIdField;
        
        private string partyRoleGeoLocRelTypeCDField;
        
        private string partyRoleGeoLocRelTypeNameField;
        
        private string postCodeField;
        
        private string statuField;
        
        private long updatedByField;
        
        private System.DateTime updatedDateField;
        
        private System.Nullable<long> stateIdField;
        
        private string stateField;
        
        private string addressFullTextField;
        
        /// <remarks/>
        public string AddressLine1 {
            get {
                return this.addressLine1Field;
            }
            set {
                this.addressLine1Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine2 {
            get {
                return this.addressLine2Field;
            }
            set {
                this.addressLine2Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressLine3 {
            get {
                return this.addressLine3Field;
            }
            set {
                this.addressLine3Field = value;
            }
        }
        
        /// <remarks/>
        public string AddressName {
            get {
                return this.addressNameField;
            }
            set {
                this.addressNameField = value;
            }
        }
        
        /// <remarks/>
        public string AddressText {
            get {
                return this.addressTextField;
            }
            set {
                this.addressTextField = value;
            }
        }
        
        /// <remarks/>
        public string City {
            get {
                return this.cityField;
            }
            set {
                this.cityField = value;
            }
        }
        
        /// <remarks/>
        public string Country {
            get {
                return this.countryField;
            }
            set {
                this.countryField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public string District {
            get {
                return this.districtField;
            }
            set {
                this.districtField = value;
            }
        }
        
        /// <remarks/>
        public string County {
            get {
                return this.countyField;
            }
            set {
                this.countyField = value;
            }
        }
        
        /// <remarks/>
        public string Complex {
            get {
                return this.complexField;
            }
            set {
                this.complexField = value;
            }
        }
        
        /// <remarks/>
        public string ComplexBlock {
            get {
                return this.complexBlockField;
            }
            set {
                this.complexBlockField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentName {
            get {
                return this.apartmentNameField;
            }
            set {
                this.apartmentNameField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentNumber {
            get {
                return this.apartmentNumberField;
            }
            set {
                this.apartmentNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ApartmentFlatNumber {
            get {
                return this.apartmentFlatNumberField;
            }
            set {
                this.apartmentFlatNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Avenue {
            get {
                return this.avenueField;
            }
            set {
                this.avenueField = value;
            }
        }
        
        /// <remarks/>
        public string Street {
            get {
                return this.streetField;
            }
            set {
                this.streetField = value;
            }
        }
        
        /// <remarks/>
        public string GeoAddressHeadlineText {
            get {
                return this.geoAddressHeadlineTextField;
            }
            set {
                this.geoAddressHeadlineTextField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string GeoAddressTypeCD {
            get {
                return this.geoAddressTypeCDField;
            }
            set {
                this.geoAddressTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId {
            get {
                return this.geoLocationIdField;
            }
            set {
                this.geoLocationIdField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId_City {
            get {
                return this.geoLocationId_CityField;
            }
            set {
                this.geoLocationId_CityField = value;
            }
        }
        
        /// <remarks/>
        public long GeoLocationId_Country {
            get {
                return this.geoLocationId_CountryField;
            }
            set {
                this.geoLocationId_CountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId_State {
            get {
                return this.geoLocationId_StateField;
            }
            set {
                this.geoLocationId_StateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoLocationId_County {
            get {
                return this.geoLocationId_CountyField;
            }
            set {
                this.geoLocationId_CountyField = value;
            }
        }
        
        /// <remarks/>
        public int IsDefault {
            get {
                return this.isDefaultField;
            }
            set {
                this.isDefaultField = value;
            }
        }
        
        /// <remarks/>
        public string LocationText {
            get {
                return this.locationTextField;
            }
            set {
                this.locationTextField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public long OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationName {
            get {
                return this.organisationNameField;
            }
            set {
                this.organisationNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentAddressId {
            get {
                return this.parentAddressIdField;
            }
            set {
                this.parentAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleGeoLocRelTypeCD {
            get {
                return this.partyRoleGeoLocRelTypeCDField;
            }
            set {
                this.partyRoleGeoLocRelTypeCDField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleGeoLocRelTypeName {
            get {
                return this.partyRoleGeoLocRelTypeNameField;
            }
            set {
                this.partyRoleGeoLocRelTypeNameField = value;
            }
        }
        
        /// <remarks/>
        public string PostCode {
            get {
                return this.postCodeField;
            }
            set {
                this.postCodeField = value;
            }
        }
        
        /// <remarks/>
        public string Statu {
            get {
                return this.statuField;
            }
            set {
                this.statuField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdatedDate {
            get {
                return this.updatedDateField;
            }
            set {
                this.updatedDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> StateId {
            get {
                return this.stateIdField;
            }
            set {
                this.stateIdField = value;
            }
        }
        
        /// <remarks/>
        public string State {
            get {
                return this.stateField;
            }
            set {
                this.stateField = value;
            }
        }
        
        /// <remarks/>
        public string AddressFullText {
            get {
                return this.addressFullTextField;
            }
            set {
                this.addressFullTextField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResourceLocationSpec : BaseClass {
        
        private System.Nullable<long> resourceLocationSpecIdField;
        
        private string resLocTypeSpecCdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private string dbsDepoKoduField;
        
        private string descriptionField;
        
        private System.DateTime creationDateField;
        
        private System.DateTime updateDateField;
        
        private System.Nullable<long> createdByField;
        
        private System.Nullable<long> updatedByField;
        
        private string logicalDeleteKeyField;
        
        private string nameField;
        
        private string partyDefaultField;
        
        private string organisationDefaultField;
        
        private System.Nullable<long> parentIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private string houseStatusTypeField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private string dbsOutletLocationField;
        
        private GeoAddress warehouseAddressField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string ResLocTypeSpecCd {
            get {
                return this.resLocTypeSpecCdField;
            }
            set {
                this.resLocTypeSpecCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsDepoKodu {
            get {
                return this.dbsDepoKoduField;
            }
            set {
                this.dbsDepoKoduField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreationDate {
            get {
                return this.creationDateField;
            }
            set {
                this.creationDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public string LogicalDeleteKey {
            get {
                return this.logicalDeleteKeyField;
            }
            set {
                this.logicalDeleteKeyField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyDefault {
            get {
                return this.partyDefaultField;
            }
            set {
                this.partyDefaultField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationDefault {
            get {
                return this.organisationDefaultField;
            }
            set {
                this.organisationDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentId {
            get {
                return this.parentIdField;
            }
            set {
                this.parentIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string HouseStatusType {
            get {
                return this.houseStatusTypeField;
            }
            set {
                this.houseStatusTypeField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        public GeoAddress WarehouseAddress {
            get {
                return this.warehouseAddressField;
            }
            set {
                this.warehouseAddressField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccountCharValue : BaseClass {
        
        private long partyRoleAccountCharValueIdField;
        
        private long partyRoleAccountIdField;
        
        private System.DateTime validFromField;
        
        private System.DateTime validThruField;
        
        private System.Nullable<long> charSpecUseIdField;
        
        private string charSpecUseCdField;
        
        private string charSpecValueCdField;
        
        private string charSpecValueField;
        
        private string charSpecCategoryCdField;
        
        private string charSpecUseTypeCdField;
        
        private string charSpecDataTypeCdField;
        
        private System.Nullable<long> charSpecValueIdField;
        
        private string charValueField;
        
        private string lovTableRowCdField;
        
        /// <remarks/>
        public long PartyRoleAccountCharValueId {
            get {
                return this.partyRoleAccountCharValueIdField;
            }
            set {
                this.partyRoleAccountCharValueIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidFrom {
            get {
                return this.validFromField;
            }
            set {
                this.validFromField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime ValidThru {
            get {
                return this.validThruField;
            }
            set {
                this.validThruField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecUseId {
            get {
                return this.charSpecUseIdField;
            }
            set {
                this.charSpecUseIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseCd {
            get {
                return this.charSpecUseCdField;
            }
            set {
                this.charSpecUseCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValueCd {
            get {
                return this.charSpecValueCdField;
            }
            set {
                this.charSpecValueCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecValue {
            get {
                return this.charSpecValueField;
            }
            set {
                this.charSpecValueField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecCategoryCd {
            get {
                return this.charSpecCategoryCdField;
            }
            set {
                this.charSpecCategoryCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecUseTypeCd {
            get {
                return this.charSpecUseTypeCdField;
            }
            set {
                this.charSpecUseTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CharSpecDataTypeCd {
            get {
                return this.charSpecDataTypeCdField;
            }
            set {
                this.charSpecDataTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CharSpecValueId {
            get {
                return this.charSpecValueIdField;
            }
            set {
                this.charSpecValueIdField = value;
            }
        }
        
        /// <remarks/>
        public string CharValue {
            get {
                return this.charValueField;
            }
            set {
                this.charValueField = value;
            }
        }
        
        /// <remarks/>
        public string LovTableRowCd {
            get {
                return this.lovTableRowCdField;
            }
            set {
                this.lovTableRowCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PaymentInstrument : BaseClass {
        
        private long paymentInstrumentIdField;
        
        private string paymentInstrumentTypeCdField;
        
        private string nameField;
        
        private string iBANField;
        
        private string vanNumberField;
        
        private string bankAccountNumberField;
        
        private string bankCdField;
        
        private string branchCodeField;
        
        private string bankGovernmentCodeField;
        
        private string bankPaymentSourceCodeField;
        
        private long billAccountIdField;
        
        private long countryIdField;
        
        private string paymentInstrStatusTCdField;
        
        private string creditCardOwnerNameField;
        
        private string creditCardExpireDateField;
        
        private string creditCardCvvNumberField;
        
        private System.Nullable<long> creditCardIdField;
        
        private string swiftBICField;
        
        private string creditorIdField;
        
        /// <remarks/>
        public long PaymentInstrumentId {
            get {
                return this.paymentInstrumentIdField;
            }
            set {
                this.paymentInstrumentIdField = value;
            }
        }
        
        /// <remarks/>
        public string PaymentInstrumentTypeCd {
            get {
                return this.paymentInstrumentTypeCdField;
            }
            set {
                this.paymentInstrumentTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string IBAN {
            get {
                return this.iBANField;
            }
            set {
                this.iBANField = value;
            }
        }
        
        /// <remarks/>
        public string VanNumber {
            get {
                return this.vanNumberField;
            }
            set {
                this.vanNumberField = value;
            }
        }
        
        /// <remarks/>
        public string BankAccountNumber {
            get {
                return this.bankAccountNumberField;
            }
            set {
                this.bankAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string BankCd {
            get {
                return this.bankCdField;
            }
            set {
                this.bankCdField = value;
            }
        }
        
        /// <remarks/>
        public string BranchCode {
            get {
                return this.branchCodeField;
            }
            set {
                this.branchCodeField = value;
            }
        }
        
        /// <remarks/>
        public string BankGovernmentCode {
            get {
                return this.bankGovernmentCodeField;
            }
            set {
                this.bankGovernmentCodeField = value;
            }
        }
        
        /// <remarks/>
        public string BankPaymentSourceCode {
            get {
                return this.bankPaymentSourceCodeField;
            }
            set {
                this.bankPaymentSourceCodeField = value;
            }
        }
        
        /// <remarks/>
        public long BillAccountId {
            get {
                return this.billAccountIdField;
            }
            set {
                this.billAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public long CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string PaymentInstrStatusTCd {
            get {
                return this.paymentInstrStatusTCdField;
            }
            set {
                this.paymentInstrStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardOwnerName {
            get {
                return this.creditCardOwnerNameField;
            }
            set {
                this.creditCardOwnerNameField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardExpireDate {
            get {
                return this.creditCardExpireDateField;
            }
            set {
                this.creditCardExpireDateField = value;
            }
        }
        
        /// <remarks/>
        public string CreditCardCvvNumber {
            get {
                return this.creditCardCvvNumberField;
            }
            set {
                this.creditCardCvvNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CreditCardId {
            get {
                return this.creditCardIdField;
            }
            set {
                this.creditCardIdField = value;
            }
        }
        
        /// <remarks/>
        public string SwiftBIC {
            get {
                return this.swiftBICField;
            }
            set {
                this.swiftBICField = value;
            }
        }
        
        /// <remarks/>
        public string CreditorId {
            get {
                return this.creditorIdField;
            }
            set {
                this.creditorIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRoleAccount : BaseClass {
        
        private long parentIdField;
        
        private long partyRoleAccountIdField;
        
        private string partyRoleAccountSpecCdField;
        
        private string partyRoleAccountSpecTCdField;
        
        private string partyRoleAccountStatusTCdField;
        
        private string accountNameField;
        
        private string accountDescriptionField;
        
        private string dbsOutletLocationField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private string satelliteTypeCdField;
        
        private KeyValueItem[] characteristicsField;
        
        private System.Nullable<long> resourceLocationSpecIdField;
        
        private string stbTipiField;
        
        private bool isAccountHeroField;
        
        private string billAmountCurrencyTypeCdField;
        
        private int isDefaultField;
        
        private System.Nullable<long> geoAddressIdField;
        
        private string membershipTypeField;
        
        private PaymentInstrument[] paymentInstrumentListField;
        
        private PartyRoleAccountCharValue[] partyRoleAccountCharValueListField;
        
        private PartyRoleAccPartyRoleRel[] partyRoleAccPartyRoleRelListField;
        
        /// <remarks/>
        public long ParentId {
            get {
                return this.parentIdField;
            }
            set {
                this.parentIdField = value;
            }
        }
        
        /// <remarks/>
        public long PartyRoleAccountId {
            get {
                return this.partyRoleAccountIdField;
            }
            set {
                this.partyRoleAccountIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecCd {
            get {
                return this.partyRoleAccountSpecCdField;
            }
            set {
                this.partyRoleAccountSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecTCd {
            get {
                return this.partyRoleAccountSpecTCdField;
            }
            set {
                this.partyRoleAccountSpecTCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountStatusTCd {
            get {
                return this.partyRoleAccountStatusTCdField;
            }
            set {
                this.partyRoleAccountStatusTCdField = value;
            }
        }
        
        /// <remarks/>
        public string AccountName {
            get {
                return this.accountNameField;
            }
            set {
                this.accountNameField = value;
            }
        }
        
        /// <remarks/>
        public string AccountDescription {
            get {
                return this.accountDescriptionField;
            }
            set {
                this.accountDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string DbsOutletLocation {
            get {
                return this.dbsOutletLocationField;
            }
            set {
                this.dbsOutletLocationField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public KeyValueItem[] Characteristics {
            get {
                return this.characteristicsField;
            }
            set {
                this.characteristicsField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ResourceLocationSpecId {
            get {
                return this.resourceLocationSpecIdField;
            }
            set {
                this.resourceLocationSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string StbTipi {
            get {
                return this.stbTipiField;
            }
            set {
                this.stbTipiField = value;
            }
        }
        
        /// <remarks/>
        public bool IsAccountHero {
            get {
                return this.isAccountHeroField;
            }
            set {
                this.isAccountHeroField = value;
            }
        }
        
        /// <remarks/>
        public string BillAmountCurrencyTypeCd {
            get {
                return this.billAmountCurrencyTypeCdField;
            }
            set {
                this.billAmountCurrencyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public int IsDefault {
            get {
                return this.isDefaultField;
            }
            set {
                this.isDefaultField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> GeoAddressId {
            get {
                return this.geoAddressIdField;
            }
            set {
                this.geoAddressIdField = value;
            }
        }
        
        /// <remarks/>
        public string MembershipType {
            get {
                return this.membershipTypeField;
            }
            set {
                this.membershipTypeField = value;
            }
        }
        
        /// <remarks/>
        public PaymentInstrument[] PaymentInstrumentList {
            get {
                return this.paymentInstrumentListField;
            }
            set {
                this.paymentInstrumentListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccountCharValue[] PartyRoleAccountCharValueList {
            get {
                return this.partyRoleAccountCharValueListField;
            }
            set {
                this.partyRoleAccountCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccPartyRoleRel[] PartyRoleAccPartyRoleRelList {
            get {
                return this.partyRoleAccPartyRoleRelListField;
            }
            set {
                this.partyRoleAccPartyRoleRelListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class KeyValueItem : BaseClass {
        
        private string keyField;
        
        private string valueField;
        
        /// <remarks/>
        public string Key {
            get {
                return this.keyField;
            }
            set {
                this.keyField = value;
            }
        }
        
        /// <remarks/>
        public string Value {
            get {
                return this.valueField;
            }
            set {
                this.valueField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Individual))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(Organisation))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class AccountInfo : BaseClass {
        
        private KeyValueItem[] characteristicsField;
        
        private string partyTypeCdField;
        
        private string partyProfileCdField;
        
        private string satelliteTypeCdField;
        
        private System.Nullable<long> partyIdField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private System.Nullable<long> partyRoleIdField;
        
        private System.Nullable<long> serviceAccountIdField;
        
        private System.Nullable<long> billAccountIdField;
        
        private System.Nullable<long> dbsAccountNumberField;
        
        private System.Nullable<long> accountLogIdField;
        
        private System.Nullable<long> countryIdField;
        
        private string taxNumberField;
        
        private System.Nullable<long> taxOfficeIdField;
        
        private string partyRoleServiceProviderCdField;
        
        private string partyRoleAccountSpecCdField;
        
        private string partyRoleAccountSpecTypeCdField;
        
        private string partyRoleStatusTypeCdField;
        
        private string partyRoleAccountNameField;
        
        private string partyRoleAccountDescriptionField;
        
        private string statusTypeCdField;
        
        private string partyRoleTypeCdField;
        
        private string contractProfileTypeField;
        
        private string memberTypePartyProfileNameField;
        
        private PartyRole accountPartyRoleField;
        
        /// <remarks/>
        public KeyValueItem[] Characteristics {
            get {
                return this.characteristicsField;
            }
            set {
                this.characteristicsField = value;
            }
        }
        
        /// <remarks/>
        public string PartyTypeCd {
            get {
                return this.partyTypeCdField;
            }
            set {
                this.partyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyProfileCd {
            get {
                return this.partyProfileCdField;
            }
            set {
                this.partyProfileCdField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyId {
            get {
                return this.partyIdField;
            }
            set {
                this.partyIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ServiceAccountId {
            get {
                return this.serviceAccountIdField;
            }
            set {
                this.serviceAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> BillAccountId {
            get {
                return this.billAccountIdField;
            }
            set {
                this.billAccountIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DbsAccountNumber {
            get {
                return this.dbsAccountNumberField;
            }
            set {
                this.dbsAccountNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> AccountLogId {
            get {
                return this.accountLogIdField;
            }
            set {
                this.accountLogIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> CountryId {
            get {
                return this.countryIdField;
            }
            set {
                this.countryIdField = value;
            }
        }
        
        /// <remarks/>
        public string TaxNumber {
            get {
                return this.taxNumberField;
            }
            set {
                this.taxNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> TaxOfficeId {
            get {
                return this.taxOfficeIdField;
            }
            set {
                this.taxOfficeIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleServiceProviderCd {
            get {
                return this.partyRoleServiceProviderCdField;
            }
            set {
                this.partyRoleServiceProviderCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecCd {
            get {
                return this.partyRoleAccountSpecCdField;
            }
            set {
                this.partyRoleAccountSpecCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountSpecTypeCd {
            get {
                return this.partyRoleAccountSpecTypeCdField;
            }
            set {
                this.partyRoleAccountSpecTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleStatusTypeCd {
            get {
                return this.partyRoleStatusTypeCdField;
            }
            set {
                this.partyRoleStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountName {
            get {
                return this.partyRoleAccountNameField;
            }
            set {
                this.partyRoleAccountNameField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleAccountDescription {
            get {
                return this.partyRoleAccountDescriptionField;
            }
            set {
                this.partyRoleAccountDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string StatusTypeCd {
            get {
                return this.statusTypeCdField;
            }
            set {
                this.statusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContractProfileType {
            get {
                return this.contractProfileTypeField;
            }
            set {
                this.contractProfileTypeField = value;
            }
        }
        
        /// <remarks/>
        public string MemberTypePartyProfileName {
            get {
                return this.memberTypePartyProfileNameField;
            }
            set {
                this.memberTypePartyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole AccountPartyRole {
            get {
                return this.accountPartyRoleField;
            }
            set {
                this.accountPartyRoleField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class PartyRole : BaseClass {
        
        private System.Nullable<long> partyRoleIdField;
        
        private string partyRoleCdField;
        
        private string partyRoleTypeCdField;
        
        private string partyTypeCdField;
        
        private string partyRoleServiceProviderCdField;
        
        private string partyRoleStatusTypeCdField;
        
        private System.Nullable<long> loginIdField;
        
        private System.Nullable<long> loginGroupIdField;
        
        private System.Nullable<int> isBranchField;
        
        private System.Nullable<long> individualIdField;
        
        private System.Nullable<long> organisationIdField;
        
        private string dbsSalesAgentCdField;
        
        private string dbsUserCdField;
        
        private string contractProfileTypeField;
        
        private string satelliteTypeCdField;
        
        private string memberTypePartyProfileNameField;
        
        private Organisation organisationInfoField;
        
        private Individual individualInfoField;
        
        private PartyRoleAccount[] partyRoleAccountListField;
        
        private PartyRoleAccount[] serviceAccountListField;
        
        private PartyRoleAccount[] billAccountListField;
        
        private ResourceLocationSpec[] resourceLocationSpecListField;
        
        private KeyValueItem[] accountAccessPermissionsField;
        
        private AccountEmail[] emailListField;
        
        private AddressInfo[] addressInfoListField;
        
        private PhoneInfo[] phoneListField;
        
        private IrisPlusUserInfo irisPlusUserInfoField;
        
        private PartyRoleCharValue[] partyRoleCharValueListField;
        
        private PartyCharValue[] partyCharValueListField;
        
        private PartyProfileMemberInfo[] partyProfileMemberInfoListField;
        
        private string branchNameField;
        
        private string branchDescriptionField;
        
        private string loginUserCdField;
        
        private ResourcesInfo[] resourcesInfoListField;
        
        private string technicalServicePaymentStatusField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> PartyRoleId {
            get {
                return this.partyRoleIdField;
            }
            set {
                this.partyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleCd {
            get {
                return this.partyRoleCdField;
            }
            set {
                this.partyRoleCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleTypeCd {
            get {
                return this.partyRoleTypeCdField;
            }
            set {
                this.partyRoleTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyTypeCd {
            get {
                return this.partyTypeCdField;
            }
            set {
                this.partyTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleServiceProviderCd {
            get {
                return this.partyRoleServiceProviderCdField;
            }
            set {
                this.partyRoleServiceProviderCdField = value;
            }
        }
        
        /// <remarks/>
        public string PartyRoleStatusTypeCd {
            get {
                return this.partyRoleStatusTypeCdField;
            }
            set {
                this.partyRoleStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LoginId {
            get {
                return this.loginIdField;
            }
            set {
                this.loginIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LoginGroupId {
            get {
                return this.loginGroupIdField;
            }
            set {
                this.loginGroupIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<int> IsBranch {
            get {
                return this.isBranchField;
            }
            set {
                this.isBranchField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IndividualId {
            get {
                return this.individualIdField;
            }
            set {
                this.individualIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsSalesAgentCd {
            get {
                return this.dbsSalesAgentCdField;
            }
            set {
                this.dbsSalesAgentCdField = value;
            }
        }
        
        /// <remarks/>
        public string DbsUserCd {
            get {
                return this.dbsUserCdField;
            }
            set {
                this.dbsUserCdField = value;
            }
        }
        
        /// <remarks/>
        public string ContractProfileType {
            get {
                return this.contractProfileTypeField;
            }
            set {
                this.contractProfileTypeField = value;
            }
        }
        
        /// <remarks/>
        public string SatelliteTypeCd {
            get {
                return this.satelliteTypeCdField;
            }
            set {
                this.satelliteTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MemberTypePartyProfileName {
            get {
                return this.memberTypePartyProfileNameField;
            }
            set {
                this.memberTypePartyProfileNameField = value;
            }
        }
        
        /// <remarks/>
        public Organisation OrganisationInfo {
            get {
                return this.organisationInfoField;
            }
            set {
                this.organisationInfoField = value;
            }
        }
        
        /// <remarks/>
        public Individual IndividualInfo {
            get {
                return this.individualInfoField;
            }
            set {
                this.individualInfoField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] PartyRoleAccountList {
            get {
                return this.partyRoleAccountListField;
            }
            set {
                this.partyRoleAccountListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] ServiceAccountList {
            get {
                return this.serviceAccountListField;
            }
            set {
                this.serviceAccountListField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleAccount[] BillAccountList {
            get {
                return this.billAccountListField;
            }
            set {
                this.billAccountListField = value;
            }
        }
        
        /// <remarks/>
        public ResourceLocationSpec[] ResourceLocationSpecList {
            get {
                return this.resourceLocationSpecListField;
            }
            set {
                this.resourceLocationSpecListField = value;
            }
        }
        
        /// <remarks/>
        public KeyValueItem[] AccountAccessPermissions {
            get {
                return this.accountAccessPermissionsField;
            }
            set {
                this.accountAccessPermissionsField = value;
            }
        }
        
        /// <remarks/>
        public AccountEmail[] EmailList {
            get {
                return this.emailListField;
            }
            set {
                this.emailListField = value;
            }
        }
        
        /// <remarks/>
        public AddressInfo[] AddressInfoList {
            get {
                return this.addressInfoListField;
            }
            set {
                this.addressInfoListField = value;
            }
        }
        
        /// <remarks/>
        public PhoneInfo[] PhoneList {
            get {
                return this.phoneListField;
            }
            set {
                this.phoneListField = value;
            }
        }
        
        /// <remarks/>
        public IrisPlusUserInfo IrisPlusUserInfo {
            get {
                return this.irisPlusUserInfoField;
            }
            set {
                this.irisPlusUserInfoField = value;
            }
        }
        
        /// <remarks/>
        public PartyRoleCharValue[] PartyRoleCharValueList {
            get {
                return this.partyRoleCharValueListField;
            }
            set {
                this.partyRoleCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyCharValue[] PartyCharValueList {
            get {
                return this.partyCharValueListField;
            }
            set {
                this.partyCharValueListField = value;
            }
        }
        
        /// <remarks/>
        public PartyProfileMemberInfo[] PartyProfileMemberInfoList {
            get {
                return this.partyProfileMemberInfoListField;
            }
            set {
                this.partyProfileMemberInfoListField = value;
            }
        }
        
        /// <remarks/>
        public string BranchName {
            get {
                return this.branchNameField;
            }
            set {
                this.branchNameField = value;
            }
        }
        
        /// <remarks/>
        public string BranchDescription {
            get {
                return this.branchDescriptionField;
            }
            set {
                this.branchDescriptionField = value;
            }
        }
        
        /// <remarks/>
        public string LoginUserCd {
            get {
                return this.loginUserCdField;
            }
            set {
                this.loginUserCdField = value;
            }
        }
        
        /// <remarks/>
        public ResourcesInfo[] ResourcesInfoList {
            get {
                return this.resourcesInfoListField;
            }
            set {
                this.resourcesInfoListField = value;
            }
        }
        
        /// <remarks/>
        public string TechnicalServicePaymentStatus {
            get {
                return this.technicalServicePaymentStatusField;
            }
            set {
                this.technicalServicePaymentStatusField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Organisation : AccountInfo {
        
        private string dbsDealerCdField;
        
        private string commercialNameField;
        
        private System.Nullable<System.DateTime> licenseAcquireDateField;
        
        private System.Nullable<long> licenseIssuerIdField;
        
        private System.Nullable<long> licenseManicipulatyIdField;
        
        private string licenseOtherManicipulatyField;
        
        private string licenseRegisterNumberField;
        
        private string nameField;
        
        private string organisationCdField;
        
        private string organisationTypeCdField;
        
        private string otherTaxOfficeField;
        
        private string arabicNameField;
        
        /// <remarks/>
        public string DbsDealerCd {
            get {
                return this.dbsDealerCdField;
            }
            set {
                this.dbsDealerCdField = value;
            }
        }
        
        /// <remarks/>
        public string CommercialName {
            get {
                return this.commercialNameField;
            }
            set {
                this.commercialNameField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> LicenseAcquireDate {
            get {
                return this.licenseAcquireDateField;
            }
            set {
                this.licenseAcquireDateField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LicenseIssuerId {
            get {
                return this.licenseIssuerIdField;
            }
            set {
                this.licenseIssuerIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> LicenseManicipulatyId {
            get {
                return this.licenseManicipulatyIdField;
            }
            set {
                this.licenseManicipulatyIdField = value;
            }
        }
        
        /// <remarks/>
        public string LicenseOtherManicipulaty {
            get {
                return this.licenseOtherManicipulatyField;
            }
            set {
                this.licenseOtherManicipulatyField = value;
            }
        }
        
        /// <remarks/>
        public string LicenseRegisterNumber {
            get {
                return this.licenseRegisterNumberField;
            }
            set {
                this.licenseRegisterNumberField = value;
            }
        }
        
        /// <remarks/>
        public string Name {
            get {
                return this.nameField;
            }
            set {
                this.nameField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationCd {
            get {
                return this.organisationCdField;
            }
            set {
                this.organisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string OrganisationTypeCd {
            get {
                return this.organisationTypeCdField;
            }
            set {
                this.organisationTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string OtherTaxOffice {
            get {
                return this.otherTaxOfficeField;
            }
            set {
                this.otherTaxOfficeField = value;
            }
        }
        
        /// <remarks/>
        public string ArabicName {
            get {
                return this.arabicNameField;
            }
            set {
                this.arabicNameField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class Individual : AccountInfo {
        
        private System.Nullable<System.DateTime> birthDateField;
        
        private string birthPlaceField;
        
        private string citizenNumberField;
        
        private string displayNameField;
        
        private string displaySurnameField;
        
        private string fatherNameField;
        
        private string firstNameField;
        
        private string genderTypeCdField;
        
        private string languageTypeCdField;
        
        private string maritalStatusTypeCdField;
        
        private string motherMaidenSurnameField;
        
        private string motherNameField;
        
        private string nationalityTypeCdField;
        
        private string otherProfessionField;
        
        private string otherTaxOfficeField;
        
        private string passportNumberField;
        
        private string professionTypeCdField;
        
        private System.Nullable<long> registeredBookNumberField;
        
        private string registeredCityField;
        
        private string registeredCountryField;
        
        private System.Nullable<long> registeredFamilySequenceNumberField;
        
        private System.Nullable<long> registeredSequenceNumberField;
        
        private string surNameField;
        
        private string titleTypeCdField;
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<System.DateTime> BirthDate {
            get {
                return this.birthDateField;
            }
            set {
                this.birthDateField = value;
            }
        }
        
        /// <remarks/>
        public string BirthPlace {
            get {
                return this.birthPlaceField;
            }
            set {
                this.birthPlaceField = value;
            }
        }
        
        /// <remarks/>
        public string CitizenNumber {
            get {
                return this.citizenNumberField;
            }
            set {
                this.citizenNumberField = value;
            }
        }
        
        /// <remarks/>
        public string DisplayName {
            get {
                return this.displayNameField;
            }
            set {
                this.displayNameField = value;
            }
        }
        
        /// <remarks/>
        public string DisplaySurname {
            get {
                return this.displaySurnameField;
            }
            set {
                this.displaySurnameField = value;
            }
        }
        
        /// <remarks/>
        public string FatherName {
            get {
                return this.fatherNameField;
            }
            set {
                this.fatherNameField = value;
            }
        }
        
        /// <remarks/>
        public string FirstName {
            get {
                return this.firstNameField;
            }
            set {
                this.firstNameField = value;
            }
        }
        
        /// <remarks/>
        public string GenderTypeCd {
            get {
                return this.genderTypeCdField;
            }
            set {
                this.genderTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string LanguageTypeCd {
            get {
                return this.languageTypeCdField;
            }
            set {
                this.languageTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MaritalStatusTypeCd {
            get {
                return this.maritalStatusTypeCdField;
            }
            set {
                this.maritalStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string MotherMaidenSurname {
            get {
                return this.motherMaidenSurnameField;
            }
            set {
                this.motherMaidenSurnameField = value;
            }
        }
        
        /// <remarks/>
        public string MotherName {
            get {
                return this.motherNameField;
            }
            set {
                this.motherNameField = value;
            }
        }
        
        /// <remarks/>
        public string NationalityTypeCd {
            get {
                return this.nationalityTypeCdField;
            }
            set {
                this.nationalityTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string OtherProfession {
            get {
                return this.otherProfessionField;
            }
            set {
                this.otherProfessionField = value;
            }
        }
        
        /// <remarks/>
        public string OtherTaxOffice {
            get {
                return this.otherTaxOfficeField;
            }
            set {
                this.otherTaxOfficeField = value;
            }
        }
        
        /// <remarks/>
        public string PassportNumber {
            get {
                return this.passportNumberField;
            }
            set {
                this.passportNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ProfessionTypeCd {
            get {
                return this.professionTypeCdField;
            }
            set {
                this.professionTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredBookNumber {
            get {
                return this.registeredBookNumberField;
            }
            set {
                this.registeredBookNumberField = value;
            }
        }
        
        /// <remarks/>
        public string RegisteredCity {
            get {
                return this.registeredCityField;
            }
            set {
                this.registeredCityField = value;
            }
        }
        
        /// <remarks/>
        public string RegisteredCountry {
            get {
                return this.registeredCountryField;
            }
            set {
                this.registeredCountryField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredFamilySequenceNumber {
            get {
                return this.registeredFamilySequenceNumberField;
            }
            set {
                this.registeredFamilySequenceNumberField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> RegisteredSequenceNumber {
            get {
                return this.registeredSequenceNumberField;
            }
            set {
                this.registeredSequenceNumberField = value;
            }
        }
        
        /// <remarks/>
        public string SurName {
            get {
                return this.surNameField;
            }
            set {
                this.surNameField = value;
            }
        }
        
        /// <remarks/>
        public string TitleTypeCd {
            get {
                return this.titleTypeCdField;
            }
            set {
                this.titleTypeCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class IrisSession : BaseClass {
        
        private long irisSessionIdField;
        
        private System.Nullable<long> irisUserIdField;
        
        private System.Nullable<long> irisPersonelIdField;
        
        private System.Nullable<long> parentIrisSessionIdField;
        
        private string userOrganisationCdField;
        
        private string userLoginCdField;
        
        private string channelTypeCdField;
        
        private string sessionStatusTypeCdField;
        
        private string sessionGuidIdField;
        
        private string clientIpField;
        
        private string clientTypeCdField;
        
        private int applicationIdField;
        
        private string applicationCdField;
        
        private string clientSerialNumberField;
        
        private string clientOperatingSystemField;
        
        private string serverHostNameField;
        
        private string serverSessionIdField;
        
        private System.DateTime lastRequestDateField;
        
        private long fLoginIdField;
        
        private string fLoginSessionCdField;
        
        private System.DateTime createDateField;
        
        private System.DateTime updateDateField;
        
        private long createdByField;
        
        private long updatedByField;
        
        private string cultureTypeCdField;
        
        private PartyRole dealerPartyRoleField;
        
        private PartyRole dealerPersonnelPartyRoleField;
        
        private PartyRole dealerBranchPartyRoleField;
        
        private PartyRole crmUserPartyRoleField;
        
        private string loginUserTypeCdField;
        
        private int channelIdField;
        
        private int clientIdField;
        
        private System.Nullable<long> irisPlusSessionIdField;
        
        private IrisPlusSession irisPlusSessionField;
        
        private long userLoginFLoginRIdField;
        
        private long userLoginPartyRoleIdField;
        
        private System.Nullable<long> dealerBranchPartyRoleIdField;
        
        private System.Nullable<long> dealerPartyRoleIdField;
        
        private System.Nullable<long> dealerPersonnelPartyRoleIdField;
        
        private string resultMessageField;
        
        private long resultCodeField;
        
        /// <remarks/>
        public long IrisSessionId {
            get {
                return this.irisSessionIdField;
            }
            set {
                this.irisSessionIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IrisUserId {
            get {
                return this.irisUserIdField;
            }
            set {
                this.irisUserIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IrisPersonelId {
            get {
                return this.irisPersonelIdField;
            }
            set {
                this.irisPersonelIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> ParentIrisSessionId {
            get {
                return this.parentIrisSessionIdField;
            }
            set {
                this.parentIrisSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string UserOrganisationCd {
            get {
                return this.userOrganisationCdField;
            }
            set {
                this.userOrganisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string UserLoginCd {
            get {
                return this.userLoginCdField;
            }
            set {
                this.userLoginCdField = value;
            }
        }
        
        /// <remarks/>
        public string ChannelTypeCd {
            get {
                return this.channelTypeCdField;
            }
            set {
                this.channelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionStatusTypeCd {
            get {
                return this.sessionStatusTypeCdField;
            }
            set {
                this.sessionStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionGuidId {
            get {
                return this.sessionGuidIdField;
            }
            set {
                this.sessionGuidIdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientIp {
            get {
                return this.clientIpField;
            }
            set {
                this.clientIpField = value;
            }
        }
        
        /// <remarks/>
        public string ClientTypeCd {
            get {
                return this.clientTypeCdField;
            }
            set {
                this.clientTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public int ApplicationId {
            get {
                return this.applicationIdField;
            }
            set {
                this.applicationIdField = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationCd {
            get {
                return this.applicationCdField;
            }
            set {
                this.applicationCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientSerialNumber {
            get {
                return this.clientSerialNumberField;
            }
            set {
                this.clientSerialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ClientOperatingSystem {
            get {
                return this.clientOperatingSystemField;
            }
            set {
                this.clientOperatingSystemField = value;
            }
        }
        
        /// <remarks/>
        public string ServerHostName {
            get {
                return this.serverHostNameField;
            }
            set {
                this.serverHostNameField = value;
            }
        }
        
        /// <remarks/>
        public string ServerSessionId {
            get {
                return this.serverSessionIdField;
            }
            set {
                this.serverSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime LastRequestDate {
            get {
                return this.lastRequestDateField;
            }
            set {
                this.lastRequestDateField = value;
            }
        }
        
        /// <remarks/>
        public long FLoginId {
            get {
                return this.fLoginIdField;
            }
            set {
                this.fLoginIdField = value;
            }
        }
        
        /// <remarks/>
        public string FLoginSessionCd {
            get {
                return this.fLoginSessionCdField;
            }
            set {
                this.fLoginSessionCdField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime CreateDate {
            get {
                return this.createDateField;
            }
            set {
                this.createDateField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        public long CreatedBy {
            get {
                return this.createdByField;
            }
            set {
                this.createdByField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatedBy {
            get {
                return this.updatedByField;
            }
            set {
                this.updatedByField = value;
            }
        }
        
        /// <remarks/>
        public string CultureTypeCd {
            get {
                return this.cultureTypeCdField;
            }
            set {
                this.cultureTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole DealerPartyRole {
            get {
                return this.dealerPartyRoleField;
            }
            set {
                this.dealerPartyRoleField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole DealerPersonnelPartyRole {
            get {
                return this.dealerPersonnelPartyRoleField;
            }
            set {
                this.dealerPersonnelPartyRoleField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole DealerBranchPartyRole {
            get {
                return this.dealerBranchPartyRoleField;
            }
            set {
                this.dealerBranchPartyRoleField = value;
            }
        }
        
        /// <remarks/>
        public PartyRole CrmUserPartyRole {
            get {
                return this.crmUserPartyRoleField;
            }
            set {
                this.crmUserPartyRoleField = value;
            }
        }
        
        /// <remarks/>
        public string LoginUserTypeCd {
            get {
                return this.loginUserTypeCdField;
            }
            set {
                this.loginUserTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public int ChannelId {
            get {
                return this.channelIdField;
            }
            set {
                this.channelIdField = value;
            }
        }
        
        /// <remarks/>
        public int ClientId {
            get {
                return this.clientIdField;
            }
            set {
                this.clientIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IrisPlusSessionId {
            get {
                return this.irisPlusSessionIdField;
            }
            set {
                this.irisPlusSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public IrisPlusSession IrisPlusSession {
            get {
                return this.irisPlusSessionField;
            }
            set {
                this.irisPlusSessionField = value;
            }
        }
        
        /// <remarks/>
        public long UserLoginFLoginRId {
            get {
                return this.userLoginFLoginRIdField;
            }
            set {
                this.userLoginFLoginRIdField = value;
            }
        }
        
        /// <remarks/>
        public long UserLoginPartyRoleId {
            get {
                return this.userLoginPartyRoleIdField;
            }
            set {
                this.userLoginPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerBranchPartyRoleId {
            get {
                return this.dealerBranchPartyRoleIdField;
            }
            set {
                this.dealerBranchPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPersonnelPartyRoleId {
            get {
                return this.dealerPersonnelPartyRoleIdField;
            }
            set {
                this.dealerPersonnelPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public long ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ResponseBase : BaseClass {
        
        private string resultMessageField;
        
        private long resultCodeField;
        
        private string[] resultErrorMessagesField;
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public long ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string[] ResultErrorMessages {
            get {
                return this.resultErrorMessagesField;
            }
            set {
                this.resultErrorMessagesField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionInfo))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(ClientSessionRequest))]
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionRequest))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SessionToken : BaseClass {
        
        private long sessionIdField;
        
        private string sessionGuidIdField;
        
        /// <remarks/>
        public long SessionId {
            get {
                return this.sessionIdField;
            }
            set {
                this.sessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionGuidId {
            get {
                return this.sessionGuidIdField;
            }
            set {
                this.sessionGuidIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SessionInfo : SessionToken {
        
        private string userOrganisationCdField;
        
        private string userLoginCdField;
        
        private long fLoginIdField;
        
        private string fLoginSessionCdField;
        
        private string serverHostNameField;
        
        private string serverSessionIdField;
        
        private string sessionStatusTypeCdField;
        
        private string cultureTypeCdField;
        
        private System.Nullable<long> dealerPartyRoleIdField;
        
        private System.Nullable<long> dealerPersonnelPartyRoleIdField;
        
        private System.Nullable<long> dealerBranchPartyRoleIdField;
        
        private string loginUserTypeCdField;
        
        private System.Nullable<long> irisPlusSessionIdField;
        
        private long userLoginPartyRoleIdField;
        
        private int channelIdField;
        
        private int applicationIdField;
        
        private long clientIdField;
        
        private long organisationIdField;
        
        private string clientIpField;
        
        /// <remarks/>
        public string UserOrganisationCd {
            get {
                return this.userOrganisationCdField;
            }
            set {
                this.userOrganisationCdField = value;
            }
        }
        
        /// <remarks/>
        public string UserLoginCd {
            get {
                return this.userLoginCdField;
            }
            set {
                this.userLoginCdField = value;
            }
        }
        
        /// <remarks/>
        public long FLoginId {
            get {
                return this.fLoginIdField;
            }
            set {
                this.fLoginIdField = value;
            }
        }
        
        /// <remarks/>
        public string FLoginSessionCd {
            get {
                return this.fLoginSessionCdField;
            }
            set {
                this.fLoginSessionCdField = value;
            }
        }
        
        /// <remarks/>
        public string ServerHostName {
            get {
                return this.serverHostNameField;
            }
            set {
                this.serverHostNameField = value;
            }
        }
        
        /// <remarks/>
        public string ServerSessionId {
            get {
                return this.serverSessionIdField;
            }
            set {
                this.serverSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionStatusTypeCd {
            get {
                return this.sessionStatusTypeCdField;
            }
            set {
                this.sessionStatusTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string CultureTypeCd {
            get {
                return this.cultureTypeCdField;
            }
            set {
                this.cultureTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPartyRoleId {
            get {
                return this.dealerPartyRoleIdField;
            }
            set {
                this.dealerPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerPersonnelPartyRoleId {
            get {
                return this.dealerPersonnelPartyRoleIdField;
            }
            set {
                this.dealerPersonnelPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> DealerBranchPartyRoleId {
            get {
                return this.dealerBranchPartyRoleIdField;
            }
            set {
                this.dealerBranchPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public string LoginUserTypeCd {
            get {
                return this.loginUserTypeCdField;
            }
            set {
                this.loginUserTypeCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(IsNullable=true)]
        public System.Nullable<long> IrisPlusSessionId {
            get {
                return this.irisPlusSessionIdField;
            }
            set {
                this.irisPlusSessionIdField = value;
            }
        }
        
        /// <remarks/>
        public long UserLoginPartyRoleId {
            get {
                return this.userLoginPartyRoleIdField;
            }
            set {
                this.userLoginPartyRoleIdField = value;
            }
        }
        
        /// <remarks/>
        public int ChannelId {
            get {
                return this.channelIdField;
            }
            set {
                this.channelIdField = value;
            }
        }
        
        /// <remarks/>
        public int ApplicationId {
            get {
                return this.applicationIdField;
            }
            set {
                this.applicationIdField = value;
            }
        }
        
        /// <remarks/>
        public long ClientId {
            get {
                return this.clientIdField;
            }
            set {
                this.clientIdField = value;
            }
        }
        
        /// <remarks/>
        public long OrganisationId {
            get {
                return this.organisationIdField;
            }
            set {
                this.organisationIdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientIp {
            get {
                return this.clientIpField;
            }
            set {
                this.clientIpField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIncludeAttribute(typeof(SessionRequest))]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class ClientSessionRequest : SessionToken {
        
        private string cultureTypeCdField;
        
        private string channelTypeCdField;
        
        private string clientIpField;
        
        private string clientTypeCdField;
        
        private string clientSerialNumberField;
        
        private string clientOperatingSystemField;
        
        private string applicationCdField;
        
        /// <remarks/>
        public string CultureTypeCd {
            get {
                return this.cultureTypeCdField;
            }
            set {
                this.cultureTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ChannelTypeCd {
            get {
                return this.channelTypeCdField;
            }
            set {
                this.channelTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientIp {
            get {
                return this.clientIpField;
            }
            set {
                this.clientIpField = value;
            }
        }
        
        /// <remarks/>
        public string ClientTypeCd {
            get {
                return this.clientTypeCdField;
            }
            set {
                this.clientTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ClientSerialNumber {
            get {
                return this.clientSerialNumberField;
            }
            set {
                this.clientSerialNumberField = value;
            }
        }
        
        /// <remarks/>
        public string ClientOperatingSystem {
            get {
                return this.clientOperatingSystemField;
            }
            set {
                this.clientOperatingSystemField = value;
            }
        }
        
        /// <remarks/>
        public string ApplicationCd {
            get {
                return this.applicationCdField;
            }
            set {
                this.applicationCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.4084.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="http://tempuri.org/")]
    public partial class SessionRequest : ClientSessionRequest {
        
        private string serverHostNameField;
        
        private string serverSessionIdField;
        
        /// <remarks/>
        public string ServerHostName {
            get {
                return this.serverHostNameField;
            }
            set {
                this.serverHostNameField = value;
            }
        }
        
        /// <remarks/>
        public string ServerSessionId {
            get {
                return this.serverSessionIdField;
            }
            set {
                this.serverSessionIdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PingCompletedEventHandler(object sender, PingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public long Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((long)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateCompletedEventHandler(object sender, SystemAuthenticateCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureCompletedEventHandler(object sender, SystemAuthenticateByCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateTokenCompletedEventHandler(object sender, SystemValidateTokenCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateTokenCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateTokenCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemValidateCultureCompletedEventHandler(object sender, SystemValidateCultureCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemValidateCultureCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemValidateCultureCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateWithExpireCompletedEventHandler(object sender, SystemAuthenticateWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SystemAuthenticateByCultureWithExpireCompletedEventHandler(object sender, SystemAuthenticateByCultureWithExpireCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SystemAuthenticateByCultureWithExpireCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SystemAuthenticateByCultureWithExpireCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public TokenData Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((TokenData)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void ChangePasswordCompletedEventHandler(object sender, ChangePasswordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ChangePasswordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ChangePasswordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ResponseBase Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ResponseBase)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CheckSessionCompletedEventHandler(object sender, CheckSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CheckSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CheckSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public IrisSession Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((IrisSession)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CloseSessionCompletedEventHandler(object sender, CloseSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CloseSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CloseSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OpenSessionCompletedEventHandler(object sender, OpenSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OpenSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OpenSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public IrisSession Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((IrisSession)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SessionTransferCompletedEventHandler(object sender, SessionTransferCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SessionTransferCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SessionTransferCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public SessionInfo Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((SessionInfo)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void SendResetPasswordKeyCompletedEventHandler(object sender, SendResetPasswordKeyCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SendResetPasswordKeyCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SendResetPasswordKeyCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void ResetPasswordCompletedEventHandler(object sender, ResetPasswordCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ResetPasswordCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ResetPasswordCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void OpenSimpleSessionCompletedEventHandler(object sender, OpenSimpleSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class OpenSimpleSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal OpenSimpleSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public SessionToken Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((SessionToken)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CheckSessionByTokenCompletedEventHandler(object sender, CheckSessionByTokenCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CheckSessionByTokenCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CheckSessionByTokenCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void GetSessionByIrisPlusSessionCompletedEventHandler(object sender, GetSessionByIrisPlusSessionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetSessionByIrisPlusSessionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetSessionByIrisPlusSessionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public IrisSession Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((IrisSession)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CheckLoginIpPermissionCompletedEventHandler(object sender, CheckLoginIpPermissionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CheckLoginIpPermissionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CheckLoginIpPermissionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void CheckLoginHourPermissionCompletedEventHandler(object sender, CheckLoginHourPermissionCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class CheckLoginHourPermissionCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal CheckLoginHourPermissionCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public bool Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((bool)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591