<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="IrisSessionSdpBS" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc?xsd=xsd0" namespace="http://tempuri.org/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IIrisSessionSdpBS_Ping_InputMessage">
    <wsdl:part name="parameters" element="tns:Ping" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_Ping_OutputMessage">
    <wsdl:part name="parameters" element="tns:PingResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticate_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticate" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticate_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateByCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCulture" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateByCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemValidateToken_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateToken" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemValidateToken_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateTokenResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemValidateCulture_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCulture" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemValidateCulture_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemValidateCultureResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpire" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateByCultureWithExpire_InputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpire" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SystemAuthenticateByCultureWithExpire_OutputMessage">
    <wsdl:part name="parameters" element="tns:SystemAuthenticateByCultureWithExpireResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_ChangePassword_InputMessage">
    <wsdl:part name="parameters" element="tns:ChangePassword" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_ChangePassword_OutputMessage">
    <wsdl:part name="parameters" element="tns:ChangePasswordResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckSession_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckSession" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckSession_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckSessionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CloseSession_InputMessage">
    <wsdl:part name="parameters" element="tns:CloseSession" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CloseSession_OutputMessage">
    <wsdl:part name="parameters" element="tns:CloseSessionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_OpenSession_InputMessage">
    <wsdl:part name="parameters" element="tns:OpenSession" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_OpenSession_OutputMessage">
    <wsdl:part name="parameters" element="tns:OpenSessionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SessionTransfer_InputMessage">
    <wsdl:part name="parameters" element="tns:SessionTransfer" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SessionTransfer_OutputMessage">
    <wsdl:part name="parameters" element="tns:SessionTransferResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SendResetPasswordKey_InputMessage">
    <wsdl:part name="parameters" element="tns:SendResetPasswordKey" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_SendResetPasswordKey_OutputMessage">
    <wsdl:part name="parameters" element="tns:SendResetPasswordKeyResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_ResetPassword_InputMessage">
    <wsdl:part name="parameters" element="tns:ResetPassword" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_ResetPassword_OutputMessage">
    <wsdl:part name="parameters" element="tns:ResetPasswordResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_OpenSimpleSession_InputMessage">
    <wsdl:part name="parameters" element="tns:OpenSimpleSession" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_OpenSimpleSession_OutputMessage">
    <wsdl:part name="parameters" element="tns:OpenSimpleSessionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckSessionByToken_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckSessionByToken" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckSessionByToken_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckSessionByTokenResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_GetSessionByIrisPlusSession_InputMessage">
    <wsdl:part name="parameters" element="tns:GetSessionByIrisPlusSession" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_GetSessionByIrisPlusSession_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetSessionByIrisPlusSessionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckLoginIpPermission_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckLoginIpPermission" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckLoginIpPermission_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckLoginIpPermissionResponse" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckLoginHourPermission_InputMessage">
    <wsdl:part name="parameters" element="tns:CheckLoginHourPermission" />
  </wsdl:message>
  <wsdl:message name="IIrisSessionSdpBS_CheckLoginHourPermission_OutputMessage">
    <wsdl:part name="parameters" element="tns:CheckLoginHourPermissionResponse" />
  </wsdl:message>
  <wsdl:portType name="IIrisSessionSdpBS">
    <wsdl:operation name="Ping">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/Ping" message="tns:IIrisSessionSdpBS_Ping_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/PingResponse" message="tns:IIrisSessionSdpBS_Ping_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticate" message="tns:IIrisSessionSdpBS_SystemAuthenticate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateResponse" message="tns:IIrisSessionSdpBS_SystemAuthenticate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCulture" message="tns:IIrisSessionSdpBS_SystemAuthenticateByCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCultureResponse" message="tns:IIrisSessionSdpBS_SystemAuthenticateByCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemValidateToken" message="tns:IIrisSessionSdpBS_SystemValidateToken_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemValidateTokenResponse" message="tns:IIrisSessionSdpBS_SystemValidateToken_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemValidateCulture" message="tns:IIrisSessionSdpBS_SystemValidateCulture_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemValidateCultureResponse" message="tns:IIrisSessionSdpBS_SystemValidateCulture_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateWithExpire" message="tns:IIrisSessionSdpBS_SystemAuthenticateWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateWithExpireResponse" message="tns:IIrisSessionSdpBS_SystemAuthenticateWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCultureWithExpire" message="tns:IIrisSessionSdpBS_SystemAuthenticateByCultureWithExpire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCultureWithExpireResponse" message="tns:IIrisSessionSdpBS_SystemAuthenticateByCultureWithExpire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ChangePassword">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/ChangePassword" message="tns:IIrisSessionSdpBS_ChangePassword_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/ChangePasswordResponse" message="tns:IIrisSessionSdpBS_ChangePassword_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckSession">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckSession" message="tns:IIrisSessionSdpBS_CheckSession_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckSessionResponse" message="tns:IIrisSessionSdpBS_CheckSession_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CloseSession">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CloseSession" message="tns:IIrisSessionSdpBS_CloseSession_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CloseSessionResponse" message="tns:IIrisSessionSdpBS_CloseSession_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="OpenSession">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/OpenSession" message="tns:IIrisSessionSdpBS_OpenSession_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/OpenSessionResponse" message="tns:IIrisSessionSdpBS_OpenSession_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SessionTransfer">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SessionTransfer" message="tns:IIrisSessionSdpBS_SessionTransfer_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SessionTransferResponse" message="tns:IIrisSessionSdpBS_SessionTransfer_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="SendResetPasswordKey">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SendResetPasswordKey" message="tns:IIrisSessionSdpBS_SendResetPasswordKey_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/SendResetPasswordKeyResponse" message="tns:IIrisSessionSdpBS_SendResetPasswordKey_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ResetPassword">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/ResetPassword" message="tns:IIrisSessionSdpBS_ResetPassword_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/ResetPasswordResponse" message="tns:IIrisSessionSdpBS_ResetPassword_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="OpenSimpleSession">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/OpenSimpleSession" message="tns:IIrisSessionSdpBS_OpenSimpleSession_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/OpenSimpleSessionResponse" message="tns:IIrisSessionSdpBS_OpenSimpleSession_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckSessionByToken">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckSessionByToken" message="tns:IIrisSessionSdpBS_CheckSessionByToken_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckSessionByTokenResponse" message="tns:IIrisSessionSdpBS_CheckSessionByToken_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetSessionByIrisPlusSession">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/GetSessionByIrisPlusSession" message="tns:IIrisSessionSdpBS_GetSessionByIrisPlusSession_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/GetSessionByIrisPlusSessionResponse" message="tns:IIrisSessionSdpBS_GetSessionByIrisPlusSession_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckLoginIpPermission">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckLoginIpPermission" message="tns:IIrisSessionSdpBS_CheckLoginIpPermission_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckLoginIpPermissionResponse" message="tns:IIrisSessionSdpBS_CheckLoginIpPermission_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CheckLoginHourPermission">
      <wsdl:input wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckLoginHourPermission" message="tns:IIrisSessionSdpBS_CheckLoginHourPermission_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IIrisSessionSdpBS/CheckLoginHourPermissionResponse" message="tns:IIrisSessionSdpBS_CheckLoginHourPermission_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IIrisSessionSdpBS" type="tns:IIrisSessionSdpBS">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="Ping">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/Ping" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticate">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCulture">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateToken">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemValidateToken" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemValidateCulture">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemValidateCulture" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateWithExpire">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SystemAuthenticateByCultureWithExpire">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SystemAuthenticateByCultureWithExpire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ChangePassword">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/ChangePassword" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckSession">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/CheckSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CloseSession">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/CloseSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OpenSession">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/OpenSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SessionTransfer">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SessionTransfer" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendResetPasswordKey">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/SendResetPasswordKey" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResetPassword">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/ResetPassword" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OpenSimpleSession">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/OpenSimpleSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckSessionByToken">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/CheckSessionByToken" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetSessionByIrisPlusSession">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/GetSessionByIrisPlusSession" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckLoginIpPermission">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/CheckLoginIpPermission" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CheckLoginHourPermission">
      <soap:operation soapAction="http://tempuri.org/IIrisSessionSdpBS/CheckLoginHourPermission" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="IrisSessionSdpBS">
    <wsdl:port name="BasicHttpBinding_IIrisSessionSdpBS" binding="tns:BasicHttpBinding_IIrisSessionSdpBS">
      <soap:address location="http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>