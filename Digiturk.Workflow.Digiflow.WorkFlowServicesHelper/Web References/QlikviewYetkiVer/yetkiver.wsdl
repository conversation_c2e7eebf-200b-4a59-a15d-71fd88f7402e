<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="YetkiVer">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="path" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="username" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="key" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="YetkiVerResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="YetkiVerResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="YetkiVerSoapIn">
    <wsdl:part name="parameters" element="tns:YetkiVer" />
  </wsdl:message>
  <wsdl:message name="YetkiVerSoapOut">
    <wsdl:part name="parameters" element="tns:YetkiVerResponse" />
  </wsdl:message>
  <wsdl:portType name="Service1Soap">
    <wsdl:operation name="YetkiVer">
      <wsdl:input message="tns:YetkiVerSoapIn" />
      <wsdl:output message="tns:YetkiVerSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="Service1Soap" type="tns:Service1Soap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="YetkiVer">
      <soap:operation soapAction="http://tempuri.org/YetkiVer" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="Service1Soap12" type="tns:Service1Soap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="YetkiVer">
      <soap12:operation soapAction="http://tempuri.org/YetkiVer" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service1">
    <wsdl:port name="Service1Soap" binding="tns:Service1Soap">
      <soap:address location="http://dtl1qlikviewtst:82/yetkiver.asmx" />
    </wsdl:port>
    <wsdl:port name="Service1Soap12" binding="tns:Service1Soap12">
      <soap12:address location="http://dtl1qlikviewtst:82/yetkiver.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>