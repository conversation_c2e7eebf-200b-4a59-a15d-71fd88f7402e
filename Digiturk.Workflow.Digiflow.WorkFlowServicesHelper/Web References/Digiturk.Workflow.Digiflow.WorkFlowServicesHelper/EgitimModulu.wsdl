<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="EgitimParametreGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="egitim_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimParametreGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimParametreGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimParametreMaxEgikodGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimParametreMaxEgikodGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimParametreMaxEgikodGetirResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimParametreEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="tur_kodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="katalog" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="aciklama1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="aciklama2" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimParametreEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimParametreEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="FirmaParametreGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreMaxFirmaKodGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreMaxFirmaKodGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="FirmaParametreMaxFirmaKodGetirResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ilgili_kisi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_telefonu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_email" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="FirmaParametreEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="FirmaParametreEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKursGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursMaxReferansNoGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursMaxReferansNoGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKursMaxReferansNoGetirResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_adi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKursEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEgitimGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="egitim_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEgitimGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKursEgitimGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEgitimEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="egitim_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKursEgitimEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKursEgitimEkleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimSinifGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifMaxKursNoGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifMaxKursNoGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimSinifMaxKursNoGetirResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_kodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yurticidisi" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="baslangic_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="bitis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="doviz_kodu" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="gerceklesen_tutar" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="sure_gun" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket_ici" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="planlama_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="dokuman_path" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimSinifEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimSinifEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1Getir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1GetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Egihar1GetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1Ekle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="pay_no" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="durumu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="notu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sertifika_path" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1EkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Egihar1EkleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1TumBilgilerEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="firma_kodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yurticidisi" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="baslangic_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="bitis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="doviz_kodu" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="gerceklesen_tutar" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="sure_gun" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket_ici" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="planlama_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="dokuman_path" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="pay_no" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="durumu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="notu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sertifika_path" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="Egihar1TumBilgilerEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="Egihar1TumBilgilerEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKartiGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="referans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kursno" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EgitimKartiGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EgitimKartiGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelListesi">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelListesiResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelListesiResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DataSet" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:element ref="s:schema" />
            <s:any />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="EgitimParametreGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimParametreGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimParametreGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimParametreMaxEgikodGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimParametreMaxEgikodGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimParametreEkle" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimParametreEkleResponse" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirSoapIn">
    <wsdl:part name="parameters" element="tns:FirmaParametreGetir" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirSoapOut">
    <wsdl:part name="parameters" element="tns:FirmaParametreGetirResponse" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirSoapIn">
    <wsdl:part name="parameters" element="tns:FirmaParametreMaxFirmaKodGetir" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirSoapOut">
    <wsdl:part name="parameters" element="tns:FirmaParametreMaxFirmaKodGetirResponse" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleSoapIn">
    <wsdl:part name="parameters" element="tns:FirmaParametreEkle" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleSoapOut">
    <wsdl:part name="parameters" element="tns:FirmaParametreEkleResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKursGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKursGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKursMaxReferansNoGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKursMaxReferansNoGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKursEkle" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKursEkleResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKursEgitimGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKursEgitimGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKursEgitimEkle" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKursEgitimEkleResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimSinifGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimSinifGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimSinifMaxKursNoGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimSinifMaxKursNoGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimSinifEkle" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimSinifEkleResponse" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirSoapIn">
    <wsdl:part name="parameters" element="tns:Egihar1Getir" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirSoapOut">
    <wsdl:part name="parameters" element="tns:Egihar1GetirResponse" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleSoapIn">
    <wsdl:part name="parameters" element="tns:Egihar1Ekle" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleSoapOut">
    <wsdl:part name="parameters" element="tns:Egihar1EkleResponse" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleSoapIn">
    <wsdl:part name="parameters" element="tns:Egihar1TumBilgilerEkle" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleSoapOut">
    <wsdl:part name="parameters" element="tns:Egihar1TumBilgilerEkleResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EgitimKartiGetir" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EgitimKartiGetirResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelListesi" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelListesiResponse" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="tur_kodu" type="s:string" />
    <wsdl:part name="katalog" type="s:string" />
    <wsdl:part name="aciklama1" type="s:string" />
    <wsdl:part name="aciklama2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="firma_adi" type="s:string" />
    <wsdl:part name="ilgili_kisi" type="s:string" />
    <wsdl:part name="firma_telefonu" type="s:string" />
    <wsdl:part name="firma_email" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="kurs_adi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
    <wsdl:part name="yurticidisi" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="doviz_kodu" type="s:string" />
    <wsdl:part name="gerceklesen_tutar" type="s:string" />
    <wsdl:part name="sure_gun" type="s:string" />
    <wsdl:part name="sirket_ici" type="s:string" />
    <wsdl:part name="planlama_tarihi" type="s:string" />
    <wsdl:part name="dokuman_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sirket" type="s:string" />
    <wsdl:part name="pay_no" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="durumu" type="s:string" />
    <wsdl:part name="notu" type="s:string" />
    <wsdl:part name="sertifika_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kurs_adi" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
    <wsdl:part name="yurticidisi" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="doviz_kodu" type="s:string" />
    <wsdl:part name="gerceklesen_tutar" type="s:string" />
    <wsdl:part name="sure_gun" type="s:string" />
    <wsdl:part name="sirket_ici" type="s:string" />
    <wsdl:part name="planlama_tarihi" type="s:string" />
    <wsdl:part name="dokuman_path" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sirket" type="s:string" />
    <wsdl:part name="pay_no" type="s:string" />
    <wsdl:part name="durumu" type="s:string" />
    <wsdl:part name="notu" type="s:string" />
    <wsdl:part name="sertifika_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreMaxEgikodGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="tur_kodu" type="s:string" />
    <wsdl:part name="katalog" type="s:string" />
    <wsdl:part name="aciklama1" type="s:string" />
    <wsdl:part name="aciklama2" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimParametreEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreMaxFirmaKodGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="firma_adi" type="s:string" />
    <wsdl:part name="ilgili_kisi" type="s:string" />
    <wsdl:part name="firma_telefonu" type="s:string" />
    <wsdl:part name="firma_email" type="s:string" />
  </wsdl:message>
  <wsdl:message name="FirmaParametreEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursMaxReferansNoGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="kurs_adi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="egitim_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKursEgitimEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifMaxKursNoGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
    <wsdl:part name="yurticidisi" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="doviz_kodu" type="s:string" />
    <wsdl:part name="gerceklesen_tutar" type="s:string" />
    <wsdl:part name="sure_gun" type="s:string" />
    <wsdl:part name="sirket_ici" type="s:string" />
    <wsdl:part name="planlama_tarihi" type="s:string" />
    <wsdl:part name="dokuman_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimSinifEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1GetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sirket" type="s:string" />
    <wsdl:part name="pay_no" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="durumu" type="s:string" />
    <wsdl:part name="notu" type="s:string" />
    <wsdl:part name="sertifika_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1EkleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kurs_adi" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="firma_kodu" type="s:string" />
    <wsdl:part name="yurticidisi" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="doviz_kodu" type="s:string" />
    <wsdl:part name="gerceklesen_tutar" type="s:string" />
    <wsdl:part name="sure_gun" type="s:string" />
    <wsdl:part name="sirket_ici" type="s:string" />
    <wsdl:part name="planlama_tarihi" type="s:string" />
    <wsdl:part name="dokuman_path" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sirket" type="s:string" />
    <wsdl:part name="pay_no" type="s:string" />
    <wsdl:part name="durumu" type="s:string" />
    <wsdl:part name="notu" type="s:string" />
    <wsdl:part name="sertifika_path" type="s:string" />
  </wsdl:message>
  <wsdl:message name="Egihar1TumBilgilerEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="referans" type="s:string" />
    <wsdl:part name="kursno" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EgitimKartiGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelListesiHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:portType name="EgitimModuluSoap">
    <wsdl:operation name="EgitimParametreGetir">
      <wsdl:input message="tns:EgitimParametreGetirSoapIn" />
      <wsdl:output message="tns:EgitimParametreGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <wsdl:input message="tns:EgitimParametreMaxEgikodGetirSoapIn" />
      <wsdl:output message="tns:EgitimParametreMaxEgikodGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <wsdl:input message="tns:EgitimParametreEkleSoapIn" />
      <wsdl:output message="tns:EgitimParametreEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <wsdl:input message="tns:FirmaParametreGetirSoapIn" />
      <wsdl:output message="tns:FirmaParametreGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <wsdl:input message="tns:FirmaParametreMaxFirmaKodGetirSoapIn" />
      <wsdl:output message="tns:FirmaParametreMaxFirmaKodGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <wsdl:input message="tns:FirmaParametreEkleSoapIn" />
      <wsdl:output message="tns:FirmaParametreEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <wsdl:input message="tns:EgitimKursGetirSoapIn" />
      <wsdl:output message="tns:EgitimKursGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <wsdl:input message="tns:EgitimKursMaxReferansNoGetirSoapIn" />
      <wsdl:output message="tns:EgitimKursMaxReferansNoGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <wsdl:input message="tns:EgitimKursEkleSoapIn" />
      <wsdl:output message="tns:EgitimKursEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <wsdl:input message="tns:EgitimKursEgitimGetirSoapIn" />
      <wsdl:output message="tns:EgitimKursEgitimGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <wsdl:input message="tns:EgitimKursEgitimEkleSoapIn" />
      <wsdl:output message="tns:EgitimKursEgitimEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <wsdl:input message="tns:EgitimSinifGetirSoapIn" />
      <wsdl:output message="tns:EgitimSinifGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <wsdl:input message="tns:EgitimSinifMaxKursNoGetirSoapIn" />
      <wsdl:output message="tns:EgitimSinifMaxKursNoGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <wsdl:input message="tns:EgitimSinifEkleSoapIn" />
      <wsdl:output message="tns:EgitimSinifEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <wsdl:input message="tns:Egihar1GetirSoapIn" />
      <wsdl:output message="tns:Egihar1GetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <wsdl:input message="tns:Egihar1EkleSoapIn" />
      <wsdl:output message="tns:Egihar1EkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <wsdl:input message="tns:Egihar1TumBilgilerEkleSoapIn" />
      <wsdl:output message="tns:Egihar1TumBilgilerEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <wsdl:input message="tns:EgitimKartiGetirSoapIn" />
      <wsdl:output message="tns:EgitimKartiGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <wsdl:input message="tns:PersonelListesiSoapIn" />
      <wsdl:output message="tns:PersonelListesiSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="EgitimModuluHttpGet">
    <wsdl:operation name="EgitimParametreGetir">
      <wsdl:input message="tns:EgitimParametreGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimParametreGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <wsdl:input message="tns:EgitimParametreMaxEgikodGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimParametreMaxEgikodGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <wsdl:input message="tns:EgitimParametreEkleHttpGetIn" />
      <wsdl:output message="tns:EgitimParametreEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <wsdl:input message="tns:FirmaParametreGetirHttpGetIn" />
      <wsdl:output message="tns:FirmaParametreGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <wsdl:input message="tns:FirmaParametreMaxFirmaKodGetirHttpGetIn" />
      <wsdl:output message="tns:FirmaParametreMaxFirmaKodGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <wsdl:input message="tns:FirmaParametreEkleHttpGetIn" />
      <wsdl:output message="tns:FirmaParametreEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <wsdl:input message="tns:EgitimKursGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimKursGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <wsdl:input message="tns:EgitimKursMaxReferansNoGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimKursMaxReferansNoGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <wsdl:input message="tns:EgitimKursEkleHttpGetIn" />
      <wsdl:output message="tns:EgitimKursEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <wsdl:input message="tns:EgitimKursEgitimGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimKursEgitimGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <wsdl:input message="tns:EgitimKursEgitimEkleHttpGetIn" />
      <wsdl:output message="tns:EgitimKursEgitimEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <wsdl:input message="tns:EgitimSinifGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimSinifGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <wsdl:input message="tns:EgitimSinifMaxKursNoGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimSinifMaxKursNoGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <wsdl:input message="tns:EgitimSinifEkleHttpGetIn" />
      <wsdl:output message="tns:EgitimSinifEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <wsdl:input message="tns:Egihar1GetirHttpGetIn" />
      <wsdl:output message="tns:Egihar1GetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <wsdl:input message="tns:Egihar1EkleHttpGetIn" />
      <wsdl:output message="tns:Egihar1EkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <wsdl:input message="tns:Egihar1TumBilgilerEkleHttpGetIn" />
      <wsdl:output message="tns:Egihar1TumBilgilerEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <wsdl:input message="tns:EgitimKartiGetirHttpGetIn" />
      <wsdl:output message="tns:EgitimKartiGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <wsdl:input message="tns:PersonelListesiHttpGetIn" />
      <wsdl:output message="tns:PersonelListesiHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="EgitimModuluHttpPost">
    <wsdl:operation name="EgitimParametreGetir">
      <wsdl:input message="tns:EgitimParametreGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimParametreGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <wsdl:input message="tns:EgitimParametreMaxEgikodGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimParametreMaxEgikodGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <wsdl:input message="tns:EgitimParametreEkleHttpPostIn" />
      <wsdl:output message="tns:EgitimParametreEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <wsdl:input message="tns:FirmaParametreGetirHttpPostIn" />
      <wsdl:output message="tns:FirmaParametreGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <wsdl:input message="tns:FirmaParametreMaxFirmaKodGetirHttpPostIn" />
      <wsdl:output message="tns:FirmaParametreMaxFirmaKodGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <wsdl:input message="tns:FirmaParametreEkleHttpPostIn" />
      <wsdl:output message="tns:FirmaParametreEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <wsdl:input message="tns:EgitimKursGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimKursGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <wsdl:input message="tns:EgitimKursMaxReferansNoGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimKursMaxReferansNoGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <wsdl:input message="tns:EgitimKursEkleHttpPostIn" />
      <wsdl:output message="tns:EgitimKursEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <wsdl:input message="tns:EgitimKursEgitimGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimKursEgitimGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <wsdl:input message="tns:EgitimKursEgitimEkleHttpPostIn" />
      <wsdl:output message="tns:EgitimKursEgitimEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <wsdl:input message="tns:EgitimSinifGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimSinifGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <wsdl:input message="tns:EgitimSinifMaxKursNoGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimSinifMaxKursNoGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <wsdl:input message="tns:EgitimSinifEkleHttpPostIn" />
      <wsdl:output message="tns:EgitimSinifEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <wsdl:input message="tns:Egihar1GetirHttpPostIn" />
      <wsdl:output message="tns:Egihar1GetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <wsdl:input message="tns:Egihar1EkleHttpPostIn" />
      <wsdl:output message="tns:Egihar1EkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <wsdl:input message="tns:Egihar1TumBilgilerEkleHttpPostIn" />
      <wsdl:output message="tns:Egihar1TumBilgilerEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <wsdl:input message="tns:EgitimKartiGetirHttpPostIn" />
      <wsdl:output message="tns:EgitimKartiGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <wsdl:input message="tns:PersonelListesiHttpPostIn" />
      <wsdl:output message="tns:PersonelListesiHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="EgitimModuluSoap" type="tns:EgitimModuluSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="EgitimParametreGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimParametreGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimParametreMaxEgikodGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <soap:operation soapAction="http://tempuri.org/EgitimParametreEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <soap:operation soapAction="http://tempuri.org/FirmaParametreGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <soap:operation soapAction="http://tempuri.org/FirmaParametreMaxFirmaKodGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <soap:operation soapAction="http://tempuri.org/FirmaParametreEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimKursGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimKursMaxReferansNoGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <soap:operation soapAction="http://tempuri.org/EgitimKursEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimKursEgitimGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <soap:operation soapAction="http://tempuri.org/EgitimKursEgitimEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimSinifGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimSinifMaxKursNoGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <soap:operation soapAction="http://tempuri.org/EgitimSinifEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <soap:operation soapAction="http://tempuri.org/Egihar1Getir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <soap:operation soapAction="http://tempuri.org/Egihar1Ekle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <soap:operation soapAction="http://tempuri.org/Egihar1TumBilgilerEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <soap:operation soapAction="http://tempuri.org/EgitimKartiGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <soap:operation soapAction="http://tempuri.org/PersonelListesi" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EgitimModuluSoap12" type="tns:EgitimModuluSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="EgitimParametreGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimParametreGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimParametreMaxEgikodGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <soap12:operation soapAction="http://tempuri.org/EgitimParametreEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <soap12:operation soapAction="http://tempuri.org/FirmaParametreGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <soap12:operation soapAction="http://tempuri.org/FirmaParametreMaxFirmaKodGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <soap12:operation soapAction="http://tempuri.org/FirmaParametreEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimKursGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimKursMaxReferansNoGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <soap12:operation soapAction="http://tempuri.org/EgitimKursEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimKursEgitimGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <soap12:operation soapAction="http://tempuri.org/EgitimKursEgitimEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimSinifGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimSinifMaxKursNoGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <soap12:operation soapAction="http://tempuri.org/EgitimSinifEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <soap12:operation soapAction="http://tempuri.org/Egihar1Getir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <soap12:operation soapAction="http://tempuri.org/Egihar1Ekle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <soap12:operation soapAction="http://tempuri.org/Egihar1TumBilgilerEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <soap12:operation soapAction="http://tempuri.org/EgitimKartiGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <soap12:operation soapAction="http://tempuri.org/PersonelListesi" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EgitimModuluHttpGet" type="tns:EgitimModuluHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="EgitimParametreGetir">
      <http:operation location="/EgitimParametreGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <http:operation location="/EgitimParametreMaxEgikodGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <http:operation location="/EgitimParametreEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <http:operation location="/FirmaParametreGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <http:operation location="/FirmaParametreMaxFirmaKodGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <http:operation location="/FirmaParametreEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <http:operation location="/EgitimKursGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <http:operation location="/EgitimKursMaxReferansNoGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <http:operation location="/EgitimKursEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <http:operation location="/EgitimKursEgitimGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <http:operation location="/EgitimKursEgitimEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <http:operation location="/EgitimSinifGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <http:operation location="/EgitimSinifMaxKursNoGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <http:operation location="/EgitimSinifEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <http:operation location="/Egihar1Getir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <http:operation location="/Egihar1Ekle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <http:operation location="/Egihar1TumBilgilerEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <http:operation location="/EgitimKartiGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <http:operation location="/PersonelListesi" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="EgitimModuluHttpPost" type="tns:EgitimModuluHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="EgitimParametreGetir">
      <http:operation location="/EgitimParametreGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreMaxEgikodGetir">
      <http:operation location="/EgitimParametreMaxEgikodGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimParametreEkle">
      <http:operation location="/EgitimParametreEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreGetir">
      <http:operation location="/FirmaParametreGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreMaxFirmaKodGetir">
      <http:operation location="/FirmaParametreMaxFirmaKodGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="FirmaParametreEkle">
      <http:operation location="/FirmaParametreEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursGetir">
      <http:operation location="/EgitimKursGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursMaxReferansNoGetir">
      <http:operation location="/EgitimKursMaxReferansNoGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEkle">
      <http:operation location="/EgitimKursEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimGetir">
      <http:operation location="/EgitimKursEgitimGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKursEgitimEkle">
      <http:operation location="/EgitimKursEgitimEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifGetir">
      <http:operation location="/EgitimSinifGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifMaxKursNoGetir">
      <http:operation location="/EgitimSinifMaxKursNoGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimSinifEkle">
      <http:operation location="/EgitimSinifEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Getir">
      <http:operation location="/Egihar1Getir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1Ekle">
      <http:operation location="/Egihar1Ekle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Egihar1TumBilgilerEkle">
      <http:operation location="/Egihar1TumBilgilerEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EgitimKartiGetir">
      <http:operation location="/EgitimKartiGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelListesi">
      <http:operation location="/PersonelListesi" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="EgitimModulu">
    <wsdl:port name="EgitimModuluSoap" binding="tns:EgitimModuluSoap">
      <soap:address location="http://dtl1iis4:3331/EgitimModulu.asmx" />
    </wsdl:port>
    <wsdl:port name="EgitimModuluSoap12" binding="tns:EgitimModuluSoap12">
      <soap12:address location="http://dtl1iis4:3331/EgitimModulu.asmx" />
    </wsdl:port>
    <wsdl:port name="EgitimModuluHttpGet" binding="tns:EgitimModuluHttpGet">
      <http:address location="http://dtl1iis4:3331/EgitimModulu.asmx" />
    </wsdl:port>
    <wsdl:port name="EgitimModuluHttpPost" binding="tns:EgitimModuluHttpPost">
      <http:address location="http://dtl1iis4:3331/EgitimModulu.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>