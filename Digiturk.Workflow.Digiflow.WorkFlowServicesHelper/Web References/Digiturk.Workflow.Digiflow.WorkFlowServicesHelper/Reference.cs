﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.Workflow.Digiflow.WorkFlowServicesHelper {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="EgitimModuluSoap", Namespace="http://tempuri.org/")]
    public partial class EgitimModulu : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback EgitimParametreGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimParametreMaxEgikodGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimParametreEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback FirmaParametreGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback FirmaParametreMaxFirmaKodGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback FirmaParametreEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKursGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKursMaxReferansNoGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKursEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKursEgitimGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKursEgitimEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimSinifGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimSinifMaxKursNoGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimSinifEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback Egihar1GetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback Egihar1EkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback Egihar1TumBilgilerEkleOperationCompleted;
        
        private System.Threading.SendOrPostCallback EgitimKartiGetirOperationCompleted;
        
        private System.Threading.SendOrPostCallback PersonelListesiOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public EgitimModulu() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_EgitimModulu;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event EgitimParametreGetirCompletedEventHandler EgitimParametreGetirCompleted;
        
        /// <remarks/>
        public event EgitimParametreMaxEgikodGetirCompletedEventHandler EgitimParametreMaxEgikodGetirCompleted;
        
        /// <remarks/>
        public event EgitimParametreEkleCompletedEventHandler EgitimParametreEkleCompleted;
        
        /// <remarks/>
        public event FirmaParametreGetirCompletedEventHandler FirmaParametreGetirCompleted;
        
        /// <remarks/>
        public event FirmaParametreMaxFirmaKodGetirCompletedEventHandler FirmaParametreMaxFirmaKodGetirCompleted;
        
        /// <remarks/>
        public event FirmaParametreEkleCompletedEventHandler FirmaParametreEkleCompleted;
        
        /// <remarks/>
        public event EgitimKursGetirCompletedEventHandler EgitimKursGetirCompleted;
        
        /// <remarks/>
        public event EgitimKursMaxReferansNoGetirCompletedEventHandler EgitimKursMaxReferansNoGetirCompleted;
        
        /// <remarks/>
        public event EgitimKursEkleCompletedEventHandler EgitimKursEkleCompleted;
        
        /// <remarks/>
        public event EgitimKursEgitimGetirCompletedEventHandler EgitimKursEgitimGetirCompleted;
        
        /// <remarks/>
        public event EgitimKursEgitimEkleCompletedEventHandler EgitimKursEgitimEkleCompleted;
        
        /// <remarks/>
        public event EgitimSinifGetirCompletedEventHandler EgitimSinifGetirCompleted;
        
        /// <remarks/>
        public event EgitimSinifMaxKursNoGetirCompletedEventHandler EgitimSinifMaxKursNoGetirCompleted;
        
        /// <remarks/>
        public event EgitimSinifEkleCompletedEventHandler EgitimSinifEkleCompleted;
        
        /// <remarks/>
        public event Egihar1GetirCompletedEventHandler Egihar1GetirCompleted;
        
        /// <remarks/>
        public event Egihar1EkleCompletedEventHandler Egihar1EkleCompleted;
        
        /// <remarks/>
        public event Egihar1TumBilgilerEkleCompletedEventHandler Egihar1TumBilgilerEkleCompleted;
        
        /// <remarks/>
        public event EgitimKartiGetirCompletedEventHandler EgitimKartiGetirCompleted;
        
        /// <remarks/>
        public event PersonelListesiCompletedEventHandler PersonelListesiCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimParametreGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimParametreGetir(string kul_adi, string kul_sifre, string egitim_kodu) {
            object[] results = this.Invoke("EgitimParametreGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        egitim_kodu});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimParametreGetirAsync(string kul_adi, string kul_sifre, string egitim_kodu) {
            this.EgitimParametreGetirAsync(kul_adi, kul_sifre, egitim_kodu, null);
        }
        
        /// <remarks/>
        public void EgitimParametreGetirAsync(string kul_adi, string kul_sifre, string egitim_kodu, object userState) {
            if ((this.EgitimParametreGetirOperationCompleted == null)) {
                this.EgitimParametreGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimParametreGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimParametreGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        egitim_kodu}, this.EgitimParametreGetirOperationCompleted, userState);
        }
        
        private void OnEgitimParametreGetirOperationCompleted(object arg) {
            if ((this.EgitimParametreGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimParametreGetirCompleted(this, new EgitimParametreGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimParametreMaxEgikodGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimParametreMaxEgikodGetir(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("EgitimParametreMaxEgikodGetir", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimParametreMaxEgikodGetirAsync(string kul_adi, string kul_sifre) {
            this.EgitimParametreMaxEgikodGetirAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void EgitimParametreMaxEgikodGetirAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.EgitimParametreMaxEgikodGetirOperationCompleted == null)) {
                this.EgitimParametreMaxEgikodGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimParametreMaxEgikodGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimParametreMaxEgikodGetir", new object[] {
                        kul_adi,
                        kul_sifre}, this.EgitimParametreMaxEgikodGetirOperationCompleted, userState);
        }
        
        private void OnEgitimParametreMaxEgikodGetirOperationCompleted(object arg) {
            if ((this.EgitimParametreMaxEgikodGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimParametreMaxEgikodGetirCompleted(this, new EgitimParametreMaxEgikodGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimParametreEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimParametreEkle(string kul_adi, string kul_sifre, string tur_kodu, string katalog, string aciklama1, string aciklama2) {
            object[] results = this.Invoke("EgitimParametreEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        tur_kodu,
                        katalog,
                        aciklama1,
                        aciklama2});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimParametreEkleAsync(string kul_adi, string kul_sifre, string tur_kodu, string katalog, string aciklama1, string aciklama2) {
            this.EgitimParametreEkleAsync(kul_adi, kul_sifre, tur_kodu, katalog, aciklama1, aciklama2, null);
        }
        
        /// <remarks/>
        public void EgitimParametreEkleAsync(string kul_adi, string kul_sifre, string tur_kodu, string katalog, string aciklama1, string aciklama2, object userState) {
            if ((this.EgitimParametreEkleOperationCompleted == null)) {
                this.EgitimParametreEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimParametreEkleOperationCompleted);
            }
            this.InvokeAsync("EgitimParametreEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        tur_kodu,
                        katalog,
                        aciklama1,
                        aciklama2}, this.EgitimParametreEkleOperationCompleted, userState);
        }
        
        private void OnEgitimParametreEkleOperationCompleted(object arg) {
            if ((this.EgitimParametreEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimParametreEkleCompleted(this, new EgitimParametreEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/FirmaParametreGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet FirmaParametreGetir(string kul_adi, string kul_sifre, string firma_kodu) {
            object[] results = this.Invoke("FirmaParametreGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        firma_kodu});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void FirmaParametreGetirAsync(string kul_adi, string kul_sifre, string firma_kodu) {
            this.FirmaParametreGetirAsync(kul_adi, kul_sifre, firma_kodu, null);
        }
        
        /// <remarks/>
        public void FirmaParametreGetirAsync(string kul_adi, string kul_sifre, string firma_kodu, object userState) {
            if ((this.FirmaParametreGetirOperationCompleted == null)) {
                this.FirmaParametreGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnFirmaParametreGetirOperationCompleted);
            }
            this.InvokeAsync("FirmaParametreGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        firma_kodu}, this.FirmaParametreGetirOperationCompleted, userState);
        }
        
        private void OnFirmaParametreGetirOperationCompleted(object arg) {
            if ((this.FirmaParametreGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.FirmaParametreGetirCompleted(this, new FirmaParametreGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/FirmaParametreMaxFirmaKodGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string FirmaParametreMaxFirmaKodGetir(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("FirmaParametreMaxFirmaKodGetir", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void FirmaParametreMaxFirmaKodGetirAsync(string kul_adi, string kul_sifre) {
            this.FirmaParametreMaxFirmaKodGetirAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void FirmaParametreMaxFirmaKodGetirAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.FirmaParametreMaxFirmaKodGetirOperationCompleted == null)) {
                this.FirmaParametreMaxFirmaKodGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnFirmaParametreMaxFirmaKodGetirOperationCompleted);
            }
            this.InvokeAsync("FirmaParametreMaxFirmaKodGetir", new object[] {
                        kul_adi,
                        kul_sifre}, this.FirmaParametreMaxFirmaKodGetirOperationCompleted, userState);
        }
        
        private void OnFirmaParametreMaxFirmaKodGetirOperationCompleted(object arg) {
            if ((this.FirmaParametreMaxFirmaKodGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.FirmaParametreMaxFirmaKodGetirCompleted(this, new FirmaParametreMaxFirmaKodGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/FirmaParametreEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string FirmaParametreEkle(string kul_adi, string kul_sifre, string firma_adi, string ilgili_kisi, string firma_telefonu, string firma_email) {
            object[] results = this.Invoke("FirmaParametreEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        firma_adi,
                        ilgili_kisi,
                        firma_telefonu,
                        firma_email});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void FirmaParametreEkleAsync(string kul_adi, string kul_sifre, string firma_adi, string ilgili_kisi, string firma_telefonu, string firma_email) {
            this.FirmaParametreEkleAsync(kul_adi, kul_sifre, firma_adi, ilgili_kisi, firma_telefonu, firma_email, null);
        }
        
        /// <remarks/>
        public void FirmaParametreEkleAsync(string kul_adi, string kul_sifre, string firma_adi, string ilgili_kisi, string firma_telefonu, string firma_email, object userState) {
            if ((this.FirmaParametreEkleOperationCompleted == null)) {
                this.FirmaParametreEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnFirmaParametreEkleOperationCompleted);
            }
            this.InvokeAsync("FirmaParametreEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        firma_adi,
                        ilgili_kisi,
                        firma_telefonu,
                        firma_email}, this.FirmaParametreEkleOperationCompleted, userState);
        }
        
        private void OnFirmaParametreEkleOperationCompleted(object arg) {
            if ((this.FirmaParametreEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.FirmaParametreEkleCompleted(this, new FirmaParametreEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKursGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimKursGetir(string kul_adi, string kul_sifre, string referans) {
            object[] results = this.Invoke("EgitimKursGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKursGetirAsync(string kul_adi, string kul_sifre, string referans) {
            this.EgitimKursGetirAsync(kul_adi, kul_sifre, referans, null);
        }
        
        /// <remarks/>
        public void EgitimKursGetirAsync(string kul_adi, string kul_sifre, string referans, object userState) {
            if ((this.EgitimKursGetirOperationCompleted == null)) {
                this.EgitimKursGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKursGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimKursGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans}, this.EgitimKursGetirOperationCompleted, userState);
        }
        
        private void OnEgitimKursGetirOperationCompleted(object arg) {
            if ((this.EgitimKursGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKursGetirCompleted(this, new EgitimKursGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKursMaxReferansNoGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimKursMaxReferansNoGetir(string kul_adi, string kul_sifre) {
            object[] results = this.Invoke("EgitimKursMaxReferansNoGetir", new object[] {
                        kul_adi,
                        kul_sifre});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKursMaxReferansNoGetirAsync(string kul_adi, string kul_sifre) {
            this.EgitimKursMaxReferansNoGetirAsync(kul_adi, kul_sifre, null);
        }
        
        /// <remarks/>
        public void EgitimKursMaxReferansNoGetirAsync(string kul_adi, string kul_sifre, object userState) {
            if ((this.EgitimKursMaxReferansNoGetirOperationCompleted == null)) {
                this.EgitimKursMaxReferansNoGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKursMaxReferansNoGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimKursMaxReferansNoGetir", new object[] {
                        kul_adi,
                        kul_sifre}, this.EgitimKursMaxReferansNoGetirOperationCompleted, userState);
        }
        
        private void OnEgitimKursMaxReferansNoGetirOperationCompleted(object arg) {
            if ((this.EgitimKursMaxReferansNoGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKursMaxReferansNoGetirCompleted(this, new EgitimKursMaxReferansNoGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKursEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimKursEkle(string kul_adi, string kul_sifre, string kurs_adi) {
            object[] results = this.Invoke("EgitimKursEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        kurs_adi});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKursEkleAsync(string kul_adi, string kul_sifre, string kurs_adi) {
            this.EgitimKursEkleAsync(kul_adi, kul_sifre, kurs_adi, null);
        }
        
        /// <remarks/>
        public void EgitimKursEkleAsync(string kul_adi, string kul_sifre, string kurs_adi, object userState) {
            if ((this.EgitimKursEkleOperationCompleted == null)) {
                this.EgitimKursEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKursEkleOperationCompleted);
            }
            this.InvokeAsync("EgitimKursEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        kurs_adi}, this.EgitimKursEkleOperationCompleted, userState);
        }
        
        private void OnEgitimKursEkleOperationCompleted(object arg) {
            if ((this.EgitimKursEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKursEkleCompleted(this, new EgitimKursEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKursEgitimGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimKursEgitimGetir(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu) {
            object[] results = this.Invoke("EgitimKursEgitimGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        egitim_kodu});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKursEgitimGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu) {
            this.EgitimKursEgitimGetirAsync(kul_adi, kul_sifre, referans, kursno, egitim_kodu, null);
        }
        
        /// <remarks/>
        public void EgitimKursEgitimGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu, object userState) {
            if ((this.EgitimKursEgitimGetirOperationCompleted == null)) {
                this.EgitimKursEgitimGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKursEgitimGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimKursEgitimGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        egitim_kodu}, this.EgitimKursEgitimGetirOperationCompleted, userState);
        }
        
        private void OnEgitimKursEgitimGetirOperationCompleted(object arg) {
            if ((this.EgitimKursEgitimGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKursEgitimGetirCompleted(this, new EgitimKursEgitimGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKursEgitimEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimKursEgitimEkle(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu) {
            object[] results = this.Invoke("EgitimKursEgitimEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        egitim_kodu});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKursEgitimEkleAsync(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu) {
            this.EgitimKursEgitimEkleAsync(kul_adi, kul_sifre, referans, kursno, egitim_kodu, null);
        }
        
        /// <remarks/>
        public void EgitimKursEgitimEkleAsync(string kul_adi, string kul_sifre, string referans, string kursno, string egitim_kodu, object userState) {
            if ((this.EgitimKursEgitimEkleOperationCompleted == null)) {
                this.EgitimKursEgitimEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKursEgitimEkleOperationCompleted);
            }
            this.InvokeAsync("EgitimKursEgitimEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        egitim_kodu}, this.EgitimKursEgitimEkleOperationCompleted, userState);
        }
        
        private void OnEgitimKursEgitimEkleOperationCompleted(object arg) {
            if ((this.EgitimKursEgitimEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKursEgitimEkleCompleted(this, new EgitimKursEgitimEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimSinifGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimSinifGetir(string kul_adi, string kul_sifre, string referans, string kursno) {
            object[] results = this.Invoke("EgitimSinifGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimSinifGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno) {
            this.EgitimSinifGetirAsync(kul_adi, kul_sifre, referans, kursno, null);
        }
        
        /// <remarks/>
        public void EgitimSinifGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, object userState) {
            if ((this.EgitimSinifGetirOperationCompleted == null)) {
                this.EgitimSinifGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimSinifGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimSinifGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno}, this.EgitimSinifGetirOperationCompleted, userState);
        }
        
        private void OnEgitimSinifGetirOperationCompleted(object arg) {
            if ((this.EgitimSinifGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimSinifGetirCompleted(this, new EgitimSinifGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimSinifMaxKursNoGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimSinifMaxKursNoGetir(string kul_adi, string kul_sifre, string referans) {
            object[] results = this.Invoke("EgitimSinifMaxKursNoGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimSinifMaxKursNoGetirAsync(string kul_adi, string kul_sifre, string referans) {
            this.EgitimSinifMaxKursNoGetirAsync(kul_adi, kul_sifre, referans, null);
        }
        
        /// <remarks/>
        public void EgitimSinifMaxKursNoGetirAsync(string kul_adi, string kul_sifre, string referans, object userState) {
            if ((this.EgitimSinifMaxKursNoGetirOperationCompleted == null)) {
                this.EgitimSinifMaxKursNoGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimSinifMaxKursNoGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimSinifMaxKursNoGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans}, this.EgitimSinifMaxKursNoGetirOperationCompleted, userState);
        }
        
        private void OnEgitimSinifMaxKursNoGetirOperationCompleted(object arg) {
            if ((this.EgitimSinifMaxKursNoGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimSinifMaxKursNoGetirCompleted(this, new EgitimSinifMaxKursNoGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimSinifEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string EgitimSinifEkle(string kul_adi, string kul_sifre, string referans, string firma_kodu, string yurticidisi, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string doviz_kodu, decimal gerceklesen_tutar, short sure_gun, string sirket_ici, System.DateTime planlama_tarihi, string dokuman_path) {
            object[] results = this.Invoke("EgitimSinifEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        firma_kodu,
                        yurticidisi,
                        baslangic_tarihi,
                        bitis_tarihi,
                        doviz_kodu,
                        gerceklesen_tutar,
                        sure_gun,
                        sirket_ici,
                        planlama_tarihi,
                        dokuman_path});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimSinifEkleAsync(string kul_adi, string kul_sifre, string referans, string firma_kodu, string yurticidisi, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string doviz_kodu, decimal gerceklesen_tutar, short sure_gun, string sirket_ici, System.DateTime planlama_tarihi, string dokuman_path) {
            this.EgitimSinifEkleAsync(kul_adi, kul_sifre, referans, firma_kodu, yurticidisi, baslangic_tarihi, bitis_tarihi, doviz_kodu, gerceklesen_tutar, sure_gun, sirket_ici, planlama_tarihi, dokuman_path, null);
        }
        
        /// <remarks/>
        public void EgitimSinifEkleAsync(string kul_adi, string kul_sifre, string referans, string firma_kodu, string yurticidisi, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string doviz_kodu, decimal gerceklesen_tutar, short sure_gun, string sirket_ici, System.DateTime planlama_tarihi, string dokuman_path, object userState) {
            if ((this.EgitimSinifEkleOperationCompleted == null)) {
                this.EgitimSinifEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimSinifEkleOperationCompleted);
            }
            this.InvokeAsync("EgitimSinifEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        firma_kodu,
                        yurticidisi,
                        baslangic_tarihi,
                        bitis_tarihi,
                        doviz_kodu,
                        gerceklesen_tutar,
                        sure_gun,
                        sirket_ici,
                        planlama_tarihi,
                        dokuman_path}, this.EgitimSinifEkleOperationCompleted, userState);
        }
        
        private void OnEgitimSinifEkleOperationCompleted(object arg) {
            if ((this.EgitimSinifEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimSinifEkleCompleted(this, new EgitimSinifEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Egihar1Getir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet Egihar1Getir(string kul_adi, string kul_sifre, string referans, string kursno, string sicil) {
            object[] results = this.Invoke("Egihar1Getir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void Egihar1GetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string sicil) {
            this.Egihar1GetirAsync(kul_adi, kul_sifre, referans, kursno, sicil, null);
        }
        
        /// <remarks/>
        public void Egihar1GetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string sicil, object userState) {
            if ((this.Egihar1GetirOperationCompleted == null)) {
                this.Egihar1GetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgihar1GetirOperationCompleted);
            }
            this.InvokeAsync("Egihar1Getir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        sicil}, this.Egihar1GetirOperationCompleted, userState);
        }
        
        private void OnEgihar1GetirOperationCompleted(object arg) {
            if ((this.Egihar1GetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.Egihar1GetirCompleted(this, new Egihar1GetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Egihar1Ekle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet Egihar1Ekle(string kul_adi, string kul_sifre, string sicil, string sirket, string pay_no, string referans, string kursno, string durumu, string notu, string sertifika_path) {
            object[] results = this.Invoke("Egihar1Ekle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sirket,
                        pay_no,
                        referans,
                        kursno,
                        durumu,
                        notu,
                        sertifika_path});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void Egihar1EkleAsync(string kul_adi, string kul_sifre, string sicil, string sirket, string pay_no, string referans, string kursno, string durumu, string notu, string sertifika_path) {
            this.Egihar1EkleAsync(kul_adi, kul_sifre, sicil, sirket, pay_no, referans, kursno, durumu, notu, sertifika_path, null);
        }
        
        /// <remarks/>
        public void Egihar1EkleAsync(string kul_adi, string kul_sifre, string sicil, string sirket, string pay_no, string referans, string kursno, string durumu, string notu, string sertifika_path, object userState) {
            if ((this.Egihar1EkleOperationCompleted == null)) {
                this.Egihar1EkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgihar1EkleOperationCompleted);
            }
            this.InvokeAsync("Egihar1Ekle", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil,
                        sirket,
                        pay_no,
                        referans,
                        kursno,
                        durumu,
                        notu,
                        sertifika_path}, this.Egihar1EkleOperationCompleted, userState);
        }
        
        private void OnEgihar1EkleOperationCompleted(object arg) {
            if ((this.Egihar1EkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.Egihar1EkleCompleted(this, new Egihar1EkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/Egihar1TumBilgilerEkle", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public string Egihar1TumBilgilerEkle(
                    string kul_adi, 
                    string kul_sifre, 
                    string referans, 
                    string kurs_adi, 
                    string kursno, 
                    string firma_kodu, 
                    string yurticidisi, 
                    System.DateTime baslangic_tarihi, 
                    System.DateTime bitis_tarihi, 
                    string doviz_kodu, 
                    decimal gerceklesen_tutar, 
                    short sure_gun, 
                    string sirket_ici, 
                    System.DateTime planlama_tarihi, 
                    string dokuman_path, 
                    string sicil, 
                    string sirket, 
                    string pay_no, 
                    string durumu, 
                    string notu, 
                    string sertifika_path) {
            object[] results = this.Invoke("Egihar1TumBilgilerEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kurs_adi,
                        kursno,
                        firma_kodu,
                        yurticidisi,
                        baslangic_tarihi,
                        bitis_tarihi,
                        doviz_kodu,
                        gerceklesen_tutar,
                        sure_gun,
                        sirket_ici,
                        planlama_tarihi,
                        dokuman_path,
                        sicil,
                        sirket,
                        pay_no,
                        durumu,
                        notu,
                        sertifika_path});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void Egihar1TumBilgilerEkleAsync(
                    string kul_adi, 
                    string kul_sifre, 
                    string referans, 
                    string kurs_adi, 
                    string kursno, 
                    string firma_kodu, 
                    string yurticidisi, 
                    System.DateTime baslangic_tarihi, 
                    System.DateTime bitis_tarihi, 
                    string doviz_kodu, 
                    decimal gerceklesen_tutar, 
                    short sure_gun, 
                    string sirket_ici, 
                    System.DateTime planlama_tarihi, 
                    string dokuman_path, 
                    string sicil, 
                    string sirket, 
                    string pay_no, 
                    string durumu, 
                    string notu, 
                    string sertifika_path) {
            this.Egihar1TumBilgilerEkleAsync(kul_adi, kul_sifre, referans, kurs_adi, kursno, firma_kodu, yurticidisi, baslangic_tarihi, bitis_tarihi, doviz_kodu, gerceklesen_tutar, sure_gun, sirket_ici, planlama_tarihi, dokuman_path, sicil, sirket, pay_no, durumu, notu, sertifika_path, null);
        }
        
        /// <remarks/>
        public void Egihar1TumBilgilerEkleAsync(
                    string kul_adi, 
                    string kul_sifre, 
                    string referans, 
                    string kurs_adi, 
                    string kursno, 
                    string firma_kodu, 
                    string yurticidisi, 
                    System.DateTime baslangic_tarihi, 
                    System.DateTime bitis_tarihi, 
                    string doviz_kodu, 
                    decimal gerceklesen_tutar, 
                    short sure_gun, 
                    string sirket_ici, 
                    System.DateTime planlama_tarihi, 
                    string dokuman_path, 
                    string sicil, 
                    string sirket, 
                    string pay_no, 
                    string durumu, 
                    string notu, 
                    string sertifika_path, 
                    object userState) {
            if ((this.Egihar1TumBilgilerEkleOperationCompleted == null)) {
                this.Egihar1TumBilgilerEkleOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgihar1TumBilgilerEkleOperationCompleted);
            }
            this.InvokeAsync("Egihar1TumBilgilerEkle", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kurs_adi,
                        kursno,
                        firma_kodu,
                        yurticidisi,
                        baslangic_tarihi,
                        bitis_tarihi,
                        doviz_kodu,
                        gerceklesen_tutar,
                        sure_gun,
                        sirket_ici,
                        planlama_tarihi,
                        dokuman_path,
                        sicil,
                        sirket,
                        pay_no,
                        durumu,
                        notu,
                        sertifika_path}, this.Egihar1TumBilgilerEkleOperationCompleted, userState);
        }
        
        private void OnEgihar1TumBilgilerEkleOperationCompleted(object arg) {
            if ((this.Egihar1TumBilgilerEkleCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.Egihar1TumBilgilerEkleCompleted(this, new Egihar1TumBilgilerEkleCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/EgitimKartiGetir", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet EgitimKartiGetir(string kul_adi, string kul_sifre, string referans, string kursno, string sicil) {
            object[] results = this.Invoke("EgitimKartiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void EgitimKartiGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string sicil) {
            this.EgitimKartiGetirAsync(kul_adi, kul_sifre, referans, kursno, sicil, null);
        }
        
        /// <remarks/>
        public void EgitimKartiGetirAsync(string kul_adi, string kul_sifre, string referans, string kursno, string sicil, object userState) {
            if ((this.EgitimKartiGetirOperationCompleted == null)) {
                this.EgitimKartiGetirOperationCompleted = new System.Threading.SendOrPostCallback(this.OnEgitimKartiGetirOperationCompleted);
            }
            this.InvokeAsync("EgitimKartiGetir", new object[] {
                        kul_adi,
                        kul_sifre,
                        referans,
                        kursno,
                        sicil}, this.EgitimKartiGetirOperationCompleted, userState);
        }
        
        private void OnEgitimKartiGetirOperationCompleted(object arg) {
            if ((this.EgitimKartiGetirCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.EgitimKartiGetirCompleted(this, new EgitimKartiGetirCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/PersonelListesi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataSet PersonelListesi(string kul_adi, string kul_sifre, string sicil) {
            object[] results = this.Invoke("PersonelListesi", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil});
            return ((System.Data.DataSet)(results[0]));
        }
        
        /// <remarks/>
        public void PersonelListesiAsync(string kul_adi, string kul_sifre, string sicil) {
            this.PersonelListesiAsync(kul_adi, kul_sifre, sicil, null);
        }
        
        /// <remarks/>
        public void PersonelListesiAsync(string kul_adi, string kul_sifre, string sicil, object userState) {
            if ((this.PersonelListesiOperationCompleted == null)) {
                this.PersonelListesiOperationCompleted = new System.Threading.SendOrPostCallback(this.OnPersonelListesiOperationCompleted);
            }
            this.InvokeAsync("PersonelListesi", new object[] {
                        kul_adi,
                        kul_sifre,
                        sicil}, this.PersonelListesiOperationCompleted, userState);
        }
        
        private void OnPersonelListesiOperationCompleted(object arg) {
            if ((this.PersonelListesiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.PersonelListesiCompleted(this, new PersonelListesiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimParametreGetirCompletedEventHandler(object sender, EgitimParametreGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimParametreGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimParametreGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimParametreMaxEgikodGetirCompletedEventHandler(object sender, EgitimParametreMaxEgikodGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimParametreMaxEgikodGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimParametreMaxEgikodGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimParametreEkleCompletedEventHandler(object sender, EgitimParametreEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimParametreEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimParametreEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void FirmaParametreGetirCompletedEventHandler(object sender, FirmaParametreGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class FirmaParametreGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal FirmaParametreGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void FirmaParametreMaxFirmaKodGetirCompletedEventHandler(object sender, FirmaParametreMaxFirmaKodGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class FirmaParametreMaxFirmaKodGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal FirmaParametreMaxFirmaKodGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void FirmaParametreEkleCompletedEventHandler(object sender, FirmaParametreEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class FirmaParametreEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal FirmaParametreEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKursGetirCompletedEventHandler(object sender, EgitimKursGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKursGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKursGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKursMaxReferansNoGetirCompletedEventHandler(object sender, EgitimKursMaxReferansNoGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKursMaxReferansNoGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKursMaxReferansNoGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKursEkleCompletedEventHandler(object sender, EgitimKursEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKursEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKursEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKursEgitimGetirCompletedEventHandler(object sender, EgitimKursEgitimGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKursEgitimGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKursEgitimGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKursEgitimEkleCompletedEventHandler(object sender, EgitimKursEgitimEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKursEgitimEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKursEgitimEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimSinifGetirCompletedEventHandler(object sender, EgitimSinifGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimSinifGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimSinifGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimSinifMaxKursNoGetirCompletedEventHandler(object sender, EgitimSinifMaxKursNoGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimSinifMaxKursNoGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimSinifMaxKursNoGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimSinifEkleCompletedEventHandler(object sender, EgitimSinifEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimSinifEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimSinifEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void Egihar1GetirCompletedEventHandler(object sender, Egihar1GetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Egihar1GetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal Egihar1GetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void Egihar1EkleCompletedEventHandler(object sender, Egihar1EkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Egihar1EkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal Egihar1EkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void Egihar1TumBilgilerEkleCompletedEventHandler(object sender, Egihar1TumBilgilerEkleCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class Egihar1TumBilgilerEkleCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal Egihar1TumBilgilerEkleCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void EgitimKartiGetirCompletedEventHandler(object sender, EgitimKartiGetirCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class EgitimKartiGetirCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal EgitimKartiGetirCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void PersonelListesiCompletedEventHandler(object sender, PersonelListesiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class PersonelListesiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal PersonelListesiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataSet Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataSet)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591