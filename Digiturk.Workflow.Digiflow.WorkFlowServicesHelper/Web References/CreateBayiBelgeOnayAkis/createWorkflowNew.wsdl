<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="CancelDTSatisliTicariYayinAcmaAkisi">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="Comment" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CancelDTSatisliTicariYayinAcmaAkisiResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CancelDTSatisliTicariYayinAcmaAkisiResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDTSatisliTicariYayinAcmaAkisi">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AccountNumber" nillable="true" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="ProspectNumber" nillable="true" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="Ad" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Soyad" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="AdresUlke" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="AdresIl" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="AdresIlce" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="AdresDetay" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="AdresUAVTId" nillable="true" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="AdresGPSLokasyon" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiKodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiAdi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiPersonelKodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiAdresIl" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiAdresIlce" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiBolgeKodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiBolgeAdi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiTemsilciDbsKodu" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="IrisKayitId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="UyduTipi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TicariGrupKod" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TicariGrupAdi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="OdemeTipi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="OdemeTipiDescr" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="SimpleOfferId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="SimpleOfferIdDescr" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="BundleOfferId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="BundleOfferIdDescr" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Frekans" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="FrekansDescr" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="IndirimOrani" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="ListeFiyati" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="IndirimUygulanmisFiyat" type="s:decimal" />
            <s:element minOccurs="0" maxOccurs="1" name="Aciklama" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="Offerbusinessinterid" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="Alterationbusinessinterid" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="ServiceAccountId" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="DbsoutletLocation" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateDTSatisliTicariYayinAcmaAkisiResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateDTSatisliTicariYayinAcmaAkisiResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariGrupDegisikligiAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="UYE_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="UYE_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_AD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_BOLGE_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_YONETICI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ACIKLAMA" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_PESIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_TAKSITLI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_PESIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_TAKSITLI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ADRES" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="IL" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ILCE" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DEMOGRAFIK" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_MAIL" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="POTANSIYEL_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="POTANSIYEL_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="KAYNAK" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariGrupDegisikligiAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateTicariGrupDegisikligiAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariFiyatIstisnaAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="UYE_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="UYE_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_AD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_BOLGE_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_YONETICI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ACIKLAMA" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ADRES" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="IL" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ILCE" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DEMOGRAFIK" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ODEME_TIPI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MEVCUT_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="MEVCUT_FIYAT" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ISTENEN_FIYAT" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_MAIL" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="POTANSIYEL_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="POTANSIYEL_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="KAYNAK" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariFiyatIstisnaAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateTicariFiyatIstisnaAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariUyeYetkilendirmeAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="UYE_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="UYE_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_AD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_BOLGE_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_YONETICI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ACIKLAMA" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_PESIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_TAKSITLI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_PESIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="YENI_TAKSITLI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ADRES" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="IL" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ILCE" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DEMOGRAFIK" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_MAIL" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="POTANSIYEL_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="POTANSIYEL_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="KAYNAK" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_IL" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariUyeYetkilendirmeAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateTicariUyeYetkilendirmeAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariUcretIadeAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="UYE_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="UYE_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_AD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_BOLGE_KOD" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_YONETICI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ACIKLAMA" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_GRUP" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_PESIN" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ESKI_TAKSITLI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ADRES" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="IL" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ILCE" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="DEMOGRAFIK" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BAYI_MAIL" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="POTANSIYEL_NO" nillable="true" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="POTANSIYEL_ADI" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="GPS_LOKASYON" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="KAYNAK" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateTicariUcretIadeAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateTicariUcretIadeAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateBayiFotografDegisikligiAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IRIS_ID" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="personel_id" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="personel_ad_soyad" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="calistigi_bayi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="onceki_calistigi_bayi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bayi_id" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="foto_adres" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="gorevi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="calisma_sekli" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ise_baslama_tarihi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TeknikServisYonetici" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="Satistemsilcisi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateBayiFotografDegisikligiAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateBayiFotografDegisikligiAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="CreateBayiBelgeOnayAkis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IrisTalepID" type="s:long" />
            <s:element minOccurs="0" maxOccurs="1" name="PersonelId" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="PersonelAdSoyad" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="PersonelGorevi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="CalistigiBayi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiKodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TeknikServisYonetici" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="SatisTemsilcisi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="TeknikServisMuduru" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="BayiBelgeDokuman" type="tns:ArrayOfBayiBelgeDokuman" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfBayiBelgeDokuman">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="BayiBelgeDokuman" nillable="true" type="tns:BayiBelgeDokuman" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="BayiBelgeDokuman">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="DokumanId" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DokumanAdi" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DokumanLinki" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="DokumanYuklemeTarihi" type="s:dateTime" />
        </s:sequence>
      </s:complexType>
      <s:element name="CreateBayiBelgeOnayAkisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="CreateBayiBelgeOnayAkisResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiSoapIn">
    <wsdl:part name="parameters" element="tns:CancelDTSatisliTicariYayinAcmaAkisi" />
  </wsdl:message>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiSoapOut">
    <wsdl:part name="parameters" element="tns:CancelDTSatisliTicariYayinAcmaAkisiResponse" />
  </wsdl:message>
  <wsdl:message name="CreateDTSatisliTicariYayinAcmaAkisiSoapIn">
    <wsdl:part name="parameters" element="tns:CreateDTSatisliTicariYayinAcmaAkisi" />
  </wsdl:message>
  <wsdl:message name="CreateDTSatisliTicariYayinAcmaAkisiSoapOut">
    <wsdl:part name="parameters" element="tns:CreateDTSatisliTicariYayinAcmaAkisiResponse" />
  </wsdl:message>
  <wsdl:message name="CreateTicariGrupDegisikligiAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateTicariGrupDegisikligiAkis" />
  </wsdl:message>
  <wsdl:message name="CreateTicariGrupDegisikligiAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateTicariGrupDegisikligiAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CreateTicariFiyatIstisnaAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateTicariFiyatIstisnaAkis" />
  </wsdl:message>
  <wsdl:message name="CreateTicariFiyatIstisnaAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateTicariFiyatIstisnaAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CreateTicariUyeYetkilendirmeAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateTicariUyeYetkilendirmeAkis" />
  </wsdl:message>
  <wsdl:message name="CreateTicariUyeYetkilendirmeAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateTicariUyeYetkilendirmeAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CreateTicariUcretIadeAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateTicariUcretIadeAkis" />
  </wsdl:message>
  <wsdl:message name="CreateTicariUcretIadeAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateTicariUcretIadeAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateBayiFotografDegisikligiAkis" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateBayiFotografDegisikligiAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CreateBayiBelgeOnayAkisSoapIn">
    <wsdl:part name="parameters" element="tns:CreateBayiBelgeOnayAkis" />
  </wsdl:message>
  <wsdl:message name="CreateBayiBelgeOnayAkisSoapOut">
    <wsdl:part name="parameters" element="tns:CreateBayiBelgeOnayAkisResponse" />
  </wsdl:message>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiHttpGetIn">
    <wsdl:part name="IRIS_ID" type="s:string" />
    <wsdl:part name="Comment" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisHttpGetIn">
    <wsdl:part name="IRIS_ID" type="s:string" />
    <wsdl:part name="personel_id" type="s:string" />
    <wsdl:part name="personel_ad_soyad" type="s:string" />
    <wsdl:part name="calistigi_bayi" type="s:string" />
    <wsdl:part name="onceki_calistigi_bayi" type="s:string" />
    <wsdl:part name="bayi_id" type="s:string" />
    <wsdl:part name="foto_adres" type="s:string" />
    <wsdl:part name="gorevi" type="s:string" />
    <wsdl:part name="calisma_sekli" type="s:string" />
    <wsdl:part name="ise_baslama_tarihi" type="s:string" />
    <wsdl:part name="TeknikServisYonetici" type="s:string" />
    <wsdl:part name="Satistemsilcisi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiHttpPostIn">
    <wsdl:part name="IRIS_ID" type="s:string" />
    <wsdl:part name="Comment" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CancelDTSatisliTicariYayinAcmaAkisiHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisHttpPostIn">
    <wsdl:part name="IRIS_ID" type="s:string" />
    <wsdl:part name="personel_id" type="s:string" />
    <wsdl:part name="personel_ad_soyad" type="s:string" />
    <wsdl:part name="calistigi_bayi" type="s:string" />
    <wsdl:part name="onceki_calistigi_bayi" type="s:string" />
    <wsdl:part name="bayi_id" type="s:string" />
    <wsdl:part name="foto_adres" type="s:string" />
    <wsdl:part name="gorevi" type="s:string" />
    <wsdl:part name="calisma_sekli" type="s:string" />
    <wsdl:part name="ise_baslama_tarihi" type="s:string" />
    <wsdl:part name="TeknikServisYonetici" type="s:string" />
    <wsdl:part name="Satistemsilcisi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="CreateBayiFotografDegisikligiAkisHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:portType name="CreateWorkflowNewSoap">
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <wsdl:input message="tns:CancelDTSatisliTicariYayinAcmaAkisiSoapIn" />
      <wsdl:output message="tns:CancelDTSatisliTicariYayinAcmaAkisiSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateDTSatisliTicariYayinAcmaAkisi">
      <wsdl:input message="tns:CreateDTSatisliTicariYayinAcmaAkisiSoapIn" />
      <wsdl:output message="tns:CreateDTSatisliTicariYayinAcmaAkisiSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateTicariGrupDegisikligiAkis">
      <wsdl:input message="tns:CreateTicariGrupDegisikligiAkisSoapIn" />
      <wsdl:output message="tns:CreateTicariGrupDegisikligiAkisSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateTicariFiyatIstisnaAkis">
      <wsdl:input message="tns:CreateTicariFiyatIstisnaAkisSoapIn" />
      <wsdl:output message="tns:CreateTicariFiyatIstisnaAkisSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUyeYetkilendirmeAkis">
      <wsdl:input message="tns:CreateTicariUyeYetkilendirmeAkisSoapIn" />
      <wsdl:output message="tns:CreateTicariUyeYetkilendirmeAkisSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUcretIadeAkis">
      <wsdl:input message="tns:CreateTicariUcretIadeAkisSoapIn" />
      <wsdl:output message="tns:CreateTicariUcretIadeAkisSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <wsdl:input message="tns:CreateBayiFotografDegisikligiAkisSoapIn" />
      <wsdl:output message="tns:CreateBayiFotografDegisikligiAkisSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateBayiBelgeOnayAkis">
      <wsdl:input message="tns:CreateBayiBelgeOnayAkisSoapIn" />
      <wsdl:output message="tns:CreateBayiBelgeOnayAkisSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="CreateWorkflowNewHttpGet">
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <wsdl:input message="tns:CancelDTSatisliTicariYayinAcmaAkisiHttpGetIn" />
      <wsdl:output message="tns:CancelDTSatisliTicariYayinAcmaAkisiHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <wsdl:input message="tns:CreateBayiFotografDegisikligiAkisHttpGetIn" />
      <wsdl:output message="tns:CreateBayiFotografDegisikligiAkisHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="CreateWorkflowNewHttpPost">
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <wsdl:input message="tns:CancelDTSatisliTicariYayinAcmaAkisiHttpPostIn" />
      <wsdl:output message="tns:CancelDTSatisliTicariYayinAcmaAkisiHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <wsdl:input message="tns:CreateBayiFotografDegisikligiAkisHttpPostIn" />
      <wsdl:output message="tns:CreateBayiFotografDegisikligiAkisHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="CreateWorkflowNewSoap" type="tns:CreateWorkflowNewSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <soap:operation soapAction="http://tempuri.org/CancelDTSatisliTicariYayinAcmaAkisi" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDTSatisliTicariYayinAcmaAkisi">
      <soap:operation soapAction="http://tempuri.org/CreateDTSatisliTicariYayinAcmaAkisi" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariGrupDegisikligiAkis">
      <soap:operation soapAction="http://tempuri.org/CreateTicariGrupDegisikligiAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariFiyatIstisnaAkis">
      <soap:operation soapAction="http://tempuri.org/CreateTicariFiyatIstisnaAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUyeYetkilendirmeAkis">
      <soap:operation soapAction="http://tempuri.org/CreateTicariUyeYetkilendirmeAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUcretIadeAkis">
      <soap:operation soapAction="http://tempuri.org/CreateTicariUcretIadeAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <soap:operation soapAction="http://tempuri.org/CreateBayiFotografDegisikligiAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiBelgeOnayAkis">
      <soap:operation soapAction="http://tempuri.org/CreateBayiBelgeOnayAkis" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="CreateWorkflowNewSoap12" type="tns:CreateWorkflowNewSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <soap12:operation soapAction="http://tempuri.org/CancelDTSatisliTicariYayinAcmaAkisi" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateDTSatisliTicariYayinAcmaAkisi">
      <soap12:operation soapAction="http://tempuri.org/CreateDTSatisliTicariYayinAcmaAkisi" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariGrupDegisikligiAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateTicariGrupDegisikligiAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariFiyatIstisnaAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateTicariFiyatIstisnaAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUyeYetkilendirmeAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateTicariUyeYetkilendirmeAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateTicariUcretIadeAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateTicariUcretIadeAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateBayiFotografDegisikligiAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiBelgeOnayAkis">
      <soap12:operation soapAction="http://tempuri.org/CreateBayiBelgeOnayAkis" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="CreateWorkflowNewHttpGet" type="tns:CreateWorkflowNewHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <http:operation location="/CancelDTSatisliTicariYayinAcmaAkisi" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <http:operation location="/CreateBayiFotografDegisikligiAkis" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="CreateWorkflowNewHttpPost" type="tns:CreateWorkflowNewHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="CancelDTSatisliTicariYayinAcmaAkisi">
      <http:operation location="/CancelDTSatisliTicariYayinAcmaAkisi" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreateBayiFotografDegisikligiAkis">
      <http:operation location="/CreateBayiFotografDegisikligiAkis" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="CreateWorkflowNew">
    <wsdl:port name="CreateWorkflowNewSoap" binding="tns:CreateWorkflowNewSoap">
      <soap:address location="http://digiflowtest/Services/createWorkflowNew.asmx" />
    </wsdl:port>
    <wsdl:port name="CreateWorkflowNewSoap12" binding="tns:CreateWorkflowNewSoap12">
      <soap12:address location="http://digiflowtest/Services/createWorkflowNew.asmx" />
    </wsdl:port>
    <wsdl:port name="CreateWorkflowNewHttpGet" binding="tns:CreateWorkflowNewHttpGet">
      <http:address location="http://digiflowtest/Services/createWorkflowNew.asmx" />
    </wsdl:port>
    <wsdl:port name="CreateWorkflowNewHttpPost" binding="tns:CreateWorkflowNewHttpPost">
      <http:address location="http://digiflowtest/Services/createWorkflowNew.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>