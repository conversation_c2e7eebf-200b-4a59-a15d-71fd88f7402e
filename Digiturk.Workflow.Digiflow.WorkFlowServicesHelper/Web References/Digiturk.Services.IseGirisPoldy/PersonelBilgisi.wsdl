<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://tempuri.org/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/">
      <s:element name="KisiBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KisiBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="KisiBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OrganizasyonelBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OrganizasyonelBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="OrganizasyonelBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IletisimBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IletisimBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="IletisimBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="OgrenimBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriEkleGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="idno" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="okul_kodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="fakulte_kodu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="bolum_kodu" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="giris_yili" type="s:short" />
            <s:element minOccurs="1" maxOccurs="1" name="cikis_yili" type="s:short" />
            <s:element minOccurs="0" maxOccurs="1" name="okul_tur_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriEkleGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="OgrenimBilgileriEkleGuncelleResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriSil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="idno" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OgrenimBilgileriSilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="OgrenimBilgileriSilResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EskiIsYeriBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EskiIsYeriBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="EskiIsYeriBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EskiIsYeriBilgileriEkleGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="idno" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sirket_sektor" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sirketteki_gorevi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ayrilma_nedeni" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="giris_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="cikis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="isyeri_adres1" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="isyeri_adres2" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="isyeri_semt" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="isyeri_il" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="EskiIsYeriBilgileriEkleGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="EskiIsYeriBilgileriEkleGuncelleResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KursSeminerBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KursSeminerBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="KursSeminerBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KursSeminerBilgileriEkleGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="idno" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="baslangic_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="bitis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="konu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_sure" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_sorumlusu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_derecesi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kurs_seviyesi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="KursSeminerBilgileriEkleGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="KursSeminerBilgileriEkleGuncelleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="IzinBilgileriGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="izinyili" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="baslangic_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="bitis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="izin_tur_kodu" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="brut_toplam" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="hafta_tatili" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="bayram_tatili" type="s:decimal" />
            <s:element minOccurs="1" maxOccurs="1" name="net_izin" type="s:decimal" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IzinBilgileriEkleResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriSil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="izinyili" type="s:int" />
            <s:element minOccurs="1" maxOccurs="1" name="baslangic_tarihi" type="s:dateTime" />
            <s:element minOccurs="1" maxOccurs="1" name="bitis_tarihi" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="izin_tur_kodu" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IzinBilgileriSilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="IzinBilgileriSilResult" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OzelSigortaBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="OzelSigortaBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="OzelSigortaBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelYeniGirisEkle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="personel_list">
              <s:complexType>
                <s:sequence>
                  <s:any minOccurs="0" maxOccurs="unbounded" namespace="http://www.w3.org/2001/XMLSchema" processContents="lax" />
                  <s:any minOccurs="1" namespace="urn:schemas-microsoft-com:xml-diffgram-v1" processContents="lax" />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelYeniGirisEkleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelYeniGirisEkleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="tckimlik" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RosterBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="RosterBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="RosterBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BolumBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="BolumBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="BolumBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DepartmanBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DepartmanBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DepartmanBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GorevBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GorevBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GorevBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UnvanBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UnvanBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UnvanBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LokasyonBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LokasyonBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LokasyonBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IsAilesiBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="IsAilesiBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="IsAilesiBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SirketBilgileriListele">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SirketBilgileriListeleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SirketBilgileriListeleResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AvansAktar">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="yil" type="s:short" />
            <s:element minOccurs="1" maxOccurs="1" name="ay" type="s:short" />
            <s:element minOccurs="1" maxOccurs="1" name="tutar" type="s:decimal" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="AvansAktarResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="AvansAktarResult" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ARGEIzinRaporu">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ARGEIzinRaporuResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ARGEIzinRaporuResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelTitleBilgi">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelTitleBilgiResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelTitleBilgiResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ParamTitleListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ParamTitleListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ParamTitleListeResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelAcilDurumListeResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="islem_kisi_no" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="adsoyad" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="istel" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="evtel" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="ceptel" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="yakinlik_derece" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="adres" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="eposta" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelAcilDurumGuncelleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumSil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="islem_kisi_no" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelAcilDurumSilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelAcilDurumSilResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelCocukListeResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="kayit_id" type="s:int" />
            <s:element minOccurs="0" maxOccurs="1" name="adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="soyadi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="cins" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="dogtar" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="tahsil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="tc_kimlik_no" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelCocukGuncelleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukSil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="kayit_id" type="s:int" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelCocukSilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelCocukSilResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelEsListeResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsGuncelle">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="soyadi" type="s:string" />
            <s:element minOccurs="1" maxOccurs="1" name="dogtar" type="s:dateTime" />
            <s:element minOccurs="0" maxOccurs="1" name="tahsil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="calisiyormu" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="tc_kimlik_no" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsGuncelleResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelEsGuncelleResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsSil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PersonelEsSilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PersonelEsSilResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulParametresiGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sort_tipi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulParametresiGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TahsilOkulParametresiGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilFakulteParametresiGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sort_tipi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilFakulteParametresiGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TahsilFakulteParametresiGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulTuruParametresiGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sort_tipi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulTuruParametresiGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TahsilOkulTuruParametresiGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulBolumParametresiGetir">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="kul_adi" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="kul_sifre" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sicil" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="sort_tipi" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="TahsilOkulBolumParametresiGetirResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="TahsilOkulBolumParametresiGetirResult">
              <s:complexType>
                <s:sequence>
                  <s:element ref="s:schema" />
                  <s:any />
                </s:sequence>
              </s:complexType>
            </s:element>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DataSet" nillable="true">
        <s:complexType>
          <s:sequence>
            <s:element ref="s:schema" />
            <s:any />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="int" type="s:int" />
      <s:element name="boolean" type="s:boolean" />
      <s:element name="string" nillable="true" type="s:string" />
    </s:schema>
  </wsdl:types>
  <wsdl:message name="KisiBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:KisiBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="KisiBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:KisiBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:OrganizasyonelBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:OrganizasyonelBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:IletisimBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:IletisimBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriEkleGuncelle" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriEkleGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilSoapIn">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriSil" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilSoapOut">
    <wsdl:part name="parameters" element="tns:OgrenimBilgileriSilResponse" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:EskiIsYeriBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:EskiIsYeriBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:EskiIsYeriBilgileriEkleGuncelle" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:EskiIsYeriBilgileriEkleGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:KursSeminerBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:KursSeminerBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:KursSeminerBilgileriEkleGuncelle" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:KursSeminerBilgileriEkleGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirSoapIn">
    <wsdl:part name="parameters" element="tns:IzinBilgileriGetir" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirSoapOut">
    <wsdl:part name="parameters" element="tns:IzinBilgileriGetirResponse" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleSoapIn">
    <wsdl:part name="parameters" element="tns:IzinBilgileriEkle" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleSoapOut">
    <wsdl:part name="parameters" element="tns:IzinBilgileriEkleResponse" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilSoapIn">
    <wsdl:part name="parameters" element="tns:IzinBilgileriSil" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilSoapOut">
    <wsdl:part name="parameters" element="tns:IzinBilgileriSilResponse" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:OzelSigortaBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:OzelSigortaBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelYeniGirisEkleSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelYeniGirisEkle" />
  </wsdl:message>
  <wsdl:message name="PersonelYeniGirisEkleSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelYeniGirisEkleResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:RosterBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:RosterBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:BolumBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:BolumBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:DepartmanBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:DepartmanBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:GorevBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:GorevBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:UnvanBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:UnvanBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:LokasyonBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:LokasyonBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:IsAilesiBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:IsAilesiBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleSoapIn">
    <wsdl:part name="parameters" element="tns:SirketBilgileriListele" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleSoapOut">
    <wsdl:part name="parameters" element="tns:SirketBilgileriListeleResponse" />
  </wsdl:message>
  <wsdl:message name="AvansAktarSoapIn">
    <wsdl:part name="parameters" element="tns:AvansAktar" />
  </wsdl:message>
  <wsdl:message name="AvansAktarSoapOut">
    <wsdl:part name="parameters" element="tns:AvansAktarResponse" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuSoapIn">
    <wsdl:part name="parameters" element="tns:ARGEIzinRaporu" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuSoapOut">
    <wsdl:part name="parameters" element="tns:ARGEIzinRaporuResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelTitleBilgi" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelTitleBilgiResponse" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeSoapIn">
    <wsdl:part name="parameters" element="tns:ParamTitleListe" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeSoapOut">
    <wsdl:part name="parameters" element="tns:ParamTitleListeResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumListe" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumListeResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumGuncelle" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumSil" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelAcilDurumSilResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelCocukListe" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelCocukListeResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelCocukGuncelle" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelCocukGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelCocukSil" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelCocukSilResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelEsListe" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelEsListeResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelEsGuncelle" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelEsGuncelleResponse" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilSoapIn">
    <wsdl:part name="parameters" element="tns:PersonelEsSil" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilSoapOut">
    <wsdl:part name="parameters" element="tns:PersonelEsSilResponse" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirSoapIn">
    <wsdl:part name="parameters" element="tns:TahsilOkulParametresiGetir" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirSoapOut">
    <wsdl:part name="parameters" element="tns:TahsilOkulParametresiGetirResponse" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirSoapIn">
    <wsdl:part name="parameters" element="tns:TahsilFakulteParametresiGetir" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirSoapOut">
    <wsdl:part name="parameters" element="tns:TahsilFakulteParametresiGetirResponse" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirSoapIn">
    <wsdl:part name="parameters" element="tns:TahsilOkulTuruParametresiGetir" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirSoapOut">
    <wsdl:part name="parameters" element="tns:TahsilOkulTuruParametresiGetirResponse" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirSoapIn">
    <wsdl:part name="parameters" element="tns:TahsilOkulBolumParametresiGetir" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirSoapOut">
    <wsdl:part name="parameters" element="tns:TahsilOkulBolumParametresiGetirResponse" />
  </wsdl:message>
  <wsdl:message name="KisiBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KisiBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="okul_kodu" type="s:string" />
    <wsdl:part name="fakulte_kodu" type="s:string" />
    <wsdl:part name="bolum_kodu" type="s:string" />
    <wsdl:part name="giris_yili" type="s:string" />
    <wsdl:part name="cikis_yili" type="s:string" />
    <wsdl:part name="okul_tur_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:int" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilHttpGetOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="sirket_adi" type="s:string" />
    <wsdl:part name="sirket_sektor" type="s:string" />
    <wsdl:part name="sirketteki_gorevi" type="s:string" />
    <wsdl:part name="ayrilma_nedeni" type="s:string" />
    <wsdl:part name="giris_tarihi" type="s:string" />
    <wsdl:part name="cikis_tarihi" type="s:string" />
    <wsdl:part name="isyeri_adres1" type="s:string" />
    <wsdl:part name="isyeri_adres2" type="s:string" />
    <wsdl:part name="isyeri_semt" type="s:string" />
    <wsdl:part name="isyeri_il" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="konu" type="s:string" />
    <wsdl:part name="kurs_sure" type="s:string" />
    <wsdl:part name="kurs_sorumlusu" type="s:string" />
    <wsdl:part name="kurs_derecesi" type="s:string" />
    <wsdl:part name="kurs_seviyesi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="izinyili" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="izin_tur_kodu" type="s:string" />
    <wsdl:part name="brut_toplam" type="s:string" />
    <wsdl:part name="hafta_tatili" type="s:string" />
    <wsdl:part name="bayram_tatili" type="s:string" />
    <wsdl:part name="net_izin" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleHttpGetOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="izinyili" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="izin_tur_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilHttpGetOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="tckimlik" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="AvansAktarHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="yil" type="s:string" />
    <wsdl:part name="ay" type="s:string" />
    <wsdl:part name="tutar" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AvansAktarHttpGetOut">
    <wsdl:part name="Body" element="tns:int" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="islem_kisi_no" type="s:string" />
    <wsdl:part name="adsoyad" type="s:string" />
    <wsdl:part name="istel" type="s:string" />
    <wsdl:part name="evtel" type="s:string" />
    <wsdl:part name="ceptel" type="s:string" />
    <wsdl:part name="yakinlik_derece" type="s:string" />
    <wsdl:part name="adres" type="s:string" />
    <wsdl:part name="eposta" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="islem_kisi_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="kayit_id" type="s:string" />
    <wsdl:part name="adi" type="s:string" />
    <wsdl:part name="soyadi" type="s:string" />
    <wsdl:part name="cins" type="s:string" />
    <wsdl:part name="dogtar" type="s:string" />
    <wsdl:part name="tahsil" type="s:string" />
    <wsdl:part name="tc_kimlik_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="kayit_id" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="adi" type="s:string" />
    <wsdl:part name="soyadi" type="s:string" />
    <wsdl:part name="dogtar" type="s:string" />
    <wsdl:part name="tahsil" type="s:string" />
    <wsdl:part name="calisiyormu" type="s:string" />
    <wsdl:part name="tc_kimlik_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilHttpGetOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirHttpGetIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirHttpGetOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="KisiBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KisiBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OrganizasyonelBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IletisimBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="okul_kodu" type="s:string" />
    <wsdl:part name="fakulte_kodu" type="s:string" />
    <wsdl:part name="bolum_kodu" type="s:string" />
    <wsdl:part name="giris_yili" type="s:string" />
    <wsdl:part name="cikis_yili" type="s:string" />
    <wsdl:part name="okul_tur_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriEkleGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:int" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OgrenimBilgileriSilHttpPostOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="sirket_adi" type="s:string" />
    <wsdl:part name="sirket_sektor" type="s:string" />
    <wsdl:part name="sirketteki_gorevi" type="s:string" />
    <wsdl:part name="ayrilma_nedeni" type="s:string" />
    <wsdl:part name="giris_tarihi" type="s:string" />
    <wsdl:part name="cikis_tarihi" type="s:string" />
    <wsdl:part name="isyeri_adres1" type="s:string" />
    <wsdl:part name="isyeri_adres2" type="s:string" />
    <wsdl:part name="isyeri_semt" type="s:string" />
    <wsdl:part name="isyeri_il" type="s:string" />
  </wsdl:message>
  <wsdl:message name="EskiIsYeriBilgileriEkleGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="idno" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="konu" type="s:string" />
    <wsdl:part name="kurs_sure" type="s:string" />
    <wsdl:part name="kurs_sorumlusu" type="s:string" />
    <wsdl:part name="kurs_derecesi" type="s:string" />
    <wsdl:part name="kurs_seviyesi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="KursSeminerBilgileriEkleGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="izinyili" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="izin_tur_kodu" type="s:string" />
    <wsdl:part name="brut_toplam" type="s:string" />
    <wsdl:part name="hafta_tatili" type="s:string" />
    <wsdl:part name="bayram_tatili" type="s:string" />
    <wsdl:part name="net_izin" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriEkleHttpPostOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="izinyili" type="s:string" />
    <wsdl:part name="baslangic_tarihi" type="s:string" />
    <wsdl:part name="bitis_tarihi" type="s:string" />
    <wsdl:part name="izin_tur_kodu" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IzinBilgileriSilHttpPostOut">
    <wsdl:part name="Body" element="tns:boolean" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="OzelSigortaBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="tckimlik" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="RosterBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="BolumBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="DepartmanBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="GorevBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="UnvanBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="LokasyonBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="IsAilesiBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="SirketBilgileriListeleHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="AvansAktarHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="yil" type="s:string" />
    <wsdl:part name="ay" type="s:string" />
    <wsdl:part name="tutar" type="s:string" />
  </wsdl:message>
  <wsdl:message name="AvansAktarHttpPostOut">
    <wsdl:part name="Body" element="tns:int" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ARGEIzinRaporuHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelTitleBilgiHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
  </wsdl:message>
  <wsdl:message name="ParamTitleListeHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumListeHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="islem_kisi_no" type="s:string" />
    <wsdl:part name="adsoyad" type="s:string" />
    <wsdl:part name="istel" type="s:string" />
    <wsdl:part name="evtel" type="s:string" />
    <wsdl:part name="ceptel" type="s:string" />
    <wsdl:part name="yakinlik_derece" type="s:string" />
    <wsdl:part name="adres" type="s:string" />
    <wsdl:part name="eposta" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="islem_kisi_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelAcilDurumSilHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukListeHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="kayit_id" type="s:string" />
    <wsdl:part name="adi" type="s:string" />
    <wsdl:part name="soyadi" type="s:string" />
    <wsdl:part name="cins" type="s:string" />
    <wsdl:part name="dogtar" type="s:string" />
    <wsdl:part name="tahsil" type="s:string" />
    <wsdl:part name="tc_kimlik_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="kayit_id" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelCocukSilHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsListeHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="adi" type="s:string" />
    <wsdl:part name="soyadi" type="s:string" />
    <wsdl:part name="dogtar" type="s:string" />
    <wsdl:part name="tahsil" type="s:string" />
    <wsdl:part name="calisiyormu" type="s:string" />
    <wsdl:part name="tc_kimlik_no" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsGuncelleHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
  </wsdl:message>
  <wsdl:message name="PersonelEsSilHttpPostOut">
    <wsdl:part name="Body" element="tns:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulParametresiGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilFakulteParametresiGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulTuruParametresiGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirHttpPostIn">
    <wsdl:part name="kul_adi" type="s:string" />
    <wsdl:part name="kul_sifre" type="s:string" />
    <wsdl:part name="sicil" type="s:string" />
    <wsdl:part name="sort_tipi" type="s:string" />
  </wsdl:message>
  <wsdl:message name="TahsilOkulBolumParametresiGetirHttpPostOut">
    <wsdl:part name="Body" element="tns:DataSet" />
  </wsdl:message>
  <wsdl:portType name="PersonelBilgisiSoap">
    <wsdl:operation name="KisiBilgileriGetir">
      <wsdl:input message="tns:KisiBilgileriGetirSoapIn" />
      <wsdl:output message="tns:KisiBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <wsdl:input message="tns:OrganizasyonelBilgileriGetirSoapIn" />
      <wsdl:output message="tns:OrganizasyonelBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <wsdl:input message="tns:IletisimBilgileriGetirSoapIn" />
      <wsdl:output message="tns:IletisimBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <wsdl:input message="tns:OgrenimBilgileriGetirSoapIn" />
      <wsdl:output message="tns:OgrenimBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <wsdl:input message="tns:OgrenimBilgileriEkleGuncelleSoapIn" />
      <wsdl:output message="tns:OgrenimBilgileriEkleGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <wsdl:input message="tns:OgrenimBilgileriSilSoapIn" />
      <wsdl:output message="tns:OgrenimBilgileriSilSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <wsdl:input message="tns:EskiIsYeriBilgileriGetirSoapIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <wsdl:input message="tns:EskiIsYeriBilgileriEkleGuncelleSoapIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriEkleGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <wsdl:input message="tns:KursSeminerBilgileriGetirSoapIn" />
      <wsdl:output message="tns:KursSeminerBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <wsdl:input message="tns:KursSeminerBilgileriEkleGuncelleSoapIn" />
      <wsdl:output message="tns:KursSeminerBilgileriEkleGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <wsdl:input message="tns:IzinBilgileriGetirSoapIn" />
      <wsdl:output message="tns:IzinBilgileriGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <wsdl:input message="tns:IzinBilgileriEkleSoapIn" />
      <wsdl:output message="tns:IzinBilgileriEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <wsdl:input message="tns:IzinBilgileriSilSoapIn" />
      <wsdl:output message="tns:IzinBilgileriSilSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <wsdl:input message="tns:OzelSigortaBilgileriListeleSoapIn" />
      <wsdl:output message="tns:OzelSigortaBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelYeniGirisEkle">
      <wsdl:input message="tns:PersonelYeniGirisEkleSoapIn" />
      <wsdl:output message="tns:PersonelYeniGirisEkleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <wsdl:input message="tns:PersonelBilgileriListeleSoapIn" />
      <wsdl:output message="tns:PersonelBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <wsdl:input message="tns:RosterBilgileriListeleSoapIn" />
      <wsdl:output message="tns:RosterBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <wsdl:input message="tns:BolumBilgileriListeleSoapIn" />
      <wsdl:output message="tns:BolumBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <wsdl:input message="tns:DepartmanBilgileriListeleSoapIn" />
      <wsdl:output message="tns:DepartmanBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <wsdl:input message="tns:GorevBilgileriListeleSoapIn" />
      <wsdl:output message="tns:GorevBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <wsdl:input message="tns:UnvanBilgileriListeleSoapIn" />
      <wsdl:output message="tns:UnvanBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <wsdl:input message="tns:LokasyonBilgileriListeleSoapIn" />
      <wsdl:output message="tns:LokasyonBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <wsdl:input message="tns:IsAilesiBilgileriListeleSoapIn" />
      <wsdl:output message="tns:IsAilesiBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <wsdl:input message="tns:SirketBilgileriListeleSoapIn" />
      <wsdl:output message="tns:SirketBilgileriListeleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <wsdl:input message="tns:AvansAktarSoapIn" />
      <wsdl:output message="tns:AvansAktarSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <wsdl:input message="tns:ARGEIzinRaporuSoapIn" />
      <wsdl:output message="tns:ARGEIzinRaporuSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <wsdl:input message="tns:PersonelTitleBilgiSoapIn" />
      <wsdl:output message="tns:PersonelTitleBilgiSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <wsdl:input message="tns:ParamTitleListeSoapIn" />
      <wsdl:output message="tns:ParamTitleListeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <wsdl:input message="tns:PersonelAcilDurumListeSoapIn" />
      <wsdl:output message="tns:PersonelAcilDurumListeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <wsdl:input message="tns:PersonelAcilDurumGuncelleSoapIn" />
      <wsdl:output message="tns:PersonelAcilDurumGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <wsdl:input message="tns:PersonelAcilDurumSilSoapIn" />
      <wsdl:output message="tns:PersonelAcilDurumSilSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <wsdl:input message="tns:PersonelCocukListeSoapIn" />
      <wsdl:output message="tns:PersonelCocukListeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <wsdl:input message="tns:PersonelCocukGuncelleSoapIn" />
      <wsdl:output message="tns:PersonelCocukGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <wsdl:input message="tns:PersonelCocukSilSoapIn" />
      <wsdl:output message="tns:PersonelCocukSilSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <wsdl:input message="tns:PersonelEsListeSoapIn" />
      <wsdl:output message="tns:PersonelEsListeSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <wsdl:input message="tns:PersonelEsGuncelleSoapIn" />
      <wsdl:output message="tns:PersonelEsGuncelleSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <wsdl:input message="tns:PersonelEsSilSoapIn" />
      <wsdl:output message="tns:PersonelEsSilSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <wsdl:input message="tns:TahsilOkulParametresiGetirSoapIn" />
      <wsdl:output message="tns:TahsilOkulParametresiGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <wsdl:input message="tns:TahsilFakulteParametresiGetirSoapIn" />
      <wsdl:output message="tns:TahsilFakulteParametresiGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <wsdl:input message="tns:TahsilOkulTuruParametresiGetirSoapIn" />
      <wsdl:output message="tns:TahsilOkulTuruParametresiGetirSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <wsdl:input message="tns:TahsilOkulBolumParametresiGetirSoapIn" />
      <wsdl:output message="tns:TahsilOkulBolumParametresiGetirSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="PersonelBilgisiHttpGet">
    <wsdl:operation name="KisiBilgileriGetir">
      <wsdl:input message="tns:KisiBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:KisiBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <wsdl:input message="tns:OrganizasyonelBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:OrganizasyonelBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <wsdl:input message="tns:IletisimBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:IletisimBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <wsdl:input message="tns:OgrenimBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:OgrenimBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <wsdl:input message="tns:OgrenimBilgileriEkleGuncelleHttpGetIn" />
      <wsdl:output message="tns:OgrenimBilgileriEkleGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <wsdl:input message="tns:OgrenimBilgileriSilHttpGetIn" />
      <wsdl:output message="tns:OgrenimBilgileriSilHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <wsdl:input message="tns:EskiIsYeriBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <wsdl:input message="tns:EskiIsYeriBilgileriEkleGuncelleHttpGetIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriEkleGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <wsdl:input message="tns:KursSeminerBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:KursSeminerBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <wsdl:input message="tns:KursSeminerBilgileriEkleGuncelleHttpGetIn" />
      <wsdl:output message="tns:KursSeminerBilgileriEkleGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <wsdl:input message="tns:IzinBilgileriGetirHttpGetIn" />
      <wsdl:output message="tns:IzinBilgileriGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <wsdl:input message="tns:IzinBilgileriEkleHttpGetIn" />
      <wsdl:output message="tns:IzinBilgileriEkleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <wsdl:input message="tns:IzinBilgileriSilHttpGetIn" />
      <wsdl:output message="tns:IzinBilgileriSilHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <wsdl:input message="tns:OzelSigortaBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:OzelSigortaBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <wsdl:input message="tns:PersonelBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:PersonelBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <wsdl:input message="tns:RosterBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:RosterBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <wsdl:input message="tns:BolumBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:BolumBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <wsdl:input message="tns:DepartmanBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:DepartmanBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <wsdl:input message="tns:GorevBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:GorevBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <wsdl:input message="tns:UnvanBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:UnvanBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <wsdl:input message="tns:LokasyonBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:LokasyonBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <wsdl:input message="tns:IsAilesiBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:IsAilesiBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <wsdl:input message="tns:SirketBilgileriListeleHttpGetIn" />
      <wsdl:output message="tns:SirketBilgileriListeleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <wsdl:input message="tns:AvansAktarHttpGetIn" />
      <wsdl:output message="tns:AvansAktarHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <wsdl:input message="tns:ARGEIzinRaporuHttpGetIn" />
      <wsdl:output message="tns:ARGEIzinRaporuHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <wsdl:input message="tns:PersonelTitleBilgiHttpGetIn" />
      <wsdl:output message="tns:PersonelTitleBilgiHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <wsdl:input message="tns:ParamTitleListeHttpGetIn" />
      <wsdl:output message="tns:ParamTitleListeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <wsdl:input message="tns:PersonelAcilDurumListeHttpGetIn" />
      <wsdl:output message="tns:PersonelAcilDurumListeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <wsdl:input message="tns:PersonelAcilDurumGuncelleHttpGetIn" />
      <wsdl:output message="tns:PersonelAcilDurumGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <wsdl:input message="tns:PersonelAcilDurumSilHttpGetIn" />
      <wsdl:output message="tns:PersonelAcilDurumSilHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <wsdl:input message="tns:PersonelCocukListeHttpGetIn" />
      <wsdl:output message="tns:PersonelCocukListeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <wsdl:input message="tns:PersonelCocukGuncelleHttpGetIn" />
      <wsdl:output message="tns:PersonelCocukGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <wsdl:input message="tns:PersonelCocukSilHttpGetIn" />
      <wsdl:output message="tns:PersonelCocukSilHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <wsdl:input message="tns:PersonelEsListeHttpGetIn" />
      <wsdl:output message="tns:PersonelEsListeHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <wsdl:input message="tns:PersonelEsGuncelleHttpGetIn" />
      <wsdl:output message="tns:PersonelEsGuncelleHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <wsdl:input message="tns:PersonelEsSilHttpGetIn" />
      <wsdl:output message="tns:PersonelEsSilHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <wsdl:input message="tns:TahsilOkulParametresiGetirHttpGetIn" />
      <wsdl:output message="tns:TahsilOkulParametresiGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <wsdl:input message="tns:TahsilFakulteParametresiGetirHttpGetIn" />
      <wsdl:output message="tns:TahsilFakulteParametresiGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <wsdl:input message="tns:TahsilOkulTuruParametresiGetirHttpGetIn" />
      <wsdl:output message="tns:TahsilOkulTuruParametresiGetirHttpGetOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <wsdl:input message="tns:TahsilOkulBolumParametresiGetirHttpGetIn" />
      <wsdl:output message="tns:TahsilOkulBolumParametresiGetirHttpGetOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:portType name="PersonelBilgisiHttpPost">
    <wsdl:operation name="KisiBilgileriGetir">
      <wsdl:input message="tns:KisiBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:KisiBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <wsdl:input message="tns:OrganizasyonelBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:OrganizasyonelBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <wsdl:input message="tns:IletisimBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:IletisimBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <wsdl:input message="tns:OgrenimBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:OgrenimBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <wsdl:input message="tns:OgrenimBilgileriEkleGuncelleHttpPostIn" />
      <wsdl:output message="tns:OgrenimBilgileriEkleGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <wsdl:input message="tns:OgrenimBilgileriSilHttpPostIn" />
      <wsdl:output message="tns:OgrenimBilgileriSilHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <wsdl:input message="tns:EskiIsYeriBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <wsdl:input message="tns:EskiIsYeriBilgileriEkleGuncelleHttpPostIn" />
      <wsdl:output message="tns:EskiIsYeriBilgileriEkleGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <wsdl:input message="tns:KursSeminerBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:KursSeminerBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <wsdl:input message="tns:KursSeminerBilgileriEkleGuncelleHttpPostIn" />
      <wsdl:output message="tns:KursSeminerBilgileriEkleGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <wsdl:input message="tns:IzinBilgileriGetirHttpPostIn" />
      <wsdl:output message="tns:IzinBilgileriGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <wsdl:input message="tns:IzinBilgileriEkleHttpPostIn" />
      <wsdl:output message="tns:IzinBilgileriEkleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <wsdl:input message="tns:IzinBilgileriSilHttpPostIn" />
      <wsdl:output message="tns:IzinBilgileriSilHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <wsdl:input message="tns:OzelSigortaBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:OzelSigortaBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <wsdl:input message="tns:PersonelBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:PersonelBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <wsdl:input message="tns:RosterBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:RosterBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <wsdl:input message="tns:BolumBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:BolumBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <wsdl:input message="tns:DepartmanBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:DepartmanBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <wsdl:input message="tns:GorevBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:GorevBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <wsdl:input message="tns:UnvanBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:UnvanBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <wsdl:input message="tns:LokasyonBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:LokasyonBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <wsdl:input message="tns:IsAilesiBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:IsAilesiBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <wsdl:input message="tns:SirketBilgileriListeleHttpPostIn" />
      <wsdl:output message="tns:SirketBilgileriListeleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <wsdl:input message="tns:AvansAktarHttpPostIn" />
      <wsdl:output message="tns:AvansAktarHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <wsdl:input message="tns:ARGEIzinRaporuHttpPostIn" />
      <wsdl:output message="tns:ARGEIzinRaporuHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <wsdl:input message="tns:PersonelTitleBilgiHttpPostIn" />
      <wsdl:output message="tns:PersonelTitleBilgiHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <wsdl:input message="tns:ParamTitleListeHttpPostIn" />
      <wsdl:output message="tns:ParamTitleListeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <wsdl:input message="tns:PersonelAcilDurumListeHttpPostIn" />
      <wsdl:output message="tns:PersonelAcilDurumListeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <wsdl:input message="tns:PersonelAcilDurumGuncelleHttpPostIn" />
      <wsdl:output message="tns:PersonelAcilDurumGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <wsdl:input message="tns:PersonelAcilDurumSilHttpPostIn" />
      <wsdl:output message="tns:PersonelAcilDurumSilHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <wsdl:input message="tns:PersonelCocukListeHttpPostIn" />
      <wsdl:output message="tns:PersonelCocukListeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <wsdl:input message="tns:PersonelCocukGuncelleHttpPostIn" />
      <wsdl:output message="tns:PersonelCocukGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <wsdl:input message="tns:PersonelCocukSilHttpPostIn" />
      <wsdl:output message="tns:PersonelCocukSilHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <wsdl:input message="tns:PersonelEsListeHttpPostIn" />
      <wsdl:output message="tns:PersonelEsListeHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <wsdl:input message="tns:PersonelEsGuncelleHttpPostIn" />
      <wsdl:output message="tns:PersonelEsGuncelleHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <wsdl:input message="tns:PersonelEsSilHttpPostIn" />
      <wsdl:output message="tns:PersonelEsSilHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <wsdl:input message="tns:TahsilOkulParametresiGetirHttpPostIn" />
      <wsdl:output message="tns:TahsilOkulParametresiGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <wsdl:input message="tns:TahsilFakulteParametresiGetirHttpPostIn" />
      <wsdl:output message="tns:TahsilFakulteParametresiGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <wsdl:input message="tns:TahsilOkulTuruParametresiGetirHttpPostIn" />
      <wsdl:output message="tns:TahsilOkulTuruParametresiGetirHttpPostOut" />
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <wsdl:input message="tns:TahsilOkulBolumParametresiGetirHttpPostIn" />
      <wsdl:output message="tns:TahsilOkulBolumParametresiGetirHttpPostOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="PersonelBilgisiSoap" type="tns:PersonelBilgisiSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="KisiBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/KisiBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/OrganizasyonelBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/IletisimBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/OgrenimBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <soap:operation soapAction="http://tempuri.org/OgrenimBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <soap:operation soapAction="http://tempuri.org/OgrenimBilgileriSil" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/EskiIsYeriBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <soap:operation soapAction="http://tempuri.org/EskiIsYeriBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/KursSeminerBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <soap:operation soapAction="http://tempuri.org/KursSeminerBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <soap:operation soapAction="http://tempuri.org/IzinBilgileriGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <soap:operation soapAction="http://tempuri.org/IzinBilgileriEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <soap:operation soapAction="http://tempuri.org/IzinBilgileriSil" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/OzelSigortaBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelYeniGirisEkle">
      <soap:operation soapAction="http://tempuri.org/PersonelYeniGirisEkle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/PersonelBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/RosterBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/BolumBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/DepartmanBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/GorevBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/UnvanBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/LokasyonBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/IsAilesiBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <soap:operation soapAction="http://tempuri.org/SirketBilgileriListele" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <soap:operation soapAction="http://tempuri.org/AvansAktar" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <soap:operation soapAction="http://tempuri.org/ARGEIzinRaporu" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <soap:operation soapAction="http://tempuri.org/PersonelTitleBilgi" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <soap:operation soapAction="http://tempuri.org/ParamTitleListe" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <soap:operation soapAction="http://tempuri.org/PersonelAcilDurumListe" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <soap:operation soapAction="http://tempuri.org/PersonelAcilDurumGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <soap:operation soapAction="http://tempuri.org/PersonelAcilDurumSil" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <soap:operation soapAction="http://tempuri.org/PersonelCocukListe" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <soap:operation soapAction="http://tempuri.org/PersonelCocukGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <soap:operation soapAction="http://tempuri.org/PersonelCocukSil" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <soap:operation soapAction="http://tempuri.org/PersonelEsListe" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <soap:operation soapAction="http://tempuri.org/PersonelEsGuncelle" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <soap:operation soapAction="http://tempuri.org/PersonelEsSil" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <soap:operation soapAction="http://tempuri.org/TahsilOkulParametresiGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <soap:operation soapAction="http://tempuri.org/TahsilFakulteParametresiGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <soap:operation soapAction="http://tempuri.org/TahsilOkulTuruParametresiGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <soap:operation soapAction="http://tempuri.org/TahsilOkulBolumParametresiGetir" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="PersonelBilgisiSoap12" type="tns:PersonelBilgisiSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="KisiBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/KisiBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/OrganizasyonelBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/IletisimBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/OgrenimBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <soap12:operation soapAction="http://tempuri.org/OgrenimBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <soap12:operation soapAction="http://tempuri.org/OgrenimBilgileriSil" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/EskiIsYeriBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <soap12:operation soapAction="http://tempuri.org/EskiIsYeriBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/KursSeminerBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <soap12:operation soapAction="http://tempuri.org/KursSeminerBilgileriEkleGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <soap12:operation soapAction="http://tempuri.org/IzinBilgileriGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <soap12:operation soapAction="http://tempuri.org/IzinBilgileriEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <soap12:operation soapAction="http://tempuri.org/IzinBilgileriSil" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/OzelSigortaBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelYeniGirisEkle">
      <soap12:operation soapAction="http://tempuri.org/PersonelYeniGirisEkle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/PersonelBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/RosterBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/BolumBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/DepartmanBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/GorevBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/UnvanBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/LokasyonBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/IsAilesiBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <soap12:operation soapAction="http://tempuri.org/SirketBilgileriListele" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <soap12:operation soapAction="http://tempuri.org/AvansAktar" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <soap12:operation soapAction="http://tempuri.org/ARGEIzinRaporu" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <soap12:operation soapAction="http://tempuri.org/PersonelTitleBilgi" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <soap12:operation soapAction="http://tempuri.org/ParamTitleListe" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <soap12:operation soapAction="http://tempuri.org/PersonelAcilDurumListe" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <soap12:operation soapAction="http://tempuri.org/PersonelAcilDurumGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <soap12:operation soapAction="http://tempuri.org/PersonelAcilDurumSil" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <soap12:operation soapAction="http://tempuri.org/PersonelCocukListe" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <soap12:operation soapAction="http://tempuri.org/PersonelCocukGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <soap12:operation soapAction="http://tempuri.org/PersonelCocukSil" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <soap12:operation soapAction="http://tempuri.org/PersonelEsListe" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <soap12:operation soapAction="http://tempuri.org/PersonelEsGuncelle" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <soap12:operation soapAction="http://tempuri.org/PersonelEsSil" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <soap12:operation soapAction="http://tempuri.org/TahsilOkulParametresiGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <soap12:operation soapAction="http://tempuri.org/TahsilFakulteParametresiGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <soap12:operation soapAction="http://tempuri.org/TahsilOkulTuruParametresiGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <soap12:operation soapAction="http://tempuri.org/TahsilOkulBolumParametresiGetir" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="PersonelBilgisiHttpGet" type="tns:PersonelBilgisiHttpGet">
    <http:binding verb="GET" />
    <wsdl:operation name="KisiBilgileriGetir">
      <http:operation location="/KisiBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <http:operation location="/OrganizasyonelBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <http:operation location="/IletisimBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <http:operation location="/OgrenimBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <http:operation location="/OgrenimBilgileriEkleGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <http:operation location="/OgrenimBilgileriSil" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <http:operation location="/EskiIsYeriBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <http:operation location="/EskiIsYeriBilgileriEkleGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <http:operation location="/KursSeminerBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <http:operation location="/KursSeminerBilgileriEkleGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <http:operation location="/IzinBilgileriGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <http:operation location="/IzinBilgileriEkle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <http:operation location="/IzinBilgileriSil" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <http:operation location="/OzelSigortaBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <http:operation location="/PersonelBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <http:operation location="/RosterBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <http:operation location="/BolumBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <http:operation location="/DepartmanBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <http:operation location="/GorevBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <http:operation location="/UnvanBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <http:operation location="/LokasyonBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <http:operation location="/IsAilesiBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <http:operation location="/SirketBilgileriListele" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <http:operation location="/AvansAktar" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <http:operation location="/ARGEIzinRaporu" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <http:operation location="/PersonelTitleBilgi" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <http:operation location="/ParamTitleListe" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <http:operation location="/PersonelAcilDurumListe" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <http:operation location="/PersonelAcilDurumGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <http:operation location="/PersonelAcilDurumSil" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <http:operation location="/PersonelCocukListe" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <http:operation location="/PersonelCocukGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <http:operation location="/PersonelCocukSil" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <http:operation location="/PersonelEsListe" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <http:operation location="/PersonelEsGuncelle" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <http:operation location="/PersonelEsSil" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <http:operation location="/TahsilOkulParametresiGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <http:operation location="/TahsilFakulteParametresiGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <http:operation location="/TahsilOkulTuruParametresiGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <http:operation location="/TahsilOkulBolumParametresiGetir" />
      <wsdl:input>
        <http:urlEncoded />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="PersonelBilgisiHttpPost" type="tns:PersonelBilgisiHttpPost">
    <http:binding verb="POST" />
    <wsdl:operation name="KisiBilgileriGetir">
      <http:operation location="/KisiBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OrganizasyonelBilgileriGetir">
      <http:operation location="/OrganizasyonelBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IletisimBilgileriGetir">
      <http:operation location="/IletisimBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriGetir">
      <http:operation location="/OgrenimBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriEkleGuncelle">
      <http:operation location="/OgrenimBilgileriEkleGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OgrenimBilgileriSil">
      <http:operation location="/OgrenimBilgileriSil" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriGetir">
      <http:operation location="/EskiIsYeriBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="EskiIsYeriBilgileriEkleGuncelle">
      <http:operation location="/EskiIsYeriBilgileriEkleGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriGetir">
      <http:operation location="/KursSeminerBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="KursSeminerBilgileriEkleGuncelle">
      <http:operation location="/KursSeminerBilgileriEkleGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriGetir">
      <http:operation location="/IzinBilgileriGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriEkle">
      <http:operation location="/IzinBilgileriEkle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IzinBilgileriSil">
      <http:operation location="/IzinBilgileriSil" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OzelSigortaBilgileriListele">
      <http:operation location="/OzelSigortaBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelBilgileriListele">
      <http:operation location="/PersonelBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RosterBilgileriListele">
      <http:operation location="/RosterBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="BolumBilgileriListele">
      <http:operation location="/BolumBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DepartmanBilgileriListele">
      <http:operation location="/DepartmanBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GorevBilgileriListele">
      <http:operation location="/GorevBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnvanBilgileriListele">
      <http:operation location="/UnvanBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LokasyonBilgileriListele">
      <http:operation location="/LokasyonBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="IsAilesiBilgileriListele">
      <http:operation location="/IsAilesiBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SirketBilgileriListele">
      <http:operation location="/SirketBilgileriListele" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AvansAktar">
      <http:operation location="/AvansAktar" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ARGEIzinRaporu">
      <http:operation location="/ARGEIzinRaporu" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelTitleBilgi">
      <http:operation location="/PersonelTitleBilgi" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ParamTitleListe">
      <http:operation location="/ParamTitleListe" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumListe">
      <http:operation location="/PersonelAcilDurumListe" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumGuncelle">
      <http:operation location="/PersonelAcilDurumGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelAcilDurumSil">
      <http:operation location="/PersonelAcilDurumSil" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukListe">
      <http:operation location="/PersonelCocukListe" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukGuncelle">
      <http:operation location="/PersonelCocukGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelCocukSil">
      <http:operation location="/PersonelCocukSil" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsListe">
      <http:operation location="/PersonelEsListe" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsGuncelle">
      <http:operation location="/PersonelEsGuncelle" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PersonelEsSil">
      <http:operation location="/PersonelEsSil" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulParametresiGetir">
      <http:operation location="/TahsilOkulParametresiGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilFakulteParametresiGetir">
      <http:operation location="/TahsilFakulteParametresiGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulTuruParametresiGetir">
      <http:operation location="/TahsilOkulTuruParametresiGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="TahsilOkulBolumParametresiGetir">
      <http:operation location="/TahsilOkulBolumParametresiGetir" />
      <wsdl:input>
        <mime:content type="application/x-www-form-urlencoded" />
      </wsdl:input>
      <wsdl:output>
        <mime:mimeXml part="Body" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PersonelBilgisi">
    <wsdl:port name="PersonelBilgisiSoap" binding="tns:PersonelBilgisiSoap">
      <soap:address location="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
    </wsdl:port>
    <wsdl:port name="PersonelBilgisiSoap12" binding="tns:PersonelBilgisiSoap12">
      <soap12:address location="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
    </wsdl:port>
    <wsdl:port name="PersonelBilgisiHttpGet" binding="tns:PersonelBilgisiHttpGet">
      <http:address location="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
    </wsdl:port>
    <wsdl:port name="PersonelBilgisiHttpPost" binding="tns:PersonelBilgisiHttpPost">
      <http:address location="http://dtl1iis4:3331/PersonelBilgisi.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>