<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="https://storage.irisws.digiturk.net" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" targetNamespace="https://storage.irisws.digiturk.net" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="https://storage.irisws.digiturk.net">
      <s:element name="SaveDocument">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="0" maxOccurs="1" name="document" type="tns:Document" />
            <s:element minOccurs="1" maxOccurs="1" name="saveDocumentInfoToDatabase" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="resizeImage" type="s:boolean" />
            <s:element minOccurs="1" maxOccurs="1" name="convertImageToGrayscale" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="Session">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="SessionId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="SessionGUID" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="IrisUserId" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="PersonnelId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="Ip" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:simpleType name="StorageServerName">
        <s:restriction base="s:string">
          <s:enumeration value="None" />
          <s:enumeration value="Default" />
          <s:enumeration value="IrisCrmDocuments" />
          <s:enumeration value="IrisAdminSharedDocuments" />
          <s:enumeration value="PersonnelImages" />
          <s:enumeration value="VehicleImages" />
          <s:enumeration value="TemporaryDocuments" />
          <s:enumeration value="BumerangDocuments" />
          <s:enumeration value="IrisCrmDocumentsSecondaryRead" />
          <s:enumeration value="IrisAdminSharedDocumentsSecondaryRead" />
          <s:enumeration value="PersonnelImagesSecondaryRead" />
          <s:enumeration value="VehicleImagesSecondaryRead" />
          <s:enumeration value="TemporaryDocumentsSecondaryRead" />
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="DocumentReferenceType">
        <s:restriction base="s:string">
          <s:enumeration value="Member" />
          <s:enumeration value="ProspectMember" />
          <s:enumeration value="Memo" />
          <s:enumeration value="Bumerang" />
          <s:enumeration value="BumerangTicket" />
          <s:enumeration value="Personnel" />
          <s:enumeration value="Vehicle" />
          <s:enumeration value="InternetApplication" />
          <s:enumeration value="IrisAdminSharedDocument" />
          <s:enumeration value="TemporaryDocument" />
          <s:enumeration value="CommercialOffer" />
          <s:enumeration value="SiteVisit" />
          <s:enumeration value="ServiceAccount" />
          <s:enumeration value="PartyRole" />
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Document">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DocumentSpecId" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="OriginalFilename" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="OutletNo" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CampaignCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ServiceCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PageNo" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="ScannerAppWorkOrderId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="Content" type="s:base64Binary" />
        </s:sequence>
      </s:complexType>
      <s:element name="SaveDocumentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SaveDocumentResult" type="tns:SaveDocumentResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SaveDocumentResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StoredDocumentId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="StoredDocumentFilename" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StoredDocumentMD5HashCode" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="ReadDocumentByStoredDocumentId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="dealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="documentId" type="s:long" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ReadDocumentByStoredDocumentIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ReadDocumentByStoredDocumentIdResult" type="tns:ReadDocumentByStoredDocumentIdResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ReadDocumentByStoredDocumentIdResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Document" type="tns:StoredDocument" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="StoredDocument">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DocumentId" type="s:long" />
          <s:element minOccurs="1" maxOccurs="1" name="DocumentSpecId" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentSpecName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StorageServerName" type="tns:StorageServerName" />
          <s:element minOccurs="1" maxOccurs="1" name="DealerIrisUserId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="Filename" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="OriginalFilename" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Extension" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="FileMD5HashCode" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="ReferenceId" type="s:long" />
          <s:element minOccurs="1" maxOccurs="1" name="ReferenceType" type="tns:DocumentReferenceType" />
          <s:element minOccurs="0" maxOccurs="1" name="OutletNo" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="CampaingCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ServiceCode" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Description" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="PageNo" type="s:int" />
          <s:element minOccurs="1" maxOccurs="1" name="ScannerAppWorkOrderId" type="s:long" />
          <s:element minOccurs="1" maxOccurs="1" name="InsertUserIrisId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="InsertUserIrisName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="InsertPersonnelId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="InsertPersonnelName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="InsertDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="InsertIp" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="UpdateUserIrisId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="UpdateUserIrisName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="UpdatePersonnelId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="UpdatePersonnelName" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="UpdateDate" type="s:dateTime" />
          <s:element minOccurs="0" maxOccurs="1" name="UpdateIp" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="InputScreenCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="Content" type="s:base64Binary" />
        </s:sequence>
      </s:complexType>
      <s:element name="ReadDocumentContentByStoredDocumentName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="0" maxOccurs="1" name="storedDocumentName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="ReadDocumentContentByStoredDocumentNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ReadDocumentContentByStoredDocumentNameResult" type="tns:ReadDocumentContentByStoredDocumentNameResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ReadDocumentContentByStoredDocumentNameResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentContent" type="s:base64Binary" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentExtension" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="DeleteDocumentByStoredDocumentId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="dealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="documentId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="deleteDocumentInfoFromDatabase" type="s:boolean" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteDocumentByStoredDocumentIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DeleteDocumentByStoredDocumentIdResult" type="tns:DeleteDocumentByStoredDocumentIdResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="DeleteDocumentByStoredDocumentIdResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="DeleteDocumentByStoredDocumentName">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="0" maxOccurs="1" name="storedDocumentName" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteDocumentByStoredDocumentNameResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DeleteDocumentByStoredDocumentNameResult" type="tns:DeleteDocumentByStoredDocumentNameResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="DeleteDocumentByStoredDocumentNameResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="DeleteAllDocumentsForReferenceId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="1" maxOccurs="1" name="dealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="deleteDocumentInfoFromDatabase" type="s:boolean" />
            <s:element minOccurs="0" maxOccurs="1" name="inputScreenCdFilter" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="DeleteAllDocumentsForReferenceIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="DeleteAllDocumentsForReferenceIdResult" type="tns:DeleteAllDocumentsForReferenceIdResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="DeleteAllDocumentsForReferenceIdResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="UpdateDocumentMetaDataByStoredDocumentId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="documentId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="currentDealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="currentReferenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="currentReferenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="1" maxOccurs="1" name="newDealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="newReferenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="newReferenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="0" maxOccurs="1" name="newDocumentMetaData" type="tns:Document" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="UpdateDocumentMetaDataByStoredDocumentIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateDocumentMetaDataByStoredDocumentIdResult" type="tns:UpdateDocumentMetaDataByStoredDocumentIdResult" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="UpdateDocumentMetaDataByStoredDocumentIdResult">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="StoredDocumentId" type="s:long" />
          <s:element minOccurs="0" maxOccurs="1" name="StoredDocumentFilename" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="StoredDocumentMD5HashCode" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetDocumentSpecListByScreenCd">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="0" maxOccurs="1" name="screenCd" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentSpecListByScreenCdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentSpecListByScreenCdResult" type="tns:GetDocumentSpecListByScreenCd" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="GetDocumentSpecListByScreenCd">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentSpecList" type="tns:ArrayOfDocumentSpec" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfDocumentSpec">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DocumentSpec" nillable="true" type="tns:DocumentSpec" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="DocumentSpec">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DocumentSpecId" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentSpecName" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="SimpleOfferRequirementCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BundleOfferRequirementCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="BundleOfferTypeCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DescriptionRequirementCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="UploadMethodTypeCd" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="ViewTypeCd" type="s:string" />
          <s:element minOccurs="1" maxOccurs="1" name="AllowedMaxLength" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="AllowedFileTypeList" type="tns:ArrayOfString" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentSpecActionCategoryCd" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="s:string" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetDocumentListByReferenceId">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="dealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="1" maxOccurs="1" name="contentRequirement" type="tns:DocumentContentRequirement" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:simpleType name="DocumentContentRequirement">
        <s:restriction base="s:string">
          <s:enumeration value="ContentRequired" />
          <s:enumeration value="ContentNotRequired" />
        </s:restriction>
      </s:simpleType>
      <s:element name="GetDocumentListByReferenceIdResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentListByReferenceIdResult" type="tns:GetDocumentListByReferenceId" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="GetDocumentListByReferenceId">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ResultCode" type="s:int" />
          <s:element minOccurs="0" maxOccurs="1" name="ResultMessage" type="s:string" />
          <s:element minOccurs="0" maxOccurs="1" name="DocumentList" type="tns:ArrayOfStoredDocument" />
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfStoredDocument">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="StoredDocument" nillable="true" type="tns:StoredDocument" />
        </s:sequence>
      </s:complexType>
      <s:element name="GetDocumentListByReferenceIdWithScreenFilter">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="irisSession" type="tns:Session" />
            <s:element minOccurs="1" maxOccurs="1" name="storageServerName" type="tns:StorageServerName" />
            <s:element minOccurs="1" maxOccurs="1" name="dealerIrisUserId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceId" type="s:long" />
            <s:element minOccurs="1" maxOccurs="1" name="referenceType" type="tns:DocumentReferenceType" />
            <s:element minOccurs="1" maxOccurs="1" name="contentRequirement" type="tns:DocumentContentRequirement" />
            <s:element minOccurs="0" maxOccurs="1" name="screenCdFilterList" type="tns:ArrayOfString" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="GetDocumentListByReferenceIdWithScreenFilterResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="GetDocumentListByReferenceIdWithScreenFilterResult" type="tns:GetDocumentListByReferenceId" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="SaveDocumentSoapIn">
    <wsdl:part name="parameters" element="tns:SaveDocument" />
  </wsdl:message>
  <wsdl:message name="SaveDocumentSoapOut">
    <wsdl:part name="parameters" element="tns:SaveDocumentResponse" />
  </wsdl:message>
  <wsdl:message name="ReadDocumentByStoredDocumentIdSoapIn">
    <wsdl:part name="parameters" element="tns:ReadDocumentByStoredDocumentId" />
  </wsdl:message>
  <wsdl:message name="ReadDocumentByStoredDocumentIdSoapOut">
    <wsdl:part name="parameters" element="tns:ReadDocumentByStoredDocumentIdResponse" />
  </wsdl:message>
  <wsdl:message name="ReadDocumentContentByStoredDocumentNameSoapIn">
    <wsdl:part name="parameters" element="tns:ReadDocumentContentByStoredDocumentName" />
  </wsdl:message>
  <wsdl:message name="ReadDocumentContentByStoredDocumentNameSoapOut">
    <wsdl:part name="parameters" element="tns:ReadDocumentContentByStoredDocumentNameResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteDocumentByStoredDocumentIdSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteDocumentByStoredDocumentId" />
  </wsdl:message>
  <wsdl:message name="DeleteDocumentByStoredDocumentIdSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteDocumentByStoredDocumentIdResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteDocumentByStoredDocumentNameSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteDocumentByStoredDocumentName" />
  </wsdl:message>
  <wsdl:message name="DeleteDocumentByStoredDocumentNameSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteDocumentByStoredDocumentNameResponse" />
  </wsdl:message>
  <wsdl:message name="DeleteAllDocumentsForReferenceIdSoapIn">
    <wsdl:part name="parameters" element="tns:DeleteAllDocumentsForReferenceId" />
  </wsdl:message>
  <wsdl:message name="DeleteAllDocumentsForReferenceIdSoapOut">
    <wsdl:part name="parameters" element="tns:DeleteAllDocumentsForReferenceIdResponse" />
  </wsdl:message>
  <wsdl:message name="UpdateDocumentMetaDataByStoredDocumentIdSoapIn">
    <wsdl:part name="parameters" element="tns:UpdateDocumentMetaDataByStoredDocumentId" />
  </wsdl:message>
  <wsdl:message name="UpdateDocumentMetaDataByStoredDocumentIdSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateDocumentMetaDataByStoredDocumentIdResponse" />
  </wsdl:message>
  <wsdl:message name="GetDocumentSpecListByScreenCdSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentSpecListByScreenCd" />
  </wsdl:message>
  <wsdl:message name="GetDocumentSpecListByScreenCdSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentSpecListByScreenCdResponse" />
  </wsdl:message>
  <wsdl:message name="GetDocumentListByReferenceIdSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentListByReferenceId" />
  </wsdl:message>
  <wsdl:message name="GetDocumentListByReferenceIdSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentListByReferenceIdResponse" />
  </wsdl:message>
  <wsdl:message name="GetDocumentListByReferenceIdWithScreenFilterSoapIn">
    <wsdl:part name="parameters" element="tns:GetDocumentListByReferenceIdWithScreenFilter" />
  </wsdl:message>
  <wsdl:message name="GetDocumentListByReferenceIdWithScreenFilterSoapOut">
    <wsdl:part name="parameters" element="tns:GetDocumentListByReferenceIdWithScreenFilterResponse" />
  </wsdl:message>
  <wsdl:portType name="IrisDocumentStorageWSSoap">
    <wsdl:operation name="SaveDocument">
      <wsdl:input message="tns:SaveDocumentSoapIn" />
      <wsdl:output message="tns:SaveDocumentSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentByStoredDocumentId">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece IRIS-CRM Dökümanları için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:ReadDocumentByStoredDocumentIdSoapIn" />
      <wsdl:output message="tns:ReadDocumentByStoredDocumentIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentContentByStoredDocumentName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Tüm storage ve döküman tipleri için kullanılabilir. Geriye döküman objesi değil sadece binary içerik ve döküman uzantısını döner.</wsdl:documentation>
      <wsdl:input message="tns:ReadDocumentContentByStoredDocumentNameSoapIn" />
      <wsdl:output message="tns:ReadDocumentContentByStoredDocumentNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentId">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece IRIS-CRM Dökümanları için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:DeleteDocumentByStoredDocumentIdSoapIn" />
      <wsdl:output message="tns:DeleteDocumentByStoredDocumentIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentName">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece IRIS-CRM Dökümanları için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:DeleteDocumentByStoredDocumentNameSoapIn" />
      <wsdl:output message="tns:DeleteDocumentByStoredDocumentNameSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="DeleteAllDocumentsForReferenceId">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece IRIS-CRM Dökümanları için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:DeleteAllDocumentsForReferenceIdSoapIn" />
      <wsdl:output message="tns:DeleteAllDocumentsForReferenceIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="UpdateDocumentMetaDataByStoredDocumentId">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod, ilgili döküman için referans no ve referans no tip değişikliği yapılıyorsa kullanılmalıdır.</wsdl:documentation>
      <wsdl:input message="tns:UpdateDocumentMetaDataByStoredDocumentIdSoapIn" />
      <wsdl:output message="tns:UpdateDocumentMetaDataByStoredDocumentIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDocumentSpecListByScreenCd">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece IRIS-CRM Dökümanları için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentSpecListByScreenCdSoapIn" />
      <wsdl:output message="tns:GetDocumentSpecListByScreenCdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceId">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece belirli storage türleri için kullanılabilir.</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentListByReferenceIdSoapIn" />
      <wsdl:output message="tns:GetDocumentListByReferenceIdSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceIdWithScreenFilter">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">Bu metod sadece belirli storage türleri için kullanılabilir. Verilen ekrana özel filtrelenmiş dosya listesini döner. Filtre boş verilirse filtre yapılmadan döner.</wsdl:documentation>
      <wsdl:input message="tns:GetDocumentListByReferenceIdWithScreenFilterSoapIn" />
      <wsdl:output message="tns:GetDocumentListByReferenceIdWithScreenFilterSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="IrisDocumentStorageWSSoap" type="tns:IrisDocumentStorageWSSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SaveDocument">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/SaveDocument" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentByStoredDocumentId">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/ReadDocumentByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentContentByStoredDocumentName">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/ReadDocumentContentByStoredDocumentName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentId">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentName">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentName" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAllDocumentsForReferenceId">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/DeleteAllDocumentsForReferenceId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateDocumentMetaDataByStoredDocumentId">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/UpdateDocumentMetaDataByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentSpecListByScreenCd">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentSpecListByScreenCd" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceId">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentListByReferenceId" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceIdWithScreenFilter">
      <soap:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentListByReferenceIdWithScreenFilter" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="IrisDocumentStorageWSSoap12" type="tns:IrisDocumentStorageWSSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="SaveDocument">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/SaveDocument" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentByStoredDocumentId">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/ReadDocumentByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ReadDocumentContentByStoredDocumentName">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/ReadDocumentContentByStoredDocumentName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentId">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteDocumentByStoredDocumentName">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentName" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="DeleteAllDocumentsForReferenceId">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/DeleteAllDocumentsForReferenceId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdateDocumentMetaDataByStoredDocumentId">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/UpdateDocumentMetaDataByStoredDocumentId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentSpecListByScreenCd">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentSpecListByScreenCd" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceId">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentListByReferenceId" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDocumentListByReferenceIdWithScreenFilter">
      <soap12:operation soapAction="https://storage.irisws.digiturk.net/GetDocumentListByReferenceIdWithScreenFilter" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="IrisDocumentStorageWS">
    <wsdl:port name="IrisDocumentStorageWSSoap" binding="tns:IrisDocumentStorageWSSoap">
      <soap:address location="https://irisws.digiturk.net/storagewstest/service.asmx" />
    </wsdl:port>
    <wsdl:port name="IrisDocumentStorageWSSoap12" binding="tns:IrisDocumentStorageWSSoap12">
      <soap12:address location="https://irisws.digiturk.net/storagewstest/service.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>