﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

// 
// This source code was auto-generated by Microsoft.VSDesigner, Version 4.0.30319.42000.
// 
#pragma warning disable 1591

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.IrisWebServis {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="IrisDocumentStorageWSSoap", Namespace="https://storage.irisws.digiturk.net")]
    public partial class IrisDocumentStorageWS : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback SaveDocumentOperationCompleted;
        
        private System.Threading.SendOrPostCallback ReadDocumentByStoredDocumentIdOperationCompleted;
        
        private System.Threading.SendOrPostCallback ReadDocumentContentByStoredDocumentNameOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteDocumentByStoredDocumentIdOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteDocumentByStoredDocumentNameOperationCompleted;
        
        private System.Threading.SendOrPostCallback DeleteAllDocumentsForReferenceIdOperationCompleted;
        
        private System.Threading.SendOrPostCallback UpdateDocumentMetaDataByStoredDocumentIdOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDocumentSpecListByScreenCdOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDocumentListByReferenceIdOperationCompleted;
        
        private System.Threading.SendOrPostCallback GetDocumentListByReferenceIdWithScreenFilterOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public IrisDocumentStorageWS() {
            this.Url = global::Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties.Settings.Default.Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_IrisWebServis_IrisDocumentStorageWS;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event SaveDocumentCompletedEventHandler SaveDocumentCompleted;
        
        /// <remarks/>
        public event ReadDocumentByStoredDocumentIdCompletedEventHandler ReadDocumentByStoredDocumentIdCompleted;
        
        /// <remarks/>
        public event ReadDocumentContentByStoredDocumentNameCompletedEventHandler ReadDocumentContentByStoredDocumentNameCompleted;
        
        /// <remarks/>
        public event DeleteDocumentByStoredDocumentIdCompletedEventHandler DeleteDocumentByStoredDocumentIdCompleted;
        
        /// <remarks/>
        public event DeleteDocumentByStoredDocumentNameCompletedEventHandler DeleteDocumentByStoredDocumentNameCompleted;
        
        /// <remarks/>
        public event DeleteAllDocumentsForReferenceIdCompletedEventHandler DeleteAllDocumentsForReferenceIdCompleted;
        
        /// <remarks/>
        public event UpdateDocumentMetaDataByStoredDocumentIdCompletedEventHandler UpdateDocumentMetaDataByStoredDocumentIdCompleted;
        
        /// <remarks/>
        public event GetDocumentSpecListByScreenCdCompletedEventHandler GetDocumentSpecListByScreenCdCompleted;
        
        /// <remarks/>
        public event GetDocumentListByReferenceIdCompletedEventHandler GetDocumentListByReferenceIdCompleted;
        
        /// <remarks/>
        public event GetDocumentListByReferenceIdWithScreenFilterCompletedEventHandler GetDocumentListByReferenceIdWithScreenFilterCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/SaveDocument", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public SaveDocumentResult SaveDocument(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, Document document, bool saveDocumentInfoToDatabase, bool resizeImage, bool convertImageToGrayscale) {
            object[] results = this.Invoke("SaveDocument", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        document,
                        saveDocumentInfoToDatabase,
                        resizeImage,
                        convertImageToGrayscale});
            return ((SaveDocumentResult)(results[0]));
        }
        
        /// <remarks/>
        public void SaveDocumentAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, Document document, bool saveDocumentInfoToDatabase, bool resizeImage, bool convertImageToGrayscale) {
            this.SaveDocumentAsync(irisSession, storageServerName, referenceId, referenceType, document, saveDocumentInfoToDatabase, resizeImage, convertImageToGrayscale, null);
        }
        
        /// <remarks/>
        public void SaveDocumentAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, Document document, bool saveDocumentInfoToDatabase, bool resizeImage, bool convertImageToGrayscale, object userState) {
            if ((this.SaveDocumentOperationCompleted == null)) {
                this.SaveDocumentOperationCompleted = new System.Threading.SendOrPostCallback(this.OnSaveDocumentOperationCompleted);
            }
            this.InvokeAsync("SaveDocument", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        document,
                        saveDocumentInfoToDatabase,
                        resizeImage,
                        convertImageToGrayscale}, this.SaveDocumentOperationCompleted, userState);
        }
        
        private void OnSaveDocumentOperationCompleted(object arg) {
            if ((this.SaveDocumentCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.SaveDocumentCompleted(this, new SaveDocumentCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/ReadDocumentByStoredDocumentId", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ReadDocumentByStoredDocumentIdResult ReadDocumentByStoredDocumentId(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId) {
            object[] results = this.Invoke("ReadDocumentByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        documentId});
            return ((ReadDocumentByStoredDocumentIdResult)(results[0]));
        }
        
        /// <remarks/>
        public void ReadDocumentByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId) {
            this.ReadDocumentByStoredDocumentIdAsync(irisSession, storageServerName, dealerIrisUserId, documentId, null);
        }
        
        /// <remarks/>
        public void ReadDocumentByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId, object userState) {
            if ((this.ReadDocumentByStoredDocumentIdOperationCompleted == null)) {
                this.ReadDocumentByStoredDocumentIdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnReadDocumentByStoredDocumentIdOperationCompleted);
            }
            this.InvokeAsync("ReadDocumentByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        documentId}, this.ReadDocumentByStoredDocumentIdOperationCompleted, userState);
        }
        
        private void OnReadDocumentByStoredDocumentIdOperationCompleted(object arg) {
            if ((this.ReadDocumentByStoredDocumentIdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ReadDocumentByStoredDocumentIdCompleted(this, new ReadDocumentByStoredDocumentIdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/ReadDocumentContentByStoredDocumentName", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public ReadDocumentContentByStoredDocumentNameResult ReadDocumentContentByStoredDocumentName(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName) {
            object[] results = this.Invoke("ReadDocumentContentByStoredDocumentName", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        storedDocumentName});
            return ((ReadDocumentContentByStoredDocumentNameResult)(results[0]));
        }
        
        /// <remarks/>
        public void ReadDocumentContentByStoredDocumentNameAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName) {
            this.ReadDocumentContentByStoredDocumentNameAsync(irisSession, storageServerName, referenceId, referenceType, storedDocumentName, null);
        }
        
        /// <remarks/>
        public void ReadDocumentContentByStoredDocumentNameAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName, object userState) {
            if ((this.ReadDocumentContentByStoredDocumentNameOperationCompleted == null)) {
                this.ReadDocumentContentByStoredDocumentNameOperationCompleted = new System.Threading.SendOrPostCallback(this.OnReadDocumentContentByStoredDocumentNameOperationCompleted);
            }
            this.InvokeAsync("ReadDocumentContentByStoredDocumentName", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        storedDocumentName}, this.ReadDocumentContentByStoredDocumentNameOperationCompleted, userState);
        }
        
        private void OnReadDocumentContentByStoredDocumentNameOperationCompleted(object arg) {
            if ((this.ReadDocumentContentByStoredDocumentNameCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.ReadDocumentContentByStoredDocumentNameCompleted(this, new ReadDocumentContentByStoredDocumentNameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentId", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DeleteDocumentByStoredDocumentIdResult DeleteDocumentByStoredDocumentId(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId, bool deleteDocumentInfoFromDatabase) {
            object[] results = this.Invoke("DeleteDocumentByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        documentId,
                        deleteDocumentInfoFromDatabase});
            return ((DeleteDocumentByStoredDocumentIdResult)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteDocumentByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId, bool deleteDocumentInfoFromDatabase) {
            this.DeleteDocumentByStoredDocumentIdAsync(irisSession, storageServerName, dealerIrisUserId, documentId, deleteDocumentInfoFromDatabase, null);
        }
        
        /// <remarks/>
        public void DeleteDocumentByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long documentId, bool deleteDocumentInfoFromDatabase, object userState) {
            if ((this.DeleteDocumentByStoredDocumentIdOperationCompleted == null)) {
                this.DeleteDocumentByStoredDocumentIdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteDocumentByStoredDocumentIdOperationCompleted);
            }
            this.InvokeAsync("DeleteDocumentByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        documentId,
                        deleteDocumentInfoFromDatabase}, this.DeleteDocumentByStoredDocumentIdOperationCompleted, userState);
        }
        
        private void OnDeleteDocumentByStoredDocumentIdOperationCompleted(object arg) {
            if ((this.DeleteDocumentByStoredDocumentIdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteDocumentByStoredDocumentIdCompleted(this, new DeleteDocumentByStoredDocumentIdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/DeleteDocumentByStoredDocumentName", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DeleteDocumentByStoredDocumentNameResult DeleteDocumentByStoredDocumentName(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName) {
            object[] results = this.Invoke("DeleteDocumentByStoredDocumentName", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        storedDocumentName});
            return ((DeleteDocumentByStoredDocumentNameResult)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteDocumentByStoredDocumentNameAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName) {
            this.DeleteDocumentByStoredDocumentNameAsync(irisSession, storageServerName, referenceId, referenceType, storedDocumentName, null);
        }
        
        /// <remarks/>
        public void DeleteDocumentByStoredDocumentNameAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, string storedDocumentName, object userState) {
            if ((this.DeleteDocumentByStoredDocumentNameOperationCompleted == null)) {
                this.DeleteDocumentByStoredDocumentNameOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteDocumentByStoredDocumentNameOperationCompleted);
            }
            this.InvokeAsync("DeleteDocumentByStoredDocumentName", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        storedDocumentName}, this.DeleteDocumentByStoredDocumentNameOperationCompleted, userState);
        }
        
        private void OnDeleteDocumentByStoredDocumentNameOperationCompleted(object arg) {
            if ((this.DeleteDocumentByStoredDocumentNameCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteDocumentByStoredDocumentNameCompleted(this, new DeleteDocumentByStoredDocumentNameCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/DeleteAllDocumentsForReferenceId", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public DeleteAllDocumentsForReferenceIdResult DeleteAllDocumentsForReferenceId(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, long dealerIrisUserId, bool deleteDocumentInfoFromDatabase, string inputScreenCdFilter) {
            object[] results = this.Invoke("DeleteAllDocumentsForReferenceId", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        dealerIrisUserId,
                        deleteDocumentInfoFromDatabase,
                        inputScreenCdFilter});
            return ((DeleteAllDocumentsForReferenceIdResult)(results[0]));
        }
        
        /// <remarks/>
        public void DeleteAllDocumentsForReferenceIdAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, long dealerIrisUserId, bool deleteDocumentInfoFromDatabase, string inputScreenCdFilter) {
            this.DeleteAllDocumentsForReferenceIdAsync(irisSession, storageServerName, referenceId, referenceType, dealerIrisUserId, deleteDocumentInfoFromDatabase, inputScreenCdFilter, null);
        }
        
        /// <remarks/>
        public void DeleteAllDocumentsForReferenceIdAsync(Session irisSession, StorageServerName storageServerName, long referenceId, DocumentReferenceType referenceType, long dealerIrisUserId, bool deleteDocumentInfoFromDatabase, string inputScreenCdFilter, object userState) {
            if ((this.DeleteAllDocumentsForReferenceIdOperationCompleted == null)) {
                this.DeleteAllDocumentsForReferenceIdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnDeleteAllDocumentsForReferenceIdOperationCompleted);
            }
            this.InvokeAsync("DeleteAllDocumentsForReferenceId", new object[] {
                        irisSession,
                        storageServerName,
                        referenceId,
                        referenceType,
                        dealerIrisUserId,
                        deleteDocumentInfoFromDatabase,
                        inputScreenCdFilter}, this.DeleteAllDocumentsForReferenceIdOperationCompleted, userState);
        }
        
        private void OnDeleteAllDocumentsForReferenceIdOperationCompleted(object arg) {
            if ((this.DeleteAllDocumentsForReferenceIdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.DeleteAllDocumentsForReferenceIdCompleted(this, new DeleteAllDocumentsForReferenceIdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/UpdateDocumentMetaDataByStoredDocumentId", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public UpdateDocumentMetaDataByStoredDocumentIdResult UpdateDocumentMetaDataByStoredDocumentId(Session irisSession, StorageServerName storageServerName, long documentId, long currentDealerIrisUserId, long currentReferenceId, DocumentReferenceType currentReferenceType, long newDealerIrisUserId, long newReferenceId, DocumentReferenceType newReferenceType, Document newDocumentMetaData) {
            object[] results = this.Invoke("UpdateDocumentMetaDataByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        documentId,
                        currentDealerIrisUserId,
                        currentReferenceId,
                        currentReferenceType,
                        newDealerIrisUserId,
                        newReferenceId,
                        newReferenceType,
                        newDocumentMetaData});
            return ((UpdateDocumentMetaDataByStoredDocumentIdResult)(results[0]));
        }
        
        /// <remarks/>
        public void UpdateDocumentMetaDataByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long documentId, long currentDealerIrisUserId, long currentReferenceId, DocumentReferenceType currentReferenceType, long newDealerIrisUserId, long newReferenceId, DocumentReferenceType newReferenceType, Document newDocumentMetaData) {
            this.UpdateDocumentMetaDataByStoredDocumentIdAsync(irisSession, storageServerName, documentId, currentDealerIrisUserId, currentReferenceId, currentReferenceType, newDealerIrisUserId, newReferenceId, newReferenceType, newDocumentMetaData, null);
        }
        
        /// <remarks/>
        public void UpdateDocumentMetaDataByStoredDocumentIdAsync(Session irisSession, StorageServerName storageServerName, long documentId, long currentDealerIrisUserId, long currentReferenceId, DocumentReferenceType currentReferenceType, long newDealerIrisUserId, long newReferenceId, DocumentReferenceType newReferenceType, Document newDocumentMetaData, object userState) {
            if ((this.UpdateDocumentMetaDataByStoredDocumentIdOperationCompleted == null)) {
                this.UpdateDocumentMetaDataByStoredDocumentIdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnUpdateDocumentMetaDataByStoredDocumentIdOperationCompleted);
            }
            this.InvokeAsync("UpdateDocumentMetaDataByStoredDocumentId", new object[] {
                        irisSession,
                        storageServerName,
                        documentId,
                        currentDealerIrisUserId,
                        currentReferenceId,
                        currentReferenceType,
                        newDealerIrisUserId,
                        newReferenceId,
                        newReferenceType,
                        newDocumentMetaData}, this.UpdateDocumentMetaDataByStoredDocumentIdOperationCompleted, userState);
        }
        
        private void OnUpdateDocumentMetaDataByStoredDocumentIdOperationCompleted(object arg) {
            if ((this.UpdateDocumentMetaDataByStoredDocumentIdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.UpdateDocumentMetaDataByStoredDocumentIdCompleted(this, new UpdateDocumentMetaDataByStoredDocumentIdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/GetDocumentSpecListByScreenCd", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public GetDocumentSpecListByScreenCd GetDocumentSpecListByScreenCd(Session irisSession, string screenCd) {
            object[] results = this.Invoke("GetDocumentSpecListByScreenCd", new object[] {
                        irisSession,
                        screenCd});
            return ((GetDocumentSpecListByScreenCd)(results[0]));
        }
        
        /// <remarks/>
        public void GetDocumentSpecListByScreenCdAsync(Session irisSession, string screenCd) {
            this.GetDocumentSpecListByScreenCdAsync(irisSession, screenCd, null);
        }
        
        /// <remarks/>
        public void GetDocumentSpecListByScreenCdAsync(Session irisSession, string screenCd, object userState) {
            if ((this.GetDocumentSpecListByScreenCdOperationCompleted == null)) {
                this.GetDocumentSpecListByScreenCdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDocumentSpecListByScreenCdOperationCompleted);
            }
            this.InvokeAsync("GetDocumentSpecListByScreenCd", new object[] {
                        irisSession,
                        screenCd}, this.GetDocumentSpecListByScreenCdOperationCompleted, userState);
        }
        
        private void OnGetDocumentSpecListByScreenCdOperationCompleted(object arg) {
            if ((this.GetDocumentSpecListByScreenCdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDocumentSpecListByScreenCdCompleted(this, new GetDocumentSpecListByScreenCdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/GetDocumentListByReferenceId", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public GetDocumentListByReferenceId GetDocumentListByReferenceId(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement) {
            object[] results = this.Invoke("GetDocumentListByReferenceId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        referenceId,
                        referenceType,
                        contentRequirement});
            return ((GetDocumentListByReferenceId)(results[0]));
        }
        
        /// <remarks/>
        public void GetDocumentListByReferenceIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement) {
            this.GetDocumentListByReferenceIdAsync(irisSession, storageServerName, dealerIrisUserId, referenceId, referenceType, contentRequirement, null);
        }
        
        /// <remarks/>
        public void GetDocumentListByReferenceIdAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement, object userState) {
            if ((this.GetDocumentListByReferenceIdOperationCompleted == null)) {
                this.GetDocumentListByReferenceIdOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDocumentListByReferenceIdOperationCompleted);
            }
            this.InvokeAsync("GetDocumentListByReferenceId", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        referenceId,
                        referenceType,
                        contentRequirement}, this.GetDocumentListByReferenceIdOperationCompleted, userState);
        }
        
        private void OnGetDocumentListByReferenceIdOperationCompleted(object arg) {
            if ((this.GetDocumentListByReferenceIdCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDocumentListByReferenceIdCompleted(this, new GetDocumentListByReferenceIdCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("https://storage.irisws.digiturk.net/GetDocumentListByReferenceIdWithScreenFilter", RequestNamespace="https://storage.irisws.digiturk.net", ResponseNamespace="https://storage.irisws.digiturk.net", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public GetDocumentListByReferenceId GetDocumentListByReferenceIdWithScreenFilter(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement, string[] screenCdFilterList) {
            object[] results = this.Invoke("GetDocumentListByReferenceIdWithScreenFilter", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        referenceId,
                        referenceType,
                        contentRequirement,
                        screenCdFilterList});
            return ((GetDocumentListByReferenceId)(results[0]));
        }
        
        /// <remarks/>
        public void GetDocumentListByReferenceIdWithScreenFilterAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement, string[] screenCdFilterList) {
            this.GetDocumentListByReferenceIdWithScreenFilterAsync(irisSession, storageServerName, dealerIrisUserId, referenceId, referenceType, contentRequirement, screenCdFilterList, null);
        }
        
        /// <remarks/>
        public void GetDocumentListByReferenceIdWithScreenFilterAsync(Session irisSession, StorageServerName storageServerName, long dealerIrisUserId, long referenceId, DocumentReferenceType referenceType, DocumentContentRequirement contentRequirement, string[] screenCdFilterList, object userState) {
            if ((this.GetDocumentListByReferenceIdWithScreenFilterOperationCompleted == null)) {
                this.GetDocumentListByReferenceIdWithScreenFilterOperationCompleted = new System.Threading.SendOrPostCallback(this.OnGetDocumentListByReferenceIdWithScreenFilterOperationCompleted);
            }
            this.InvokeAsync("GetDocumentListByReferenceIdWithScreenFilter", new object[] {
                        irisSession,
                        storageServerName,
                        dealerIrisUserId,
                        referenceId,
                        referenceType,
                        contentRequirement,
                        screenCdFilterList}, this.GetDocumentListByReferenceIdWithScreenFilterOperationCompleted, userState);
        }
        
        private void OnGetDocumentListByReferenceIdWithScreenFilterOperationCompleted(object arg) {
            if ((this.GetDocumentListByReferenceIdWithScreenFilterCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.GetDocumentListByReferenceIdWithScreenFilterCompleted(this, new GetDocumentListByReferenceIdWithScreenFilterCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class Session {
        
        private long sessionIdField;
        
        private string sessionGUIDField;
        
        private int irisUserIdField;
        
        private long personnelIdField;
        
        private string ipField;
        
        /// <remarks/>
        public long SessionId {
            get {
                return this.sessionIdField;
            }
            set {
                this.sessionIdField = value;
            }
        }
        
        /// <remarks/>
        public string SessionGUID {
            get {
                return this.sessionGUIDField;
            }
            set {
                this.sessionGUIDField = value;
            }
        }
        
        /// <remarks/>
        public int IrisUserId {
            get {
                return this.irisUserIdField;
            }
            set {
                this.irisUserIdField = value;
            }
        }
        
        /// <remarks/>
        public long PersonnelId {
            get {
                return this.personnelIdField;
            }
            set {
                this.personnelIdField = value;
            }
        }
        
        /// <remarks/>
        public string Ip {
            get {
                return this.ipField;
            }
            set {
                this.ipField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class GetDocumentListByReferenceId {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private StoredDocument[] documentListField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public StoredDocument[] DocumentList {
            get {
                return this.documentListField;
            }
            set {
                this.documentListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class StoredDocument {
        
        private long documentIdField;
        
        private int documentSpecIdField;
        
        private string documentSpecNameField;
        
        private StorageServerName storageServerNameField;
        
        private long dealerIrisUserIdField;
        
        private string filenameField;
        
        private string originalFilenameField;
        
        private string extensionField;
        
        private string fileMD5HashCodeField;
        
        private long referenceIdField;
        
        private DocumentReferenceType referenceTypeField;
        
        private string outletNoField;
        
        private string campaingCodeField;
        
        private string serviceCodeField;
        
        private string descriptionField;
        
        private int pageNoField;
        
        private long scannerAppWorkOrderIdField;
        
        private long insertUserIrisIdField;
        
        private string insertUserIrisNameField;
        
        private long insertPersonnelIdField;
        
        private string insertPersonnelNameField;
        
        private System.DateTime insertDateField;
        
        private string insertIpField;
        
        private long updateUserIrisIdField;
        
        private string updateUserIrisNameField;
        
        private long updatePersonnelIdField;
        
        private string updatePersonnelNameField;
        
        private System.DateTime updateDateField;
        
        private string updateIpField;
        
        private string inputScreenCdField;
        
        private byte[] contentField;
        
        /// <remarks/>
        public long DocumentId {
            get {
                return this.documentIdField;
            }
            set {
                this.documentIdField = value;
            }
        }
        
        /// <remarks/>
        public int DocumentSpecId {
            get {
                return this.documentSpecIdField;
            }
            set {
                this.documentSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentSpecName {
            get {
                return this.documentSpecNameField;
            }
            set {
                this.documentSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public StorageServerName StorageServerName {
            get {
                return this.storageServerNameField;
            }
            set {
                this.storageServerNameField = value;
            }
        }
        
        /// <remarks/>
        public long DealerIrisUserId {
            get {
                return this.dealerIrisUserIdField;
            }
            set {
                this.dealerIrisUserIdField = value;
            }
        }
        
        /// <remarks/>
        public string Filename {
            get {
                return this.filenameField;
            }
            set {
                this.filenameField = value;
            }
        }
        
        /// <remarks/>
        public string OriginalFilename {
            get {
                return this.originalFilenameField;
            }
            set {
                this.originalFilenameField = value;
            }
        }
        
        /// <remarks/>
        public string Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
            }
        }
        
        /// <remarks/>
        public string FileMD5HashCode {
            get {
                return this.fileMD5HashCodeField;
            }
            set {
                this.fileMD5HashCodeField = value;
            }
        }
        
        /// <remarks/>
        public long ReferenceId {
            get {
                return this.referenceIdField;
            }
            set {
                this.referenceIdField = value;
            }
        }
        
        /// <remarks/>
        public DocumentReferenceType ReferenceType {
            get {
                return this.referenceTypeField;
            }
            set {
                this.referenceTypeField = value;
            }
        }
        
        /// <remarks/>
        public string OutletNo {
            get {
                return this.outletNoField;
            }
            set {
                this.outletNoField = value;
            }
        }
        
        /// <remarks/>
        public string CampaingCode {
            get {
                return this.campaingCodeField;
            }
            set {
                this.campaingCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ServiceCode {
            get {
                return this.serviceCodeField;
            }
            set {
                this.serviceCodeField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public int PageNo {
            get {
                return this.pageNoField;
            }
            set {
                this.pageNoField = value;
            }
        }
        
        /// <remarks/>
        public long ScannerAppWorkOrderId {
            get {
                return this.scannerAppWorkOrderIdField;
            }
            set {
                this.scannerAppWorkOrderIdField = value;
            }
        }
        
        /// <remarks/>
        public long InsertUserIrisId {
            get {
                return this.insertUserIrisIdField;
            }
            set {
                this.insertUserIrisIdField = value;
            }
        }
        
        /// <remarks/>
        public string InsertUserIrisName {
            get {
                return this.insertUserIrisNameField;
            }
            set {
                this.insertUserIrisNameField = value;
            }
        }
        
        /// <remarks/>
        public long InsertPersonnelId {
            get {
                return this.insertPersonnelIdField;
            }
            set {
                this.insertPersonnelIdField = value;
            }
        }
        
        /// <remarks/>
        public string InsertPersonnelName {
            get {
                return this.insertPersonnelNameField;
            }
            set {
                this.insertPersonnelNameField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime InsertDate {
            get {
                return this.insertDateField;
            }
            set {
                this.insertDateField = value;
            }
        }
        
        /// <remarks/>
        public string InsertIp {
            get {
                return this.insertIpField;
            }
            set {
                this.insertIpField = value;
            }
        }
        
        /// <remarks/>
        public long UpdateUserIrisId {
            get {
                return this.updateUserIrisIdField;
            }
            set {
                this.updateUserIrisIdField = value;
            }
        }
        
        /// <remarks/>
        public string UpdateUserIrisName {
            get {
                return this.updateUserIrisNameField;
            }
            set {
                this.updateUserIrisNameField = value;
            }
        }
        
        /// <remarks/>
        public long UpdatePersonnelId {
            get {
                return this.updatePersonnelIdField;
            }
            set {
                this.updatePersonnelIdField = value;
            }
        }
        
        /// <remarks/>
        public string UpdatePersonnelName {
            get {
                return this.updatePersonnelNameField;
            }
            set {
                this.updatePersonnelNameField = value;
            }
        }
        
        /// <remarks/>
        public System.DateTime UpdateDate {
            get {
                return this.updateDateField;
            }
            set {
                this.updateDateField = value;
            }
        }
        
        /// <remarks/>
        public string UpdateIp {
            get {
                return this.updateIpField;
            }
            set {
                this.updateIpField = value;
            }
        }
        
        /// <remarks/>
        public string InputScreenCd {
            get {
                return this.inputScreenCdField;
            }
            set {
                this.inputScreenCdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Content {
            get {
                return this.contentField;
            }
            set {
                this.contentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public enum StorageServerName {
        
        /// <remarks/>
        None,
        
        /// <remarks/>
        Default,
        
        /// <remarks/>
        IrisCrmDocuments,
        
        /// <remarks/>
        IrisAdminSharedDocuments,
        
        /// <remarks/>
        PersonnelImages,
        
        /// <remarks/>
        VehicleImages,
        
        /// <remarks/>
        TemporaryDocuments,
        
        /// <remarks/>
        BumerangDocuments,
        
        /// <remarks/>
        IrisCrmDocumentsSecondaryRead,
        
        /// <remarks/>
        IrisAdminSharedDocumentsSecondaryRead,
        
        /// <remarks/>
        PersonnelImagesSecondaryRead,
        
        /// <remarks/>
        VehicleImagesSecondaryRead,
        
        /// <remarks/>
        TemporaryDocumentsSecondaryRead,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public enum DocumentReferenceType {
        
        /// <remarks/>
        Member,
        
        /// <remarks/>
        ProspectMember,
        
        /// <remarks/>
        Memo,
        
        /// <remarks/>
        Bumerang,
        
        /// <remarks/>
        BumerangTicket,
        
        /// <remarks/>
        Personnel,
        
        /// <remarks/>
        Vehicle,
        
        /// <remarks/>
        InternetApplication,
        
        /// <remarks/>
        IrisAdminSharedDocument,
        
        /// <remarks/>
        TemporaryDocument,
        
        /// <remarks/>
        CommercialOffer,
        
        /// <remarks/>
        SiteVisit,
        
        /// <remarks/>
        ServiceAccount,
        
        /// <remarks/>
        PartyRole,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class DocumentSpec {
        
        private int documentSpecIdField;
        
        private string documentSpecNameField;
        
        private string simpleOfferRequirementCdField;
        
        private string bundleOfferRequirementCdField;
        
        private string bundleOfferTypeCdField;
        
        private string descriptionRequirementCdField;
        
        private string uploadMethodTypeCdField;
        
        private string viewTypeCdField;
        
        private int allowedMaxLengthField;
        
        private string[] allowedFileTypeListField;
        
        private string documentSpecActionCategoryCdField;
        
        /// <remarks/>
        public int DocumentSpecId {
            get {
                return this.documentSpecIdField;
            }
            set {
                this.documentSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentSpecName {
            get {
                return this.documentSpecNameField;
            }
            set {
                this.documentSpecNameField = value;
            }
        }
        
        /// <remarks/>
        public string SimpleOfferRequirementCd {
            get {
                return this.simpleOfferRequirementCdField;
            }
            set {
                this.simpleOfferRequirementCdField = value;
            }
        }
        
        /// <remarks/>
        public string BundleOfferRequirementCd {
            get {
                return this.bundleOfferRequirementCdField;
            }
            set {
                this.bundleOfferRequirementCdField = value;
            }
        }
        
        /// <remarks/>
        public string BundleOfferTypeCd {
            get {
                return this.bundleOfferTypeCdField;
            }
            set {
                this.bundleOfferTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string DescriptionRequirementCd {
            get {
                return this.descriptionRequirementCdField;
            }
            set {
                this.descriptionRequirementCdField = value;
            }
        }
        
        /// <remarks/>
        public string UploadMethodTypeCd {
            get {
                return this.uploadMethodTypeCdField;
            }
            set {
                this.uploadMethodTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public string ViewTypeCd {
            get {
                return this.viewTypeCdField;
            }
            set {
                this.viewTypeCdField = value;
            }
        }
        
        /// <remarks/>
        public int AllowedMaxLength {
            get {
                return this.allowedMaxLengthField;
            }
            set {
                this.allowedMaxLengthField = value;
            }
        }
        
        /// <remarks/>
        public string[] AllowedFileTypeList {
            get {
                return this.allowedFileTypeListField;
            }
            set {
                this.allowedFileTypeListField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentSpecActionCategoryCd {
            get {
                return this.documentSpecActionCategoryCdField;
            }
            set {
                this.documentSpecActionCategoryCdField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class GetDocumentSpecListByScreenCd {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private DocumentSpec[] documentSpecListField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public DocumentSpec[] DocumentSpecList {
            get {
                return this.documentSpecListField;
            }
            set {
                this.documentSpecListField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class UpdateDocumentMetaDataByStoredDocumentIdResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private long storedDocumentIdField;
        
        private string storedDocumentFilenameField;
        
        private string storedDocumentMD5HashCodeField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public long StoredDocumentId {
            get {
                return this.storedDocumentIdField;
            }
            set {
                this.storedDocumentIdField = value;
            }
        }
        
        /// <remarks/>
        public string StoredDocumentFilename {
            get {
                return this.storedDocumentFilenameField;
            }
            set {
                this.storedDocumentFilenameField = value;
            }
        }
        
        /// <remarks/>
        public string StoredDocumentMD5HashCode {
            get {
                return this.storedDocumentMD5HashCodeField;
            }
            set {
                this.storedDocumentMD5HashCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class DeleteAllDocumentsForReferenceIdResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class DeleteDocumentByStoredDocumentNameResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class DeleteDocumentByStoredDocumentIdResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class ReadDocumentContentByStoredDocumentNameResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private byte[] documentContentField;
        
        private string documentExtensionField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] DocumentContent {
            get {
                return this.documentContentField;
            }
            set {
                this.documentContentField = value;
            }
        }
        
        /// <remarks/>
        public string DocumentExtension {
            get {
                return this.documentExtensionField;
            }
            set {
                this.documentExtensionField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class ReadDocumentByStoredDocumentIdResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private StoredDocument documentField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public StoredDocument Document {
            get {
                return this.documentField;
            }
            set {
                this.documentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class SaveDocumentResult {
        
        private int resultCodeField;
        
        private string resultMessageField;
        
        private long storedDocumentIdField;
        
        private string storedDocumentFilenameField;
        
        private string storedDocumentMD5HashCodeField;
        
        /// <remarks/>
        public int ResultCode {
            get {
                return this.resultCodeField;
            }
            set {
                this.resultCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ResultMessage {
            get {
                return this.resultMessageField;
            }
            set {
                this.resultMessageField = value;
            }
        }
        
        /// <remarks/>
        public long StoredDocumentId {
            get {
                return this.storedDocumentIdField;
            }
            set {
                this.storedDocumentIdField = value;
            }
        }
        
        /// <remarks/>
        public string StoredDocumentFilename {
            get {
                return this.storedDocumentFilenameField;
            }
            set {
                this.storedDocumentFilenameField = value;
            }
        }
        
        /// <remarks/>
        public string StoredDocumentMD5HashCode {
            get {
                return this.storedDocumentMD5HashCodeField;
            }
            set {
                this.storedDocumentMD5HashCodeField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public partial class Document {
        
        private int documentSpecIdField;
        
        private string originalFilenameField;
        
        private string extensionField;
        
        private string outletNoField;
        
        private string campaignCodeField;
        
        private string serviceCodeField;
        
        private string descriptionField;
        
        private int pageNoField;
        
        private long scannerAppWorkOrderIdField;
        
        private byte[] contentField;
        
        /// <remarks/>
        public int DocumentSpecId {
            get {
                return this.documentSpecIdField;
            }
            set {
                this.documentSpecIdField = value;
            }
        }
        
        /// <remarks/>
        public string OriginalFilename {
            get {
                return this.originalFilenameField;
            }
            set {
                this.originalFilenameField = value;
            }
        }
        
        /// <remarks/>
        public string Extension {
            get {
                return this.extensionField;
            }
            set {
                this.extensionField = value;
            }
        }
        
        /// <remarks/>
        public string OutletNo {
            get {
                return this.outletNoField;
            }
            set {
                this.outletNoField = value;
            }
        }
        
        /// <remarks/>
        public string CampaignCode {
            get {
                return this.campaignCodeField;
            }
            set {
                this.campaignCodeField = value;
            }
        }
        
        /// <remarks/>
        public string ServiceCode {
            get {
                return this.serviceCodeField;
            }
            set {
                this.serviceCodeField = value;
            }
        }
        
        /// <remarks/>
        public string Description {
            get {
                return this.descriptionField;
            }
            set {
                this.descriptionField = value;
            }
        }
        
        /// <remarks/>
        public int PageNo {
            get {
                return this.pageNoField;
            }
            set {
                this.pageNoField = value;
            }
        }
        
        /// <remarks/>
        public long ScannerAppWorkOrderId {
            get {
                return this.scannerAppWorkOrderIdField;
            }
            set {
                this.scannerAppWorkOrderIdField = value;
            }
        }
        
        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType="base64Binary")]
        public byte[] Content {
            get {
                return this.contentField;
            }
            set {
                this.contentField = value;
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Xml", "4.8.9037.0")]
    [System.SerializableAttribute()]
    [System.Xml.Serialization.XmlTypeAttribute(Namespace="https://storage.irisws.digiturk.net")]
    public enum DocumentContentRequirement {
        
        /// <remarks/>
        ContentRequired,
        
        /// <remarks/>
        ContentNotRequired,
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void SaveDocumentCompletedEventHandler(object sender, SaveDocumentCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class SaveDocumentCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal SaveDocumentCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public SaveDocumentResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((SaveDocumentResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ReadDocumentByStoredDocumentIdCompletedEventHandler(object sender, ReadDocumentByStoredDocumentIdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ReadDocumentByStoredDocumentIdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ReadDocumentByStoredDocumentIdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ReadDocumentByStoredDocumentIdResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ReadDocumentByStoredDocumentIdResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void ReadDocumentContentByStoredDocumentNameCompletedEventHandler(object sender, ReadDocumentContentByStoredDocumentNameCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class ReadDocumentContentByStoredDocumentNameCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal ReadDocumentContentByStoredDocumentNameCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public ReadDocumentContentByStoredDocumentNameResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((ReadDocumentContentByStoredDocumentNameResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteDocumentByStoredDocumentIdCompletedEventHandler(object sender, DeleteDocumentByStoredDocumentIdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteDocumentByStoredDocumentIdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteDocumentByStoredDocumentIdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DeleteDocumentByStoredDocumentIdResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DeleteDocumentByStoredDocumentIdResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteDocumentByStoredDocumentNameCompletedEventHandler(object sender, DeleteDocumentByStoredDocumentNameCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteDocumentByStoredDocumentNameCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteDocumentByStoredDocumentNameCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DeleteDocumentByStoredDocumentNameResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DeleteDocumentByStoredDocumentNameResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void DeleteAllDocumentsForReferenceIdCompletedEventHandler(object sender, DeleteAllDocumentsForReferenceIdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class DeleteAllDocumentsForReferenceIdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal DeleteAllDocumentsForReferenceIdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public DeleteAllDocumentsForReferenceIdResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((DeleteAllDocumentsForReferenceIdResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void UpdateDocumentMetaDataByStoredDocumentIdCompletedEventHandler(object sender, UpdateDocumentMetaDataByStoredDocumentIdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class UpdateDocumentMetaDataByStoredDocumentIdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal UpdateDocumentMetaDataByStoredDocumentIdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public UpdateDocumentMetaDataByStoredDocumentIdResult Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((UpdateDocumentMetaDataByStoredDocumentIdResult)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetDocumentSpecListByScreenCdCompletedEventHandler(object sender, GetDocumentSpecListByScreenCdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDocumentSpecListByScreenCdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDocumentSpecListByScreenCdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public GetDocumentSpecListByScreenCd Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((GetDocumentSpecListByScreenCd)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetDocumentListByReferenceIdCompletedEventHandler(object sender, GetDocumentListByReferenceIdCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDocumentListByReferenceIdCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDocumentListByReferenceIdCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public GetDocumentListByReferenceId Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((GetDocumentListByReferenceId)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    public delegate void GetDocumentListByReferenceIdWithScreenFilterCompletedEventHandler(object sender, GetDocumentListByReferenceIdWithScreenFilterCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9037.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class GetDocumentListByReferenceIdWithScreenFilterCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal GetDocumentListByReferenceIdWithScreenFilterCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public GetDocumentListByReferenceId Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((GetDocumentListByReferenceId)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591