﻿using System.Net;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public static class WebServicesProxyHelper
    {
        private static WebProxy _proxy;
        private static NetworkCredential _ServicesCreadinal;

        /// <summary>
        /// Web Servislerinde kullanılan Proxy Bilgisini Tutar
        /// </summary>
        public static WebProxy ServicesProxy
        {
            get
            {
                if (IsProxyUsing)
                {
                    if (_proxy == null)
                    {
                        string ProxyIp = System.Configuration.ConfigurationManager.AppSettings["Web.Services.ProxyServicesIp"];
                        _proxy = new WebProxy(ProxyIp, true);
                        _proxy.Credentials = ServicesCreadinal;
                    }
                    return _proxy;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Web Servislerinde Kullanılan Creadinal Bilgisini Tutar
        /// </summary>
        public static NetworkCredential ServicesCreadinal
        {
            get
            {
                if (IsCredentialUsing)
                {
                    if (_ServicesCreadinal == null)
                    {
                        string UserName = System.Configuration.ConfigurationManager.AppSettings["Web.Services.UserName"];
                        string Password = System.Configuration.ConfigurationManager.AppSettings["Web.Services.Password"];
                        string Domain = System.Configuration.ConfigurationManager.AppSettings["Web.Services.Domain"];
                        _ServicesCreadinal = new NetworkCredential(UserName, Password, Domain);
                    }
                    return _ServicesCreadinal;
                }
                else
                {
                    return null;
                }
            }
        }

        /// <summary>
        /// Proxy devre dışı bırakıp bırakmamayı seçer
        /// </summary>
        public static bool IsProxyUsing
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["Web.Services.IsProxyUsing"] == "True";
            }
        }

        /// <summary>
        /// Creadinal ı devre dışı bırakıp bırakmamayı seçer
        /// </summary>
        public static bool IsCredentialUsing
        {
            get
            {
                return System.Configuration.ConfigurationManager.AppSettings["Web.Services.IsCredentialUsing"] == "True";
            }
        }
    }
}