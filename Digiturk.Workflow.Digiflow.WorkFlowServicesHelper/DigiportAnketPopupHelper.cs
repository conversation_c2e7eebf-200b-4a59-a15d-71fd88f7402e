﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public class DigiportAnketPopupHelper
    {
    
            public static DigiportAnketPopup.DigiportAnketPopup CreateClient()
            {
                DigiportAnketPopup.DigiportAnketPopup Client = new DigiportAnketPopup.DigiportAnketPopup();
             
                Client.Url = System.Configuration.ConfigurationManager.AppSettings["DigiportAnketPopupServisi"];
                Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                Client.Proxy = WebServicesProxyHelper.ServicesProxy;
                return Client;

            }
        //    {
        //        Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
        //        System.ServiceModel.EndpointAddress Adress;

        //        Adress = new System.ServiceModel.EndpointAddress("http://dtl1iis4:3331/EgitimModulu.asmx");

        //        Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //        Client.Proxy = WebServicesProxyHelper.ServicesProxy;
        //        return Client;
        //    }

        //}

        //public Digiturk.Workflow.Digiflow.ServicesHelper.DigiportAnketPopup PermissionProcessObj
        //{
        //    get
        //    {
        //        _PermissionProcessObj = new Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess.PermissionProcess();
        //        _PermissionProcessObj.Credentials = WebServicesProxyHelper.ServicesCreadinal;
        //        _PermissionProcessObj.Proxy = WebServicesProxyHelper.ServicesProxy;
        //        return _PermissionProcessObj;
        //    }
        //}

    }
}
