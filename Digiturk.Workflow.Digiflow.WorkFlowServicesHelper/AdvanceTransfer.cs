﻿namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public class AdvanceTransfer
    {
        public static string Username { get; set; }
        public static string Password { get; set; }

        /// <summary>
        /// Yeni bir Servis Nesnesi Üretir
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static Digiturk.Services.AvansAktar.PersonelBilgisi CreateClient(string Sicil)
        {
            Digiturk.Services.AvansAktar.PersonelBilgisi Client = new Digiturk.Services.AvansAktar.PersonelBilgisi();
            System.ServiceModel.EndpointAddress Adress;
            Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["AvansEndPointDefaul"]);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client;
        }

        public static int AdvanceTranferSet(string sicil, short yil, short ay, decimal avanstutar)
        {
            Digiturk.Services.AvansAktar.PersonelBilgisi Client = CreateClient(sicil);
            Username = "WEBSERVICE";
            Password = "w17211s";
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            int rtdurum = Client.AvansAktar(Username, Password, sicil, yil, ay, avanstutar);
            return rtdurum;
        }
    }
}