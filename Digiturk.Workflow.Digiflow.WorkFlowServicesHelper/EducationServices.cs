﻿using System;
using System.Data;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public class EducationServices
    {
        //TARIK TODO
        public static string Username { get; set; }

        public static string Password { get; set; }

        /// <summary>
        /// Yeni bir Servis Nesnesi Üretir
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu CreateClient()
        {
            Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
            System.ServiceModel.EndpointAddress Adress;

            Adress = new System.ServiceModel.EndpointAddress("http://dtl1iis4:3331/EgitimModulu.asmx");

            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client;
        }

        /// <summary>
        /// Eğitim Döndürür
        /// </summary>
        /// <returns></returns>
        public static DataTable DtEgitimSinifGetir()
        {
            //TODO TARIK
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            dt = Client.EgitimSinifGetir(Username, Password, "A", "A").Tables[0];

            return dt;
        }

        /// <summary>
        /// firma Döndürür
        /// </summary>
        /// <returns></returns>
        public static DataTable DtFirmaGetir()
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            dt = Client.FirmaParametreGetir(Username, Password, "A").Tables[0];

            return dt;
        }

        /// <summary>
        /// eğitim ekle
        /// </summary>
        /// <returns></returns>
        public static string Egihar1TumBilgilerEkle(string kurs_adi, string kursno, string firma, string yurticidisi, DateTime baslangic, DateTime bitis, string doviz, decimal gerceklesen_tutar, short sure_gun, string sirket_ici, DateTime planlama, string dok_path, string sicil, string sirket, string pay_no, string durum, string not, string sertifika_path)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            string sonuc = Client.Egihar1TumBilgilerEkle(Username, Password, "A", kurs_adi, kursno, firma, yurticidisi, baslangic, bitis, doviz, gerceklesen_tutar, sure_gun, sirket_ici, planlama, dok_path, sicil, sirket, pay_no, durum, not, sertifika_path);

            return sonuc;
        }

        public static DataSet PersonelListesi(string sicil)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu Client = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.EgitimModulu();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            DataSet ds = Client.PersonelListesi(Username, Password, sicil);

            return ds;
        }
    }
}