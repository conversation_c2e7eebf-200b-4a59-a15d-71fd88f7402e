﻿using Digiturk.Workflow.Digiflow.DataAccessLayer;
using System;
using System.Collections.Generic;
using System.Data;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    /// <summary>
    /// Eğitim Uygulaması Servis Proxy Nesnesidir
    /// </summary>
    public class HRInformation
    {
        //TARIK TODO
        public static string Username { get; set; }

        public static string Password { get; set; }

        public enum UserType { Poldy, PoldyBasin, None };

        /// <summary>
        /// Yeni bir Servis Nesnesi Üretir
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static Digiturk.Services.HRInfos.PersonelBilgisi CreateClient(string Sicil)
        {
            Digiturk.Services.HRInfos.PersonelBilgisi Client = new Digiturk.Services.HRInfos.PersonelBilgisi();
            System.ServiceModel.EndpointAddress Adress;
            //TARIK TODO
            //Client..UserName.UserName = "WEBSERVICE";
            //Client.Credentials.UserName.Password = "w17211s";
            Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointDefaul"]);

            //TARIK TODO
            //Client.Endpoint.Address = Adress;
            //if (!CheckSicilDefault(Sicil))
            //{
            //    Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointMedia"]);
            //    //TARIK TODO
            //    //Client.Endpoint.Address = Adress;
            //}
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client;
        }

        /// <summary>
        /// Yeni bir Servis Nesnesi Üretir test
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static Digiturk.Services.IseGirisPoldy.PersonelBilgisi CreateClientTest()
        {
            Digiturk.Services.IseGirisPoldy.PersonelBilgisi Client = new Digiturk.Services.IseGirisPoldy.PersonelBilgisi();
            System.ServiceModel.EndpointAddress Adress;

            //Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["AvansEndPointDefaul"]);
            Adress = new System.ServiceModel.EndpointAddress("http://dtl1sp1:20000/Service.asmx");

            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client;
        }

        public static Digiturk.Services.HRInfosB.PersonelBilgisi CreateClientB(string Sicil)
        {
            Digiturk.Services.HRInfosB.PersonelBilgisi Client = new Digiturk.Services.HRInfosB.PersonelBilgisi();
            System.ServiceModel.EndpointAddress Adress;

            //if (!CheckSicilDefault(Sicil))
            //{
            Adress = new System.ServiceModel.EndpointAddress(System.Configuration.ConfigurationManager.AppSettings["HrServicesEndPointMedia"]);
            //TARIK TODO
            //Client.Endpoint.Address = Adress;
            //}
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client;
        }

        /// <summary>
        ///  Kullanıcının Sicilini bulur.
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static bool CheckSicilDefault(string Sicil)
        {
            string Cmd = "Select DEPT_FLAG,Sicil from DT_WORKFLOW.DP_HR_USERS   where Sicil='" + Sicil + "'";
            string Result = ModelWorking.GetOnlyColumnSQL<string>("FrameworkConnection", Cmd, new List<DataAccessLayer.CustomParameterList>());
            if (Result == "1")
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        ///  Kullanıcının Sicilini bulur.INQUIRY
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static bool CheckSicilDefaultInq(string Sicil)
        {
            string Cmd = "Select DEPT_FLAG,Sicil from INQUIRY.DP_HR_USERS where Sicil='" + Sicil + "'";
            string Result = ModelWorking.GetOnlyColumnSQL<string>("DBSConnection", Cmd, new List<DataAccessLayer.CustomParameterList>());
            if (Result == "1")
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// İzin Tiplerini Döndürür
        /// </summary>
        /// <returns></returns>
        public static DataTable DtbLeaveType()
        {
            //TODO TARIK
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Services.HRConnect.Service1 Client = new Digiturk.Services.HRConnect.Service1();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                dt = Client.getVacationItemAll().Tables[0];
            }
            else
            {
                dt = Client.getVacationItem().Tables[0];
            }

            #endregion ortama göre çalıştırılacak

            return dt;
        }

        /// <summary>
        /// İzin limit Döndürür
        /// </summary>
        /// <returns></returns>
        public static DataTable DtbLeaveTypeLimit(string izinTurKod)
        {
            //TODO TARIK
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;
            Digiturk.Services.HRConnect.Service1 Client = new Digiturk.Services.HRConnect.Service1();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                dt = Client.getVacationItemLimit(izinTurKod).Tables[0];
            }
            else
            {
                dt = Client.getVacationItemLimit(izinTurKod).Tables[0];
            }

            #endregion ortama göre çalıştırılacak

            return dt;
        }

        /// <summary>
        /// Döviz Tiplerini Döndürür
        /// </summary>
        /// <returns></returns>
        public static DataTable GetDoviz()
        {
            Digiturk.Services.HRConnect.Service1 Client = new Digiturk.Services.HRConnect.Service1();
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            return Client.getDoviz().Tables[0];
        }

        /// <summary>
        /// İzin Türlerini ve Kotalarını Döndürür Sicile Göre
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static DataTable DtbLeaveInformationGet(string Sicil)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Sicil = Sicil.PadLeft(5, '0');
            DataTable dt = null;

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                Digiturk.Services.IseGirisPoldy.PersonelBilgisi ClientTest = CreateClientTest();
                ClientTest.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                ClientTest.Proxy = WebServicesProxyHelper.ServicesProxy;
                dt = ClientTest.IzinBilgileriGetir(Username, Password, Sicil).Tables[0];
            }
            else
            {
                Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(Sicil);
                Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                Client.Proxy = WebServicesProxyHelper.ServicesProxy;
                dt = Client.IzinBilgileriGetir(Username, Password, Sicil).Tables[0];
            }

            #endregion ortama göre çalıştırılacak

            return dt;
        }

        public class DSource
        {
            public DSource(string _ID, string _ADI)
            {
                ID = _ID;
                ADI = _ADI;
            }

            public string ID { get; set; }
            public string ADI { get; set; }
        }

        public static List<DSource> GetTitle(UserType KullaniciGrubu)
        {
            List<DSource> ret = new List<DSource>();
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient("");
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            DataSet DS = Client.ParamTitleListe(Username, Password);
            if (DS.Tables.Count > 0)
            {
                ret.Add(new DSource("-1", "---Seçiniz---"));
                foreach (DataRow DR in DS.Tables[0].Select("", "title_adi"))
                    ret.Add(new DSource(DR["title_kodu"].ToString(), DR["title_adi"].ToString().Trim()));
            }
            return ret;
        }

        /// <summary>
        /// İzin Türlerini ve Kotalarını Döndürür Sicile Göre
        /// </summary>
        /// <param name="Sicil"></param>
        /// <returns></returns>
        public static DataTable DtbLeaveInformationGetB(string Sicil)
        {
            Sicil = Sicil.PadLeft(5, '0');
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataTable dt = null;

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                Digiturk.Services.IseGirisPoldy.PersonelBilgisi ClientTest = CreateClientTest();
                ClientTest.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                ClientTest.Proxy = WebServicesProxyHelper.ServicesProxy;
                dt = ClientTest.IzinBilgileriGetir(Username, Password, Sicil).Tables[0];
            }
            else
            {
                Digiturk.Services.HRInfosB.PersonelBilgisi Client = CreateClientB(Sicil);
                Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                Client.Proxy = WebServicesProxyHelper.ServicesProxy;
                dt = Client.IzinBilgileriGetir(Username, Password, Sicil).Tables[0];
            }

            #endregion ortama göre çalıştırılacak

            return dt;
        }

        /// <summary>
        /// İzin Bilgilerini Servise Yazar
        /// </summary>
        /// <param name="sicil"></param>
        /// <param name="izinyili"></param>
        /// <param name="baslangic_tarihi"></param>
        /// <param name="bitis_tarihi"></param>
        /// <param name="izin_tur_kodu"></param>
        /// <param name="brut_toplam"></param>
        /// <param name="hafta_tatili"></param>
        /// <param name="bayram_tatili"></param>
        /// <param name="net_izin"></param>
        /// <returns></returns>
        public static DataTable DtbLeaveInformationSet(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            DataSet ds = null;

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                //Digiturk.Services.IseGirisPoldy.PersonelBilgisi ClientTest = CreateClientTest();
                //ClientTest.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                //ClientTest.Proxy = WebServicesProxyHelper.ServicesProxy;
                //bool blIzin = ClientTest.IzinBilgileriEkle(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin);
            }
            else
            {
                Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(sicil);
                Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                Client.Proxy = WebServicesProxyHelper.ServicesProxy;
                Client.IzinBilgileriEkle(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin);
            }

            #endregion ortama göre çalıştırılacak

            if (ds != null)
            {
                if (ds.Tables.Count > 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            else
            {
                return new DataTable();
            }
        }

        /// <summary>
        /// İzin Bilgilerini Servise Yazar
        /// </summary>
        /// <param name="sicil"></param>
        /// <param name="izinyili"></param>
        /// <param name="baslangic_tarihi"></param>
        /// <param name="bitis_tarihi"></param>
        /// <param name="izin_tur_kodu"></param>
        /// <param name="brut_toplam"></param>
        /// <param name="hafta_tatili"></param>
        /// <param name="bayram_tatili"></param>
        /// <param name="net_izin"></param>
        /// <returns></returns>
        public static DataTable DtbLeaveInformationSetB(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu, decimal brut_toplam, decimal hafta_tatili, decimal bayram_tatili, decimal net_izin)
        {
            DataSet ds = null;
            Username = "WEBSERVICE";
            Password = "w17211s";

            #region ortama göre çalıştırılacak

            if (System.Configuration.ConfigurationManager.AppSettings["Workflow.Mail.IsMailDebugMode"] == "True")
            {
                //Digiturk.Services.IseGirisPoldy.PersonelBilgisi ClientTest = CreateClientTest();
                //ClientTest.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                //ClientTest.Proxy = WebServicesProxyHelper.ServicesProxy;
                //bool blIzin = ClientTest.IzinBilgileriEkle(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin);
            }
            else
            {
                Digiturk.Services.HRInfosB.PersonelBilgisi Client = CreateClientB(sicil);
                Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                Client.Proxy = WebServicesProxyHelper.ServicesProxy;
                Client.IzinBilgileriEkle(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu, brut_toplam, hafta_tatili, bayram_tatili, net_izin);
            }

            #endregion ortama göre çalıştırılacak

            if (ds != null)
            {
                if (ds.Tables.Count > 0)
                {
                    return ds.Tables[0];
                }
                else
                {
                    return new DataTable();
                }
            }
            else
            {
                return new DataTable();
            }
        }

        /// <summary>
        /// İzin formu iptal edildiğinde kullanılan izini geri eklemesi için kullanılır
        /// </summary>
        /// <param name="sicil"></param>
        /// <param name="izinyili"></param>
        /// <param name="baslangic_tarihi"></param>
        /// <param name="bitis_tarihi"></param>
        /// <param name="izin_tur_kodu"></param>
        public static void DtbLeaveInformationCancelWFCorrection(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu)
        {
            Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(sicil);
            Username = "WEBSERVICE";
            Password = "w17211s";
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            Client.IzinBilgileriSil(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu);
        }

        /// <summary>
        /// İzin formu iptal edildiğinde kullanılan izini geri eklemesi için kullanılır
        /// </summary>
        /// <param name="sicil"></param>
        /// <param name="izinyili"></param>
        /// <param name="baslangic_tarihi"></param>
        /// <param name="bitis_tarihi"></param>
        /// <param name="izin_tur_kodu"></param>
        public static void DtbLeaveInformationCancelWFCorrectionB(string sicil, int izinyili, System.DateTime baslangic_tarihi, System.DateTime bitis_tarihi, string izin_tur_kodu)
        {
            Digiturk.Services.HRInfosB.PersonelBilgisi Client = CreateClientB(sicil);
            Username = "WEBSERVICE";
            Password = "w17211s";
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;
            Client.IzinBilgileriSil(Username, Password, sicil, izinyili, baslangic_tarihi, bitis_tarihi, izin_tur_kodu);
        }

        /// <summary>
        /// İzin Bilgilerini Servise Yazar.
        /// </summary>
        /// <param name="Sicil"></param>
        /// <param name="StartDate"></param>
        /// <param name="EndDate"></param>
        /// <param name="TurKodu"></param>
        /// <param name="Burut"></param>
        /// <param name="HaftaTatili"></param>
        /// <param name="ResmiTatil"></param>
        /// <param name="NetIzin"></param>
        public static void ExecuteLeave(string Sicil, DateTime StartDate, DateTime EndDate, string TurKodu, long Burut, long HaftaTatili, long ResmiTatil, long NetIzin)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.HRInfos.PersonelBilgisi Client = CreateClient(Sicil);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            Client.IzinBilgileriEkle(Username, Password, Sicil, StartDate.Year, StartDate, EndDate, TurKodu, Burut, HaftaTatili, ResmiTatil, NetIzin);
        }

        /// <summary>
        /// İzin Bilgilerini Servise Yazar.
        /// </summary>
        /// <param name="Sicil"></param>
        /// <param name="StartDate"></param>
        /// <param name="EndDate"></param>
        /// <param name="TurKodu"></param>
        /// <param name="Burut"></param>
        /// <param name="HaftaTatili"></param>
        /// <param name="ResmiTatil"></param>
        /// <param name="NetIzin"></param>
        public static void ExecuteLeaveB(string Sicil, DateTime StartDate, DateTime EndDate, string TurKodu, long Burut, long HaftaTatili, long ResmiTatil, long NetIzin)
        {
            Username = "WEBSERVICE";
            Password = "w17211s";
            Digiturk.Services.HRInfosB.PersonelBilgisi Client = CreateClientB(Sicil);
            Client.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            Client.Proxy = WebServicesProxyHelper.ServicesProxy;

            Client.IzinBilgileriEkle(Username, Password, Sicil, StartDate.Year, StartDate, EndDate, TurKodu, Burut, HaftaTatili, ResmiTatil, NetIzin);
        }
    }
}