﻿
using Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.IrisWebServis;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
   public class BayiFotoServis
    {
        public static ReadDocumentContentByStoredDocumentNameResult ReadDocment(long referenceId, string storedDocumentName)
        {
            ReadDocumentContentByStoredDocumentNameResult result = new ReadDocumentContentByStoredDocumentNameResult();

            IrisDocumentStorageWS ReadDocumentServis = new IrisDocumentStorageWS();

            Session SessionI = new Session();
            SessionI.SessionId = 0;
            SessionI.SessionGUID = "00000000-0000-0000-0000-000000000000";
            SessionI.IrisUserId = 0;
            SessionI.PersonnelId = 0;

            var response = ReadDocumentServis.ReadDocumentContentByStoredDocumentName(SessionI, StorageServerName.PersonnelImages, referenceId, DocumentReferenceType.Personnel, storedDocumentName);

            result.DocumentContent = response.DocumentContent;
            result.DocumentExtension = response.DocumentExtension;
            result.ResultCode = response.ResultCode;
            result.ResultMessage = response.ResultMessage;

            return result;
        }
    }
}
