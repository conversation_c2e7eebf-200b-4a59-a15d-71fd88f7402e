﻿using Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AydinlatmaService;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public static class AydinlatmaServis
    {
        public static string MetinVer(string sirket, string donulecek, long? documentID)
        {
            string sonuc = "";
            AydinlatmaService.PersonalDataProtectionBS client = new PersonalDataProtectionBS();
            client.Url = System.Configuration.ConfigurationManager.AppSettings["AydinlatmaMetniServisi"];
            var token = client.SystemAuthenticate("SYSIQ", "SYSIQ111", "DIGITURK", "DIGIPORT", sirket);

            GetPermissionTextRequest request = new GetPermissionTextRequest();
            request.ClientName = "PC";
            if (documentID != null)
            {
                request.DocumentId = documentID;
            }
            else
            {
                request.PermissionTypes = new DataPermissionType[] { DataPermissionType.AYDINLATMA_METNI };
            }

            var response = client.GetPermissionText(request, token);

            if (response != null && response.IsSuccess)
            {
                if (response.Data != null && response.Data.Result != null)
                {
                    foreach (var item in response.Data.Result)
                    {
                        if (donulecek == "Content")
                        {
                            sonuc += item.Content;
                        }
                        else if (donulecek == "DocumentId")
                        {
                            sonuc += item.DocumentId;
                        }
                    }
                }
            }
            return sonuc;
        }

        
    }
}