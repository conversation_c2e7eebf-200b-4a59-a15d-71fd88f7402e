﻿using Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Digiturk.OrganisationalSaleRecordBS;
using System;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public class DTSatisliYayinAcmaResult
    {
        public bool IsSuccess { get; set; }

        public string ErrorCode { get; set; }

        public string ErrorMessage { get; set; }
    } 

    public class DTSatisliYayinAcma
    {
        public static DTSatisliYayinAcmaResult CancelOrganisationalSaleRecord(long? AccountNumber, long ProspectNumber, long ServiceAccountId, long ProductBusinessInterId, long AlterationBusinessInterId, string Comment, bool IsDigiportRequest, long IrisRecordId, string DbsOutletLocation, string DealerCode)
        {
            DTSatisliYayinAcmaResult result = new DTSatisliYayinAcmaResult();
            Digiturk.OrganisationalSaleRecordBS.OrganisationalSaleRecordBS client = new Digiturk.OrganisationalSaleRecordBS.OrganisationalSaleRecordBS();
            client.Url = System.Configuration.ConfigurationManager.AppSettings["OrganisationalSaleRecordBSUrl"];
            client.UseDefaultCredentials = false;
            client.Credentials = WebServicesProxyHelper.ServicesCreadinal;

            string Username = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlUsername"];
            string Password = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlPassword"];
            string Company = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlCompany"];
            string Application = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlApplication"];
            string ChannelName = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlChannelName"];
            string token = client.SystemAuthenticate(Username, Password, Company, Application, ChannelName);
            var cancelresult = client.CancelOrganisationalSaleRecord(new CancelOrganisationalSaleRecordRequestModel()
            {
                AccountNumber = AccountNumber,
                AlterationBusinessInterId = AlterationBusinessInterId,
                DbsOutletLocation = DbsOutletLocation,
                DealerCode = DealerCode,
                DigiportCancelDescription = Comment,
                IrisCancelDescription = string.Empty,
                IrisRecordId = IrisRecordId,
                IsDigiportRequest = true,
                ProductBusinessInterId = ProductBusinessInterId,
                ProspectNumber = ProductBusinessInterId,
                ServiceAccountId = ServiceAccountId
            }, token);
            result.IsSuccess = cancelresult.IsSuccess;
            result.ErrorCode = cancelresult.ErrorCode;
            result.ErrorMessage = cancelresult.ErrorMessage;
            return result;
            //if (!cancelresult.IsSuccess)
            //{
            //    //Todo: Generic
            //    //Digiturk.Workflow.Digiflow.GenericMailHelper.GenericMailHelper.SendEmailDirect(98, actionInstance, wfContext, MailTemp, ToList, CcList);
            //    //Digiturk.Workflow.Digiflow.DataAccessLayer.WorkFlowTraceWorker.OracleLog("Sistem", "Akış Sonlanmadı Iris Prosedür Hata", " IRIS_ID: " + strIrisId + "- Son Akış no:" + strWfIns);

            //    throw new Exception(cancelresult.ErrorCode + " " + cancelresult.ErrorMessage);
            //}
            //else
            //{
            //    return cancelresult.IsSuccess;
            //}
        }

        public static DTSatisliYayinAcmaResult CloseOrganisationalSaleRecord(long? AccountNumber, long ProspectNumber, long ServiceAccountId, long ProductBusinessInterId, long AlterationBusinessInterId, string Comment, bool IsDigiportRequest, long IrisRecordId, string DbsOutletLocation, string DealerCode, string SatelliteType)
        {
            DTSatisliYayinAcmaResult result = new DTSatisliYayinAcmaResult();
            Digiturk.OrganisationalSaleRecordBS.OrganisationalSaleRecordBS client = new Digiturk.OrganisationalSaleRecordBS.OrganisationalSaleRecordBS();
            client.Url = System.Configuration.ConfigurationManager.AppSettings["OrganisationalSaleRecordBSUrl"];
            client.UseDefaultCredentials = false;
            client.Credentials = WebServicesProxyHelper.ServicesCreadinal;

            string Username = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlUsername"];
            string Password = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlPassword"];
            string Company = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlCompany"];
            string Application = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlApplication"];
            string ChannelName = System.Configuration.ConfigurationSettings.AppSettings["OrganisationalSaleRecordBSUrlChannelName"];
            string token = client.SystemAuthenticate(Username, Password, Company, Application, ChannelName);

            var closeresult = client.CloseOrganisationalSaleRecord(new CloseOrganisationalSaleRecordRequestModel()
            {
                AccountNumber = AccountNumber,
                AlterationBusinessInterId = AlterationBusinessInterId,
                DbsOutletLocation = DbsOutletLocation,
                DealerCode = DealerCode,
                DigiportStatus = "OK",
                IrisRecordId = IrisRecordId,
                IsDigiportRequest = IsDigiportRequest,
                ProductBusinessInterId = ProductBusinessInterId,
                ProspectNumber = ProspectNumber,
                ServiceAccountId = ServiceAccountId,
                SatelliteType = SatelliteType
            }, token);
            result.IsSuccess = closeresult.IsSuccess;
            result.ErrorCode = closeresult.ErrorCode;
            result.ErrorMessage = closeresult.ErrorMessage;
            return result;
            //if (!closeresult.IsSuccess)
            //{
            //    throw new Exception(closeresult.ErrorCode + " " + closeresult.ErrorMessage);
            //}
            //else
            //{
            //    return closeresult.IsSuccess;
            //}
        }
    }
}