﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "16.10.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
        
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
        
        public static Settings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3332/Service1.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRConnect_Service1 {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRConnect_Ser" +
                    "vice1"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:8081/PermissionProcess.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_Serv" +
                    "icesHelper_PermissionProcess_PermissionProcess"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3331/PersonelBilgisi.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_AvansAktar_PersonelBilgisi {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_AvansAktar_Pe" +
                    "rsonelBilgisi"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3331/PersonelBilgisi.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_IseGirisPoldy_PersonelBilgisi {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_IseGirisPoldy" +
                    "_PersonelBilgisi"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3335/PersonelBilgisi.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfosB_PersonelBilgisi {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfosB_Pers" +
                    "onelBilgisi"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIris_IrisSessionSdpBS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIris_IrisSessionSdpBS" +
                    ""]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIrisDealer_IrisDealerSdpBS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIrisDealer_IrisDealer" +
                    "SdpBS"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3331/PersonelBilgisi.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfos_PersonelBilgisi {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfos_Perso" +
                    "nelBilgisi"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:8081/PermissionProcess.asmx")]
        public string Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3331/EgitimModulu.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_EgitimModulu {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_Work" +
                    "FlowServicesHelper_EgitimModulu"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1iis4:3333/appdataservice.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AppData_AppDataService {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_Work" +
                    "FlowServicesHelper_AppData_AppDataService"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://dtl1qlikviewtst:82/yetkiver.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_QlikviewYetkiVer_Service1 {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_QlikviewYetkiVer_Service1"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtec" +
            "tionBS.svc")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AydinlatmaService_PersonalDataProtectionBS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AydinlatmaService_PersonalDataP" +
                    "rotectionBS"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_OrganisationalSaleRecordBS_OrganisationalSaleRecordBS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_OrganisationalSaleReco" +
                    "rdBS_OrganisationalSaleRecordBS"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_SosyalMedyaService_PersonalDataProtectionBS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_SosyalMedyaService_PersonalData" +
                    "ProtectionBS"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("https://irisws.digiturk.net/storagews/service.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_IrisWebServis_IrisDocumentStorageWS {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_IrisWebServis_IrisDocumentStora" +
                    "geWS"]));
            }
        }
        
        [global::System.Configuration.ApplicationScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.SpecialSettingAttribute(global::System.Configuration.SpecialSetting.WebServiceUrl)]
        [global::System.Configuration.DefaultSettingValueAttribute("http://digiflowtest/Services/createWorkflowNew.asmx")]
        public string Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_CreateBayiBelgeOnayAkis_CreateWorkflowNew {
            get {
                return ((string)(this["Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_CreateBayiBelgeOnayAkis_CreateW" +
                    "orkflowNew"]));
            }
        }
    }
}
