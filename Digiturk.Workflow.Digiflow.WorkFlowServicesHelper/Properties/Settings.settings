﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRConnect_Service1" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3332/Service1.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:8081/PermissionProcess.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_AvansAktar_PersonelBilgisi" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3331/PersonelBilgisi.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_IseGirisPoldy_PersonelBilgisi" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3331/PersonelBilgisi.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfosB_PersonelBilgisi" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3335/PersonelBilgisi.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIris_IrisSessionSdpBS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIrisDealer_IrisDealerSdpBS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfos_PersonelBilgisi" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3331/PersonelBilgisi.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:8081/PermissionProcess.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_EgitimModulu" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3331/EgitimModulu.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AppData_AppDataService" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1iis4:3333/appdataservice.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_QlikviewYetkiVer_Service1" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://dtl1qlikviewtst:82/yetkiver.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AydinlatmaService_PersonalDataProtectionBS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_OrganisationalSaleRecordBS_OrganisationalSaleRecordBS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_SosyalMedyaService_PersonalDataProtectionBS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_IrisWebServis_IrisDocumentStorageWS" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">https://irisws.digiturk.net/storagews/service.asmx</Value>
    </Setting>
    <Setting Name="Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_CreateBayiBelgeOnayAkis_CreateWorkflowNew" Type="(Web Service URL)" Scope="Application">
      <Value Profile="(Default)">http://digiflowtest/Services/createWorkflowNew.asmx</Value>
    </Setting>
  </Settings>
</SettingsFile>