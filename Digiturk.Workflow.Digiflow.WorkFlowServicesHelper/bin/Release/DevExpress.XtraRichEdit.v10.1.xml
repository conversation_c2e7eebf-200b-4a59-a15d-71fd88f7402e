<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.XtraRichEdit.v10.1</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraRichEdit.Commands.FindPrevCommand">

            <summary>
                <para>Looks for the previous matching string as defined in the search criteria given by the preceding FindCommand.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.FindPrevCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the FindPrevCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.FindPrevCommand.Id">
            <summary>
                <para>Gets the ID of the FindPrevCommand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.FindNextCommand">

            <summary>
                <para>Looks for the next matching string as defined in the search criteria given by the preceding FindCommand.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.FindNextCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the FindNextCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.FindNextCommand.Id">
            <summary>
                <para>Gets the ID of the FindNextCommand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.QuickPrintCommand">

            <summary>
                <para>Prints the current document using the default printer.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.QuickPrintCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the QuickPrintCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.QuickPrintCommand.Id">
            <summary>
                <para>Gets the ID of the QuickPrintCommand.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.PrintCommand">

            <summary>
                <para>Invokes the <b>Print</b> dialog to print the current document.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.PrintCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the PrintCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.PrintCommand.Id">
            <summary>
                <para>Gets the ID of the PrintCommand.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu">

            <summary>
                <para>Represents a popup (context) menu of the <b>RichEditControl</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu.#ctor">
            <summary>
                <para>Initializes a new instance of the RichEditPopupMenu class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the RichEditPopupMenu class with the specified handler for the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.
</para>
            </summary>
            <param name="beforePopup">
		An event handler that will be invoked before the menu is displayed.

            </param>


        </member>
        <member name="T:DevExpress.XtraRichEdit.PreparePopupMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PreparePopupMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.PreparePopupMenuEventArgs.#ctor(DevExpress.XtraRichEdit.Menu.RichEditPopupMenu)">
            <summary>
                <para>Initializes a new instance of the PreparePopupMenuEventArgs class with the specified menu object.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.XtraRichEdit.Menu.RichEditPopupMenu"/> object specifying the menu for the event. This object is assigned to the <see cref="P:DevExpress.XtraRichEdit.PreparePopupMenuEventArgs.Menu"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.PreparePopupMenuEventArgs.Menu">
            <summary>
                <para>Gets or sets the popup (context) menu for which this event was raised.

</para>
            </summary>
            <value>A <b>RichEditPopupMenu</b> object, which represents the context menu for the event. 

</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.RichEditScrollbarVisibility">

            <summary>
                <para>Specifies the visibility of a scroll bar.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Auto">
            <summary>
                <para>A scroll bar is automatically displayed when required.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Hidden">
            <summary>
                <para>A scroll bar is hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditScrollbarVisibility.Visible">
            <summary>
                <para>A scroll bar is visible.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.ScrollbarOptions">

            <summary>
                <para>Represents the base class for scrollbar options.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraRichEdit.ScrollbarOptions.Visibility">
            <summary>
                <para>Gets or sets a value that specifies the visibility of a scroll bar.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditScrollbarVisibility"/> enumeration member specifying the visibility mode.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.RichEditControl">

            <summary>
                <para>Represents a RichEdit control which is a container for the rich-text document with all the necessary functionality for loading, editing and saving.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.#ctor">
            <summary>
                <para>Initializes a new instance of the RichEditControl class with default settings.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.About">
            <summary>
                <para>Invokes the <b>About</b> dialog box.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.ActiveView">
            <summary>
                <para>Gets the <b>View</b> currently used by the RichEditControl to display the document. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditView"/> class instance, which is one of the views listed in the <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.ActiveViewChanged">
            <summary>
                <para>Occurs when the value of the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.ActiveView"/> property is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.ActiveViewType">
            <summary>
                <para>Gets or sets the type of the <b>View</b> which is currently used by the RichEditControl to show the document. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration value specifying the active <b>View</b> type.
</value>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to add. 

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested. 

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
            <summary>
                <para>Adds the specified service to the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to add. 

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested. 

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.Object,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to add. 

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>. 

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.AddService(System.Type,System.Object)">
            <summary>
                <para>Adds the specified service to the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to add. 

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.AllowDrop">
            <summary>
                <para>Gets or sets a value indicating whether the control allows drag-and-drop operations. This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value><b>true</b> if drag-and-drop is enabled in the control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Appearance">
            <summary>
                <para>Provides access to the object containing appearance settings for the control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditAppearance"/> object containig appearance settings specific to the RichEditControl.

</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackColor">
            <summary>
                <para>This property is not in effect for the RichEditControl class. 
</para>
            </summary>
            <value> A <see cref="T:System.Drawing.Color"/> value representing the background color.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackgroundImage">
            <summary>
                <para>This property is not in effect for the RichEditControl class. 
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.BackgroundImageLayout">
            <summary>
                <para>This property is not in effect for the RichEditControl class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeDispose">
            <summary>
                <para>Occurs before the RichEdit control is released from memory
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeExport">
            <summary>
                <para>Occurs before the document is saved (exported to a certain format).
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.BeforeImport">
            <summary>
                <para>Occurs before a document is loaded (imported from an external source).
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.BeginUpdate">
            <summary>
                <para>Prevents the control from being updated until the <see cref="M:DevExpress.XtraRichEdit.RichEditControl.EndUpdate"/> method is called. 

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.BorderStyle">
            <summary>
                <para>Gets or sets the border style for the RichEdit control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style of the scheduler control.
</value>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.CancelUpdate">
            <summary>
                <para>Unlocks the RichEdit control after a call to the <see cref="M:DevExpress.XtraRichEdit.RichEditControl.BeginUpdate"/> method, and causes an immediate update without raising any notification events.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.CloseImeWindow(DevExpress.XtraRichEdit.ImeCloseStatus)">
            <summary>
                <para>Finalizes the input method editor composition and closes IME window.
</para>
            </summary>
            <param name="closeSatus">
		A <see cref="T:DevExpress.XtraRichEdit.ImeCloseStatus"/> enumeration member specifying how the composition string is treated.

            </param>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.ContentChanged">
            <summary>
                <para>Occurs when the document content was changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateCommand(DevExpress.XtraRichEdit.Commands.RichEditCommandId)">
            <summary>
                <para>Create a <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommand"/> object by the command identifier.
</para>
            </summary>
            <param name="commandId">
		A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> structure member, which specifies a command.

            </param>
            <returns>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommand"/> instance, representing a Rich Text Control command.
</returns>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.CreateNewDocument">
            <summary>
                <para>Creates a new empty document, loads and displays it.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.DefaultViewType">
            <summary>
                <para>Gets the default view type used by the RichEdit control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditViewType"/> enumeration member, which specifies the RichEdit view.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Document">
            <summary>
                <para>Provides access to a <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface representing the control's document.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.API.Native.Document"/> interface representing a document loaded in the control.

</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentClosing">
            <summary>
                <para>Occurs when a document that has not yet been saved is about to be closed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.DocumentLoaded">
            <summary>
                <para>Occurs after a document is loaded into the RichEdit control.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.DpiX">
            <summary>
                <para>Gets the current dpi value for the X-coordinate.

</para>
            </summary>
            <value>A <b>Single</b> dpi value.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.DpiY">
            <summary>
                <para>Gets the current dpi value for the Y-coordinate.

</para>
            </summary>
            <value>A <b>Single</b> dpi value.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.DragDropMode">
            <summary>
                <para>Gets or sets the drag-and-drop mode which is active in the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.DragDropMode"/> enumeration value.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.EmptyDocumentCreated">
            <summary>
                <para>Occurs when a new document is created in the RichEdit Control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.EndUpdate">
            <summary>
                <para>Unlocks the control after a call to the <see cref="M:DevExpress.XtraRichEdit.RichEditControl.BeginUpdate"/> method, and causes an immediate update. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.FinishHeaderFooterEditing">
            <summary>
                <para>Occurs when the end-user finishes editing a header or footer.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Font">
            <summary>
                <para>Gets or sets the font of the text that has no direct font formatting or style applied.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.ForeColor">
            <summary>
                <para>Gets or sets the color of the text that has no direct font formatting or style applied.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the color.
</value>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetBoundsFromPosition(DevExpress.XtraRichEdit.API.Native.DocumentPosition)">
            <summary>
                <para>Gets the rectangle representing the character at the specified position.

</para>
            </summary>
            <param name="pos">
		A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> representing the position in the document.

            </param>
            <returns>A <see cref="T:System.Drawing.Rectangle"/> representing the character.
</returns>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetPositionFromPoint(System.Drawing.Point)">
            <summary>
                <para>Gets the position in the document closest to the specified point.
</para>
            </summary>
            <param name="clientPoint">
		A <see cref="T:System.Windows.Point"/> object specifying the location for which the position is retrieved. The point coordinates are measured in <i>documents</i> units.

            </param>
            <returns>A <see cref="T:DevExpress.XtraRichEdit.API.Native.DocumentPosition"/> object representing a position in the document.
</returns>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetService(System.Type)">
            <summary>
                <para>Gets the service object of the specified type. 
</para>
            </summary>
            <param name="serviceType">
		An object that specifies the type of service object to get. 

            </param>
            <returns>A service object of the specified type,or a null reference (Nothing in Visual Basic) if there is no service object of this type. 
</returns>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.GetService``1">
            <summary>
                <para>Gets the service object of the specified generic type.
</para>
            </summary>
            <returns>A service object of the specified generic type, or a null reference (<b>Nothing</b> in Visual Basic) if there is no service object of this type. 
</returns>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.HtmlText">
            <summary>
                <para>Gets or sets the control's content as HTML text.
</para>
            </summary>
            <value>A string containing text in HTML format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.HtmlTextChanged">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.HyperlinkClick">
            <summary>
                <para>Occurs when an end-user clicks the hyperlink to activate it.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.IsImeWindowOpen">
            <summary>
                <para>Determines whether the Input Method Editor (IME) composition window is active.
</para>
            </summary>
            <returns><b>true</b> if the IME window is open; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsPrintingAvailable">
            <summary>
                <para>Indicates whether the RichEditControl can be printed or exported.
</para>
            </summary>
            <value><b>true</b> if the control can be printed and exported; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.IsUpdateLocked">
            <summary>
                <para>Gets whether the scheduler control has been locked for updating.
</para>
            </summary>
            <value><b>true</b> if the scheduler control is locked; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.LayoutUnit">
            <summary>
                <para>Gets or sets a unit of measure used for a control's layout.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraRichEdit.DocumentUnit"/> enumeration values.
</value>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument">
            <summary>
                <para>Invokes the "Open..." file dialog, creates a specific importer and loads the file.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the <b>Open</b> file dialog as a <i>child</i> of the specified <i>parent</i> window.
</para>
            </summary>
            <param name="parent">
		The <see cref="T:System.Windows.Forms.IWin32Window"/> that represents the parent window.

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
            <summary>
                <para>Loads a document from a file, specifying the document's format.
</para>
            </summary>
            <param name="fileName">
		A string value specifying the path to the file from which to load a document.

            </param>
            <param name="documentFormat">
		One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> members.

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
            <summary>
                <para>Loads a document from a stream, specifying the document's format.
</para>
            </summary>
            <param name="stream">
		The stream from which to load a document.

            </param>
            <param name="documentFormat">
		One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> members.


            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.LoadDocument(System.String)">
            <summary>
                <para>Loads a specified file. The file format is identified by the file extension.
</para>
            </summary>
            <param name="fileName">
		A string specifying a path of the file to load.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings that specify the RichEdit control's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the control's look and feel.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.MenuManager">
            <summary>
                <para>Gets or sets the menu manager which controls the look and feel of context menus.

</para>
            </summary>
            <value>An object that implements the <see cref="T:DevExpress.Utils.Menu.IDXMenuManager"/> interface.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.MhtText">
            <summary>
                <para>Gets or sets the control's content as MHT text.
</para>
            </summary>
            <value>A string containing text in MHT format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.MhtTextChanged">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Model">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.

</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Modified">
            <summary>
                <para>Gets or sets a value that indicates that the RichEdit control contents was modified since it was last saved. 

</para>
            </summary>
            <value><b>true</b> if the control's contents was modified since it was last saved.; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.ModifiedChanged">
            <summary>
                <para>Occurs when the value of the <see cref="P:DevExpress.XtraRichEdit.RichEditControl.Modified"/> property is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.OpenDocumentBytes">
            <summary>
                <para>Gets or sets the control's content as an array of bytes in Open Office Text (.odt) format.
</para>
            </summary>
            <value>An array of bytes representing the OpenDocument Text (.odt) format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.OpenDocumentBytesChanged">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.OpenXmlBytes">
            <summary>
                <para>Gets or sets the control's content as an array of bytes in Office Open XML (Docx) format.
</para>
            </summary>
            <value>An array of bytes representing the document in Docx format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.OpenXmlBytesChanged">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Options">
            <summary>
                <para>Provides access to the variety of options which can be specified for the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions"/> object containing various RichEditControl's options.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.PreparePopupMenu">
            <summary>
                <para>Occurs before a context (popup) menu is created for the control's document every time a context menu is being invoked.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.Print">
            <summary>
                <para>Prints the document to the default printer.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.ReadOnly">
            <summary>
                <para>Gets or sets whether document modifications are prohibited.
</para>
            </summary>
            <value><b>true</b> if the document is in a read-only state; otherwise, <b>false</b>.

</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.ReadOnlyChanged">
            <summary>
                <para>Occurs when the read-only state of the RichEdit control is changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveService(System.Type)">
            <summary>
                <para>Removes the service of specified type from the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to remove. 

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.RemoveService(System.Type,System.Boolean)">
            <summary>
                <para>Removes the service of the specified type from the service container. 

</para>
            </summary>
            <param name="serviceType">
		The type of service to remove. 

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; <b>otherwise</b>, false. 

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ResetText">
            <summary>
                <para>Clears the text within the control.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.RightToLeft">
            <summary>
                <para>This property is not in effect for the RichEditControl class.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.RtfText">
            <summary>
                <para>Gets or sets the formatted text content of the control.
</para>
            </summary>
            <value>A string, containing the document's content in rich text format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.RtfTextChanged">
            <summary>
                <para>Occurs when the text in the control is changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Saves the document or invokes the <b>Save As</b> file dialog as a <i>child</i> of the specified <i>parent</i> window.


</para>
            </summary>
            <param name="parent">
		The <see cref="T:System.Windows.Forms.IWin32Window"/> that represents the parent window.

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument">
            <summary>
                <para>Saves a document.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.String,DevExpress.XtraRichEdit.DocumentFormat)">
            <summary>
                <para>Saves the control's document to a file, specifying the document's format.
</para>
            </summary>
            <param name="fileName">
		A string value specifying the path to a file into which to save the control's document.


            </param>
            <param name="documentFormat">
		One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration values.



            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocument(System.IO.Stream,DevExpress.XtraRichEdit.DocumentFormat)">
            <summary>
                <para>Saves the control's document to a stream, specifying the document's format.
</para>
            </summary>
            <param name="stream">
		The stream to output the document to.

            </param>
            <param name="documentFormat">
		One of the <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration values.



            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocumentAs">
            <summary>
                <para>Invokes a <b>Save As</b> dialog and saves a document.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.SaveDocumentAs(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the <b>Save As</b> form which is shown modally as a <i>child</i> of the specified <i>parent</i> window.

</para>
            </summary>
            <param name="parent">
		The <see cref="T:System.Windows.Forms.IWin32Window"/> that represents the parent window.

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ScrollToCaret">
            <summary>
                <para>Scrolls the document to the caret position.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.SearchFormShowing">
            <summary>
                <para>Occurs when a search form is invoked before it is displayed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.SelectionChanged">
            <summary>
                <para>Fires in response to changing a selection in the document.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowPrintDialog">
            <summary>
                <para>Invokes the <b>Print</b> dialog.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowPrintPreview">
            <summary>
                <para>Invokes the <b>Print Preview</b> window.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowReplaceForm">
            <summary>
                <para>Invokes the SearchForm dialog switched to the <b>Replace</b> tab.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditControl.ShowSearchForm">
            <summary>
                <para>Invokes the "Find and Replace" dialog.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.SpellChecker">
            <summary>
                <para>Gets or sets the component used for spelling check by the RichEdit control.
</para>
            </summary>
            <value>A component which provides the <b>DevExpress.XtraSpellChecker.ISpellChecker</b> interface.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.StartHeaderFooterEditing">
            <summary>
                <para>Occurs when the end-user starts editing a header or footer.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Text">
            <summary>
                <para>Gets or sets the plain text content of the control.
</para>
            </summary>
            <value>A string, containing the document's unformatted text.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.ToolTipController">
            <summary>
                <para>Gets or sets the tooltip controller component that controls the appearance, position and the content of the hints displayed by the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the RichEditControl.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.UnhandledException">
            <summary>
                <para>This event is raised when an exception unhandled by the <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> occurs.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Unit">
            <summary>
                <para>Gets or sets a unit of measure used within the control.

</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraRichEdit.DocumentUnit"/> enumeration values.


</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.UpdateUI">
            <summary>
                <para>Raised when changes occur which may affect the control's UI.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.Views">
            <summary>
                <para>Contains settings of the Views that are used to display a document in the RichEdit Control. 
</para>
            </summary>
            <value> A <see cref="T:DevExpress.XtraRichEdit.RichEditViewRepository"/> object which stores the settings of the document Views. 
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditControl.WordMLText">
            <summary>
                <para>Gets or sets the control's content as the text in WordProcessingML (Microsoft Office Word 2003 XML) format.
</para>
            </summary>
            <value>A string of text in WordML format.
</value>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.WordMLTextChanged">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraRichEdit.RichEditControl.ZoomChanged">
            <summary>
                <para>Fires when the zoom factor used to display the document is changed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.RichEditAppearance">

            <summary>
                <para>Represents appearance settings specific to the RichEditControl.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.RichEditAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the RichEditAppearance class with default settings. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraRichEdit.RichEditAppearance.Text">
            <summary>
                <para>Provides access to the appearance object containing appearance settings for the text.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> representing properties used to customize the look and feel of the text.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.PreparePopupMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PreparePopupMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.PreparePopupMenuEventHandler.Invoke(System.Object,DevExpress.XtraRichEdit.PreparePopupMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraRichEdit.RichEditControl.PreparePopupMenu"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender (typically a <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/>).


            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraRichEdit.PreparePopupMenuEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraRichEdit.HorizontalScrollbarOptions">

            <summary>
                <para>Represents the class containing options for the horizontal scrollbar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.HorizontalScrollbarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the HorizontalScrollbarOptions class with default settings.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions">

            <summary>
                <para>Represents the class containing options for the vertical scrollbar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.VerticalScrollbarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the VerticalScrollbarOptions class with default settings.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit">

            <summary>
                <para>Contains settings specific to an in-place editor that displays RTF data.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the RepositoryItemRichTextEdit class with default settings. 


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.AcceptsTab">
            <summary>
                <para>Gets or sets a value specifying whether a tab character can be inserted into the editor's text.
</para>
            </summary>
            <value><b>true</b> if tab characters typed within the control are accepted and processed by an editor; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Assign(DevExpress.XtraEditors.Repository.RepositoryItem)">
            <summary>
                <para>Copies the settings of the specified repository item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> object or its descendant that represents the source of the operation.

            </param>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.AutoHeight">
            <summary>
                <para>Gets whether the editor's height is calculated automatically to fit the editor's content. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>Always <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.CustomHeight">
            <summary>
                <para>Gets or sets the editor's height.
</para>
            </summary>
            <value>An integer value that specifies the editor's height, in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.DocumentFormat">
            <summary>
                <para>Gets or sets the format of the document contained in the editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.DocumentFormat"/> enumeration member, specifying the document format.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of an editor corresponding to the current repository item.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that identifies the class name of a corresponding editor.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Encoding">
            <summary>
                <para>Gets or sets the character encoding of the document contained in the editor.
</para>
            </summary>
            <value>A  <see cref="T:System.Text.Encoding"/> object specifying the character encoding.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.EncodingWebName">
            <summary>
                <para>Gets or sets the character encoding by specifying the name registered with the Internet Assigned Numbers Authority (IANA).
</para>
            </summary>
            <value>A string, representing the IANA encoding.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.GetBrick(DevExpress.XtraEditors.PrintCellHelperInfo)">
            <summary>
                <para>Returns a brick object that contains information on how the current editor should be printed.
</para>
            </summary>
            <param name="info">
		A <see cref="T:DevExpress.XtraEditors.PrintCellHelperInfo"/> object that provides information on the editor's state and appearance settings that will be used when the editor is printed.

            </param>
            <returns>An <see cref="T:DevExpress.XtraPrinting.IVisualBrick"/> object that provides information on how the current editor should be printed.
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.HorizontalIndent">
            <summary>
                <para>Gets or sets a horizontal margin between the border and text.
</para>
            </summary>
            <value>An integer value that specifies a horizontal margin, in pixels, between the border and text.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.MaxHeight">
            <summary>
                <para>Gets or sets the editor's maximum height.
</para>
            </summary>
            <value>An integer value that specifies the editor's maximum height, in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsBehavior">
            <summary>
                <para>Provides access to an object that enables you to apply restrictions on different editor operations.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions"/> class instance containing restriction specifications.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsExport">
            <summary>
                <para>Provides access to options specific for document export to different formats.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions"/> class instance containing export options.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsHorizontalScrollbar">
            <summary>
                <para>Provides access to the options specific to the horizontal scrollbar of the rich text editor.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions"/> object used to specify options for the horizontal scrollbar.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsImport">
            <summary>
                <para>Provides access to options specific for document import from different formats.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions"/> class instance containing export options.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.OptionsVerticalScrollbar">
            <summary>
                <para>Provides access to the options specific to the vertical scrollbar of the RichEditControl.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions"/> object used to specify options for the vertical scrollbar.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.Register">
            <summary>
                <para>Registers the current RepositoryItemRichTextEdit instance within the default Repository.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit.VerticalIndent">
            <summary>
                <para>Gets or sets a vertical margin between the border and text.
</para>
            </summary>
            <value>An integer value that specifies a vertical margin, in pixels, between the border and text.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.RulerOptions">

            <summary>
                <para>Represents the base class for ruler options.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraRichEdit.RulerOptions.Visibility">
            <summary>
                <para>Gets or sets whether the ruler is shown.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.RichEditRulerVisibility"/> enumeration member specifying the visibility mode.
</value>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.RichTextPrintFormat">

            <summary>
                <para>Specifies how the <see cref="T:System.Windows.Forms.RichTextBox"/> is printed via the <see cref="T:DevExpress.XtraPrintingLinks.RichTextBoxLink"/>.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPrintingLinks.RichTextPrintFormat.ClientPageSize">
            <summary>
                <para>A RichTextBox is printed using the page width specified by the <see cref="P:DevExpress.XtraPrinting.PrintingSystem.PageSettings"/> of a link's <see cref="T:DevExpress.XtraPrinting.PrintingSystem"/>.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPrintingLinks.RichTextPrintFormat.Custom">
            <summary>
                <para>A RichTextBox is printed using the width specified via the <see cref="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.CustomFormatSize"/> property.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPrintingLinks.RichTextPrintFormat.RichTextBoxSize">
            <summary>
                <para>A RichTextBox is printed using the <b>Width</b> property value of the RichTextBox control.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase">

            <summary>
                <para>Represents the base class for the <see cref="T:DevExpress.XtraPrintingLinks.RichTextBoxLink"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextBoxLinkBase class with default settings. 


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.#ctor(DevExpress.XtraPrinting.PrintingSystemBase)">
            <summary>
                <para>Initializes a new instance of the RichTextBoxLinkBase class with the specified printing system. 


</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> object which specifies the printing system used to draw the current link. This value is assigned to the <see cref="P:DevExpress.XtraPrintingLinks.RichTextBoxLink.PrintingSystem"/> property. 

            </param>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.AddSubreport(System.Drawing.PointF)">
            <summary>
                <para>Adds a subreport to the current report. 
</para>
            </summary>
            <param name="offset">
		A <see cref="T:System.Drawing.PointF"/> object which specifies the vertical offset of the subreport within the current report. 

            </param>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.CustomFormatSize">
            <summary>
                <para>Gets or sets the custom size with which a System.Windows.Forms.RichTextBox control should be printed. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> value. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.InfiniteFormatHeight">
            <summary>
                <para>Gets or sets a value indicating whether the <see cref="T:System.Windows.Forms.RichTextBox"/> should be printed with unlimited height. 
</para>
            </summary>
            <value><b>true</b> if a RichTextBox should be printed with unlimited height; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.PrintableObjectType">
            <summary>
                <para>Gets the type of the object to be printed by the link. 
</para>
            </summary>
            <value>A <see cref="T:System.Type"/> class descendant representing the <see cref="T:System.Windows.Forms.RichTextBox"/> type. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.PrintFormat">
            <summary>
                <para>Gets or sets a value indicating what size a <see cref="T:System.Windows.Forms.RichTextBox"/> is printed. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrintingLinks.RichTextPrintFormat"/> enumeration value. The default is <b>ClientPageSize</b>. 
</value>


        </member>
        <member name="P:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.RichTextBox">
            <summary>
                <para>Gets or sets a <see cref="T:System.Windows.Forms.RichTextBox"/> object to be printed via the current link. 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.RichTextBox"/> object to be printed. The default is <b>null</b>. 
</value>


        </member>
        <member name="M:DevExpress.XtraPrintingLinks.RichTextBoxLinkBase.SetDataObject(System.Object)">
            <summary>
                <para>Sets the object to be printed by this link.
</para>
            </summary>
            <param name="data">
		A <see cref="T:System.Windows.Forms.RichTextBox"/> object to be printed by this link.

            </param>


        </member>
        <member name="T:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions">

            <summary>
                <para>Represents the class containing options for the horizontal scrollbar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RichTextEditHorizontalScrollbarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextEditHorizontalScrollbarOptions class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions">

            <summary>
                <para>Represents the storage of settings specifying end-user restrictions applied to document operations.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.Repository.RichTextEditBehaviorOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextEditBehaviorOptions class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.ImeCloseStatus">

            <summary>
                <para>Lists possible results of finalizing the input and closing the input method editor window.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraRichEdit.ImeCloseStatus.ImeCompositionCancel">
            <summary>
                <para>Composition string in the input method editor is discarded.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraRichEdit.ImeCloseStatus.ImeCompositionComplete">
            <summary>
                <para>The composition string is used as the result string to insert in the document.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions">

            <summary>
                <para>Represents the storage for settings specific for WinForms (as opposed to SilverLight-specific settings).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the WinFormsRichEditControlOptions class with default settings.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraRichEdit.RichEditControl"/> instance.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions.HorizontalRuler">
            <summary>
                <para>Provides access to the horizontal ruler of the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.HorizontalRulerOptions"/> object used to specify options for the horizontal ruler.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions.HorizontalScrollbar">
            <summary>
                <para>Provides access to the options specific for the horizontal scrollbar of the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.HorizontalScrollbarOptions"/> object used to specify options for the horizontal scrollbar.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions.VerticalRuler">
            <summary>
                <para>Provides access to the vertical ruler of the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.VerticalRulerOptions"/> object used to specify options for the vertical ruler.
</value>


        </member>
        <member name="P:DevExpress.XtraRichEdit.WinFormsRichEditControlOptions.VerticalScrollbar">
            <summary>
                <para>Provides access to the options specific for the vertical scrollbar of the RichEditControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.VerticalScrollbarOptions"/> object used to specify options for the vertical scrollbar.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions">

            <summary>
                <para>Contains options used for loading (importing) documents from the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Import.RichTextEditDocumentImportOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextEditDocumentImportOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions">

            <summary>
                <para>Contains options used for saving (exporting) documents from the <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItemRichTextEdit"/> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Export.RichTextEditDocumentExportOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the RichTextEditDocumentExportOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.VerticalRulerOptions">

            <summary>
                <para>Represents the class containing options for the vertical ruler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.VerticalRulerOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the VerticalRulerOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.HorizontalRulerOptions">

            <summary>
                <para>Represents the class containing options for the horizontal ruler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.HorizontalRulerOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the HorizontalRulerOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.ShowSymbolFormCommand">

            <summary>
                <para>Invokes the <b>Symbol</b> dialog window.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.ShowSymbolFormCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the ShowSymbolFormCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.ShowSymbolFormCommand.CreateDefaultCommandUIState">
            <summary>
                <para>Creates an object representing the user interface state for the command.
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface, providing information on the UI state.
</returns>


        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.ShowSymbolFormCommand.ForceExecute(DevExpress.Utils.Commands.ICommandUIState)">
            <summary>
                <para>Executes the command specifying the UI state explicitly.
</para>
            </summary>
            <param name="state">
		An object which implements the <see cref="T:DevExpress.Utils.Commands.ICommandUIState"/> interface.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.ShowSymbolFormCommand.Id">
            <summary>
                <para>Gets the ID of the ShowSymbolFormCommand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.RichEditRulerVisibility">

            <summary>
                <para>Specifies the visibility of a ruler.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditRulerVisibility.Auto">
            <summary>
                <para>The ruler visibility is determined by the current view.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditRulerVisibility.Hidden">
            <summary>
                <para>The ruler is always hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraRichEdit.RichEditRulerVisibility.Visible">
            <summary>
                <para>The ruler is always visible.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.PrintPreviewCommand">

            <summary>
                <para>Displays the <b>Print Preview</b> window for the current document.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.PrintPreviewCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the PrintPreviewCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.PrintPreviewCommand.Id">
            <summary>
                <para>Gets the ID of the PrintPreviewCommand.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
        <member name="T:DevExpress.XtraRichEdit.Commands.SaveDocumentCommand">

            <summary>
                <para>Saves a document to a file.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraRichEdit.Commands.SaveDocumentCommand.#ctor(DevExpress.XtraRichEdit.IRichEditControl)">
            <summary>
                <para>Initializes a new instance of the SaveDocumentCommand class with the specified owner.
</para>
            </summary>
            <param name="control">
		An object exposing the <b>DevExpress.XtraRichEdit.IRichEditControl</b> interface specifying the owner of the command.

            </param>


        </member>
        <member name="P:DevExpress.XtraRichEdit.Commands.SaveDocumentCommand.Id">
            <summary>
                <para>Gets the ID of the SaveDocumentCommand.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraRichEdit.Commands.RichEditCommandId"/> member that represents the command identifier.
</value>


        </member>
    </members>
</doc>
