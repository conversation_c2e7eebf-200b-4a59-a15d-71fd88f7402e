﻿using System.Collections.Generic;

namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    public class IrisServices
    {
        //Session
        public static ServiceRefIris.IrisSessionSdpBS CreateSessionClient()
        {
            ServiceRefIris.IrisSessionSdpBS sessionI = new ServiceRefIris.IrisSessionSdpBS();

            sessionI.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            //sessionI.Proxy = WebServicesProxyHelper.ServicesProxy;

            return sessionI;
        }

        //Dealer
        public static ServiceRefIrisDealer.IrisDealerSdpBS CreateDealerClient()
        {
            ServiceRefIrisDealer.IrisDealerSdpBS sessionI = new ServiceRefIrisDealer.IrisDealerSdpBS();

            sessionI.Credentials = WebServicesProxyHelper.ServicesCreadinal;
            //sessionI.Proxy = WebServicesProxyHelper.ServicesProxy;

            return sessionI;
        }

        //Open
        public static ServiceRefIris.SessionToken OpenSession()
        {
            ServiceRefIris.IrisSessionSdpBS Client = CreateSessionClient();

            ServiceRefIris.ClientSimpleLoginRequest clientLogin = new ServiceRefIris.ClientSimpleLoginRequest();
            clientLogin.UserLoginCd = "SYSKRM";
            clientLogin.UserPassword = "SYSKRM11";

            ServiceRefIris.SessionToken st = Client.OpenSimpleSession(clientLogin);

            return st;
        }

        ////Open
        //public static ServiceRefIris.MethodResultOfBoolean CloseSession(ServiceRefIris.SessionToken st)
        //{
        //    ServiceRefIris.IrisSessionSdpBS Client = CreateSessionClient();

        //    ServiceRefIris.SessionInfo sInfo = new ServiceRefIris.SessionInfo();

        //    sInfo.SessionId = st.SessionId;
        //    sInfo.SessionGuidId = st.SessionGuidId;

        //    return Client.CloseSession(sInfo);

        //}

        //Open
        public static bool CloseSession(ServiceRefIris.SessionToken st)
        {
            ServiceRefIris.IrisSessionSdpBS Client = CreateSessionClient();

            ServiceRefIris.SessionInfo sInfo = new ServiceRefIris.SessionInfo();

            sInfo.SessionId = st.SessionId;
            sInfo.SessionGuidId = st.SessionGuidId;

            return Client.CloseSession(sInfo); ;
        }

        //Check
        public static bool CheckSession(ServiceRefIris.SessionToken st)
        {
            bool boolCheckSession = true;

            ServiceRefIris.IrisSessionSdpBS sessionI = new ServiceRefIris.IrisSessionSdpBS();

            boolCheckSession = sessionI.CheckSessionByToken(st);

            return boolCheckSession;
        }

        public static IList<ServiceRefIrisDealer.DealerServiceInfoIttp> GetListBayi(string BayiKodu, string BayiAdi, ServiceRefIris.SessionToken SessionT)
        {
            ServiceRefIrisDealer.SessionToken ServiceTokenIris = new ServiceRefIrisDealer.SessionToken();
            ServiceTokenIris.SessionGuidId = SessionT.SessionGuidId;
            ServiceTokenIris.SessionId = SessionT.SessionId;

            ServiceRefIrisDealer.IrisDealerSdpBS Client = CreateDealerClient();

            ServiceRefIrisDealer.DealerCriteria dealerCriteria = new ServiceRefIrisDealer.DealerCriteria();
            if (BayiAdi != "")
            {
                dealerCriteria.DealerNameLike = BayiAdi;
            }
            else
            {
                dealerCriteria.DealerNameLike = null;
            }

            if (BayiKodu != "")
            {
                dealerCriteria.DealerCdEq = BayiKodu;
            }
            else
            {
                dealerCriteria.DealerCdEq = null;
            }

            //ServiceRefIrisDealer.DealerServiceInfoIttp[] GetList = Client.GetDealerServiceInfoListByCriteria(dealerCriteria, ServiceTokenIris);

            IList<ServiceRefIrisDealer.DealerServiceInfoIttp> GetList = Client.GetDealerServiceInfoListByCriteria(dealerCriteria, ServiceTokenIris);

            return GetList;
        }
    }
}