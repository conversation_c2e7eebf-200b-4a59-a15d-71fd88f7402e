﻿namespace Digiturk.Workflow.Digiflow.WorkFlowServicesHelper
{
    /// <summary>
    /// Satınalma Uygulaması ServisProxy Nesnesidir
    /// </summary>
    public class AppDataServicesManager
    {
        private Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData.AppDataService _AppDataServiceSoapClientObj;

        /// <summary>
        /// Satınalma Uygulaması ServisProxy Nesnesidir
        /// </summary>
        public Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData.AppDataService AppDataServiceSoapClientObj
        {
            get
            {
                if (_AppDataServiceSoapClientObj == null)
                {
                    _AppDataServiceSoapClientObj = new Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData.AppDataService();
                    _AppDataServiceSoapClientObj.Credentials = WebServicesProxyHelper.ServicesCreadinal;
                    _AppDataServiceSoapClientObj.Proxy = WebServicesProxyHelper.ServicesProxy;
                }
                return _AppDataServiceSoapClientObj;
            }
        }
    }
}