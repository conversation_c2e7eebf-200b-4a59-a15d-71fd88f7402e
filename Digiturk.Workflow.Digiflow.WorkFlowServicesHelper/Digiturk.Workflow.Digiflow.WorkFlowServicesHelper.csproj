﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9FA5D1F9-73FC-44D7-8165-A0F6BBAF7095}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Digiturk.Workflow.Digiflow.WorkFlowServicesHelper</RootNamespace>
    <AssemblyName>Digiturk.Workflow.Digiflow.WorkFlowServicesHelper</AssemblyName>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Digiturk.Workflow.Digiflow.DataAccessLayer">
      <HintPath>\\dtl1iis3\Deployment\Digiturk.Workflow.Digiflow.DataAccessLayer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AdPortalServices.cs" />
    <Compile Include="AdvanceTransfer.cs" />
    <Compile Include="AppDataServices.cs" />
    <Compile Include="BayiFotoServis.cs" />
    <Compile Include="SosyalMedyaServis.cs" />
    <Compile Include="AydinlatmaServis.cs" />
    <Compile Include="DTSatisliYayinAcma.cs" />
    <Compile Include="IrisServices.cs" />
    <Compile Include="PersonelProcesses.cs" />
    <Compile Include="EducationServices.cs" />
    <Compile Include="HRInformation.cs" />
    <Compile Include="PermissionProcess.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Web References\AydinlatmaService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\CreateBayiBelgeOnayAkis\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.OrganisationalSaleRecordBS\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Services.AvansAktar\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Services.HRConnect\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Services.HRInfosB\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Services.HRInfos\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Services.IseGirisPoldy\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\IrisWebServis\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\QlikviewYetkiVer\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\ServiceRefIrisDealer\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\ServiceRefIris\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="Web References\SosyalMedyaService\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.map</DependentUpon>
    </Compile>
    <Compile Include="WebServicesProxyHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="Properties\DataSources\System.Data.DataSet.datasource" />
    <None Include="Properties\DataSources\System.Data.DataTable.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Web References\AydinlatmaService\PersonalDataProtectionBS.wsdl" />
    <None Include="Web References\AydinlatmaService\PersonalDataProtectionBS.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\AydinlatmaService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\AydinlatmaService\ResponseWrapperOfGetAccountPermissionsResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\AydinlatmaService\ResponseWrapperOfGetAccountPermissionsResponseV2.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\AydinlatmaService\ResponseWrapperOfGetPermissionTextResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\AydinlatmaService\ResponseWrapperOfIsPermissionExistResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\AydinlatmaService\ResponseWrapperOfSaveAccountPermissionsResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\AydinlatmaService\TokenData.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\CreateBayiBelgeOnayAkis\createWorkflowNew.wsdl" />
    <None Include="Web References\CreateBayiBelgeOnayAkis\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\CancelOrganisationalSaleRecordResponseModel.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\CloseOrganisationalSaleRecordResponseModel.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\OrganisationalSaleRecordBS.wsdl" />
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\OrganisationalSaleRecordBS.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.OrganisationalSaleRecordBS\TokenData.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\Digiturk.Services.AvansAktar\PersonelBilgisi.wsdl" />
    <None Include="Web References\Digiturk.Services.AvansAktar\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Services.HRConnect\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Services.HRConnect\Service1.wsdl" />
    <None Include="Web References\Digiturk.Services.HRInfosB\PersonelBilgisi.wsdl" />
    <None Include="Web References\Digiturk.Services.HRInfosB\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Services.HRInfos\PersonelBilgisi.wsdl" />
    <None Include="Web References\Digiturk.Services.HRInfos\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Services.IseGirisPoldy\PersonelBilgisi.wsdl" />
    <None Include="Web References\Digiturk.Services.IseGirisPoldy\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess\PermissionProcess.wsdl" />
    <None Include="Web References\Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData\appdataservice.wsdl" />
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\EgitimModulu.wsdl" />
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\IrisWebServis\DeleteAllDocumentsForReferenceIdResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\DeleteDocumentByStoredDocumentIdResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\DeleteDocumentByStoredDocumentNameResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\GetDocumentListByReferenceId.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\GetDocumentSpecListByScreenCd.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\ReadDocumentByStoredDocumentIdResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\ReadDocumentContentByStoredDocumentNameResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\IrisWebServis\SaveDocumentResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\IrisWebServis\service.wsdl" />
    <None Include="Web References\IrisWebServis\UpdateDocumentMetaDataByStoredDocumentIdResult.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\QlikviewYetkiVer\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\QlikviewYetkiVer\yetkiver.wsdl" />
    <None Include="Web References\ServiceRefIrisDealer\CaseReasonDealerRel.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerCollectionQuotaInfo.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerInfo.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerServiceInfo.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerServiceInfoIttp.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerServiceRegion.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\DealerSiteVisit.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\IrisDealerSdpBS.wsdl" />
    <None Include="Web References\ServiceRefIrisDealer\IrisDealerSdpBS.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\IrisDealerSdpBS0.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\PartyRole.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\PartyRoleCharValue.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\ServiceRefIrisDealer\TokenData.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIris\IrisSession.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIris\IrisSessionSdpBS.wsdl" />
    <None Include="Web References\ServiceRefIris\IrisSessionSdpBS.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\ServiceRefIris\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\ServiceRefIris\ResponseBase.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIris\SessionInfo.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIris\SessionToken.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\ServiceRefIris\TokenData.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\PersonalDataProtectionBS.wsdl" />
    <None Include="Web References\SosyalMedyaService\PersonalDataProtectionBS.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Web References\SosyalMedyaService\Reference.map">
      <Generator>MSDiscoCodeGenerator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Web References\SosyalMedyaService\ResponseWrapperOfGetAccountPermissionsResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\ResponseWrapperOfGetAccountPermissionsResponseV2.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\ResponseWrapperOfGetPermissionTextResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\ResponseWrapperOfIsPermissionExistResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\ResponseWrapperOfSaveAccountPermissionsResponse.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
    <None Include="Web References\SosyalMedyaService\TokenData.datasource">
      <DependentUpon>Reference.map</DependentUpon>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WebReferences Include="Web References\" />
  </ItemGroup>
  <ItemGroup>
    <WebReferenceUrl Include="http://digiflowtest/Services/createWorkflowNew.asmx%3fop=CreateBayiBelgeOnayAkis">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\CreateBayiBelgeOnayAkis\</RelPath>
      <UpdateFromURL>http://digiflowtest/Services/createWorkflowNew.asmx%3fop=CreateBayiBelgeOnayAkis</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_CreateBayiBelgeOnayAkis_CreateWorkflowNew</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3331/EgitimModulu.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3331/EgitimModulu.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_EgitimModulu</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3333/appdataservice.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3333/appdataservice.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AppData_AppDataService</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:8081/PermissionProcess.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess\</RelPath>
      <UpdateFromURL>http://dtl1iis4:8081/PermissionProcess.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Workflow_Digiflow_ServicesHelper_PermissionProcess_PermissionProcess</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3331/PersonelBilgisi.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Services.HRInfos\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3331/PersonelBilgisi.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfos_PersonelBilgisi</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3332/Service1.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Services.HRConnect\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3332/Service1.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRConnect_Service1</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3331/PersonelBilgisi.asmx%3fop=AvansAktar">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Services.AvansAktar\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3331/PersonelBilgisi.asmx%3fop=AvansAktar</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_AvansAktar_PersonelBilgisi</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3335/PersonelBilgisi.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Services.HRInfosB\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3335/PersonelBilgisi.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_HRInfosB_PersonelBilgisi</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1iis4:3331/PersonelBilgisi.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.Services.IseGirisPoldy\</RelPath>
      <UpdateFromURL>http://dtl1iis4:3331/PersonelBilgisi.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_Services_IseGirisPoldy_PersonelBilgisi</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://dtl1qlikviewtst:82/yetkiver.asmx%3fop=YetkiVer">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\QlikviewYetkiVer\</RelPath>
      <UpdateFromURL>http://dtl1qlikviewtst:82/yetkiver.asmx%3fop=YetkiVer</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_QlikviewYetkiVer_Service1</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\AydinlatmaService\</RelPath>
      <UpdateFromURL>http://sdp-lcl-tst.digiturk.net/Maintenance_Test/virtual/basic/PersonalDataProtectionBS.svc%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_AydinlatmaService_PersonalDataProtectionBS</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\ServiceRefIrisDealer\</RelPath>
      <UpdateFromURL>http://sdp-lcl.digiturk.net/virtual/basic/IrisDealerSdpBS.svc%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIrisDealer_IrisDealerSdpBS</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\ServiceRefIris\</RelPath>
      <UpdateFromURL>http://sdp-lcl.digiturk.net/virtual/basic/IrisSessionSdpBS.svc%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_ServiceRefIris_IrisSessionSdpBS</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\Digiturk.OrganisationalSaleRecordBS\</RelPath>
      <UpdateFromURL>http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_Digiturk_OrganisationalSaleRecordBS_OrganisationalSaleRecordBS</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc%3fwsdl">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\SosyalMedyaService\</RelPath>
      <UpdateFromURL>http://test-sdp-lcl.digiturk.net/virtual/basic/PersonalDataProtectionBS.svc%3fwsdl</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_SosyalMedyaService_PersonalDataProtectionBS</CachedSettingsPropName>
    </WebReferenceUrl>
    <WebReferenceUrl Include="https://irisws.digiturk.net/storagews/service.asmx">
      <UrlBehavior>Dynamic</UrlBehavior>
      <RelPath>Web References\IrisWebServis\</RelPath>
      <UpdateFromURL>https://irisws.digiturk.net/storagews/service.asmx</UpdateFromURL>
      <ServiceLocationURL>
      </ServiceLocationURL>
      <CachedDynamicPropName>
      </CachedDynamicPropName>
      <CachedAppSettingsObjectName>Settings</CachedAppSettingsObjectName>
      <CachedSettingsPropName>Digiturk_Workflow_Digiflow_WorkFlowServicesHelper_IrisWebServis_IrisDocumentStorageWS</CachedSettingsPropName>
    </WebReferenceUrl>
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Services.HRConnect\Service1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Workflow.Digiflow.ServicesHelper.PermissionProcess\PermissionProcess.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Services.HRInfosB\PersonelBilgisi.disco" />
    <None Include="Web References\Digiturk.Services.AvansAktar\PersonelBilgisi.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Services.HRInfos\PersonelBilgisi.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\EgitimModulu.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper.AppData\appdataservice.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\Digiturk.Services.IseGirisPoldy\PersonelBilgisi.disco" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\QlikviewYetkiVer\yetkiver.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\IrisWebServis\service.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Web References\CreateBayiBelgeOnayAkis\createWorkflowNew.disco" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>COPY /Y "$(TargetPath)" \\dtl1iis3\Deployment</PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PreBuildEvent>if exist "$(TargetPath).locked" del "$(TargetPath).locked" if exist "$(TargetPath)" if not exist "$(TargetPath).locked" move "$(TargetPath)" "$(TargetPath).locked"
attrib -r c:\TFS\DigiflowPM\Digiturk.Workflow.Digiflow.WorkFlowServicesHelper\*.* /s</PreBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>