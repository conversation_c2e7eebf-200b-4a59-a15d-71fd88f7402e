# WebView Embedding Fix for DigiflowReact

## Issue Summary
The DigiflowReact application was not displaying properly in mobile app webviews due to restrictive security headers that prevented iframe/webview embedding.

## Root Cause Analysis

### Primary Issues
1. **X-Frame-Options: DENY** - Explicitly blocked all iframe/webview embedding
2. **CSP frame-ancestors 'none'** - Content Security Policy prevented embedding
3. **Missing mobile app origins** - CORS configuration didn't include webview origins

### Secondary Issues
- Environment configurations were set to maximum security (DENY/none)
- No webview detection logic for conditional security policies
- Missing mobile app schemes in allowed origins

## Solution Implemented

### 1. Updated Security Middleware (WebApi)
**File:** `src/DigiflowAPI.WebApi/Middlewares/UnifiedSecurityMiddleware.cs`

#### Changes Made:
- Added webview detection method `IsWebViewRequest()`
- Conditional X-Frame-Options: `SAMEORIGIN` for webviews, `DENY` for others
- Created webview-friendly CSP policy with `frame-ancestors 'self' capacitor://localhost ionic://localhost`
- Added detection patterns for <PERSON>gi<PERSON><PERSON><PERSON>, ReactNative-WebView, etc.

#### Key Code Changes:
```csharp
// X-Frame-Options: Allow webview embedding for mobile apps
if (IsWebViewRequest(context))
{
    headers["X-Frame-Options"] = "SAMEORIGIN";
}
else
{
    headers["X-Frame-Options"] = "DENY";
}

// WebView-friendly CSP
private const string WebViewContentSecurityPolicy = "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; " +
    "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; " +
    "font-src 'self' https://fonts.gstatic.com data:; " +
    "img-src 'self' data: https: blob:; " +
    "connect-src 'self' ws: wss: https:; " +
    "frame-ancestors 'self' capacitor://localhost ionic://localhost; " +
    "base-uri 'self'; " +
    "form-action 'self'";
```

### 2. Updated Mobile API Security
**File:** `src/DigiflowAPI.MobileApi/Middlewares/UnifiedSecurityMiddleware.cs`

#### Changes Made:
- Changed X-Frame-Options from `DENY` to `SAMEORIGIN`
- Updated CSP policies to allow webview embedding
- Both development and production CSP now include `frame-ancestors 'self' capacitor://localhost ionic://localhost`

### 3. Configuration Updates

#### Environment Configuration
**File:** `src/DigiflowAPI.WebApi/.env.example`
```bash
# Clickjacking Protection
# Note: Changed from DENY to SAMEORIGIN to allow webview embedding for mobile apps
DIGIFLOW_API_X_FRAME_OPTIONS=SAMEORIGIN
DIGIFLOW_API_FRAME_ANCESTORS=self
```

#### CORS Origins
**Files:** 
- `src/DigiflowAPI.WebApi/appsettings.json`
- `src/DigiflowAPI.WebApi/appsettings.Test.json`

Added mobile app origins:
```json
"MobileAppOrigins": [
  "http://localhost:8081",
  "http://localhost:19000",
  "http://localhost:19001",
  "http://localhost:19002",
  "exp://localhost:19000",
  "capacitor://localhost",
  "ionic://localhost",
  "http://********:8081",
  "http://***********/24",
  "digihrapp://",
  "file://",
  "about:blank"
]
```

## WebView Detection Logic

The middleware now detects webview requests using:

### User Agent Patterns:
- ReactNative-WebView
- DigiHR-App
- DigiflowMobile
- wv
- reactnative

### Headers:
- X-Mobile-App
- X-From-Mobile-WebView
- X-Is-Mobile
- X-Request-Source: DigiHRApp

## Security Considerations

### Maintained Security:
- Regular web requests still use `X-Frame-Options: DENY`
- CSP policies remain strict for non-webview requests
- Only trusted mobile app origins are allowed
- Webview detection prevents abuse

### Enhanced Security:
- Added specific mobile app origin validation
- Maintained nonce-based CSP for inline scripts
- Preserved all other security headers

## Testing Instructions

### 1. Test WebView Embedding
1. Deploy the updated API
2. Load DigiflowReact in DigiHRApp webview
3. Verify the application loads without frame-related errors
4. Check browser console for CSP violations

### 2. Verify Security Headers
```bash
# Test regular web request (should have DENY)
curl -I https://your-api-domain.com/api/health

# Test webview request (should have SAMEORIGIN)
curl -I -H "X-Mobile-App: true" https://your-api-domain.com/api/health
```

### 3. Browser Developer Tools
1. Open DigiflowReact in browser
2. Check Network tab for security headers
3. Verify CSP policy in Security tab
4. Test iframe embedding (should be blocked for regular requests)

## Deployment Checklist

- [ ] Deploy WebApi with updated middleware
- [ ] Deploy MobileApi with updated middleware
- [ ] Update environment variables if using .env files
- [ ] Test webview embedding in DigiHRApp
- [ ] Verify security headers are correctly applied
- [ ] Monitor for CSP violations in logs
- [ ] Test regular web access still works

## Rollback Plan

If issues occur, revert these changes:
1. Change `IsWebViewRequest()` to always return `false`
2. Restore `X-Frame-Options: DENY` for all requests
3. Restore original CSP with `frame-ancestors 'none'`

## Related Files Modified

1. `src/DigiflowAPI.WebApi/Middlewares/UnifiedSecurityMiddleware.cs`
2. `src/DigiflowAPI.MobileApi/Middlewares/UnifiedSecurityMiddleware.cs`
3. `src/DigiflowAPI.WebApi/.env.example`
4. `src/DigiflowAPI.WebApi/appsettings.json`
5. `src/DigiflowAPI.WebApi/appsettings.Test.json`

## Next Steps

1. Test the changes in development environment
2. Deploy to test environment and verify webview functionality
3. Monitor security logs for any issues
4. Deploy to production after successful testing
5. Update mobile app to ensure proper header sending
