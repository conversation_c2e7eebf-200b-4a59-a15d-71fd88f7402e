#!/bin/bash

echo "========================================"
echo "TEST 1: React App Direct Access (MacBook)"
echo "========================================"
echo ""

# JWT Authentication Configuration
# FILL IN YOUR CREDENTIALS HERE:
USERNAME="DTADPORTALTEST1"  # e.g., "DTUMKORKMAZ"
PASSWORD="xF66X6V43DGQ"  # Your AD password
API_BASE="https://digiflowtest.digiturk.com.tr"

echo "Testing React app with JWT authentication..."
echo "Username: $USERNAME"
echo ""

# Function to get JWT token
get_jwt_token() {
    echo "[AUTH] Getting JWT token..."

    # Prepare login request with all required fields
    login_payload=$(cat <<EOF
{
    "Username": "$USERNAME",
    "Password": "$PASSWORD",
    "Domain": "DIGITURK",
    "DeviceId": "test-device-$(date +%s)",
    "DeviceName": "MacBook-Test-Device"
}
EOF
)

    echo "[AUTH] Login payload prepared"

    # Login request to get JWT token
    login_response=$(curl -s -X POST "$API_BASE/auth/login" \
        -H "Content-Type: application/json" \
        -H "X-Mobile-App: true" \
        -H "X-Is-Mobile: true" \
        -H "Accept: application/json" \
        -d "$login_payload")

    echo "[AUTH] Login response received"

    # Extract token from response (try both accessToken and AccessToken)
    token=$(echo "$login_response" | grep -o '"[aA]ccessToken":"[^"]*' | cut -d'"' -f4)

    if [ -n "$token" ]; then
        echo "✅ JWT token acquired successfully"
        echo "Token: ${token:0:50}..."
        return 0
    else
        echo "❌ Failed to get JWT token"
        echo "Response: $login_response"
        return 1
    fi
}

# Get JWT token first
if ! get_jwt_token; then
    echo ""
    echo "❌ Cannot proceed without JWT token"
    echo "Please check your username and password in this script"
    exit 1
fi
echo ""

# Test 1.1: Basic React app access (no auth needed)
echo "[1.1] Testing React app HTTP status (public access)..."
status=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE")
echo "Status Code: $status"

if [ "$status" = "200" ]; then
    echo "✅ React app responds with 200 OK"
else
    echo "❌ React app returns status: $status"
fi
echo ""

# Test 1.2: Test authenticated API endpoint
echo "[1.2] Testing authenticated API endpoint..."
auth_status=$(curl -s -o /dev/null -w "%{http_code}" \
    -H "Authorization: Bearer $token" \
    "$API_BASE/api/auth/verify-session")
echo "Auth API Status: $auth_status"

if [ "$auth_status" = "200" ]; then
    echo "✅ Authenticated API access works"
else
    echo "❌ Authenticated API access failed: $auth_status"
fi
echo ""

# Test 1.3: Check for HTML content
echo "[1.3] Testing React app content type..."
content_type=$(curl -s -I "$API_BASE" | grep -i "content-type")
echo "Content-Type: $content_type"

if [[ $content_type == *"text/html"* ]]; then
    echo "✅ Content-Type is HTML"
else
    echo "❌ Content-Type is not HTML"
fi
echo ""

# Test 1.4: Check for React content
echo "[1.4] Testing for React content..."
content=$(curl -s "$API_BASE" | head -20)
if [[ $content == *"react"* ]] || [[ $content == *"React"* ]] || [[ $content == *"root"* ]]; then
    echo "✅ React content detected"
else
    echo "⚠️  No obvious React content detected"
fi
echo ""

# Test 1.5: Check X-Frame-Options
echo "[1.5] Testing X-Frame-Options header..."
frame_options=$(curl -s -I "$API_BASE" | grep -i "x-frame-options")
echo "X-Frame-Options: $frame_options"

if [[ $frame_options == *"DENY"* ]]; then
    echo "❌ X-Frame-Options blocks embedding (DENY)"
elif [[ $frame_options == *"SAMEORIGIN"* ]]; then
    echo "✅ X-Frame-Options allows same-origin embedding"
elif [[ -z "$frame_options" ]]; then
    echo "✅ No X-Frame-Options header (allows embedding)"
else
    echo "⚠️  Unknown X-Frame-Options value"
fi
echo ""

echo "========================================"
echo "TEST 1 RESULTS SUMMARY"
echo "========================================"
echo "Please report back:"
echo "1. Status code result"
echo "2. Content-Type result"
echo "3. React content detection result"
echo "4. X-Frame-Options result"
echo "5. Any error messages you see"
echo ""
