#!/bin/bash

echo "========================================"
echo "TEST 1: React App Direct Access (MacBook)"
echo "========================================"
echo ""
echo "Testing if React app loads directly..."
echo ""

# Test 1.1: Basic React app access
echo "[1.1] Testing React app HTTP status..."
status=$(curl -s -o /dev/null -w "%{http_code}" https://digiflowtest.digiturk.com.tr)
echo "Status Code: $status"

if [ "$status" = "200" ]; then
    echo "✅ React app responds with 200 OK"
else
    echo "❌ React app returns status: $status"
fi
echo ""

# Test 1.2: Check for HTML content
echo "[1.2] Testing React app content type..."
content_type=$(curl -s -I https://digiflowtest.digiturk.com.tr | grep -i "content-type")
echo "Content-Type: $content_type"

if [[ $content_type == *"text/html"* ]]; then
    echo "✅ Content-Type is HTML"
else
    echo "❌ Content-Type is not HTML"
fi
echo ""

# Test 1.3: Check for React content
echo "[1.3] Testing for React content..."
content=$(curl -s https://digiflowtest.digiturk.com.tr | head -20)
if [[ $content == *"react"* ]] || [[ $content == *"React"* ]] || [[ $content == *"root"* ]]; then
    echo "✅ React content detected"
else
    echo "⚠️  No obvious React content detected"
fi
echo ""

# Test 1.4: Check X-Frame-Options
echo "[1.4] Testing X-Frame-Options header..."
frame_options=$(curl -s -I https://digiflowtest.digiturk.com.tr | grep -i "x-frame-options")
echo "X-Frame-Options: $frame_options"

if [[ $frame_options == *"DENY"* ]]; then
    echo "❌ X-Frame-Options blocks embedding (DENY)"
elif [[ $frame_options == *"SAMEORIGIN"* ]]; then
    echo "✅ X-Frame-Options allows same-origin embedding"
elif [[ -z "$frame_options" ]]; then
    echo "✅ No X-Frame-Options header (allows embedding)"
else
    echo "⚠️  Unknown X-Frame-Options value"
fi
echo ""

echo "========================================"
echo "TEST 1 RESULTS SUMMARY"
echo "========================================"
echo "Please report back:"
echo "1. Status code result"
echo "2. Content-Type result" 
echo "3. React content detection result"
echo "4. X-Frame-Options result"
echo "5. Any error messages you see"
echo ""
