#!/bin/bash

echo "========================================"
echo "CONFIGURE CREDENTIALS FOR JWT TESTING"
echo "========================================"
echo ""
echo "This script will help you configure your AD credentials"
echo "for JWT testing in the test scripts."
echo ""

# Get credentials from user
read -p "Enter your AD username (e.g., DTUMKORKMAZ): " username
read -s -p "Enter your AD password: " password
echo ""
echo ""

if [ -z "$username" ] || [ -z "$password" ]; then
    echo "❌ Username and password are required"
    exit 1
fi

echo "Updating test scripts with your credentials..."

# Update test-01-react-app-access.sh
sed -i.bak "s/YOUR_AD_USERNAME_HERE/$username/g" test-01-react-app-access.sh
sed -i.bak "s/YOUR_AD_PASSWORD_HERE/$password/g" test-01-react-app-access.sh

# Update test-02-webview-headers.sh
sed -i.bak "s/YOUR_AD_USERNAME_HERE/$username/g" test-02-webview-headers.sh
sed -i.bak "s/YOUR_AD_PASSWORD_HERE/$password/g" test-02-webview-headers.sh

# Update test-03-workflow-url.sh
sed -i.bak "s/YOUR_AD_USERNAME_HERE/$username/g" test-03-workflow-url.sh
sed -i.bak "s/YOUR_AD_PASSWORD_HERE/$password/g" test-03-workflow-url.sh

echo "✅ Credentials configured in all test scripts"
echo ""
echo "You can now run the tests:"
echo "  ./test-01-react-app-access.sh"
echo "  ./test-02-webview-headers.sh"
echo "  ./test-03-workflow-url.sh"
echo ""
echo "Note: Backup files (.bak) were created for safety"
echo ""

# Make scripts executable
chmod +x test-01-react-app-access.sh
chmod +x test-02-webview-headers.sh
chmod +x test-03-workflow-url.sh

echo "✅ Test scripts are now executable"
echo ""
echo "SECURITY NOTE:"
echo "Your credentials are now stored in the test scripts."
echo "Remember to remove them or delete the scripts when done testing."
echo ""
