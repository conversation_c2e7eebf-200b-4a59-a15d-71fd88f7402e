@echo off
setlocal enabledelayedexpansion

REM ===================================================================
REM DigiflowAPI Complete Environment Variables Setup for PRODUCTION
REM ===================================================================
REM This script sets ALL environment variables based on .env.production values
REM Run as Administrator for system-wide variables
REM ===================================================================

echo.
echo ===================================================================
echo DigiflowAPI Complete Environment Variables Setup for PRODUCTION
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script MUST be run as Administrator
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo WARNING: You are about to configure PRODUCTION environment variables
echo This will set system-wide environment variables for production use
echo.
set /p "CONFIRM=Are you sure you want to continue? (yes/no): "
if /i not "%CONFIRM%"=="yes" (
    echo Setup cancelled by user
    exit /b 0
)

echo Setting all PRODUCTION environment variables as SYSTEM variables...
echo.

REM =============================================================================
REM DATABASE CONFIGURATION
REM =============================================================================
echo Setting Database Configuration...
setx DIGIFLOW_DB_HOST "dpsms16" /M
setx DIGIFLOW_DB_PORT "1521" /M
setx DIGIFLOW_DB_SERVICE_NAME "FLOWPROD" /M

REM Framework Database
setx DIGIFLOW_FRAMEWORK_USER "FRAMEWORK" /M
setx DIGIFLOW_FRAMEWORK_PASSWORD "FRAMEWORK_PROD_PASSWORD" /M

REM Workflow Database
setx DIGIFLOW_WORKFLOW_USER "DT_WORKFLOW" /M
setx DIGIFLOW_WORKFLOW_PASSWORD "DT_WORKFLOW_PROD_PASSWORD" /M

REM Application Database
setx DIGIFLOW_APP_USER "DT_APPLICATION_USR" /M
setx DIGIFLOW_APP_PASSWORD "DT_APPLICATION_USR_PROD_PASSWORD" /M

REM Required for ConnectionStringBuilder
setx DIGIFLOW_DB_PASSWORD "DT_APPLICATION_USR_PROD_PASSWORD" /M
setx DIGIFLOW_SQL_PASSWORD "DT_APPLICATION_USR_PROD_PASSWORD" /M

REM Inquiry Database
setx DIGIFLOW_INQUIRY_USER "INQUIRY" /M
setx DIGIFLOW_INQUIRY_PASSWORD "INQUIRY_PROD_PASSWORD" /M

REM ESSB Database
setx DIGIFLOW_ESSB_USER "ESSB_USR" /M
setx DIGIFLOW_ESSB_PASSWORD "ESSB_PROD_PASSWORD" /M

REM Netsis Database
setx DIGIFLOW_NETSIS_USER "NETSISORTAK" /M
setx DIGIFLOW_NETSIS_PASSWORD "NETSIS_PROD_PASSWORD" /M

REM Saperion Database
setx DIGIFLOW_SAPERION_USER "saperion" /M
setx DIGIFLOW_SAPERION_PASSWORD "SAPERION_PROD_PASSWORD" /M

echo Database configuration set.
echo.

REM =============================================================================
REM JWT CONFIGURATION
REM =============================================================================
echo Setting JWT Configuration...
setx DIGIFLOW_JWT_SECRET "PRODUCTION_JWT_SECRET_MINIMUM_32_CHARS_REPLACE_THIS" /M
setx DIGIFLOW_JWT_ISSUER "DigiflowAPI" /M
setx DIGIFLOW_JWT_AUDIENCE "DigiflowAPI" /M
setx DIGIFLOW_JWT_ACCESS_TOKEN_MINUTES "15" /M
setx DIGIFLOW_JWT_REFRESH_TOKEN_DAYS "30" /M
setx DIGIFLOW_JWT_WEBSOCKET_TOKEN_MINUTES "120" /M
setx DIGIFLOW_MAX_REFRESH_TOKEN_DAYS "90" /M
setx DIGIFLOW_MAX_REFRESH_TOKENS_PER_USER "3" /M

echo JWT configuration set.
echo.

REM =============================================================================
REM LDAP CONFIGURATION
REM =============================================================================
echo Setting LDAP Configuration...
setx DIGIFLOW_LDAP_PATH "LDAP://dtldap.digiturk.local" /M
setx DIGIFLOW_LDAP_DOMAIN "DIGITURK" /M
setx LDAP_USERNAME "DIGITURK\\Digiflow_sa" /M
setx LDAP_PASSWORD "PRODUCTION_LDAP_PASSWORD_REPLACE_THIS" /M

echo LDAP configuration set.
echo.

REM =============================================================================
REM ENVIRONMENT SETTINGS
REM =============================================================================
echo Setting Environment Settings...
setx ASPNETCORE_ENVIRONMENT "Production" /M

echo Environment settings set.
echo.

REM =============================================================================
REM API CONFIGURATION
REM =============================================================================
echo Setting API Configuration...
setx DIGIFLOW_API_BASE_URL "https://digiflow.digiturk.com.tr/api" /M
setx DIGIFLOW_API_DEBUG_MODE "false" /M
setx DIGIFLOW_API_BYPASS_SSL "false" /M

echo API configuration set.
echo.

REM =============================================================================
REM CORS CONFIGURATION
REM =============================================================================
echo Setting CORS Configuration...
setx DIGIFLOW_API_CORS_ALLOWED_ORIGINS "https://digiflow.digiturk.com.tr,http://digiflow.digiturk.com.tr" /M

echo CORS configuration set.
echo.

REM =============================================================================
REM SECURITY SETTINGS
REM =============================================================================
echo Setting Security Configuration...
setx DIGIFLOW_API_SYSTEM_ADMIN_USERS "DTKBAYRAKTAR,DTZKUCUK,DIGIFLOW_SA,SPSMOSS_SA,DTBGUNAY,DTMKASAPOGLU,DTUMKORKMAZ,DTYAELMAS" /M
setx HMAC_SECRET_KEY "PRODUCTION_HMAC_SECRET_MINIMUM_32_CHARS_REPLACE_THIS" /M
setx ANTI_TAMPERING_SECRET "PRODUCTION_ANTI_TAMPERING_SECRET_MINIMUM_32_CHARS_REPLACE_THIS" /M

REM WebView Security Configuration (for mobile app embedding)
setx DIGIFLOW_API_X_FRAME_OPTIONS "SAMEORIGIN" /M
setx DIGIFLOW_API_FRAME_ANCESTORS "self" /M

echo Security configuration set.
echo.

REM =============================================================================
REM RATE LIMITING CONFIGURATION
REM =============================================================================
echo Setting Rate Limiting Configuration...
setx DIGIFLOW_API_ENABLE_RATE_LIMITING "true" /M
setx DIGIFLOW_API_RATE_LIMIT_DEFAULT "100" /M

echo Rate limiting configuration set.
echo.

REM =============================================================================
REM LOGGING CONFIGURATION
REM =============================================================================
echo Setting Logging Configuration...
setx DIGIFLOW_API_LOG_LEVEL "Information" /M
setx DIGIFLOW_API_LOG_FILE_PATH "./logs/digiflow-webapi-prod-.txt" /M
setx DIGIFLOW_API_LOG_DETAILED "false" /M

echo Logging configuration set.
echo.

REM =============================================================================
REM CONNECTION STRINGS (PRODUCTION)
REM =============================================================================
echo Setting Connection Strings...
setx CONNECTIONSTRING_FRAMEWORKCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWPROD)));User Id=FRAMEWORK;Password=FRAMEWORK_PROD_PASSWORD;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_DEFAULTCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWPROD)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR_PROD_PASSWORD;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_DT_WORKFLOW "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWPROD)));User Id=DT_WORKFLOW;Password=DT_WORKFLOW_PROD_PASSWORD;Pooling=true" /M
setx CONNECTIONSTRING_REPORTCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWPROD)));User Id=DT_WORKFLOW;Password=DT_WORKFLOW_PROD_PASSWORD;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_DBSCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=KURUMSAL.DIGITURK.LOCAL)));User Id=INQUIRY;Password=INQUIRY_PROD_PASSWORD;Self Tuning=false" /M
setx CONNECTIONSTRING_NETSISCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1527))(CONNECT_DATA=(SID=FUSION)));User Id=NETSISORTAK;Password=NETSIS_PROD_PASSWORD;Pooling=true;Statement Cache Size=10;Self Tuning=false" /M
setx CONNECTIONSTRING_SAPERION "Data Source=DTL1SAPERION01;Initial Catalog=SAPERION;Persist Security Info=True;User ID=saperion;Password=SAPERION_PROD_PASSWORD;Pooling=False;" /M

echo Connection strings set.
echo.

REM =============================================================================
REM EMAIL CONFIGURATION
REM =============================================================================
echo Setting Email Configuration...
setx DIGIFLOW_API_MAIL_SERVER "************" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS "<EMAIL>" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS_USERNAME "noreply_workflow" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS_PASSWORD "PRODUCTION_MAIL_PASSWORD_REPLACE_THIS" /M
setx DIGIFLOW_SMTP_SERVER "************" /M
setx DIGIFLOW_SMTP_USERNAME "noreply_workflow" /M
setx DIGIFLOW_SMTP_PASSWORD "PRODUCTION_MAIL_PASSWORD_REPLACE_THIS" /M
setx DIGIFLOW_API_MAIL_DEBUG_MODE "false" /M

echo Email configuration set.
echo.

REM =============================================================================
REM WORKFLOW CONFIGURATION
REM =============================================================================
echo Setting Workflow Configuration...
setx DIGIFLOW_API_WORKFLOW_MAIL_PARAMS "PRODUCTION_WORKFLOW_MAIL_PARAMS_REPLACE_THIS" /M

echo Workflow configuration set.
echo.

REM =============================================================================
REM REDIS CONFIGURATION
REM =============================================================================
echo Setting Redis Configuration...
setx DIGIFLOW_REDIS_CONNECTION "localhost:6379" /M
setx DIGIFLOW_API_USE_REDIS "true" /M

echo Redis configuration set.
echo.

echo ===================================================================
echo ALL PRODUCTION ENVIRONMENT VARIABLES HAVE BEEN SET!
echo ===================================================================
echo.
echo CRITICAL SECURITY NOTICE:
echo This script contains placeholder passwords marked with "REPLACE_THIS"
echo You MUST update these with actual production passwords before use:
echo.
echo - All database passwords
echo - JWT secret key
echo - LDAP password
echo - HMAC secret key
echo - Anti-tampering secret
echo - Email password
echo - Workflow mail parameters
echo.
echo IMPORTANT NEXT STEPS:
echo.
echo 1. UPDATE ALL PLACEHOLDER PASSWORDS with actual production values
echo 2. RESTART IIS to apply the changes:
echo    iisreset
echo.
echo 3. Verify the application:
echo    - Check: https://digiflow.digiturk.com.tr/api/diagnostics/health
echo    - Check: https://digiflow.digiturk.com.tr/api/swagger/index.html
echo.
echo 4. Test WebView embedding with mobile app
echo.
echo 5. Check logs if issues persist:
echo    C:\inetpub\wwwroot\DigiflowAPI-Production\logs\
echo.

pause
