@echo off
setlocal enabledelayedexpansion

REM ===================================================================
REM DigiflowAPI Complete Environment Variables Setup for TEST
REM ===================================================================
REM This script sets ALL environment variables based on .env.test values
REM Run as Administrator for system-wide variables
REM ===================================================================

echo.
echo ===================================================================
echo DigiflowAPI Complete Environment Variables Setup for TEST
echo ===================================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script MUST be run as Administrator
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo Setting all TEST environment variables as SYSTEM variables...
echo.

REM =============================================================================
REM DATABASE CONFIGURATION
REM =============================================================================
echo Setting Database Configuration...
setx DIGIFLOW_DB_HOST "dpsms16" /M
setx DIGIFLOW_DB_PORT "1521" /M
setx DIGIFLOW_DB_SERVICE_NAME "FLOWDEV" /M

REM Framework Database
setx DIGIFLOW_FRAMEWORK_USER "FRAMEWORK" /M
setx DIGIFLOW_FRAMEWORK_PASSWORD "FRAMEWORK" /M

REM Workflow Database
setx DIGIFLOW_WORKFLOW_USER "***********" /M
setx DIGIFLOW_WORKFLOW_PASSWORD "***********" /M

REM Application Database
setx DIGIFLOW_APP_USER "DT_APPLICATION_USR" /M
setx DIGIFLOW_APP_PASSWORD "DT_APPLICATION_USR" /M

REM Required for ConnectionStringBuilder
setx DIGIFLOW_DB_PASSWORD "DT_APPLICATION_USR" /M
setx DIGIFLOW_SQL_PASSWORD "DT_APPLICATION_USR" /M

REM Inquiry Database
setx DIGIFLOW_*******_USER "*******" /M
setx DIGIFLOW_*******_PASSWORD "*******" /M

REM ESSB Database
setx DIGIFLOW_ESSB_USER "ESSB_USR" /M
setx DIGIFLOW_ESSB_PASSWORD "FINDER" /M

REM Netsis Database
setx DIGIFLOW_NETSIS_USER "NETSISORTAK" /M
setx DIGIFLOW_NETSIS_PASSWORD "****" /M

REM Saperion Database
setx DIGIFLOW_SAPERION_USER "********" /M
setx DIGIFLOW_SAPERION_PASSWORD "********" /M

echo Database configuration set.
echo.

REM =============================================================================
REM JWT CONFIGURATION
REM =============================================================================
echo Setting JWT Configuration...
setx DIGIFLOW_JWT_SECRET "EuaewXe+kLs4NMlvRsC6cbwYI/q+TvwWgMzutBZjrH0=" /M
setx DIGIFLOW_JWT_ISSUER "DigiflowAPI" /M
setx DIGIFLOW_JWT_AUDIENCE "DigiflowAPI" /M
setx DIGIFLOW_JWT_ACCESS_TOKEN_MINUTES "15" /M
setx DIGIFLOW_JWT_REFRESH_TOKEN_DAYS "30" /M
setx DIGIFLOW_JWT_WEBSOCKET_TOKEN_MINUTES "120" /M
setx DIGIFLOW_MAX_REFRESH_TOKEN_DAYS "90" /M
setx DIGIFLOW_MAX_REFRESH_TOKENS_PER_USER "3" /M

echo JWT configuration set.
echo.

REM =============================================================================
REM LDAP CONFIGURATION
REM =============================================================================
echo Setting LDAP Configuration...
setx DIGIFLOW_LDAP_PATH "LDAP://dtldap.digiturk.local" /M
setx DIGIFLOW_LDAP_DOMAIN "DIGITURK" /M
setx LDAP_USERNAME "DIGITURK\\Digiflow_sa" /M
setx LDAP_PASSWORD "Digif16up+-" /M

echo LDAP configuration set.
echo.

REM =============================================================================
REM ENVIRONMENT SETTINGS
REM =============================================================================
echo Setting Environment Settings...
setx ASPNETCORE_ENVIRONMENT "Test" /M

echo Environment settings set.
echo.

REM =============================================================================
REM API CONFIGURATION
REM =============================================================================
echo Setting API Configuration...
setx DIGIFLOW_API_BASE_URL "http://digiflowtest.digiturk.com.tr/api" /M
setx DIGIFLOW_API_DEBUG_MODE "true" /M
setx DIGIFLOW_API_BYPASS_SSL "true" /M

echo API configuration set.
echo.

REM =============================================================================
REM CORS CONFIGURATION
REM =============================================================================
echo Setting CORS Configuration...
setx DIGIFLOW_API_CORS_ALLOWED_ORIGINS "http://localhost:3000,http://localhost:5173,http://localhost:8080,http://digiflowtest.digiturk.com.tr,https://digiflowtest.digiturk.com.tr" /M

echo CORS configuration set.
echo.

REM =============================================================================
REM SECURITY SETTINGS
REM =============================================================================
echo Setting Security Configuration...
setx DIGIFLOW_API_SYSTEM_ADMIN_USERS "DTKBAYRAKTAR,DTZKUCUK,DIGIFLOW_SA,SPSMOSS_SA,DTBGUNAY,DTMKASAPOGLU,DTUMKORKMAZ,DTYAELMAS" /M
setx HMAC_SECRET_KEY "3JVWMH08h8rSBETvub/1t0LIXDQWt70MdmIljQQ7+tw=" /M
setx ANTI_TAMPERING_SECRET "/KnPMGuOvF9YhN1wgYYnbhNgJigzGzpm6aiLym7Go9E=" /M

REM WebView Security Configuration (for mobile app embedding)
setx DIGIFLOW_API_X_FRAME_OPTIONS "SAMEORIGIN" /M
setx DIGIFLOW_API_FRAME_ANCESTORS "self" /M

echo Security configuration set.
echo.

REM =============================================================================
REM RATE LIMITING CONFIGURATION
REM =============================================================================
echo Setting Rate Limiting Configuration...
setx DIGIFLOW_API_ENABLE_RATE_LIMITING "false" /M
setx DIGIFLOW_API_RATE_LIMIT_DEFAULT "1000" /M

echo Rate limiting configuration set.
echo.

REM =============================================================================
REM LOGGING CONFIGURATION
REM =============================================================================
echo Setting Logging Configuration...
setx DIGIFLOW_API_LOG_LEVEL "Debug" /M
setx DIGIFLOW_API_LOG_FILE_PATH "./logs/digiflow-webapi-.txt" /M
setx DIGIFLOW_API_LOG_DETAILED "true" /M

echo Logging configuration set.
echo.

REM =============================================================================
REM CONNECTION STRINGS
REM =============================================================================
echo Setting Connection Strings...
setx CONNECTIONSTRING_FRAMEWORKCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=FRAMEWORK;Password=FRAMEWORK;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_DEFAULTCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=DT_APPLICATION_USR;Password=DT_APPLICATION_USR;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_*********** "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true" /M
setx CONNECTIONSTRING_REPORTCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=FLOWDEV)));User Id=***********;Password=***********;Pooling=true;Self Tuning=false" /M
setx CONNECTIONSTRING_DBSCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=dpsms16.digiturk.local)(PORT=1521))(CONNECT_DATA=(SID=KURUMSAL.DIGITURK.LOCAL)));User Id=*******;Password=*******;Self Tuning=false" /M
setx CONNECTIONSTRING_NETSISCONNECTION "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=***********)(PORT=1527))(CONNECT_DATA=(SID=FUSION)));User Id=NETSISORTAK;Password=****;Pooling=true;Statement Cache Size=10;Self Tuning=false" /M
setx CONNECTIONSTRING_SAPERION "Data Source=DTL1SAPERION01;Initial Catalog=SAPERION;Persist Security Info=True;User ID=********;Password=********;Pooling=False;" /M

echo Connection strings set.
echo.

REM =============================================================================
REM PASSWORDBOX CONFIGURATION
REM =============================================================================
echo Setting PasswordBox Configuration...
setx DIGIFLOW_API_ESSBDURUM "E" /M
setx DIGIFLOW_API_DBS_*******_USER "*******" /M
setx DIGIFLOW_API_DBSLIVE_*******_UNIQUE "4!fedL0w" /M
setx DIGIFLOW_API_DBSLIVE_RAPOR_UNIQUE "8up!5f0rrt" /M

echo PasswordBox configuration set.
echo.

REM =============================================================================
REM WEB SERVICES CONFIGURATION
REM =============================================================================
echo Setting Web Services Configuration...
setx DIGIFLOW_API_WEB_SERVICES_DOMAIN "DIGITURK" /M
setx DIGIFLOW_API_WEB_SERVICES_USERNAME "Digiflow_sa" /M
setx DIGIFLOW_API_WEB_SERVICES_PASSWORD "Digif16up+-" /M
setx DIGIFLOW_API_SERVICE_USERNAME "Digiflow_sa" /M
setx DIGIFLOW_API_SERVICE_PASSWORD "Digif16up+-" /M

echo Web services configuration set.
echo.

REM =============================================================================
REM EMAIL CONFIGURATION
REM =============================================================================
echo Setting Email Configuration...
setx DIGIFLOW_API_MAIL_SERVER "************" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS "<EMAIL>" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS_USERNAME "noreply_workflow" /M
setx DIGIFLOW_API_MAIL_FROM_ADDRESS_PASSWORD "flow123456+" /M
setx DIGIFLOW_SMTP_SERVER "************" /M
setx DIGIFLOW_SMTP_USERNAME "noreply_workflow" /M
setx DIGIFLOW_SMTP_PASSWORD "flow123456+" /M
setx DIGIFLOW_API_MAIL_DEBUG_MODE "true" /M
setx DIGIFLOW_API_MAIL_DEBUG_ADDRESS "<EMAIL>" /M

echo Email configuration set.
echo.

REM =============================================================================
REM WORKFLOW CONFIGURATION
REM =============================================================================
echo Setting Workflow Configuration...
setx DIGIFLOW_API_WORKFLOW_MAIL_PARAMS "ad9bBOUpHG1st9IlCOvZA9DCTJKj7XTlewXqZpa4xWo/m0f/ZXwzFpTy9cdYK53Hx2MQqWxlyxSVT5lg5waY6LC3p5i77oc4pHAEGgnKFbAuL48SNlMELo9dIiUOo2RmdTprZ/SAkyKF03+gmRGRexw3+qCFnr/iVOx/58S075o=" /M

echo Workflow configuration set.
echo.

REM =============================================================================
REM REDIS CONFIGURATION
REM =============================================================================
echo Setting Redis Configuration...
setx DIGIFLOW_REDIS_CONNECTION "localhost:6379" /M
setx DIGIFLOW_API_USE_REDIS "false" /M

echo Redis configuration set.
echo.

REM =============================================================================
REM EXTERNAL SERVICE CONFIGURATION
REM =============================================================================
echo Setting External Service Configuration...
setx DIGIFLOW_API_EDUCATION_WS_USERNAME "WEBSERVICE" /M
setx DIGIFLOW_API_EDUCATION_WS_PASSWORD "w17211s" /M
setx DIGIFLOW_API_ORG_SALE_URL "http://test-sdp-lcl.digiturk.net/virtual/basic/OrganisationalSaleRecordBS.svc" /M
setx DIGIFLOW_API_ORG_SALE_USERNAME "SYSIQ" /M
setx DIGIFLOW_API_ORG_SALE_PASSWORD "SYSIQ111" /M
setx DIGIFLOW_API_ORG_SALE_COMPANY "DIGITURK" /M
setx DIGIFLOW_API_ORG_SALE_APPLICATION "DIGIPORT" /M
setx DIGIFLOW_API_ORG_SALE_CHANNEL "DEFAULT" /M

echo External service configuration set.
echo.

echo ===================================================================
echo ALL TEST ENVIRONMENT VARIABLES HAVE BEEN SET!
echo ===================================================================
echo.
echo IMPORTANT NEXT STEPS:
echo.
echo 1. RESTART IIS to apply the changes:
echo    iisreset
echo.
echo 2. Verify the application:
echo    - Check: http://digiflowtest.digiturk.com.tr/api/diagnostics/health
echo    - Check: http://digiflowtest.digiturk.com.tr/api/swagger/index.html
echo.
echo 3. Check logs if issues persist:
echo    C:\inetpub\wwwroot\DigiflowAPI-Test\logs\stdout\
echo.
echo 4. Run diagnostics:
echo    diagnose-startup-issues.bat
echo.

pause