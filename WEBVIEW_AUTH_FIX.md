# WebView Authentication Fix

## Issue
The React app loaded in the WebView was showing a 401 authentication error ("Kimlik Doğrulaması Gerekli") because the JWT token from the mobile app wasn't being passed to the WebView's API requests.

## Root Cause
The WebView was intentionally NOT injecting JWT tokens into API requests for security reasons. This caused the `/auth/verify-session` endpoint to fail with 401 because no Authorization header was present.

## Solution Implemented
Updated the WebViewScreen component to securely inject JWT tokens into API requests made by the React app.

### Changes Made in `/src/screens/WebViewScreen.tsx`:

1. **Added JWT Token Storage**
   ```javascript
   // Store JWT token for API requests (secure implementation)
   let jwtToken = null;
   ```

2. **Handle JWT_TOKEN Messages**
   ```javascript
   case 'JWT_TOKEN':
     // Update the stored JWT token
     jwtToken = data.payload.token;
     console.log('[WebView] JWT token received for API authentication');
     break;
   ```

3. **Request JWT Token on Load**
   ```javascript
   // Also request JWT token for API authentication
   window.ReactNativeWebView.postMessage(JSON.stringify({
     type: 'REQUEST_SECURE_SESSION',
     timestamp: Date.now()
   }));
   ```

4. **Inject JWT Token in Fetch Requests**
   ```javascript
   // Add JWT token for API calls (including auth endpoints)
   if (jwtToken && (url.includes('/api/') || url.includes('/auth/'))) {
     newOptions.headers['Authorization'] = 'Bearer ' + jwtToken;
     console.log('[API] Adding JWT token to request:', url);
   }
   ```

5. **Inject JWT Token in XHR Requests**
   ```javascript
   // Add JWT token for API calls (including auth endpoints)
   if (jwtToken && (url.includes('/api/') || url.includes('/auth/'))) {
     xhr.setRequestHeader('Authorization', 'Bearer ' + jwtToken);
     console.log('[API] Adding JWT token to XHR request:', url);
   }
   ```

## How It Works Now

1. **On WebView Load**:
   - WebView requests JWT token from mobile app using `REQUEST_SECURE_SESSION`
   - Mobile app responds with current JWT token
   - Token is stored in WebView's JavaScript context

2. **API Requests**:
   - All fetch() and XMLHttpRequest calls to `/api/` or `/auth/` endpoints automatically include the JWT token
   - The Authorization header is added: `Bearer <jwt-token>`

3. **Token Refresh**:
   - If a 401 error occurs, WebView sends `AUTH_ERROR` message
   - Mobile app refreshes the token and sends the new token back
   - WebView updates its stored token for future requests

## Security Considerations

- JWT token is only injected for API requests to trusted endpoints (`/api/` and `/auth/`)
- Token is stored in the WebView's JavaScript context, not exposed globally
- Token is automatically refreshed when authentication fails
- Mobile headers (X-Mobile-App, X-From-Mobile-WebView) are still included for identification

## Testing

1. Login to the mobile app
2. Navigate to any WebView screen (e.g., Delegation workflow)
3. The React app should load without authentication errors
4. Check console logs for JWT token injection messages
5. Verify API calls include Authorization header

## Related Files

- `/src/screens/WebViewScreen.tsx` - Main WebView component (updated)
- `/src/screens/WebViewScreenAuth.tsx` - Alternative implementation with full JWT support (created for reference)
- API Endpoint: `/auth/verify-session` - No changes needed, already supports JWT via [Authorize] attribute

## Logs to Watch

**Success Case**:
```
[WebView] JWT token received for API authentication
[API] Adding JWT token to request: https://digiflowtest.digiturk.com.tr/api/auth/verify-session
```

**Previous Error (Fixed)**:
```
Session verification failed - user not authenticated
HTTP GET /auth/verify-session responded 401
```